import 'package:flutter/material.dart';
import '../../Health/models/health_record.dart';
import '../../Cattle/models/cattle.dart';
import 'report_data.dart';
import 'chart_data.dart';

class CattleReportData extends ReportData {
  final List<HealthRecord> healthRecords;
  final List<Cattle> cattle;

  CattleReportData({
    required this.healthRecords,
    required this.cattle,
    DateTime? startDate,
    DateTime? endDate,
    String? filterCriteria,
  }) : super(
          startDate: startDate,
          endDate: endDate,
          filterCriteria: filterCriteria,
        );

  @override
  String get reportTitle => 'Cattle Report';

  @override
  List<DataColumn> get tableColumns => const [
        DataColumn(label: Text('Tag ID')),
        DataColumn(label: Text('Name')),
        DataColumn(label: Text('Category')),
        DataColumn(label: Text('Status')),
        DataColumn(label: Text('Age')),
        DataColumn(label: Text('Health Status')),
      ];

  @override
  List<DataRow> get tableRows {
    return cattle.map((cattle) {
      final healthStatus = _getHealthStatus(cattle.id);
      return DataRow(cells: [
        DataCell(Text(cattle.tagId)),
        DataCell(Text(cattle.name)),
        DataCell(Text(cattle.category ?? 'Unknown')),
        DataCell(Text(cattle.status ?? 'Active')),
        DataCell(Text(_calculateAge(cattle.dateOfBirth))),
        DataCell(Text(healthStatus)),
      ]);
    }).toList();
  }

  String _calculateAge(DateTime? birthDate) {
    if (birthDate == null) return 'Unknown';
    final now = DateTime.now();
    final age = now.difference(birthDate);
    final years = age.inDays ~/ 365;
    final months = (age.inDays % 365) ~/ 30;
    return '$years years, $months months';
  }

  String _getHealthStatus(String cattleId) {
    final records = healthRecords.where((record) => record.cattleId == cattleId);
    if (records.isEmpty) return 'Healthy';
    final latestRecord = records.reduce((a, b) => 
      a.date.isAfter(b.date) ? a : b);
    return latestRecord.status;
  }

  @override
  Map<String, dynamic> get summaryData {
    final Map<String, int> distribution = {};
    final Map<String, int> statusDistribution = {};

    for (var c in cattle) {
      final cate