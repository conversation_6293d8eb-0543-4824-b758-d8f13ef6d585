import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/transactions_report_data.dart';

class TransactionSummaryTab extends StatelessWidget {
  final TransactionsReportData reportData;

  const TransactionSummaryTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final summaryData = reportData.summaryData;
    final categoryData = reportData.categoryData;

    return SingleChildScrollView(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildSummaryCard(
                    'Total Income',
                    reportData.currencyFormat.format(summaryData['totalIncome']),
                    Icons.arrow_upward,
                    Colors.green,
                  ),
                  const SizedBox(width: 16),
                  _buildSummaryCard(
                    'Total Expense',
                    reportData.currencyFormat.format(summaryData['totalExpense']),
                    Icons.arrow_downward,
                    Colors.red,
                  ),
                  const SizedBox(width: 16),
                  _buildSummaryCard(
                    'Net Income',
                    reportData.currencyFormat.format(summaryData['netIncome']),
                    Icons.account_balance,
                    Colors.blue,
                  ),
                ],
              ),
            ),
          ),
          if (categoryData.isNotEmpty) ...[
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Expenses by Category',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(
              height: 300,
              child: PieChart(
                PieChartData(
                  sections: _buildPieChartSections(categoryData),
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                  startDegreeOffset: -90,
                ),
              ),
            ),
          ],
          const SizedBox(height: 16),
          if (reportData.dailyData.isNotEmpty) ...[
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Daily Transaction Trend',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(
              height: 300,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: false),
                  titlesData: const FlTitlesData(show: false),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: _buildLineChartSpots(reportData.dailyData),
                      isCurved: true,
                      color: Colors.blue,
                      barWidth: 2,
                      dotData: const FlDotData(show: false),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<PieChartSectionData> _buildPieChartSections(Map<String, double> categoryData) {
    final colors = [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
    ];

    final total = categoryData.values.fold(0.0, (sum, value) => sum + value);
    var colorIndex = 0;

    return categoryData.entries.map((entry) {
      final percentage = (entry.value / total) * 100;
      final color = colors[colorIndex % colors.length];
      colorIndex++;

      return PieChartSectionData(
        value: entry.value,
        title: '${entry.key}\n${percentage.toStringAsFixed(1)}%',
        color: color,
        radius: 100,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  List<FlSpot> _buildLineChartSpots(Map<DateTime, double> dailyData) {
    final sortedDates = dailyData.keys.toList()..sort();
    if (sortedDates.isEmpty) return [];

    final minDate = sortedDates.first;
    return sortedDates.map((date) {
      final days = date.difference(minDate).inDays.toDouble();
      return FlSpot(days, dailyData[date]!);
    }).toList();
  }
}
