class MilkRecord {
  final String id;
  final String cattleId;
  final double quantity;
  final DateTime date;
  final String shift; // Morning/Evening
  final String? notes;
  final double morningQuantity;
  final double eveningQuantity;
  final double fatContent;
  final double totalRevenue;

  MilkRecord({
    required this.id,
    required this.cattleId,
    required this.quantity,
    required this.date,
    required this.shift,
    this.notes,
    required this.morningQuantity,
    required this.eveningQuantity,
    required this.fatContent,
    required this.totalRevenue,
  });

  double get totalQuantity => morningQuantity + eveningQuantity;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'cattleId': cattleId,
      'quantity': quantity,
      'date': date.toIso8601String(),
      'shift': shift,
      'notes': notes,
      'morningQuantity': morningQuantity,
      'eveningQuantity': eveningQuantity,
      'fatContent': fatContent,
      'totalRevenue': totalRevenue,
    };
  }

  factory MilkRecord.fromMap(Map<String, dynamic> map) {
    return MilkRecord(
      id: map['id'],
      cattleId: map['cattleId'],
      quantity: (map['quantity'] ?? 0.0) as double,
      date: DateTime.parse(map['date']),
      shift: map['shift'],
      notes: map['notes'],
      morningQuantity: (map['morningQuantity'] ?? 0.0) as double,
      eveningQuantity: (map['eveningQuantity'] ?? 0.0) as double,
      fatContent: (map['fatContent'] ?? 0.0) as double,
      totalRevenue: (map['totalRevenue'] ?? 0.0) as double,
    );
  }
}
