import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/milk_record_isar.dart';
import '../services/milk_service.dart';
import '../widgets/milk_record_card.dart';
import '../dialogs/milk_form_dialog.dart';
import '../../../utils/message_utils.dart';
import '../../../widgets/fab_styles.dart';
import '../../widgets/date_range_filter_widget.dart';
import '../../widgets/filter_status_bar.dart';
import '../../widgets/sort_widget.dart';
import '../../widgets/index.dart';

class CattleMilkRecordsTab extends StatefulWidget {
  final CattleIsar cattle;
  final List<MilkRecordIsar> milkRecords;
  final VoidCallback onRefresh;

  const CattleMilkRecordsTab({
    Key? key,
    required this.cattle,
    required this.milkRecords,
    required this.onRefresh,
  }) : super(key: key);

  @override
  State<CattleMilkRecordsTab> createState() => _CattleMilkRecordsTabState();
}

class _CattleMilkRecordsTabState extends State<CattleMilkRecordsTab> {
  final MilkService _milkService = MilkService();
  
  // Filtering and sorting
  DateTime? _startDate;
  DateTime? _endDate;
  String _sortBy = 'date';
  bool _sortAscending = false;
  
  // Cached filtered records
  List<MilkRecordIsar> _filteredRecords = [];
  
  // Colors
  final Color _recordsColor = Colors.green;

  @override
  void initState() {
    super.initState();
    _applyFiltersAndSort();
  }

  @override
  void didUpdateWidget(CattleMilkRecordsTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.milkRecords != widget.milkRecords) {
      _applyFiltersAndSort();
    }
  }

  void _applyFiltersAndSort() {
    List<MilkRecordIsar> filtered = List.from(widget.milkRecords);
    
    // Apply date filtering
    if (_startDate != null || _endDate != null) {
      filtered = filtered.where((record) {
        final recordDate = record.date;
        if (recordDate == null) return false;
        
        if (_startDate != null && recordDate.isBefore(_startDate!)) {
          return false;
        }
        if (_endDate != null && recordDate.isAfter(_endDate!)) {
          return false;
        }
        return true;
      }).toList();
    }
    
    // Apply sorting
    filtered.sort((a, b) {
      int comparison = 0;
      
      switch (_sortBy) {
        case 'date':
          comparison = (a.date ?? DateTime(0)).compareTo(b.date ?? DateTime(0));
          break;
        case 'yield':
          comparison = a.totalYield.compareTo(b.totalYield);
          break;
        case 'morning':
          comparison = (a.morningYield ?? 0.0).compareTo(b.morningYield ?? 0.0);
          break;
        case 'evening':
          comparison = (a.eveningYield ?? 0.0).compareTo(b.eveningYield ?? 0.0);
          break;
      }
      
      return _sortAscending ? comparison : -comparison;
    });
    
    setState(() {
      _filteredRecords = filtered;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.milkRecords.isEmpty) {
      return _buildEmptyState();
    }

    return Scaffold(
      body: Column(
        children: [
          _buildControls(),
          Expanded(
            child: _buildRecordsList(_filteredRecords),
          ),
        ],
      ),
      floatingActionButton: FabStyles.add(
        onPressed: _addMilkRecord,
        tooltip: 'Add Milk Record',
      ),
    );
  }

  Widget _buildControls() {
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      child: Column(
        children: [
          // Date Range Filter
          DateRangeFilterWidget(
            startDate: _startDate,
            endDate: _endDate,
            onStartDateChanged: (date) {
              setState(() {
                _startDate = date;
              });
              _applyFiltersAndSort();
            },
            onEndDateChanged: (date) {
              setState(() {
                _endDate = date;
              });
              _applyFiltersAndSort();
            },
            onClearFilter: () {
              setState(() {
                _startDate = null;
                _endDate = null;
              });
              _applyFiltersAndSort();
            },
            themeColor: _recordsColor,
          ),
          const SizedBox(height: 8),
          
          // Sort Widget
          SortWidget(
            sortBy: _sortBy,
            sortAscending: _sortAscending,
            sortFields: const [
              SortField(value: 'date', label: 'Date', icon: Icons.calendar_today, iconColor: Colors.blue),
              SortField(value: 'yield', label: 'Total Yield', icon: Icons.water_drop, iconColor: Colors.green),
              SortField(value: 'morning', label: 'Morning Yield', icon: Icons.wb_sunny, iconColor: Colors.orange),
              SortField(value: 'evening', label: 'Evening Yield', icon: Icons.nights_stay, iconColor: Colors.purple),
            ],
            onSortByChanged: (sortBy) {
              setState(() {
                _sortBy = sortBy ?? 'date';
              });
              _applyFiltersAndSort();
            },
            onSortAscendingChanged: (ascending) {
              setState(() {
                _sortAscending = ascending;
              });
              _applyFiltersAndSort();
            },
            themeColor: _recordsColor,
          ),
          const SizedBox(height: 8),
          
          // Filter Status Bar
          FilterStatusBar.milk(
            filterStates: {},
            totalCount: widget.milkRecords.length,
            filteredCount: _filteredRecords.length,
            onClearFilters: _clearFilters,
            defaultStartDate: DateTime.now().subtract(const Duration(days: 30)),
            defaultEndDate: DateTime.now(),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordsList(List<MilkRecordIsar> records) {
    if (records.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.filter_list_off,
              size: 64,
              color: _recordsColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No Records Found',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: _recordsColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Try adjusting your filters or add a new record',
              style: TextStyle(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _addMilkRecord,
              icon: const Icon(Icons.add),
              label: const Text('Add Milk Record'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _recordsColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: _getResponsivePadding()),
      itemCount: records.length,
      itemBuilder: (context, index) {
        final record = records[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: MilkRecordCard(
            record: record,
            cattle: widget.cattle,
            isSelected: false,
            isSelectionMode: false,
            onTap: () => _editMilkRecord(record),
            onEdit: () => _editMilkRecord(record),
            onDelete: () => _deleteMilkRecord(record),
            compact: false,
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 300),
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 32),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: _recordsColor.withAlpha(76)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: EmptyState.custom(
        icon: Icons.water_drop_outlined,
        message: 'No Milk Records',
        subtitle: 'Start by adding the first milk record for ${widget.cattle.name}',
        color: _recordsColor,
        hasData: false,
        action: EmptyState.createActionButton(
          onPressed: () => _addMilkRecord(),
          icon: Icons.add,
          label: 'Add First Record',
          backgroundColor: _recordsColor,
        ),
      ),
    );
  }

  // Filter helper methods
  bool _hasActiveFilters() {
    return _startDate != null || _endDate != null;
  }

  void _clearFilters() {
    setState(() {
      _startDate = null;
      _endDate = null;
    });
    _applyFiltersAndSort();
  }

  List<String> _getActiveFiltersList() {
    List<String> filters = [];
    if (_startDate != null || _endDate != null) {
      if (_startDate != null && _endDate != null) {
        filters.add('Date: ${DateFormat('MMM dd').format(_startDate!)} - ${DateFormat('MMM dd, yyyy').format(_endDate!)}');
      } else if (_startDate != null) {
        filters.add('From: ${DateFormat('MMM dd, yyyy').format(_startDate!)}');
      } else if (_endDate != null) {
        filters.add('Until: ${DateFormat('MMM dd, yyyy').format(_endDate!)}');
      }
    }
    return filters;
  }

  // Action handlers
  void _addMilkRecord() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MilkFormDialog(
          preselectedCattle: widget.cattle,
        ),
      ),
    ).then((result) {
      if (result == true) {
        widget.onRefresh();
      }
    });
  }

  void _editMilkRecord(MilkRecordIsar record) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MilkFormDialog(
          record: record,
        ),
      ),
    ).then((result) {
      if (result == true) {
        widget.onRefresh();
      }
    });
  }

  Future<void> _deleteMilkRecord(MilkRecordIsar record) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Record'),
        content: const Text('Are you sure you want to delete this milk record?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && record.businessId != null) {
      try {
        await _milkService.deleteMilkRecord(record.businessId!);
        widget.onRefresh();

        if (mounted) {
          MessageUtils.showSuccess(context, 'Record deleted successfully');
        }
      } catch (e) {
        if (mounted) {
          MessageUtils.showError(context, 'Error deleting record: $e');
        }
      }
    }
  }

  // Responsive design helpers
  double _getResponsivePadding() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 12.0;
    if (screenWidth < 1200) return 16.0;
    return 20.0;
  }
}
