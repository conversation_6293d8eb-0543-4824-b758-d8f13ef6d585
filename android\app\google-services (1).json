import 'package:flutter/material.dart';
import '../models/milk_record.dart';
import '../dialogs/milk_form_dialog.dart';
import '../milk_tabs/production_summary_tab.dart';
import '../milk_tabs/milk_sales_tab.dart';
import '../milk_tabs/alerts_tab.dart';
import '../../Cattle/models/cattle.dart';
import '../../../services/milk_record_service.dart';
import '../../../services/csv_export_service.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class MilkRecordsScreen extends StatefulWidget {
  const MilkRecordsScreen({Key? key}) : super(key: key);

  @override
  MilkRecordsScreenState createState() => MilkRecordsScreenState();
}

class MilkRecordsScreenState extends State<MilkRecordsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<MilkRecord> _milkRecords = [];
  final List<Cattle> _cattle = []; // Ensure this is a List of Cattle

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadMilkRecords();
  }

  Future<void> _loadMilkRecords() async {
    final records = await MilkRecordService().getMilkRecords();
    if (mounted) {
      setState(() {
        _milkRecords.clear();
        _milkRecords.addAll(records);
      });
    }
  }

  Future<void> _syncData() async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final milkRecordService = MilkRecordService();

    scaffoldMessenger.showSnackBar(
      const SnackBar(content: Text('Syncing data...')),
    );

    final success = await milkRecordService.sy