import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../Transactions/models/transaction.dart';
import 'report_data.dart';
import 'chart_data.dart';

class TransactionsReportData extends ReportData {
  final List<Transaction> transactions;
  final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');

  TransactionsReportData({
    required this.transactions,
    required DateTime startDate,
    required DateTime endDate,
  }) : super(startDate: startDate, endDate: endDate);

  @override
  String get reportTitle => 'Transactions Report';

  @override
  List<DataColumn> get tableColumns => [
        const DataColumn(label: Text('Date')),
        const DataColumn(label: Text('Type')),
        const DataColumn(label: Text('Category')),
        const DataColumn(label: Text('Description')),
        const DataColumn(label: Text('Amount')),
      ];

  @override
  List<DataRow> get tableRows => filteredTransactions.map((transaction) {
        return DataRow(
          cells: [
            DataCell(Text(transaction.formattedDate)),
            DataCell(Text(transaction.type)),
            DataCell(Text(transaction.category)),
            DataCell(Text(transaction.description)),
            DataCell(Text(
              currencyFormat.format(transaction.amount),
              style: TextStyle(
                color: transaction.type == 'Income' ? Colors.green : Colors.red,
              ),
            )),
          ],
        );
      }).toList();

  @override
  Map<String, double> get summaryData {
    final summary = <String, double>{
      'totalIncome': 0,
      'totalExpense': 0,
      'netIncome': 0,
      'averageTransaction': 0,
    };

    if (transactions.isEmpty) return summary;

    for (var transaction in transactions) {
      if (transaction.type == 'Income') {
        summary['totalIncome'] =
            (summary['totalIncome'] ?? 0) + transaction.amount;
      } else {
        summary['totalExpense'] =
            (summary['totalExpense'] ?? 0) + transaction.amount;
      }
    }

    summary['net