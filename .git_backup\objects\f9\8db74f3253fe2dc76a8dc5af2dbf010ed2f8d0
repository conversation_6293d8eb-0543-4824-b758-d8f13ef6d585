import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logging/logging.dart';
import '../Dashboard/Cattle/models/animal_type.dart';
import '../Dashboard/Cattle/models/breed_category.dart';
import '../Dashboard/Transactions/models/category.dart';
import '../Dashboard/Cattle/models/cattle.dart';
import '../Dashboard/Transactions/models/transaction.dart';
import '../Dashboard/Farm Setup/models/farm.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'utils/responsive_helper.dart';
import 'utils/responsive_layout.dart';
import 'theme/responsive_theme.dart';

class DatabaseHelper with ChangeNotifier {
  static final Logger _logger = Logger('DatabaseHelper');

  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static DatabaseHelper get instance => _instance;

  // Milk Record methods
  static const String _milkRecordsKey = 'milk_records_db';

  Future<void> insertMilkRecord(Map<String, dynamic> record) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recordsJson = prefs.getString(_milkRecordsKey);
      List<Map<String, dynamic>> records = [];

      if (recordsJson != null) {
        final List<dynamic> decoded = jsonDecode(recordsJson);
        records = decoded.cast<Map<String, dynamic>>().toList();
      }

      records.add(record);
      await prefs.setString(_milkRecordsKey, jsonEncode(records));
      notifyListeners();
    } catch (e) {
      _logger.severe('Error inserting milk record', e);
      throw Exception('Failed to insert milk record: $e');
    }
  }

  // Animal Type methods
  static const String _animalTypesKey = 'animal_types';

  Future<void> _saveAnimalTypes(List<AnimalType> types) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
        _animalTypesKey, jsonEncode(types.map((e) => e.toMap()).toList()));
  }

  Future<List<AnimalType>> getAnimalTypes() async {
    final prefs = await SharedPreferences.getInstance();
    final typesJson = prefs.getString(_animalTypesKey);
    if (typesJson == null) {
      // Initialize with default animal types
      final defaultTypes = [
        AnimalType(
          id: const Uuid().v4(),
          name: 'Cow',
          icon: FontAwesomeIcons.cow,
          defaultGestationDays: 283,
          defaultHeatCycleDays: 21,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        AnimalType(
          id: const Uuid().v4(),
          name: 'Buffalo',
          icon: FontAwesomeIcons.hippo,
          defaultGestationDays: 310,
          defaultHeatCycleDays: 21,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        AnimalType(
          id: const Uuid().v4(),
          name: 'Goat',
          icon: FontAwesomeIcons.kiwiBird,
          defaultGestationDays: 150,
          defaultHeatCycleDays: 21,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        AnimalType(
          id: const Uuid().v4(),
          name: 'Sheep',
          icon: FontAwesomeIcons.paw,
          defaultGestationDays: 152,
          defaultHeatCycleDays: 17,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        AnimalType(
          id: const Uuid().v4(),
          name: 'Horse',
          icon: FontAwesomeIcons.horse,
          defaultGestationDays: 340,
          defaultHeatCycleDays: 21,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];
      await _saveAnimalTypes(defaultTypes);
      return defaultTypes;
    }

    try {
      final List<dynamic> decoded = jsonDecode(typesJson);
      return decoded
          .map((e) => AnimalType.fromMap(e as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.severe('Error decoding animal types', e);
      return [];
    }
  }

  Future<void> createAnimalType(AnimalType type) async {
    final types = await getAnimalTypes();
    types.add(type);
    await _saveAnimalTypes(types);
  }

  Future<void> updateAnimalType(AnimalType type) async {
    final types = await getAnimalTypes();
    final index = types.indexWhere((t) => t.id == type.id);
    if (index != -1) {
      types[index] = type;
      await _saveAnimalTypes(types);
    }
  }

  Future<void> deleteAnimalType(String id) async {
    final types = await getAnimalTypes();
    types.removeWhere((t) => t.id == id);
    await _saveAnimalTypes(types);
  }

  // Category methods
  static const String _categoriesKey = 'categories';

  Future<List<Category>> getCategories() async {
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = prefs.getString(_categoriesKey);
    if (categoriesJson == null) {
      // Initialize with default categories
      final defaultCategories = [
        // Income categories
        Category(
          id: const Uuid().v4(),
          name: 'Milk Sales',
          description: 'Income from milk sales',
          type: 'Income',
          icon: Icons.local_drink,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: const Uuid().v4(),
          name: 'Cattle Sale',
          description: 'Income from cattle sales',
          type: 'Income',
          icon: Icons.pets,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: const Uuid().v4(),
          name: 'Other Income',
          description: 'Other sources of income',
          type: 'Income',
          icon: Icons.attach_money,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        // Expense categories
        Category(
          id: const Uuid().v4(),
          name: 'Feed',
          description: 'Expenses for animal feed',
          type: 'Expense',
          icon: Icons.grass,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: const Uuid().v4(),
          name: 'Veterinary',
          description: 'Veterinary and medical expenses',
          type: 'Expense',
          icon: Icons.medical_services,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: const Uuid().v4(),
          name: 'Labor',
          description: 'Labor and workforce expenses',
          type: 'Expense',
          icon: Icons.engineering,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: const Uuid().v4(),
          name: 'Equipment',
          description: 'Equipment and maintenance expenses',
          type: 'Expense',
          icon: Icons.build,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Category(
          id: const Uuid().v4(),
          name: 'Other Expense',
          description: 'Other miscellaneous expenses',
          type: 'Expense',
          icon: Icons.money_off,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      await saveCategories(defaultCategories);
      return defaultCategories;
    }

    final List<dynamic> decoded = jsonDecode(categoriesJson);
    return decoded
        .map((json) => Category.fromMap(json as Map<String, dynamic>))
        .toList();
  }

  Future<void> saveCategories(List<Category> categories) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
        _categoriesKey, jsonEncode(categories.map((c) => c.toMap()).toList()));
  }

  Future<List<Category>> getIncomeCategories() async {
    final categories = await getCategories();
    return categories.where((c) => c.type == 'Income').toList();
  }

  Future<List<Category>> getExpenseCategories() async {
    final categories = await getCategories();
    return categories.where((c) => c.type == 'Expense').toList();
  }

  Future<void> createCategory(Category category) async {
    final allCategories = await getCategories();
    allCategories.add(category);
    await saveCategories(allCategories);
  }

  Future<void> updateCategory(Category category) async {
    final allCategories = await getCategories();
    final index = allCategories.indexWhere((c) => c.id == category.id);
    if (index != -1) {
      allCategories[index] = category;
      await saveCategories(allCategories);
    }
  }

  Future<void> deleteCategory(String id) async {
    final allCategories = await getCategories();
    allCategories.removeWhere((c) => c.id == id);
    await saveCategories(allCategories);
  }

  // Category methods
  static const String _cattleBreedsKey = 'cattle_breeds';

  Future<void> _saveBreedCategories(
      List<BreedCategory> breeds, String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
        key, jsonEncode(breeds.map((e) => e.toMap()).toList()));
  }

  Future<List<BreedCategory>> getCattleBreeds() async {
    final prefs = await SharedPreferences.getInstance();
    final breedsJson = prefs.getString(_cattleBreedsKey);
    if (breedsJson == null) {
      // Get all animal types to create breeds for each
      final animalTypes = await getAnimalTypes();
      final defaultBreeds = <BreedCategory>[];

      // Create default breeds for each animal type
      for (final animalType in animalTypes) {
        if (animalType.name.toLowerCase() == 'cow') {
          defaultBreeds.addAll([
            BreedCategory(
              id: const Uuid().v4(),
              name: 'Holstein Friesian',
              description: 'High milk-producing dairy cattle breed',
              icon: Icons.pets_outlined,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              animalTypeId: animalType.id,
            ),
            BreedCategory(
              id: const Uuid().v4(),
              name: 'Jersey',
              description: 'Known for high butterfat content milk',
              icon: Icons.pets_outlined,
             