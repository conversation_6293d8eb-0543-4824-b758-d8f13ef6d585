import 'dart:convert';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class GoogleDriveService {
  static const _clientId =
      '1234567890-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com';
  static const _clientSecret = 'GOCSPX-abcdefghijklmnopqrstuvwxyz1234';
  static const _scopes = [drive.DriveApi.driveFileScope];

  late drive.DriveApi _driveApi;
  late AuthClient _client;

  static final GoogleDriveService _instance = GoogleDriveService._internal();

  factory GoogleDriveService() {
    return _instance;
  }

  GoogleDriveService._internal();

  Future<bool> initialize() async {
    try {
      final clientId = ClientId(_clientId, _clientSecret);
      _client =
          await clientViaUserConsent(clientId, _scopes, _promptForConsent);
      _driveApi = drive.DriveApi(_client);
      return true;
    } catch (e) {
      debugPrint('Error initializing Google Drive service: $e');
      return false;
    }
  }

  void _promptForConsent(String url) {
    debugPrint('Please go to the following URL and grant access:');
    debugPrint(url);
    debugPrint('\nAfter granting access, return here and press enter.');
  }

  Future<String?> uploadBackup(String farmId, Map<String, dynamic> data) async {
    try {
      final timestamp = DateTime.now().toIso8601String();
      final fileName = 'cattle_manager_backup_${farmId}_$timestamp.json';
      final content = jsonEncode(data);

      final file = drive.File()
        ..name = fileName