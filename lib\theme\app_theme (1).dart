import 'package:flutter/material.dart';
import '../../Health/models/health_record.dart';
import '../../Cattle/models/cattle.dart';
import 'report_data.dart';
import 'chart_data.dart';

class CattleReportData extends ReportData {
  final List<HealthRecord> healthRecords;
  final List<Cattle> cattle;

  CattleReportData({
    required this.healthRecords,
    required this.cattle,
    DateTime? startDate,
    DateTime? endDate,
    String? filterCriteria,
  }) : super(
          startDate: startDate,
          endDate: endDate,
          filterCriteria: filterCriteria,
        );

  @override
  String get reportTitle => 'Cattle Report';

  @override
  List<DataColumn> get tableColumns => const [
        DataColumn(label: Text('Tag ID')),
        DataColumn(label: Text('Name')),
        