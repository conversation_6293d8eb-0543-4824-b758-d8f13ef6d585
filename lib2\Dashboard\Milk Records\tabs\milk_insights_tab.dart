import 'package:flutter/material.dart';
// import 'package:intl/intl.dart'; // Unused import
import '../models/milk_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/milk_service.dart';

class MilkInsightsTab extends StatefulWidget {
  final List<MilkRecordIsar> milkRecords;
  final Map<String, CattleIsar> cattleMap;

  const MilkInsightsTab({
    Key? key,
    required this.milkRecords,
    required this.cattleMap,
  }) : super(key: key);

  @override
  State<MilkInsightsTab> createState() => _MilkInsightsTabState();
}

class _MilkInsightsTabState extends State<MilkInsightsTab> {
  // Multi-colors for milk insights following the pattern
  static const _insightsColor = Color(0xFFE74C3C); // Red for insights
  static const _productionColor = Color(0xFF1976D2); // Blue for production
  static const _sessionColor = Color(0xFF2E7D32); // Green for sessions
  static const _cattleColor = Color(0xFF8E44AD); // Purple for cattle

  final MilkService _milkService = MilkService();

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        // Refresh data if needed
      },
      child: ListView(
        padding: EdgeInsets.all(_getResponsivePadding()),
        children: [
          _buildInsightsHeader(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildProductionInsights(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildPredictiveAnalytics(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildCattlePerformanceInsights(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildSeasonalInsights(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildRecommendations(),
        ],
      ),
    );
  }

  Widget _buildInsightsHeader() {
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _insightsColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.insights,
                color: _insightsColor,
                size: _getResponsiveIconSize(),
              ),
              const SizedBox(width: 12),
              Text(
                'Milk Production Insights',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: _insightsColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'AI-powered insights to optimize your milk production',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductionInsights() {
    final insights = _getProductionInsights();
    
    return _buildInsightCard(
      'Production Performance',
      Icons.trending_up,
      _productionColor,
      insights,
    );
  }

  Widget _buildPredictiveAnalytics() {
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _insightsColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.trending_up,
                color: _insightsColor,
                size: _getResponsiveIconSize(),
              ),
              SizedBox(width: _getResponsiveSpacing()),
              Text(
                'Predictive Analytics',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _insightsColor,
                ),
              ),
            ],
          ),
          SizedBox(height: _getResponsiveSpacing()),
          FutureBuilder<Map<String, dynamic>>(
            future: _milkService.getPredictiveAnalytics(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20),
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              if (snapshot.hasError) {
                return Center(
                  child: Text(
                    'Error loading predictions: ${snapshot.error}',
                    style: TextStyle(color: Colors.red),
                  ),
                );
              }

              final analytics = snapshot.data ?? {};
              return _buildPredictiveContent(analytics);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPredictiveContent(Map<String, dynamic> analytics) {
    final forecast = analytics['forecast'] ?? 'No forecast available';
    final trend = analytics['trend'] ?? 'Unknown';
    final confidence = analytics['confidence'] ?? 0.0;
    final nextWeekPrediction = analytics['nextWeekPrediction'] ?? 0.0;
    final recommendations = analytics['recommendations'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Forecast Summary
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Production Forecast',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[800],
                ),
              ),
              const SizedBox(height: 8),
              Text(forecast),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    _getTrendIcon(trend),
                    color: _getTrendColor(trend),
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '$trend (${confidence.toStringAsFixed(1)}% confidence)',
                    style: TextStyle(
                      color: _getTrendColor(trend),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // Next Week Prediction
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.calendar_view_week, color: Colors.green[700]),
              const SizedBox(width: 8),
              Text(
                'Next Week Prediction: ${nextWeekPrediction.toStringAsFixed(1)}L',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green[700],
                ),
              ),
            ],
          ),
        ),

        // Top Recommendations
        if (recommendations.isNotEmpty) ...[
          const SizedBox(height: 16),
          Text(
            'AI Recommendations',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: _insightsColor,
            ),
          ),
          const SizedBox(height: 8),
          ...recommendations.take(2).map((rec) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  size: 16,
                  color: Colors.amber[700],
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    rec.toString(),
                    style: const TextStyle(fontSize: 13),
                  ),
                ),
              ],
            ),
          )),
        ],
      ],
    );
  }

  IconData _getTrendIcon(String trend) {
    switch (trend.toLowerCase()) {
      case 'increasing':
        return Icons.trending_up;
      case 'decreasing':
        return Icons.trending_down;
      default:
        return Icons.trending_flat;
    }
  }

  Color _getTrendColor(String trend) {
    switch (trend.toLowerCase()) {
      case 'increasing':
        return Colors.green;
      case 'decreasing':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildCattlePerformanceInsights() {
    final insights = _getCattlePerformanceInsights();

    return _buildInsightCard(
      'Cattle Performance',
      Icons.pets,
      _cattleColor,
      insights,
    );
  }

  Widget _buildSeasonalInsights() {
    final insights = _getSeasonalInsights();
    
    return _buildInsightCard(
      'Seasonal Patterns',
      Icons.calendar_today,
      _sessionColor,
      insights,
    );
  }

  Widget _buildRecommendations() {
    final recommendations = _getRecommendations();
    
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _insightsColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb,
                color: _insightsColor,
                size: _getResponsiveIconSize(),
              ),
              const SizedBox(width: 12),
              Text(
                'Recommendations',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _insightsColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...recommendations.map((recommendation) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 6,
                  height: 6,
                  margin: const EdgeInsets.only(top: 6),
                  decoration: BoxDecoration(
                    color: _insightsColor,
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    recommendation,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[800],
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildInsightCard(String title, IconData icon, Color color, List<String> insights) {
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: _getResponsiveIconSize(),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...insights.map((insight) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.check_circle,
                  color: color,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    insight,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[800],
                      height: 1.3,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  List<String> _getProductionInsights() {
    final records = widget.milkRecords;
    final insights = <String>[];
    
    if (records.isEmpty) {
      insights.add('No production data available for analysis');
      return insights;
    }
    
    // Calculate average daily production
    final totalProduction = records.fold<double>(0.0, (sum, record) => sum + record.totalYield);
    final avgDaily = totalProduction / records.length;
    
    insights.add('Average daily production: ${avgDaily.toStringAsFixed(1)} liters');
    
    // Analyze session distribution
    final morningTotal = records.fold<double>(0.0, (sum, record) => sum + (record.morningAmount ?? 0.0));
    final eveningTotal = records.fold<double>(0.0, (sum, record) => sum + (record.eveningAmount ?? 0.0));
    
    if (morningTotal > eveningTotal * 1.2) {
      insights.add('Morning sessions are significantly more productive');
    } else if (eveningTotal > morningTotal * 1.2) {
      insights.add('Evening sessions are significantly more productive');
    } else {
      insights.add('Morning and evening sessions are well balanced');
    }
    
    return insights;
  }

  List<String> _getCattlePerformanceInsights() {
    final records = widget.milkRecords;
    final insights = <String>[];
    
    if (records.isEmpty) {
      insights.add('No cattle performance data available');
      return insights;
    }
    
    // Group records by cattle
    final Map<String, List<MilkRecordIsar>> cattleRecords = {};
    for (final record in records) {
      final cattleId = record.cattleBusinessId ?? 'unknown';
      cattleRecords.putIfAbsent(cattleId, () => []).add(record);
    }
    
    insights.add('${cattleRecords.length} cattle are actively producing milk');
    
    return insights;
  }

  List<String> _getSeasonalInsights() {
    final records = widget.milkRecords;
    final insights = <String>[];
    
    if (records.isEmpty) {
      insights.add('No seasonal data available for analysis');
      return insights;
    }
    
    insights.add('Seasonal analysis requires more historical data');
    
    return insights;
  }

  List<String> _getRecommendations() {
    final records = widget.milkRecords;
    final recommendations = <String>[];
    
    if (records.isEmpty) {
      recommendations.add('Start recording milk production data to get personalized recommendations');
      return recommendations;
    }
    
    recommendations.add('Record milk production daily for better insights');
    recommendations.add('Monitor cattle health and nutrition regularly');
    
    return recommendations;
  }

  // Responsive design helpers
  double _getResponsivePadding() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 12.0;
    if (screenWidth < 1200) return 16.0;
    return 20.0;
  }

  double _getResponsiveSpacing() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 8.0;
    if (screenWidth < 1200) return 12.0;
    return 16.0;
  }

  double _getResponsiveIconSize() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) return 24.0;
    if (screenWidth < 600) return 28.0;
    return 32.0;
  }
}
