import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/cattle_milk_record.dart';
import '../../Milk Records/models/milk_record.dart';
import '../../../services/milk_record_service.dart';
import 'package:uuid/uuid.dart';

class CattleMilkService {
  final MilkRecordService _milkRecordService = MilkRecordService();
  static const String _storageKey = 'cattle_milk_records';

  Future<List<CattleMilkRecord>> getCattleMilkRecords(String cattleId) async {
    final prefs = await SharedPreferences.getInstance();
    final recordsJson = prefs.getStringList('${_storageKey}_$cattleId') ?? [];
    
    return recordsJson
        .map((json) => CattleMilkRecord.fromMap(jsonDecode(json)))
        .where((record) => record.cattleId == cattleId)
        .toList();
  }

  Future<void> addMilkRecord(CattleMilkRecord record) async {
    final prefs = await SharedPreferences.getInstance();
    final records = await getCattleMilkRecords(record.cattleId);
    
    records.add(record);
    
    final recordsJson = records.map((r) => jsonEncode(r.toMap())).toList();
    await prefs.setStringList('${_storageKey}_${record.cattleId}', recordsJson);

    // Add morning record
    if (record.morningYield > 0) {
      await _milkRecordService.addMilkRecord(MilkRecord(
        id: const Uuid().v4(),
        cattleId: record.cattleId,
        quantity: record.morningYield,
        date: record.date,
        shift: 'Morning',
        notes: record.notes,
        morningQuantity: record.morningYield,
        eveningQuantity: 0.0,
        fatContent: 0.0,
        totalRevenue: 0.0,
      ));
    }

    // Add evening record
    if (record.eveningYield > 0) {
      await _milkRecordService.addMilkRecord(MilkRecord(
        id: const Uuid().v4(),
        cattleId: record.cattleId,
        quantity: record.eveningYield,
        date: record.date,
        shift: 'Evening',
        notes: record.notes,
        morningQuantity: 0.0,
        eveningQuantity: record.eveningYield,
        fatContent: 0.0,
        totalRevenue: 0.0,
      ));
    }
  }

  Future<Map<String, double>> getProductionSummary(String cattleId, {int days = 7}) async {
    final records = await getCattleMilkRecords(cattleId);
    final now = DateTime.now();
    final startDate = now.subtract(Duration(days: days));
    
    final filteredRecords = records.where((r) => r.date.isAfter(startDate)).toList();
    
    double totalMorning = 0;
    double totalEvening = 0;
    
    for (var record in filteredRecords) {
      totalMorning += record.morningYield;
      totalEvening += record.eveningYield;
    }
    
    return {
      'morningAverage': filteredRecords.isEmpty ? 0 : totalMorning / days,
      'eveningAverage': filteredRecords.isEmpty ? 0 : totalEvening / days,
      'totalAverage': filteredRecords.isEmpty ? 0 : (totalMorning + totalEvening) / days,
    };
  }

  Future<List<CattleMilkRecord>> getRecordsForPeriod(String cattleId, DateTime start, DateTime end) async {
    final records = await getCattleMilkRecords(cattleId);
    return records
        .where((r) => r.date.isAfter(start) && r.date.isBefore(end))
        .toList();
  }

  Future<void> deleteMilkRecord(String cattleId, String recordId) async {
    final prefs = await SharedPreferences.getInstance();
    final records = await getCattleMilkRecords(cattleId);
    
    records.removeWhere((r) => r.id == recordId);
    
    final recordsJson = records.map((r) => jsonEncode(r.toMap())).toList();
    await prefs.setStringList('${_storageKey}_$cattleId', recordsJson);
  }
}
