import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Configuration class for filter display - similar to HistoryRecordCard approach
class FilterDisplayConfig {
  final String label;
  final bool showValue;
  final bool checkAll;
  final bool showLabel;  // New property to control label display

  const FilterDisplayConfig({
    required this.label,
    required this.showValue,
    required this.checkAll,
    this.showLabel = true,  // Default to showing labels
  });
}

/// A universal filter status bar widget with consistent layout across all modules
///
/// This widget displays:
/// - Clear Filters button when filters are active
/// - Active filter information
/// - Record count (filtered vs total)
/// - Only appears when filters are applied
///
/// All complex logic is handled internally - just pass filter state variables
class FilterStatusBar extends StatelessWidget {
  // Core properties
  final int? totalCount;
  final int? filteredCount;
  final VoidCallback? onClearFilters;
  final Color themeColor;
  final EdgeInsets? padding;
  final bool showRecordCount;

  // Filter state variables - the widget determines if filters are active
  final Map<String, dynamic> filterStates;
  final DateTime? defaultStartDate;
  final DateTime? defaultEndDate;

  const FilterStatusBar({
    Key? key,
    this.totalCount,
    this.filteredCount,
    this.onClearFilters,
    this.themeColor = Colors.purple,
    this.padding,
    this.showRecordCount = true,
    required this.filterStates,
    this.defaultStartDate,
    this.defaultEndDate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Only show when filters are active
    if (!_hasActiveFilters()) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: padding ?? const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Single row with equal width clear button and active filter info
          Row(
            children: [
              // Clear Filters Button (left side) - equal flex
              if (onClearFilters != null)
                Expanded(
                  flex: 1,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: _getResponsivePadding(context),
                      vertical: _getResponsiveVerticalPadding(context),
                    ),
                    decoration: BoxDecoration(
                      color: themeColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: themeColor.withValues(alpha: 0.3)),
                    ),
                    child: InkWell(
                      onTap: () {
                        onClearFilters!();
                        // Show feedback
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: const Text('Filters cleared'),
                            duration: const Duration(seconds: 1),
                            backgroundColor: themeColor,
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.clear,
                            size: _getResponsiveIconSize(context),
                            color: themeColor,
                          ),
                          SizedBox(width: _getResponsiveSpacing(context)),
                          Text(
                            'Clear Filters',
                            style: TextStyle(
                              color: themeColor,
                              fontSize: _getResponsiveFontSize(context),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

              // Spacing between elements
              SizedBox(width: _getResponsiveSpacing(context)),

              // Active filters display (right side) - equal flex
              Expanded(
                flex: 1,
                child: _buildActiveFiltersDisplay(context),
              ),
            ],
          ),

          // Record count at bottom (if available)
          if (_shouldShowCount())
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                'Showing ${filteredCount ?? 0} of ${totalCount ?? 0} records',
                style: TextStyle(
                  color: themeColor.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w500,
                  fontSize: _getResponsiveFontSize(context),
                ),
                textAlign: TextAlign.center,
              ),
            ),
        ],
      ),
    );
  }

  // Internal logic to determine if any filters are active
  bool _hasActiveFilters() {
    // First check for date filters specifically
    final startDate = filterStates['startDate'] as DateTime?;
    final endDate = filterStates['endDate'] as DateTime?;

    if (startDate != null && endDate != null) {
      if (defaultStartDate != null && defaultEndDate != null) {
        // Check against defaults
        if (!_isSameDay(startDate, defaultStartDate!) || !_isSameDay(endDate, defaultEndDate!)) {
          return true;
        }
      } else {
        // No defaults set, any date range is considered active
        return true;
      }
    }

    // Check other filter fields
    for (final entry in filterStates.entries) {
      final key = entry.key;
      final value = entry.value;

      // Skip null or empty values
      if (value == null) continue;

      // Skip date fields (already handled above)
      if (key == 'startDate' || key == 'endDate') continue;

      // Check different types of filter values
      if (value is String && value.isNotEmpty && value != 'All') return true;
      if (value is List && value.isNotEmpty) return true;
      if (value is Map && value.isNotEmpty) return true;
      if (value is DateTime) return true; // Handle any other DateTime fields
    }

    return false;
  }

  // Helper to check if two dates are the same day
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  bool _shouldShowCount() {
    return showRecordCount && totalCount != null && filteredCount != null;
  }



  Widget _buildActiveFiltersDisplay(BuildContext context) {
    final activeFiltersList = _getActiveFiltersList();

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: _getResponsivePadding(context),
        vertical: _getResponsiveVerticalPadding(context),
      ),
      decoration: BoxDecoration(
        color: themeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: themeColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            size: _getResponsiveIconSize(context),
            color: themeColor,
          ),
          SizedBox(width: _getResponsiveSpacing(context)),
          Expanded(
            child: Text(
              activeFiltersList.isNotEmpty
                  ? activeFiltersList.join(', ')
                  : 'Filters active',
              style: TextStyle(
                color: themeColor,
                fontSize: _getResponsiveFontSize(context),
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // Filter configuration map - similar to HistoryRecordCard approach
  static const Map<String, FilterDisplayConfig> _filterConfigs = {
    // Transaction/Financial filters
    'type': FilterDisplayConfig(label: 'Type', showValue: true, checkAll: true, showLabel: false),
    'categoryType': FilterDisplayConfig(label: 'Type', showValue: true, checkAll: true, showLabel: false),
    'category': FilterDisplayConfig(label: 'Category', showValue: true, checkAll: true, showLabel: false),
    'paymentMethod': FilterDisplayConfig(label: 'Payment', showValue: true, checkAll: true, showLabel: false),
    'amountRange': FilterDisplayConfig(label: 'Amount', showValue: true, checkAll: false, showLabel: false),

    // Common action filters
    'sortBy': FilterDisplayConfig(label: 'Sort', showValue: true, checkAll: false, showLabel: false),
    'searchQuery': FilterDisplayConfig(label: 'Search', showValue: false, checkAll: false, showLabel: false),

    // Common entity filters
    'status': FilterDisplayConfig(label: 'Status', showValue: true, checkAll: true, showLabel: false),
    'method': FilterDisplayConfig(label: 'Method', showValue: true, checkAll: true, showLabel: false),
    'gender': FilterDisplayConfig(label: 'Gender', showValue: true, checkAll: true, showLabel: false),
    'breed': FilterDisplayConfig(label: 'Breed', showValue: true, checkAll: true, showLabel: false),
    'animalType': FilterDisplayConfig(label: 'Animal Type', showValue: true, checkAll: true, showLabel: false),
    'cattle': FilterDisplayConfig(label: 'Cattle', showValue: true, checkAll: true, showLabel: false),

    // Health module specific
    'condition': FilterDisplayConfig(label: 'Condition', showValue: true, checkAll: true, showLabel: false),
    'healthStatus': FilterDisplayConfig(label: 'Health Status', showValue: true, checkAll: true, showLabel: false),
    'severity': FilterDisplayConfig(label: 'Severity', showValue: true, checkAll: true, showLabel: false),
    'recordType': FilterDisplayConfig(label: 'Record Type', showValue: true, checkAll: true, showLabel: false),
    'veterinarian': FilterDisplayConfig(label: 'Veterinarian', showValue: true, checkAll: true, showLabel: false),

    // Breeding module specific
    'breedingStatus': FilterDisplayConfig(label: 'Breeding Status', showValue: true, checkAll: true, showLabel: false),
    'breedingMethod': FilterDisplayConfig(label: 'Breeding Method', showValue: true, checkAll: true, showLabel: false),
    'pregnancyStatus': FilterDisplayConfig(label: 'Pregnancy Status', showValue: true, checkAll: true, showLabel: false),

    // Weight module specific
    'measurementMethod': FilterDisplayConfig(label: 'Measurement Method', showValue: true, checkAll: true, showLabel: false),

    // Cattle module specific
    'stage': FilterDisplayConfig(label: 'Stage', showValue: true, checkAll: true, showLabel: false),
  };

  // Internal logic to build active filters list
  List<String> _getActiveFiltersList() {
    List<String> activeFilters = [];

    for (final entry in filterStates.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value == null) continue;

      // Handle date range separately (special case) - only process once for startDate
      if (key == 'startDate') {
        _handleDateRangeFilter(activeFilters);
        continue;
      } else if (key == 'endDate') {
        // Skip endDate processing since we handle both dates in startDate case
        continue;
      }

      // Get filter configuration
      final config = _filterConfigs[key];
      if (config != null) {
        final filterText = _buildFilterText(key, value, config);
        if (filterText != null) {
          activeFilters.add(filterText);
        }
      } else {
        // Fallback for unknown filter types
        final filterText = _buildGenericFilterText(key, value);
        if (filterText != null) {
          activeFilters.add(filterText);
        }
      }
    }

    return activeFilters;
  }

  // Build filter text based on configuration
  String? _buildFilterText(String key, dynamic value, FilterDisplayConfig config) {
    if (value is! String || value.isEmpty) return null;

    // Check if we should ignore "All" values
    if (config.checkAll && value == 'All') return null;

    // Special handling for sortBy to include direction
    if (key == 'sortBy') {
      final sortAscending = filterStates['sortAscending'] as bool? ?? true;
      final direction = sortAscending ? 'Asc' : 'Desc';
      final capitalizedValue = value[0].toUpperCase() + value.substring(1).toLowerCase();
      return '$capitalizedValue ($direction)';  // Show "Date (Desc)" or "Amount (Asc)"
    }

    // Build display text based on configuration
    if (config.showValue) {
      if (config.showLabel) {
        return '${config.label}: $value';  // Show "Label: Value"
      } else {
        return value;  // Show only "Value"
      }
    } else {
      return config.label;  // Show only "Label"
    }
  }

  // Generic fallback for unknown filter types
  String? _buildGenericFilterText(String key, dynamic value) {
    if (value is! String || value.isEmpty || value == 'All') return null;

    // For unknown filters, show only the value (no label)
    return value;
  }

  // Handle date range filter logic
  void _handleDateRangeFilter(List<String> activeFilters) {
    final startDate = filterStates['startDate'] as DateTime?;
    final endDate = filterStates['endDate'] as DateTime?;

    // If both dates are present, show the date range filter
    if (startDate != null && endDate != null) {
      // Check if this is different from default dates (if defaults are provided)
      bool isDifferentFromDefault = true;
      if (defaultStartDate != null && defaultEndDate != null) {
        isDifferentFromDefault = !_isSameDay(startDate, defaultStartDate!) ||
                                !_isSameDay(endDate, defaultEndDate!);
      }

      // Show date range if it's different from default OR if no defaults are set
      if (isDifferentFromDefault) {
        // Get specific date range label instead of generic "Date range"
        final dateRangeLabel = _getDateRangeDisplayText(startDate, endDate);
        activeFilters.add(dateRangeLabel);
      }
    }
  }

  /// Get the appropriate display text for the current date range selection
  String _getDateRangeDisplayText(DateTime startDate, DateTime endDate) {
    // Check for specific date range patterns
    if (_isDateRangeSelected(startDate, endDate, 0)) {
      return 'Today';
    } else if (_isDateRangeSelected(startDate, endDate, 1)) {
      return 'Yesterday';
    } else if (_isDateRangeSelected(startDate, endDate, 7)) {
      return 'Last 7 Days';
    } else if (_isDateRangeSelected(startDate, endDate, 30)) {
      return 'Last 30 Days';
    } else if (_isDateRangeSelected(startDate, endDate, 90)) {
      return 'Last 90 Days';
    } else if (_isDateRangeSelected(startDate, endDate, 180)) {
      return 'Last 6 Months';
    } else {
      // Custom date range - show the actual dates
      final startFormatted = DateFormat('MMM dd').format(startDate);
      final endFormatted = DateFormat('MMM dd').format(endDate);

      // If same year, don't repeat it
      if (startDate.year == endDate.year && startDate.year == DateTime.now().year) {
        return '$startFormatted - $endFormatted';
      } else {
        final startWithYear = DateFormat('MMM dd, yyyy').format(startDate);
        final endWithYear = DateFormat('MMM dd, yyyy').format(endDate);
        return '$startWithYear - $endWithYear';
      }
    }
  }

  /// Check if the given date range matches a specific preset range
  bool _isDateRangeSelected(DateTime startDate, DateTime endDate, int days) {
    final now = DateTime.now();
    DateTime expectedStart;
    DateTime expectedEnd = DateTime(now.year, now.month, now.day, 23, 59, 59);

    if (days == 0) {
      expectedStart = DateTime(now.year, now.month, now.day);
    } else if (days == 1) {
      final yesterday = now.subtract(const Duration(days: 1));
      expectedStart = DateTime(yesterday.year, yesterday.month, yesterday.day);
      expectedEnd = DateTime(yesterday.year, yesterday.month, yesterday.day, 23, 59, 59);
    } else {
      expectedStart = now.subtract(Duration(days: days));
    }

    return startDate.year == expectedStart.year &&
           startDate.month == expectedStart.month &&
           startDate.day == expectedStart.day &&
           endDate.year == expectedEnd.year &&
           endDate.month == expectedEnd.month &&
           endDate.day == expectedEnd.day;
  }

  // Responsive helper methods
  double _getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 12.0; // Small screens - increased from 10
    if (screenWidth < 900) return 14.0; // Medium screens - increased from 12
    return 16.0; // Large screens - increased from 14
  }

  double _getResponsiveVerticalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 8.0; // Small screens - increased from 6
    if (screenWidth < 900) return 9.0; // Medium screens - increased from 7
    return 10.0; // Large screens - increased from 8
  }

  double _getResponsiveIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 16.0; // Small screens - increased from 14
    if (screenWidth < 900) return 17.0; // Medium screens - increased from 15
    return 18.0; // Large screens - increased from 16
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 13.0; // Small screens - increased from 11
    if (screenWidth < 900) return 14.0; // Medium screens - increased from 12
    return 15.0; // Large screens - increased from 13
  }

  double _getResponsiveSpacing(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 6.0; // Small screens
    if (screenWidth < 900) return 8.0; // Medium screens
    return 10.0; // Large screens
  }

  /// Factory constructor for Weight Module
  factory FilterStatusBar.weight({
    required Map<String, dynamic> filterStates,
    required int totalCount,
    required int filteredCount,
    required VoidCallback onClearFilters,
    DateTime? defaultStartDate,
    DateTime? defaultEndDate,
  }) {
    return FilterStatusBar(
      filterStates: filterStates,
      totalCount: totalCount,
      filteredCount: filteredCount,
      onClearFilters: onClearFilters,
      themeColor: Colors.purple,
      defaultStartDate: defaultStartDate,
      defaultEndDate: defaultEndDate,
    );
  }

  /// Factory constructor for Breeding Module
  factory FilterStatusBar.breeding({
    required Map<String, dynamic> filterStates,
    required int totalCount,
    required int filteredCount,
    required VoidCallback onClearFilters,
    DateTime? defaultStartDate,
    DateTime? defaultEndDate,
  }) {
    return FilterStatusBar(
      filterStates: filterStates,
      totalCount: totalCount,
      filteredCount: filteredCount,
      onClearFilters: onClearFilters,
      themeColor: Colors.purple,
      defaultStartDate: defaultStartDate,
      defaultEndDate: defaultEndDate,
    );
  }

  /// Factory constructor for Health Module
  factory FilterStatusBar.health({
    required Map<String, dynamic> filterStates,
    required int totalCount,
    required int filteredCount,
    required VoidCallback onClearFilters,
    DateTime? defaultStartDate,
    DateTime? defaultEndDate,
  }) {
    return FilterStatusBar(
      filterStates: filterStates,
      totalCount: totalCount,
      filteredCount: filteredCount,
      onClearFilters: onClearFilters,
      themeColor: Colors.purple,
      defaultStartDate: defaultStartDate,
      defaultEndDate: defaultEndDate,
    );
  }

  /// Factory constructor for Transaction Module
  factory FilterStatusBar.transaction({
    required Map<String, dynamic> filterStates,
    required int totalCount,
    required int filteredCount,
    required VoidCallback onClearFilters,
    DateTime? defaultStartDate,
    DateTime? defaultEndDate,
  }) {
    return FilterStatusBar(
      filterStates: filterStates,
      totalCount: totalCount,
      filteredCount: filteredCount,
      onClearFilters: onClearFilters,
      themeColor: Colors.purple,
      defaultStartDate: defaultStartDate,
      defaultEndDate: defaultEndDate,
    );
  }

  /// Factory constructor for Cattle Module
  factory FilterStatusBar.cattle({
    required Map<String, dynamic> filterStates,
    required int totalCount,
    required int filteredCount,
    required VoidCallback onClearFilters,
    DateTime? defaultStartDate,
    DateTime? defaultEndDate,
  }) {
    return FilterStatusBar(
      filterStates: filterStates,
      totalCount: totalCount,
      filteredCount: filteredCount,
      onClearFilters: onClearFilters,
      themeColor: Colors.purple,
      defaultStartDate: defaultStartDate,
      defaultEndDate: defaultEndDate,
    );
  }

  /// Factory constructor for Milk Module
  factory FilterStatusBar.milk({
    required Map<String, dynamic> filterStates,
    required int totalCount,
    required int filteredCount,
    required VoidCallback onClearFilters,
    DateTime? defaultStartDate,
    DateTime? defaultEndDate,
  }) {
    return FilterStatusBar(
      filterStates: filterStates,
      totalCount: totalCount,
      filteredCount: filteredCount,
      onClearFilters: onClearFilters,
      themeColor: Colors.purple,
      defaultStartDate: defaultStartDate,
      defaultEndDate: defaultEndDate,
    );
  }

  /// Factory constructor for Notifications Module
  factory FilterStatusBar.notifications({
    required Map<String, dynamic> filterStates,
    required int totalCount,
    required int filteredCount,
    required VoidCallback onClearFilters,
    DateTime? defaultStartDate,
    DateTime? defaultEndDate,
  }) {
    return FilterStatusBar(
      filterStates: filterStates,
      totalCount: totalCount,
      filteredCount: filteredCount,
      onClearFilters: onClearFilters,
      themeColor: Colors.purple,
      defaultStartDate: defaultStartDate,
      defaultEndDate: defaultEndDate,
    );
  }

  /// Factory constructor for Analytics/Summary tabs (no count display)
  factory FilterStatusBar.analytics({
    required Map<String, dynamic> filterStates,
    required VoidCallback onClearFilters,
    int? totalCount,
    int? filteredCount,
    DateTime? defaultStartDate,
    DateTime? defaultEndDate,
  }) {
    return FilterStatusBar(
      filterStates: filterStates,
      totalCount: totalCount,
      filteredCount: filteredCount,
      onClearFilters: onClearFilters,
      themeColor: Colors.purple,
      showRecordCount: false,
      defaultStartDate: defaultStartDate,
      defaultEndDate: defaultEndDate,
    );
  }
}
