import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

part 'event_isar.g.dart';

/// Enum for event types - Comprehensive list covering all module activities
enum EventType {
  // Health & Medical Events
  vaccination,
  healthCheckup,
  deworming,
  medication,
  veterinaryVisit,
  treatment,
  injury,
  illness,

  // Breeding & Reproduction Events
  breeding,
  heatCycle,
  pregnancyCheck,
  calving,
  dryOff,
  weaning,

  // Production Events
  milkRecording,
  weightMeasurement,
  feedChange,

  // Management Events
  purchased,
  sold,
  transport,
  groupChange,
  locationChange,

  // Financial Events
  expense,
  income,

  // Maintenance Events
  facilityMaintenance,
  equipmentMaintenance,

  // Custom & General
  miscellaneous,
  custom,
  reminder,
  alert,
}

/// Enum for event priorities
enum EventPriority {
  low,
  medium,
  high,
  urgent,
  critical,
}

/// Enum for event sources
enum EventSource {
  manual,
  autoGenerated,
  imported,
  scheduled,
  triggered,
}

/// Enum for event status
enum EventStatus {
  pending,
  inProgress,
  completed,
  missed,
  cancelled,
  postponed,
}

/// Embedded class for TimeOfDay since <PERSON><PERSON> doesn't directly support it
@embedded
class TimeOfDayIsar {
  /// Hour of the day (0-23)
  int? hour;
  
  /// Minute (0-59)
  int? minute;
  
  /// Convert to Flutter's TimeOfDay
  TimeOfDay toTimeOfDay() {
    return TimeOfDay(hour: hour ?? 0, minute: minute ?? 0);
  }
  
  /// Create from Flutter's TimeOfDay
  static TimeOfDayIsar fromTimeOfDay(TimeOfDay time) {
    return TimeOfDayIsar()
      ..hour = time.hour
      ..minute = time.minute;
  }
}

/// Embedded class to support compatibility with CattleEventIsar from Cattle module
@embedded
class EventTypeEmbedded {
  // Stored as a string: 'vaccination', 'healthCheckup', etc.
  String? value;

  /// Convert to EventType enum
  EventType toEventType() {
    switch (value) {
      case 'EventType.vaccination':
        return EventType.vaccination;
      case 'EventType.healthCheckup':
        return EventType.healthCheckup;
      case 'EventType.breeding':
        return EventType.breeding;
      case 'EventType.pregnancyCheck':
        return EventType.pregnancyCheck;
      case 'EventType.weightMeasurement':
      case 'EventType.weighing':
        return EventType.weightMeasurement;
      case 'EventType.alert':
        return EventType.miscellaneous;
      case 'EventType.deworming':
        return EventType.deworming;
      default:
        return EventType.miscellaneous;
    }
  }

  /// Set value from EventType enum
  void fromEventType(EventType type) {
    switch (type) {
      case EventType.vaccination:
        value = 'EventType.vaccination';
        break;
      case EventType.healthCheckup:
        value = 'EventType.healthCheckup';
        break;
      case EventType.breeding:
        value = 'EventType.breeding';
        break;
      case EventType.pregnancyCheck:
        value = 'EventType.pregnancyCheck';
        break;
      case EventType.weightMeasurement:
        value = 'EventType.weighing';
        break;
      case EventType.deworming:
        value = 'EventType.deworming';
        break;
      default:
        value = 'EventType.other';
        break;
    }
  }

  /// Get display name for the type
  String getDisplayName() {
    final eventType = toEventType();
    switch (eventType) {
      // Health & Medical Events
      case EventType.vaccination:
        return 'Vaccination';
      case EventType.healthCheckup:
        return 'Health Checkup';
      case EventType.deworming:
        return 'Deworming';
      case EventType.medication:
        return 'Medication';
      case EventType.veterinaryVisit:
        return 'Veterinary Visit';
      case EventType.treatment:
        return 'Treatment';
      case EventType.injury:
        return 'Injury';
      case EventType.illness:
        return 'Illness';

      // Breeding & Reproduction Events
      case EventType.breeding:
        return 'Breeding';
      case EventType.heatCycle:
        return 'Heat Cycle';
      case EventType.pregnancyCheck:
        return 'Pregnancy Check';
      case EventType.calving:
        return 'Calving';
      case EventType.dryOff:
        return 'Dry Off';
      case EventType.weaning:
        return 'Weaning';

      // Production Events
      case EventType.milkRecording:
        return 'Milk Recording';
      case EventType.weightMeasurement:
        return 'Weight Measurement';
      case EventType.feedChange:
        return 'Feed Change';

      // Management Events
      case EventType.purchased:
        return 'Purchased';
      case EventType.sold:
        return 'Sold';
      case EventType.transport:
        return 'Transport';
      case EventType.groupChange:
        return 'Group Change';
      case EventType.locationChange:
        return 'Location Change';

      // Financial Events
      case EventType.expense:
        return 'Expense';
      case EventType.income:
        return 'Income';

      // Maintenance Events
      case EventType.facilityMaintenance:
        return 'Facility Maintenance';
      case EventType.equipmentMaintenance:
        return 'Equipment Maintenance';

      // Custom & General
      case EventType.miscellaneous:
        return 'Miscellaneous';
      case EventType.custom:
        return 'Custom';
      case EventType.reminder:
        return 'Reminder';
      case EventType.alert:
        return 'Alert';
    }
  }

  /// Get icon data
  IconData getIcon() {
    final eventType = toEventType();
    switch (eventType) {
      // Health & Medical Events
      case EventType.vaccination:
        return Icons.medical_services;
      case EventType.healthCheckup:
        return Icons.health_and_safety;
      case EventType.deworming:
        return Icons.pest_control;
      case EventType.medication:
        return Icons.medication;
      case EventType.veterinaryVisit:
        return Icons.local_hospital;
      case EventType.treatment:
        return Icons.healing;
      case EventType.injury:
        return Icons.personal_injury;
      case EventType.illness:
        return Icons.sick;

      // Breeding & Reproduction Events
      case EventType.breeding:
        return Icons.favorite;
      case EventType.heatCycle:
        return Icons.favorite_border;
      case EventType.pregnancyCheck:
        return Icons.pregnant_woman;
      case EventType.calving:
        return Icons.child_care;
      case EventType.dryOff:
        return Icons.water_drop;
      case EventType.weaning:
        return Icons.child_friendly;

      // Production Events
      case EventType.milkRecording:
        return Icons.opacity;
      case EventType.weightMeasurement:
        return Icons.monitor_weight;
      case EventType.feedChange:
        return Icons.grass;

      // Management Events
      case EventType.purchased:
        return Icons.shopping_cart;
      case EventType.sold:
        return Icons.monetization_on;
      case EventType.transport:
        return Icons.local_shipping;
      case EventType.groupChange:
        return Icons.group;
      case EventType.locationChange:
        return Icons.location_on;

      // Financial Events
      case EventType.expense:
        return Icons.money_off;
      case EventType.income:
        return Icons.attach_money;

      // Maintenance Events
      case EventType.facilityMaintenance:
        return Icons.home_repair_service;
      case EventType.equipmentMaintenance:
        return Icons.build;

      // Custom & General
      case EventType.miscellaneous:
        return Icons.event_note;
      case EventType.custom:
        return Icons.edit_note;
      case EventType.reminder:
        return Icons.alarm;
      case EventType.alert:
        return Icons.warning;
    }
  }

  /// Get color for the event type
  Color getColor() {
    final eventType = toEventType();
    switch (eventType) {
      // Health & Medical Events - Blue tones
      case EventType.vaccination:
        return Colors.blue;
      case EventType.healthCheckup:
        return Colors.green;
      case EventType.deworming:
        return Colors.teal;
      case EventType.medication:
        return Colors.lightBlue;
      case EventType.veterinaryVisit:
        return Colors.indigo;
      case EventType.treatment:
        return Colors.cyan;
      case EventType.injury:
        return Colors.deepOrange;
      case EventType.illness:
        return Colors.red;

      // Breeding & Reproduction Events - Pink/Purple tones
      case EventType.breeding:
        return Colors.pink;
      case EventType.heatCycle:
        return Colors.pinkAccent;
      case EventType.pregnancyCheck:
        return Colors.purple;
      case EventType.calving:
        return const Color(0xFFFF6B6B);
      case EventType.dryOff:
        return Colors.lightBlue;
      case EventType.weaning:
        return Colors.deepPurple;

      // Production Events - Green tones
      case EventType.milkRecording:
        return const Color(0xFF4ECDC4);
      case EventType.weightMeasurement:
        return const Color(0xFF45B7D1);
      case EventType.feedChange:
        return const Color(0xFF96CEB4);

      // Management Events - Various colors
      case EventType.purchased:
        return const Color(0xFF6C5CE7);
      case EventType.sold:
        return const Color(0xFFE17055);
      case EventType.transport:
        return const Color(0xFF74B9FF);
      case EventType.groupChange:
        return const Color(0xFFA29BFE);
      case EventType.locationChange:
        return const Color(0xFF00B894);

      // Financial Events - Money colors
      case EventType.expense:
        return const Color(0xFFE84393);
      case EventType.income:
        return const Color(0xFF00B894);

      // Maintenance Events - Tool colors
      case EventType.facilityMaintenance:
        return const Color(0xFF636E72);
      case EventType.equipmentMaintenance:
        return const Color(0xFF2D3436);

      // Custom & General - Neutral colors
      case EventType.miscellaneous:
        return const Color(0xFFB2BEC3);
      case EventType.custom:
        return const Color(0xFF74B9FF);
      case EventType.reminder:
        return const Color(0xFFFDCB6E);
      case EventType.alert:
        return const Color(0xFFE17055);
    }
  }
}

/// Event model for Isar database
@collection
class EventIsar {
  /// Unique identifier for the event
  Id id = Isar.autoIncrement;

  /// Business ID for the event (UUID)
  @Index(unique: true, replace: true)
  String? businessId;

  /// ID of the cattle this event belongs to
  @Index()
  String? cattleId;

  /// Title of the event
  String? title;

  /// Type of the event
  EventTypeEmbedded? type;

  /// Priority of the event
  @enumerated
  EventPriority priority = EventPriority.medium;

  /// Date when the event occurs
  DateTime? eventDate;

  /// Due date for the event (if applicable)
  DateTime? dueDate;

  /// Time of the event
  TimeOfDayIsar? time;

  /// Notes about the event
  String? notes;

  /// Whether the event is completed
  bool isCompleted = false;

  /// Whether the event is missed
  bool isMissed = false;

  /// Event status
  @enumerated
  EventStatus status = EventStatus.pending;

  /// Event source
  @enumerated
  EventSource source = EventSource.manual;

  /// Whether the event is recurring
  bool isRecurring = false;

  /// Recurrence pattern (daily, weekly, monthly, yearly)
  String? recurrencePattern;

  /// Recurrence interval (every X days/weeks/months/years)
  int? recurrenceInterval;

  /// End date for recurring events
  DateTime? recurrenceEndDate;

  /// Whether the event is auto-generated
  bool isAutoGenerated = false;

  /// Source module that generated this event
  String? sourceModule;

  /// Related record ID from source module
  String? relatedRecordId;

  /// Parent event ID for recurring events
  String? parentEventId;

  /// Tags for categorization
  List<String> tags = [];

  /// Location where the event takes place
  String? location;

  /// Duration of the event in minutes
  int? durationMinutes;

  /// Cost associated with the event
  double? cost;

  /// Currency for the cost
  String? currency;

  /// Reminder settings (minutes before event)
  List<int> reminderMinutes = [];

  /// Whether notifications are enabled for this event
  bool notificationsEnabled = true;

  /// Custom fields for additional data (stored as JSON string)
  @ignore
  Map<String, String> customFields = {};

  /// Custom fields as JSON string for Isar storage
  String? customFieldsJson;

  /// Completion percentage (0-100)
  int completionPercentage = 0;

  /// Assigned to (user/staff member)
  String? assignedTo;

  /// When the event was created
  DateTime? createdAt;

  /// When the event was last updated
  DateTime? updatedAt;

  /// Constructor
  EventIsar() {
    businessId = 'event_${const Uuid().v4()}';
    createdAt = DateTime.now();
    updatedAt = DateTime.now();
    isCompleted = false;
    isMissed = false;
  }
}