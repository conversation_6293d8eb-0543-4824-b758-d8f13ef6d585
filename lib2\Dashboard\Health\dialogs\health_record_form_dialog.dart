import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/health_record_isar.dart';
import '../models/veterinarian_isar.dart';
import '../services/health_service.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../constants/health_constants.dart';
import '../../../utils/message_utils.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
// Removed farm context service - single farm per user

class HealthRecordFormDialog extends StatefulWidget {
  final List<CattleIsar> cattle;
  final List<VeterinarianIsar> veterinarians;
  final HealthRecordIsar? existingRecord;
  final VoidCallback onRecordAdded;

  const HealthRecordFormDialog({
    super.key,
    required this.cattle,
    required this.veterinarians,
    this.existingRecord,
    required this.onRecordAdded,
  });

  @override
  State<HealthRecordFormDialog> createState() => _HealthRecordFormDialogState();
}

class _HealthRecordFormDialogState extends State<HealthRecordFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _healthService = HealthService.instance;

  // Form controllers
  late TextEditingController _symptomsController;
  late TextEditingController _diagnosisController;
  late TextEditingController _treatmentController;
  late TextEditingController _medicationController;
  late TextEditingController _dosageController;
  late TextEditingController _frequencyController;
  late TextEditingController _durationController;
  late TextEditingController _veterinarianContactController;
  late TextEditingController _treatmentCostController;
  late TextEditingController _notesController;
  late TextEditingController _temperatureController;
  late TextEditingController _heartRateController;
  late TextEditingController _respiratoryRateController;
  late TextEditingController _weightController;
  late TextEditingController _bodyConditionController;

  // Form values
  CattleIsar? _selectedCattle;
  String? _selectedRecordType;
  DateTime _selectedDate = DateTime.now();
  String? _selectedCondition;
  String? _selectedHealthStatus;
  String? _selectedSeverity;
  VeterinarianIsar? _selectedVeterinarian;
  DateTime? _followUpDate;
  DateTime? _recoveryDate;
  bool _isEmergency = false;
  bool _isUnderTreatment = false;
  bool _requiresFollowUp = false;
  bool _isChronic = false;

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadExistingData();
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  void _initializeControllers() {
    _symptomsController = TextEditingController();
    _diagnosisController = TextEditingController();
    _treatmentController = TextEditingController();
    _medicationController = TextEditingController();
    _dosageController = TextEditingController();
    _frequencyController = TextEditingController();
    _durationController = TextEditingController();
    _veterinarianContactController = TextEditingController();
    _treatmentCostController = TextEditingController();
    _notesController = TextEditingController();
    _temperatureController = TextEditingController();
    _heartRateController = TextEditingController();
    _respiratoryRateController = TextEditingController();
    _weightController = TextEditingController();
    _bodyConditionController = TextEditingController();
  }

  void _disposeControllers() {
    _symptomsController.dispose();
    _diagnosisController.dispose();
    _treatmentController.dispose();
    _medicationController.dispose();
    _dosageController.dispose();
    _frequencyController.dispose();
    _durationController.dispose();
    _veterinarianContactController.dispose();
    _treatmentCostController.dispose();
    _notesController.dispose();
    _temperatureController.dispose();
    _heartRateController.dispose();
    _respiratoryRateController.dispose();
    _weightController.dispose();
    _bodyConditionController.dispose();
  }

  void _loadExistingData() {
    if (widget.existingRecord != null) {
      final record = widget.existingRecord!;
      
      // Find selected cattle
      _selectedCattle = widget.cattle.firstWhere(
        (c) => c.businessId == record.cattleBusinessId,
        orElse: () => widget.cattle.first,
      );

      // Find selected veterinarian
      if (record.veterinarian != null && widget.veterinarians.isNotEmpty) {
        try {
          _selectedVeterinarian = widget.veterinarians.firstWhere(
            (v) => v.name == record.veterinarian,
          );
        } catch (e) {
          _selectedVeterinarian = widget.veterinarians.first;
        }
      }

      // Set form values
      _selectedRecordType = record.recordType;
      _selectedDate = record.date ?? DateTime.now();
      _selectedCondition = record.condition;
      _selectedHealthStatus = record.healthStatus;
      _selectedSeverity = record.severity;
      _followUpDate = record.followUpDate;
      _recoveryDate = record.recoveryDate;
      _isEmergency = record.isEmergency;
      _isUnderTreatment = record.isUnderTreatment;
      _requiresFollowUp = record.requiresFollowUp;
      _isChronic = record.isChronic;

      // Set controller values
      _symptomsController.text = record.symptoms ?? '';
      _diagnosisController.text = record.diagnosis ?? '';
      _treatmentController.text = record.treatment ?? '';
      _medicationController.text = record.medication ?? '';
      _dosageController.text = record.dosage ?? '';
      _frequencyController.text = record.frequency ?? '';
      _durationController.text = record.duration ?? '';
      _veterinarianContactController.text = record.veterinarianContact ?? '';
      _treatmentCostController.text = record.treatmentCost?.toString() ?? '';
      _notesController.text = record.notes ?? '';
      _temperatureController.text = record.temperature?.toString() ?? '';
      _heartRateController.text = record.heartRate?.toString() ?? '';
      _respiratoryRateController.text = record.respiratoryRate?.toString() ?? '';
      _weightController.text = record.weight?.toString() ?? '';
      _bodyConditionController.text = record.bodyConditionScore?.toString() ?? '';
    } else {
      // Set defaults for new record
      _selectedCattle = widget.cattle.isNotEmpty ? widget.cattle.first : null;
      _selectedRecordType = HealthConstants.recordTypes.first;
      _selectedHealthStatus = HealthConstants.healthStatus.first;
      _selectedSeverity = HealthConstants.severityLevels.first;
    }
  }

  Future<void> _saveRecord() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedCattle == null) {
      MessageUtils.showErrorSnackBar(context, 'Please select a cattle');
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Single farm per user - no farm context needed
      // Single farm per user - get farm directly
      final farmSetupHandler = FarmSetupHandler.instance;
      final farms = await farmSetupHandler.getAllFarms();

      if (farms.isEmpty) {
        throw Exception('No farm found');
      }

      final farmId = farms.first.farmBusinessId;

      final record = widget.existingRecord?.copyWith(
        cattleBusinessId: _selectedCattle!.businessId,
        cattleTagId: _selectedCattle!.tagId,
        cattleName: _selectedCattle!.name,
        farmBusinessId: farmId,
        recordType: _selectedRecordType,
        date: _selectedDate,
        condition: _selectedCondition,
        healthStatus: _selectedHealthStatus,
        severity: _selectedSeverity,
        symptoms: _symptomsController.text.trim().isEmpty ? null : _symptomsController.text.trim(),
        diagnosis: _diagnosisController.text.trim().isEmpty ? null : _diagnosisController.text.trim(),
        treatment: _treatmentController.text.trim().isEmpty ? null : _treatmentController.text.trim(),
        medication: _medicationController.text.trim().isEmpty ? null : _medicationController.text.trim(),
        dosage: _dosageController.text.trim().isEmpty ? null : _dosageController.text.trim(),
        frequency: _frequencyController.text.trim().isEmpty ? null : _frequencyController.text.trim(),
        duration: _durationController.text.trim().isEmpty ? null : _durationController.text.trim(),
        veterinarian: _selectedVeterinarian?.name,
        veterinarianContact: _veterinarianContactController.text.trim().isEmpty ? null : _veterinarianContactController.text.trim(),
        treatmentCost: double.tryParse(_treatmentCostController.text.trim()),
        followUpDate: _followUpDate,
        recoveryDate: _recoveryDate,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        temperature: double.tryParse(_temperatureController.text.trim()),
        heartRate: int.tryParse(_heartRateController.text.trim()),
        respiratoryRate: int.tryParse(_respiratoryRateController.text.trim()),
        weight: double.tryParse(_weightController.text.trim()),
        bodyConditionScore: double.tryParse(_bodyConditionController.text.trim()),
        isEmergency: _isEmergency,
        isUnderTreatment: _isUnderTreatment,
        requiresFollowUp: _requiresFollowUp,
        isChronic: _isChronic,
      ) ?? HealthRecordIsar.create(
        cattleBusinessId: _selectedCattle!.businessId!,
        cattleTagId: _selectedCattle!.tagId!,
        cattleName: _selectedCattle!.name!,
        farmBusinessId: farmId!,
        recordType: _selectedRecordType!,
        date: _selectedDate,
        condition: _selectedCondition!,
        healthStatus: _selectedHealthStatus!,
        severity: _selectedSeverity,
        symptoms: _symptomsController.text.trim().isEmpty ? null : _symptomsController.text.trim(),
        diagnosis: _diagnosisController.text.trim().isEmpty ? null : _diagnosisController.text.trim(),
        treatment: _treatmentController.text.trim().isEmpty ? null : _treatmentController.text.trim(),
        medication: _medicationController.text.trim().isEmpty ? null : _medicationController.text.trim(),
        dosage: _dosageController.text.trim().isEmpty ? null : _dosageController.text.trim(),
        frequency: _frequencyController.text.trim().isEmpty ? null : _frequencyController.text.trim(),
        duration: _durationController.text.trim().isEmpty ? null : _durationController.text.trim(),
        veterinarian: _selectedVeterinarian?.name,
        veterinarianContact: _veterinarianContactController.text.trim().isEmpty ? null : _veterinarianContactController.text.trim(),
        treatmentCost: double.tryParse(_treatmentCostController.text.trim()),
        followUpDate: _followUpDate,
        recoveryDate: _recoveryDate,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        temperature: double.tryParse(_temperatureController.text.trim()),
        heartRate: int.tryParse(_heartRateController.text.trim()),
        respiratoryRate: int.tryParse(_respiratoryRateController.text.trim()),
        weight: double.tryParse(_weightController.text.trim()),
        bodyConditionScore: double.tryParse(_bodyConditionController.text.trim()),
        isEmergency: _isEmergency,
        isUnderTreatment: _isUnderTreatment,
        requiresFollowUp: _requiresFollowUp,
        isChronic: _isChronic,
      );

      if (widget.existingRecord != null) {
        await _healthService.updateHealthRecord(record);
      } else {
        await _healthService.addHealthRecord(record);
      }

      if (mounted) {
        Navigator.pop(context);
        widget.onRecordAdded();
        MessageUtils.showSuccessSnackBar(
          context,
          widget.existingRecord != null 
              ? 'Health record updated successfully'
              : 'Health record added successfully',
        );
      }
    } catch (e) {
      if (mounted) {
        MessageUtils.showErrorSnackBar(context, 'Failed to save health record: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.existingRecord != null ? 'Edit Health Record' : 'Add Health Record'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveRecord,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'Save',
                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // Basic Information Section
            _buildSectionHeader('Basic Information', Icons.info, Colors.red),
            const SizedBox(height: 16),
            
            // Cattle Selection
            DropdownButtonFormField<CattleIsar>(
              value: _selectedCattle,
              decoration: const InputDecoration(
                labelText: 'Select Cattle *',
                border: OutlineInputBorder(),
              ),
              items: widget.cattle.map((cattle) {
                return DropdownMenuItem(
                  value: cattle,
                  child: Text('${cattle.name} (${cattle.tagId})'),
                );
              }).toList(),
              onChanged: (cattle) => setState(() => _selectedCattle = cattle),
              validator: (value) => value == null ? 'Please select a cattle' : null,
            ),
            const SizedBox(height: 16),

            // Record Type
            DropdownButtonFormField<String>(
              value: _selectedRecordType,
              decoration: const InputDecoration(
                labelText: 'Record Type *',
                border: OutlineInputBorder(),
              ),
              items: HealthConstants.recordTypes.map((type) {
                return DropdownMenuItem(value: type, child: Text(type));
              }).toList(),
              onChanged: (type) => setState(() => _selectedRecordType = type),
              validator: (value) => value == null ? 'Please select a record type' : null,
            ),
            const SizedBox(height: 16),

            // Date
            InkWell(
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _selectedDate,
                  firstDate: DateTime(2000),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (date != null) {
                  setState(() => _selectedDate = date);
                }
              },
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Date *',
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(DateFormat('MMM dd, yyyy').format(_selectedDate)),
              ),
            ),
            const SizedBox(height: 24),

            // Health Details Section
            _buildSectionHeader('Health Details', Icons.medical_information, Colors.blue),
            const SizedBox(height: 16),

            // Condition
            DropdownButtonFormField<String>(
              value: _selectedCondition,
              decoration: const InputDecoration(
                labelText: 'Condition *',
                border: OutlineInputBorder(),
              ),
              items: _getConditionsForSelectedCattle().map((condition) {
                return DropdownMenuItem(value: condition, child: Text(condition));
              }).toList(),
              onChanged: (condition) => setState(() => _selectedCondition = condition),
              validator: (value) => value == null ? 'Please select a condition' : null,
            ),
            const SizedBox(height: 16),

            // Health Status
            DropdownButtonFormField<String>(
              value: _selectedHealthStatus,
              decoration: const InputDecoration(
                labelText: 'Health Status *',
                border: OutlineInputBorder(),
              ),
              items: HealthConstants.healthStatus.map((status) {
                return DropdownMenuItem(value: status, child: Text(status));
              }).toList(),
              onChanged: (status) => setState(() => _selectedHealthStatus = status),
              validator: (value) => value == null ? 'Please select a health status' : null,
            ),
            const SizedBox(height: 16),

            // Severity
            DropdownButtonFormField<String>(
              value: _selectedSeverity,
              decoration: const InputDecoration(
                labelText: 'Severity',
                border: OutlineInputBorder(),
              ),
              items: HealthConstants.severityLevels.map((severity) {
                return DropdownMenuItem(value: severity, child: Text(severity));
              }).toList(),
              onChanged: (severity) => setState(() => _selectedSeverity = severity),
            ),
            const SizedBox(height: 16),

            // Symptoms
            TextFormField(
              controller: _symptomsController,
              decoration: const InputDecoration(
                labelText: 'Symptoms',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),

            // Diagnosis
            TextFormField(
              controller: _diagnosisController,
              decoration: const InputDecoration(
                labelText: 'Diagnosis',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 24),

            // Treatment Section
            _buildSectionHeader('Treatment Information', Icons.medication, Colors.green),
            const SizedBox(height: 16),

            // Treatment
            TextFormField(
              controller: _treatmentController,
              decoration: const InputDecoration(
                labelText: 'Treatment',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),

            // Medication
            TextFormField(
              controller: _medicationController,
              decoration: const InputDecoration(
                labelText: 'Medication',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),

            // Dosage and Frequency Row
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _dosageController,
                    decoration: const InputDecoration(
                      labelText: 'Dosage',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _frequencyController,
                    decoration: const InputDecoration(
                      labelText: 'Frequency',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Duration
            TextFormField(
              controller: _durationController,
              decoration: const InputDecoration(
                labelText: 'Duration',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 24),

            // Veterinarian Section
            _buildSectionHeader('Veterinarian Information', Icons.person, Colors.purple),
            const SizedBox(height: 16),

            // Veterinarian
            DropdownButtonFormField<VeterinarianIsar>(
              value: _selectedVeterinarian,
              decoration: const InputDecoration(
                labelText: 'Veterinarian',
                border: OutlineInputBorder(),
              ),
              items: widget.veterinarians.map((vet) {
                return DropdownMenuItem(
                  value: vet,
                  child: Text(vet.name ?? 'Unknown'),
                );
              }).toList(),
              onChanged: (vet) {
                setState(() {
                  _selectedVeterinarian = vet;
                  _veterinarianContactController.text = vet?.phoneNumber ?? '';
                });
              },
            ),
            const SizedBox(height: 16),

            // Veterinarian Contact
            TextFormField(
              controller: _veterinarianContactController,
              decoration: const InputDecoration(
                labelText: 'Veterinarian Contact',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),

            // Treatment Cost
            TextFormField(
              controller: _treatmentCostController,
              decoration: const InputDecoration(
                labelText: 'Treatment Cost',
                border: OutlineInputBorder(),
                prefixText: '\$ ',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 24),

            // Vital Signs Section
            _buildSectionHeader('Vital Signs', Icons.monitor_heart, Colors.teal),
            const SizedBox(height: 16),

            // Temperature and Heart Rate Row
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _temperatureController,
                    decoration: const InputDecoration(
                      labelText: 'Temperature (°C)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _heartRateController,
                    decoration: const InputDecoration(
                      labelText: 'Heart Rate (BPM)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Respiratory Rate and Weight Row
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _respiratoryRateController,
                    decoration: const InputDecoration(
                      labelText: 'Respiratory Rate',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _weightController,
                    decoration: const InputDecoration(
                      labelText: 'Weight (kg)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Body Condition Score
            TextFormField(
              controller: _bodyConditionController,
              decoration: const InputDecoration(
                labelText: 'Body Condition Score (1-9)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 24),

            // Dates Section
            _buildSectionHeader('Important Dates', Icons.date_range, Colors.indigo),
            const SizedBox(height: 16),

            // Follow-up Date
            InkWell(
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _followUpDate ?? DateTime.now().add(const Duration(days: 7)),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (date != null) {
                  setState(() => _followUpDate = date);
                }
              },
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Follow-up Date',
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  _followUpDate != null 
                      ? DateFormat('MMM dd, yyyy').format(_followUpDate!)
                      : 'Select follow-up date',
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Recovery Date
            InkWell(
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _recoveryDate ?? DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (date != null) {
                  setState(() => _recoveryDate = date);
                }
              },
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Recovery Date',
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  _recoveryDate != null 
                      ? DateFormat('MMM dd, yyyy').format(_recoveryDate!)
                      : 'Select recovery date',
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Flags Section
            _buildSectionHeader('Record Flags', Icons.flag, Colors.deepPurple),
            const SizedBox(height: 16),

            // Emergency Flag
            CheckboxListTile(
              title: const Text('Emergency Case'),
              subtitle: const Text('Mark as emergency health record'),
              value: _isEmergency,
              onChanged: (value) => setState(() => _isEmergency = value ?? false),
              activeColor: Colors.red,
            ),

            // Under Treatment Flag
            CheckboxListTile(
              title: const Text('Currently Under Treatment'),
              subtitle: const Text('Animal is receiving ongoing treatment'),
              value: _isUnderTreatment,
              onChanged: (value) => setState(() => _isUnderTreatment = value ?? false),
              activeColor: Colors.blue,
            ),

            // Requires Follow-up Flag
            CheckboxListTile(
              title: const Text('Requires Follow-up'),
              subtitle: const Text('Schedule follow-up examination'),
              value: _requiresFollowUp,
              onChanged: (value) => setState(() => _requiresFollowUp = value ?? false),
              activeColor: Colors.green,
            ),

            // Chronic Condition Flag
            CheckboxListTile(
              title: const Text('Chronic Condition'),
              subtitle: const Text('Long-term or recurring health issue'),
              value: _isChronic,
              onChanged: (value) => setState(() => _isChronic = value ?? false),
              activeColor: Colors.purple,
            ),
            const SizedBox(height: 24),

            // Notes Section
            _buildSectionHeader('Additional Notes', Icons.notes, Colors.blueGrey),
            const SizedBox(height: 16),

            // Notes
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes',
                border: OutlineInputBorder(),
                hintText: 'Additional observations, recommendations, or comments...',
              ),
              maxLines: 4,
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(child: Divider(color: color)),
      ],
    );
  }

  List<String> _getConditionsForSelectedCattle() {
    if (_selectedCattle == null) {
      return HealthConstants.commonConditions['general'] ?? [];
    }

    // Get animal type to determine appropriate conditions
    // For now, return general conditions. In a real app, you'd map animal type to conditions
    return HealthConstants.commonConditions['general'] ?? [];
  }
}
