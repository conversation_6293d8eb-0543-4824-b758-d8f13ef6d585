import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import '../models/event_isar.dart';
import '../services/event_stream_service.dart';
import '../../../services/database/database_helper.dart';

import '../../../constants/app_icons.dart';

class AutoEventsTab extends StatefulWidget {
  const AutoEventsTab({Key? key}) : super(key: key);

  @override
  State<AutoEventsTab> createState() => _AutoEventsTabState();
}

class _AutoEventsTabState extends State<AutoEventsTab> {
  static final Logger _logger = Logger('AutoEventsTab');
  final EventStreamService _eventStreamService = EventStreamService.instance;
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  
  List<EventIsar> _autoEvents = [];
  bool _isLoading = true;
  String _selectedFilter = 'all';
  String _searchQuery = '';

  // Auto-generation settings
  final Map<String, bool> _autoGenerationSettings = {
    'breeding_events': true,
    'health_followups': true,
    'vaccination_schedule': true,
    'milk_reminders': false,
    'weight_reminders': true,
    'heat_cycle_predictions': true,
  };

  // Responsive helper methods
  double _getResponsiveFontSize() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 11.0; // Small screens
    } else if (screenWidth < 600) {
      return 12.0; // Medium screens
    } else {
      return 13.0; // Large screens
    }
  }

  double _getResponsiveIconSize() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 20.0; // Small screens
    } else if (screenWidth < 600) {
      return 22.0; // Medium screens
    } else {
      return 24.0; // Large screens
    }
  }

  double _getResponsivePadding() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 12.0; // Small screens
    } else if (screenWidth < 600) {
      return 16.0; // Medium screens
    } else {
      return 20.0; // Large screens
    }
  }

  double _getResponsiveSpacing() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 16.0; // Small screens
    } else if (screenWidth < 600) {
      return 20.0; // Medium screens
    } else {
      return 24.0; // Large screens
    }
  }

  @override
  void initState() {
    super.initState();
    _logger.info('⚡ AutoEventsTab: Initializing auto events with instant data loading...');
    _loadDataInstantly();
    _loadAutoGenerationSettings();
  }

  Future<void> _loadDataInstantly() async {
    try {
      _logger.info('⚡ AutoEventsTab: Loading auto events directly from database...');

      // Initialize service first (if not already initialized)
      await _eventStreamService.initialize();

      // Load data directly from database - NO WAITING FOR STREAMS
      final dbHelper = DatabaseHelper.instance;
      final allEvents = await dbHelper.eventsHandler.getAllEvents();
      final autoEvents = allEvents.where((event) => event.isAutoGenerated).toList();

      _logger.info('⚡ AutoEventsTab: Loaded ${autoEvents.length} auto-generated events from ${allEvents.length} total events');

      // Update UI immediately - NO DELAYS
      if (mounted) {
        setState(() {
          _autoEvents = autoEvents;
          _isLoading = false;
        });
        _logger.info('⚡ AutoEventsTab: Auto events updated instantly');
      }

      // Set up streams for real-time updates AFTER initial load
      _setupRealTimeStreams();

    } catch (e) {
      _logger.severe('❌ AutoEventsTab: Error loading data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _setupRealTimeStreams() {
    _logger.info('🔄 AutoEventsTab: Setting up real-time streams for updates...');

    // Listen to all events stream for real-time updates only
    _eventStreamService.allEventsStream.listen((events) {
      final autoEvents = events.where((event) => event.isAutoGenerated).toList();
      if (mounted) {
        setState(() {
          _autoEvents = autoEvents;
        });
      }
    });
  }

  Future<void> _loadAutoGenerationSettings() async {
    // Load settings from shared preferences or database
    // For now, using default values
    setState(() {
      // Settings loaded
    });
  }

  List<EventIsar> _getFilteredEvents() {
    var filteredEvents = _autoEvents;

    // Apply source filter
    if (_selectedFilter != 'all') {
      filteredEvents = filteredEvents.where((event) => 
        event.sourceModule == _selectedFilter).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filteredEvents = filteredEvents.where((event) =>
        (event.title?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
        (event.notes?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false)).toList();
    }

    return filteredEvents;
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // Header with controls
        _buildHeader(),

        // Scrollable content
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async {
              await _eventStreamService.initialize();
            },
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                children: [
                  // Auto-generation settings
                  _buildAutoGenerationSettings(),

                  // Filters and search
                  _buildFiltersAndSearch(),

                  const SizedBox(height: 16),

                  // Auto events list
                  _buildAutoEventsList(),

                  // Bottom padding
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFE0E0E0), // Solid light grey instead of alpha
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.auto_awesome,
            color: const Color(0xFF8E44AD), // Purple instead of violet
            size: _getResponsiveIconSize(),
          ),
          SizedBox(width: _getResponsivePadding() * 0.5),
          Text(
            'Auto-Generated Events',
            style: TextStyle(
              fontSize: _getResponsiveFontSize() + 7,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: _getResponsivePadding() * 0.75,
              vertical: _getResponsivePadding() * 0.375,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              '${_autoEvents.length} Events',
              style: TextStyle(
                color: const Color(0xFF8E44AD),
                fontWeight: FontWeight.bold,
                fontSize: _getResponsiveFontSize(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAutoGenerationSettings() {
    return Container(
      margin: EdgeInsets.all(_getResponsivePadding()),
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFE0E0E0), // Solid light grey instead of alpha
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.settings,
                color: const Color(0xFF1976D2), // Blue
                size: _getResponsiveIconSize(),
              ),
              SizedBox(width: _getResponsivePadding() * 0.5),
              Text(
                'Auto-Generation Settings',
                style: TextStyle(
                  fontSize: _getResponsiveFontSize() + 3,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: _getResponsiveSpacing()),
          _buildSettingChipsGrid(),
        ],
      ),
    );
  }

  Widget _buildSettingChipsGrid() {
    final screenWidth = MediaQuery.of(context).size.width;
    final padding = _getResponsivePadding();

    // Calculate optimal columns and spacing
    int columns;
    double spacing;
    if (screenWidth < 360) {
      columns = 1; // 1 chip per row on very small screens
      spacing = padding * 0.5;
    } else if (screenWidth < 480) {
      columns = 2; // 2 chips per row on small screens
      spacing = padding * 0.5;
    } else if (screenWidth < 768) {
      columns = 2; // 2 chips per row on medium screens
      spacing = padding * 0.75;
    } else {
      columns = 3; // 3 chips per row on large screens
      spacing = padding;
    }

    final chipData = [
      {'label': 'Breeding Events', 'key': 'breeding_events', 'icon': AppIcons.breeding, 'color': const Color(0xFFE74C3C)},
      {'label': 'Health Follow-ups', 'key': 'health_followups', 'icon': AppIcons.health, 'color': const Color(0xFF27AE60)},
      {'label': 'Vaccination Schedule', 'key': 'vaccination_schedule', 'icon': AppIcons.vaccination, 'color': const Color(0xFF3498DB)},
      {'label': 'Milk Reminders', 'key': 'milk_reminders', 'icon': AppIcons.milk, 'color': const Color(0xFF16A085)},
      {'label': 'Weight Reminders', 'key': 'weight_reminders', 'icon': AppIcons.weight, 'color': const Color(0xFF9B59B6)},
      {'label': 'Heat Cycle Predictions', 'key': 'heat_cycle_predictions', 'icon': Icons.thermostat, 'color': const Color(0xFF1976D2)},
    ];

    return Column(
      children: [
        for (int i = 0; i < chipData.length; i += columns)
          Padding(
            padding: EdgeInsets.only(bottom: spacing),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                for (int j = 0; j < columns && (i + j) < chipData.length; j++)
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: spacing / 2),
                      child: _buildSettingChip(
                        chipData[i + j]['label'] as String,
                        chipData[i + j]['key'] as String,
                        chipData[i + j]['icon'] as IconData,
                        chipData[i + j]['color'] as Color,
                      ),
                    ),
                  ),
                // Add empty expanded widgets to maintain spacing if needed
                for (int k = 0; k < columns - ((chipData.length - i).clamp(0, columns)); k++)
                  const Expanded(child: SizedBox()),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildSettingChip(String label, String key, IconData icon, Color color) {
    final isEnabled = _autoGenerationSettings[key] ?? false;
    final iconSize = ((((((_getResponsiveIconSize() * 0.75) * 1.32) * 0.9) * 0.9) * 0.9) * 1.1) * 1.1;

    return GestureDetector(
      onTap: () {
        setState(() {
          _autoGenerationSettings[key] = !isEnabled;
        });
        _saveAutoGenerationSettings();
      },
      child: Container(
        constraints: BoxConstraints(
          minHeight: _getResponsiveIconSize() * 2.5, // Ensure consistent height for 2-line text
        ),
        padding: EdgeInsets.symmetric(
          horizontal: _getResponsivePadding() * 0.4,
          vertical: _getResponsivePadding() * 0.6, // Enhanced padding for 2-line text support
        ),
        decoration: BoxDecoration(
          color: isEnabled ? color : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: iconSize,
                  color: isEnabled ? Colors.white : color,
                ),
                if (isEnabled) ...[
                  SizedBox(width: (_getResponsivePadding() * 0.5) * 1.1),
                  Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFFBDBDBD), // Solid grey instead of alpha
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.check,
                      size: iconSize * 0.5, // Same proportional size as other icons
                      color: color,
                    ),
                  ),
                ],
              ],
            ),
            SizedBox(height: _getResponsivePadding() * 0.3),
            Flexible(
              child: Text(
                label,
                style: TextStyle(
                  color: isEnabled ? Colors.white : color,
                  fontWeight: FontWeight.w500,
                  fontSize: ((((_getResponsiveFontSize() * 1.32) * 0.9) * 0.9) * 0.9) * 1.1, // Increased by 10%
                  height: 1.2, // Line height for better 2-line text spacing
                ),
                textAlign: TextAlign.center,
                maxLines: 2, // Allow 2 lines for longer text
                overflow: TextOverflow.visible, // No trimming - show full text
                softWrap: true, // Enable text wrapping
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFiltersAndSearch() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: _getResponsivePadding()),
      child: Row(
        children: [
          // Source filter dropdown
          Expanded(
            flex: 2,
            child: DropdownButtonFormField<String>(
              value: _selectedFilter,
              decoration: InputDecoration(
                labelText: 'Source',
                prefixIcon: Icon(
                  Icons.filter_list,
                  size: _getResponsiveIconSize() * 0.8,
                  color: const Color(0xFF3498DB), // Blue
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: _getResponsivePadding() * 0.5,
                  vertical: _getResponsivePadding() * 0.5,
                ),
                isDense: true,
              ),
              style: TextStyle(fontSize: _getResponsiveFontSize()),
              items: [
                DropdownMenuItem(
                  value: 'all',
                  child: Text('All', style: TextStyle(fontSize: _getResponsiveFontSize())),
                ),
                DropdownMenuItem(
                  value: 'breeding',
                  child: Text('Breeding', style: TextStyle(fontSize: _getResponsiveFontSize())),
                ),
                DropdownMenuItem(
                  value: 'health',
                  child: Text('Health', style: TextStyle(fontSize: _getResponsiveFontSize())),
                ),
                DropdownMenuItem(
                  value: 'cattle',
                  child: Text('Cattle', style: TextStyle(fontSize: _getResponsiveFontSize())),
                ),
                DropdownMenuItem(
                  value: 'milk',
                  child: Text('Milk', style: TextStyle(fontSize: _getResponsiveFontSize())),
                ),
                DropdownMenuItem(
                  value: 'weight',
                  child: Text('Weight', style: TextStyle(fontSize: _getResponsiveFontSize())),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedFilter = value ?? 'all';
                });
              },
            ),
          ),

          SizedBox(width: _getResponsivePadding() * 0.75),

          // Search field
          Expanded(
            flex: 3,
            child: TextField(
              style: TextStyle(fontSize: _getResponsiveFontSize()),
              decoration: InputDecoration(
                labelText: 'Search...',
                prefixIcon: Icon(
                  Icons.search,
                  size: _getResponsiveIconSize() * 0.8,
                  color: const Color(0xFF8E44AD), // Purple
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: _getResponsivePadding() * 0.5,
                  vertical: _getResponsivePadding() * 0.5,
                ),
                isDense: true,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAutoEventsList() {
    final filteredEvents = _getFilteredEvents();

    if (filteredEvents.isEmpty) {
      return Container(
        padding: EdgeInsets.all(_getResponsivePadding() * 2),
        child: Column(
          children: [
            Icon(
              Icons.auto_awesome_outlined,
              size: _getResponsiveIconSize() * 3,
              color: const Color(0xFF9E9E9E), // Solid dark grey instead of alpha
            ),
            SizedBox(height: _getResponsiveSpacing()),
            Text(
              _autoEvents.isEmpty
                ? 'No auto-generated events yet'
                : 'No events match your filters',
              style: TextStyle(
                fontSize: _getResponsiveFontSize() + 5,
                color: Colors.black87, // Changed from light color to black87
              ),
            ),
            SizedBox(height: _getResponsivePadding() * 0.5),
            Text(
              _autoEvents.isEmpty
                ? 'Events will be automatically generated based on your module activities'
                : 'Try adjusting your search or filter criteria',
              style: TextStyle(
                fontSize: _getResponsiveFontSize() + 1,
                color: Colors.black87, // Changed from light color to avoid light colors
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getResponsivePadding()),
      child: Column(
        children: filteredEvents.map((event) => _buildAutoEventCard(event)).toList(),
      ),
    );
  }

  Widget _buildAutoEventCard(EventIsar event) {
    final eventColor = event.type?.getColor() ?? const Color(0xFF795548); // Brown instead of grey
    final sourceColor = _getSourceModuleColor(event.sourceModule);
    
    return Container(
      margin: EdgeInsets.only(bottom: _getResponsivePadding() * 0.75),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFE0E0E0), // Solid light grey instead of alpha
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header with source module
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: sourceColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _getSourceModuleIcon(event.sourceModule),
                  color: Colors.white, // Changed to white for visibility on colored background
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  'Auto-generated from ${event.sourceModule?.toUpperCase() ?? 'UNKNOWN'}',
                  style: const TextStyle(
                    color: Colors.white, // Changed to white for visibility on colored background
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.auto_awesome,
                  color: Colors.white, // Changed to white for visibility on colored background
                  size: 16,
                ),
              ],
            ),
          ),
          
          // Event content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Event icon and type
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: eventColor),
                  ),
                  child: Icon(
                    event.type?.getIcon() ?? Icons.event,
                    color: eventColor,
                    size: 24,
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Event details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        event.title ?? 'Untitled Event',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      if (event.eventDate != null)
                        Text(
                          'Scheduled for ${_formatDate(event.eventDate!)}',
                          style: const TextStyle(
                            color: Colors.black87, // Changed from Colors.grey[600] to black87
                            fontSize: 14,
                          ),
                        ),
                      if (event.notes != null && event.notes!.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            event.notes!,
                            style: const TextStyle(
                              color: Colors.black87, // Changed from Colors.grey[600] to black87
                              fontSize: 12,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          // Priority badge
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getPriorityColor(event.priority),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              event.priority.name.toUpperCase(),
                              style: const TextStyle(
                                color: Colors.white, // Changed to white for visibility on colored background
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          
                          const SizedBox(width: 8),
                          
                          // Status badge
                          if (event.isCompleted)
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: _getResponsivePadding() * 0.5,
                                vertical: _getResponsivePadding() * 0.25,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFF27AE60),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'COMPLETED',
                                style: TextStyle(
                                  color: Colors.white, // Changed to white for visibility on colored background
                                  fontSize: _getResponsiveFontSize() - 2,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Action buttons
                Column(
                  children: [
                    IconButton(
                      onPressed: () => _toggleEventCompletion(event),
                      icon: Icon(
                        event.isCompleted ? Icons.undo : Icons.check,
                        color: event.isCompleted ? const Color(0xFF3498DB) : const Color(0xFF27AE60), // Blue and Green
                        size: _getResponsiveIconSize(),
                      ),
                      tooltip: event.isCompleted ? 'Mark as Pending' : 'Mark as Completed',
                    ),
                    IconButton(
                      onPressed: () => _showEventOptions(event),
                      icon: Icon(
                        Icons.more_vert,
                        size: _getResponsiveIconSize(),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getSourceModuleColor(String? module) {
    switch (module?.toLowerCase()) {
      case 'breeding':
        return const Color(0xFFE74C3C); // Red
      case 'health':
        return const Color(0xFF27AE60); // Green
      case 'cattle':
        return const Color(0xFF8E44AD); // Purple
      case 'milk':
        return const Color(0xFF16A085); // Teal
      case 'weight':
        return const Color(0xFF9B59B6); // Purple
      default:
        return const Color(0xFF3498DB); // Blue
    }
  }

  IconData _getSourceModuleIcon(String? module) {
    switch (module?.toLowerCase()) {
      case 'breeding':
        return AppIcons.breeding;
      case 'health':
        return AppIcons.health;
      case 'cattle':
        return AppIcons.cow;
      case 'milk':
        return AppIcons.milk;
      case 'weight':
        return AppIcons.weight;
      default:
        return Icons.auto_awesome;
    }
  }

  Color _getPriorityColor(EventPriority priority) {
    switch (priority) {
      case EventPriority.low:
        return const Color(0xFF27AE60); // Green
      case EventPriority.medium:
        return const Color(0xFF3498DB); // Blue
      case EventPriority.high:
        return const Color(0xFF16A085); // Teal
      case EventPriority.urgent:
        return const Color(0xFF8E44AD); // Purple
      case EventPriority.critical:
        return const Color(0xFFE74C3C); // Red
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _toggleEventCompletion(EventIsar event) async {
    await _dbHelper.eventsHandler.markEventAsCompleted(
      event.businessId!,
      !event.isCompleted,
    );
    _eventStreamService.notifyEventChange('update', event);
  }

  void _showEventOptions(EventIsar event) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit Event'),
              onTap: () {
                Navigator.pop(context);
                // Show edit dialog
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Duplicate Event'),
              onTap: () {
                Navigator.pop(context);
                // Duplicate event logic
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete Event', style: TextStyle(color: Colors.red)),
              onTap: () async {
                Navigator.pop(context);
                final confirmed = await _showDeleteConfirmation();
                if (confirmed) {
                  await _dbHelper.eventsHandler.deleteEvent(event.businessId!);
                  _eventStreamService.notifyEventChange('delete', event);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<bool> _showDeleteConfirmation() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Auto-Generated Event'),
        content: const Text(
          'Are you sure you want to delete this auto-generated event? '
          'It may be regenerated automatically based on your module activities.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    ) ?? false;
  }

  Future<void> _saveAutoGenerationSettings() async {
    // Save settings to shared preferences or database
    // Implementation will be added
  }
}
