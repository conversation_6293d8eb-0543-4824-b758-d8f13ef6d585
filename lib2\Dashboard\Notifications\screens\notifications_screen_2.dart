import 'package:flutter/material.dart';

class NotificationItem {
  final String title;
  final String message;
  final DateTime time;
  final bool isRead;
  final NotificationType type;

  NotificationItem({
    required this.title,
    required this.message,
    required this.time,
    this.isRead = false,
    required this.type,
  });
}

enum NotificationType {
  health,
  breeding,
  general,
  alert
}

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<NotificationItem> _notifications = [
    NotificationItem(
      title: 'Health Check Due',
      message: 'Cattle #1234 is due for vaccination',
      time: DateTime.now().subtract(const Duration(hours: 2)),
      type: NotificationType.health,
    ),
    NotificationItem(
      title: 'Breeding Alert',
      message: 'Cow #5678 is in heat',
      time: DateTime.now().subtract(const Duration(hours: 5)),
      type: NotificationType.breeding,
    ),
    NotificationItem(
      title: 'Feed Stock Low',
      message: 'Feed inventory is below 20%',
      time: DateTime.now().subtract(const Duration(days: 1)),
      type: NotificationType.alert,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Color _getTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.health:
        return Colors.blue;
      case NotificationType.breeding:
        return Colors.purple;
      case NotificationType.general:
        return Colors.green;
      case NotificationType.alert:
        return Colors.red;
    }
  }

  IconData _getTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.health:
        return Icons.medical_services;
      case NotificationType.breeding:
        return Icons.pets;
      case NotificationType.general:
        return Icons.notifications;
      case NotificationType.alert:
        return Icons.warning;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        backgroundColor: const Color(0xFF2E7D32),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Health'),
            Tab(text: 'Breeding'),
            Tab(text: 'Alerts'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildNotificationList(_notifications),
          _buildNotificationList(_notifications.where((n) => n.type == NotificationType.health).toList()),
          _buildNotificationList(_notifications.where((n) => n.type == NotificationType.breeding).toList()),
          _buildNotificationList(_notifications.where((n) => n.type == NotificationType.alert).toList()),
        ],
      ),
    );
  }

  Widget _buildNotificationList(List<NotificationItem> notifications) {
    if (notifications.isEmpty) {
      return const Center(
        child: Text('No notifications'),
      );
    }

    return ListView.builder(
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getTypeColor(notification.type).withAlpha((0.2 * 255).round()),
              child: Icon(
                _getTypeIcon(notification.type),
                color: _getTypeColor(notification.type),
              ),
            ),
            title: Text(
              notification.title,
              style: TextStyle(
                fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(notification.message),
                const SizedBox(height: 4),
                Text(
                  _formatTime(notification.time),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            onTap: () {
              // Handle notification tap
              setState(() {
                notifications[index] = NotificationItem(
                  title: notification.title,
                  message: notification.message,
                  time: notification.time,
                  isRead: true,
                  type: notification.type,
                );
              });
            },
          ),
        );
      },
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
