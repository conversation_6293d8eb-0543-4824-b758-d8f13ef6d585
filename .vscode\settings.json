class Cattle {
  final String id;
  final String tagId;
  final String name;
  final String animalTypeId;
  final String breedId;  
  final String gender;
  final String source;
  final String? motherTagId;
  final DateTime? dateOfBirth;
  final DateTime? purchaseDate;
  final double? purchasePrice;
  final double? weight;
  final String? color;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Cattle({
    required this.id,
    required this.tagId,
    required this.name,
    required this