import 'package:flutter/material.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/health_record_isar.dart';
import '../models/treatment_record_isar.dart';
import '../models/vaccination_record_isar.dart';
import '../services/health_service.dart';
import '../../../constants/app_bar.dart';
import '../../../widgets/reusable_tab_bar.dart';

import 'cattle_health_analytics_tab.dart';
import 'cattle_health_records_tab.dart';
import 'cattle_treatment_records_tab.dart';
import 'cattle_vaccination_records_tab.dart';

class CattleHealthDetailScreen extends StatefulWidget {
  final CattleIsar cattle;
  final VoidCallback onCattleUpdated;

  const CattleHealthDetailScreen({
    Key? key,
    required this.cattle,
    required this.onCattleUpdated,
  }) : super(key: key);

  @override
  State<CattleHealthDetailScreen> createState() => _CattleHealthDetailScreenState();
}

class _CattleHealthDetailScreenState extends State<CattleHealthDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final HealthService _healthService = HealthService.instance;

  // Data state
  bool _isLoading = true;
  List<HealthRecordIsar> _healthRecords = [];
  List<TreatmentRecordIsar> _treatmentRecords = [];
  List<VaccinationRecordIsar> _vaccinationRecords = [];

  // Tab configuration using the reusable widget
  late List<TabItem> _tabs;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // Rebuild to update colors
    });

    // Define tabs using the reusable widget configuration
    _tabs = TabConfigurations.fourTabModule(
      tab1Label: 'Analytics',
      tab1Icon: Icons.analytics,
      tab2Label: 'Health',
      tab2Icon: Icons.medical_information,
      tab3Label: 'Treatments',
      tab3Icon: Icons.medication,
      tab4Label: 'Vaccines',
      tab4Icon: Icons.vaccines,
    );

    _loadHealthData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadHealthData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _healthService.initialize();

      final futures = await Future.wait([
        _healthService.getHealthRecordsForCattle(widget.cattle.businessId ?? ''),
        _healthService.getTreatmentRecordsForCattle(widget.cattle.businessId ?? ''),
        _healthService.getVaccinationRecordsForCattle(widget.cattle.businessId ?? ''),
      ]);

      setState(() {
        _healthRecords = futures[0] as List<HealthRecordIsar>;
        _treatmentRecords = futures[1] as List<TreatmentRecordIsar>;
        _vaccinationRecords = futures[2] as List<VaccinationRecordIsar>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      debugPrint('Error loading health data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarConfig.standard(
        title: '${widget.cattle.name ?? 'Unknown'} - Health',
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          // Use the reusable tab bar widget
          ReusableTabBar(
            tabController: _tabController,
            tabs: _tabs,
            context: context,
          ),
          // TabBarView
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      CattleHealthAnalyticsTab(
                        cattle: widget.cattle,
                        healthRecords: _healthRecords,
                        treatmentRecords: _treatmentRecords,
                        vaccinationRecords: _vaccinationRecords,
                      ),
                      CattleHealthRecordsTab(
                        cattle: widget.cattle,
                        onCattleUpdated: widget.onCattleUpdated,
                      ),
                      CattleTreatmentRecordsTab(
                        cattle: widget.cattle,
                        onCattleUpdated: widget.onCattleUpdated,
                      ),
                      CattleVaccinationRecordsTab(
                        cattle: widget.cattle,
                        onCattleUpdated: widget.onCattleUpdated,
                      ),
                    ],
                  ),
          ),
        ],
      ),
    );
  }
}
