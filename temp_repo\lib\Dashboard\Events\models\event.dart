import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import '../../Cattle/models/cattle.dart';
import '../../Health/models/health_record.dart';
import '../models/cattle_report_data.dart';
import '../report_tabs/cattle_summary_tab.dart';
import '../report_tabs/cattle_details_tab.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class CattleReportScreen extends StatefulWidget {
  const CattleReportScreen({Key? key}) : super(key: key);

  @override
  CattleReportScreenState createState() => CattleReportScreenState();
}

class CattleReportScreenState extends State<CattleReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late CattleReportData reportData;
  DateTime? startDate;
  DateTime? endDate;
  String? selectedCategory;
  String? selectedStatus;
  bool isLoading = true;
  String? errorMessage;

  final List<String> categories = ['Dairy', 'Beef', 'Calf', 'Heifer', 'Bull'];
  final List<String> statuses = ['Active', 'Inactive', 'Sold', 'Deceased'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      final cattleBox = await Hive.openBox<Cattle>('cattle');
      final healthBox = await Hive.openBox<HealthRecord>('health_records');

      setState(() {
        reportData = CattleReportData(
          cattle: cattleBox.values.toList(),
          healthRecords: healthBox.values.toList(),
          startDate: startDate ?? DateTime.now().subtract(const Duration(days: 30)),
          endDate: endDate ?? DateTime.now(),
          filterCriteria: selectedCategory,
        );
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load data: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cattle Report'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Summary'),
            Tab(text: 'Details'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFilters(),
          if (isLoading)
            const Expanded(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          else if (errorMessage != null)
            Expanded(
              child: Center(
                child: Text(
                  errorMessage!,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            )
          else
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  CattleSummaryTab(reportData: reportData),
                  CattleDetailsTab(reportData: reportData),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: ResponsiveHelper.getResponsivePadding(context),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Category'),
                  value: selectedCategory,
                  items: [
                    const DropdownMenuItem<String>(
                      value: null,
                      child: Text('All Categories'),
                    ),
                    ...categories.map((category) {
                      return DropdownMenuItem