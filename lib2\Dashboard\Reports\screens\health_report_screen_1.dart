# Flutter Wrapper
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# SQLite
-keep class org.sqlite.** { *; }
-keep class org.sqlite.database.** { *; }

# Keep your model classes
-keep class com.example.cattle_manager.models.** { *; }

# Prevent obfuscation of names but still allow optimization
-keepnames class * { *; }

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep setters and getters
-keepclassmembers class * {
    void set*(***);
    *** get*();
}

# Keep Parcelable classes
-keep class * implements android.os.Parcelable {
    public 