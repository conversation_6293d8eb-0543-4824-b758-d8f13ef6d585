import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../models/farm.dart';
import 'package:uuid/uuid.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class FarmFormDialog extends StatefulWidget {
  final Farm? farm;

  const FarmFormDialog({Key? key, this.farm}) : super(key: key);

  @override
  State<FarmFormDialog> createState() => _FarmFormDialogState();
}

class _FarmFormDialogState extends State<FarmFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ownerNameController = TextEditingController();
  final _ownerContactController = TextEditingController();
  final _ownerEmailController = TextEditingController();
  final _addressController = TextEditingController();
  final _cattleCountController = TextEditingController();
  final _capacityController = TextEditingController();

  FarmType _selectedFarmType = FarmType.dairy;
  double? _latitude;
  double? _longitude;

  void _showError(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  void initState() {
    super.initState();
    if (widget.farm != null) {
      _nameController.text = widget.farm!.name;
      _ownerNameController.text = widget.farm!.ownerName;
      _ownerContactController.text = widget.farm!.ownerContact;
      _ownerEmailController.text = widget.farm!.ownerEmail;
      _addressController.text = widget.farm!.address ?? '';
      _cattleCountController.text = widget.farm!.cattleCount.toString();
      _capacityController.text = widget.farm!.capacity.toString();
      _selectedFarmType = widget.farm!.farmType;
      _latitude = widget.farm!.latitude;
      _longitude = widget.farm!.longitude;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ownerNameController.dispose();
    _ownerContactController.dispose();
    _ownerEmailController.dispose();
    _addressController.dispose();
    _cattleCountController.dispose();
    _capacityController.dispose();
    super.dispose();
  }

  Future<void> _getCurrentLocation() async {
    try {
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        final request = await Geolocator.requestPermission();
        if (request == LocationPermission.denied) {
          _showError('Location permission denied');
          return;
        }
      }

      final position = await Geolocator.getCurrentPosition();
      setState(() {
        _latitude = position.latitude;
        _longitude = position.longitude;
      });

      _showError('Location updated successfully');
    } catch (e) {
      _showError('Error getting location: $e');
    }
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;
    
    try {
      final farm = Farm(
        id: widget.farm?.id ?? const Uuid().v4(),
        name: _nameController.text,
        ownerName: _ownerNameController.text,
        ownerContact: _ownerContactController.text,
        ownerEmail: _ownerEmailController.text,
        latitude: _latitude,
        longitude: _longitude,
        address: _addressController.text,
        farmType: _selectedFarmType,
        cattleCount: int.parse(_cattleCountController.text),
        capacity: int.parse(_capacityController.text),
        lastUpdated: DateTime.now(),
      );
      if (!mounted) return;
      Navigator.of(context).pop(farm);
    } catch (e) {
      _showError(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: SingleChildScrollView(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.farm == null ? 'Add New Farm' : 'Edit Farm',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
              TextFormField(
                controller: _nameController,
                decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Farm Name'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter farm name';
                  }
                  return null;
                },
              ),
              SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
              TextFormField(
                controller: _ownerNameController,
                decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Owner Name'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter owner name';
                  }
                  return null;
                },
              ),
              SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
              TextFormField(
                controller: _ownerContactController,
                decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Owner Contact'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter owner contact';
                  }
                  return null;
                },
              ),
              SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
              TextFormField(
                controller: _ownerEmailController,
                decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Owner Email'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter owner email';
                  }
                  if (!value.contains('@')) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _addressController,
                      decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Address'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton.icon(
                    onPressed: _getCurrentLocation,
                    icon: const Icon(Icons.location_on),
                    label: const Text('Get Location'),
                  ),
                ],
              ),
              if (_latitude != null && _longitude != null)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    'GPS: ${_latitude!.toStringAsFixed(6)}, ${_longitude!.toStringAsFixed(6)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
              DropdownButtonFormField<FarmType>(
                value: _selectedFarmType,
                decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Farm Type'),
                items: FarmType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.toString().split('.').last.toUpperCase()),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedFarmType = value;
                    });
                  }
                },
              ),
              SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _cattleCountController,
                      decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Current Cattle Count'),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter cattle count';
                        }
                        if (int.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _capacityController,
                      decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Farm Capacity'),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter farm capacity';
                        }
                        if (int.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('CANCEL'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _handleSubmit,
                    child: Text(widget.farm == null ? 'ADD' : 'SAVE'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              