// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'breeding_record.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BreedingRecordAdapter extends TypeAdapter<BreedingRecord> {
  @override
  final int typeId = 3;

  @override
  BreedingRecord read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BreedingRecord(
      cattleId: fields[0] as String,
      date: fields[1] as DateTime,
      bullIdOrType: fields[2] as String,
      method: fields[3] as String,
      status: fields[4] as String,
      expectedDate: fields[5] as DateTime?,
      cost: fields[6] as double,
    );
  }

  @override
  void write(BinaryWriter writer, BreedingRecord obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.cattleId)
      ..writeByte(1)
      ..write(obj.date)
      ..writeByte(2)
      ..write(obj.bullIdOrType)
      ..writeByte(3)
      ..write(obj.method)
      ..writeByte(4)
      ..write(obj.status)
      ..writeByte(5)
      ..write(obj.expectedDate)
      ..writeByte(6)
      ..write(obj.cost);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BreedingRecordAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
