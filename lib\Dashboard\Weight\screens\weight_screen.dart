import 'package:flutter/material.dart';

import '../models/weight_record_isar.dart';
import '../services/weight_service.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../Cattle/services/cattle_handler.dart';
import '../../../services/database/database_helper.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_bar.dart';
import '../../../widgets/fab_styles.dart';
import '../../../widgets/reusable_tab_bar.dart';
import '../dialogs/weight_form_dialog.dart';
import '../tabs/weight_records_tab.dart';
import '../tabs/weight_analytics_tab.dart';
import '../tabs/weight_insights_tab.dart';
import '../details/cattle_weight_detail_screen.dart';


class WeightScreen extends StatefulWidget {
  const WeightScreen({Key? key}) : super(key: key);

  @override
  State<WeightScreen> createState() => _WeightScreenState();
}

class _WeightScreenState extends State<WeightScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final WeightService _weightService = WeightService();
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  late final CattleHandler _cattleHandler;

  // Data
  List<WeightRecordIsar> _allWeightRecords = [];
  List<WeightRecordIsar> _filteredWeightRecords = [];
  List<CattleIsar> _allCattle = [];
  Map<String, CattleIsar> _cattleMap = {};

  // Loading states
  bool _isLoading = true;

  // Filter states with 4 core filters + module-specific filters
  // Core common filters
  String? _selectedAnimalType;
  String? _selectedBreed;
  String? _selectedGender;
  String? _selectedCattleId;

  // Module-specific filters
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedMethod;
  String? _selectedQuality;
  String? _sortBy;
  bool _sortAscending = true;  // Default to ascending (more intuitive)
  String _searchQuery = '';

  // Data maps for generating dependent filter options
  Map<String, AnimalTypeIsar> _animalTypeMap = {};
  Map<String, BreedCategoryIsar> _breedMap = {};

  // Selection mode
  bool _isSelectionMode = false;
  final Set<String> _selectedRecords = {};

  // Tab configuration using the reusable widget
  late List<TabItem> _tabs;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // Rebuild to update colors
    });

    // Define tabs using the reusable widget configuration
    _tabs = TabConfigurations.threeTabModule(
      tab1Label: 'Analytics',
      tab1Icon: Icons.analytics,
      tab2Label: 'Records',
      tab2Icon: Icons.list,
      tab3Label: 'Insights',
      tab3Icon: Icons.insights,
    );

    _cattleHandler = _dbHelper.cattleHandler;
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      // Load cattle data
      final cattle = await _cattleHandler.getAllCattle();
      final weightRecords = await _weightService.getAllWeightRecords();

      // Create cattle map for quick lookup
      final cattleMap = <String, CattleIsar>{};
      for (final animal in cattle) {
        if (animal.businessId != null) {
          cattleMap[animal.businessId!] = animal;
        }
      }

      // Load animal types for filtering
      final allAnimalTypes = await _dbHelper.farmSetupHandler.getAllAnimalTypes();
      final animalTypeMap = <String, AnimalTypeIsar>{};
      for (var animalType in allAnimalTypes) {
        if (animalType.businessId != null) {
          animalTypeMap[animalType.businessId!] = animalType;
        }
      }

      // Load breed categories for filtering
      final allBreeds = await _dbHelper.farmSetupHandler.getAllBreedCategories();
      final breedMap = <String, BreedCategoryIsar>{};
      for (var breed in allBreeds) {
        if (breed.businessId != null) {
          breedMap[breed.businessId!] = breed;
        }
      }

      setState(() {
        _allCattle = cattle;
        _cattleMap = cattleMap;
        _animalTypeMap = animalTypeMap;
        _breedMap = breedMap;
        _allWeightRecords = weightRecords;
        _filteredWeightRecords = weightRecords;
        _isLoading = false;
      });

      _applyFilters();
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        MessageUtils.showError(context, 'Error loading weight data: $e');
      }
    }
  }

  Future<void> _refreshData() async {
    await _loadData();
  }

  void _applyFilters() {
    List<WeightRecordIsar> filtered = List.from(_allWeightRecords);

    // Core common filter 1: Animal type filtering
    if (_selectedAnimalType != null && _selectedAnimalType != 'All') {
      filtered = filtered.where((record) {
        final cattle = _cattleMap[record.cattleBusinessId];
        return cattle?.animalTypeId == _selectedAnimalType;
      }).toList();
    }

    // Core common filter 2: Breed filtering
    if (_selectedBreed != null && _selectedBreed != 'All') {
      filtered = filtered.where((record) {
        final cattle = _cattleMap[record.cattleBusinessId];
        return cattle?.breedId == _selectedBreed;
      }).toList();
    }

    // Core common filter 3: Gender filtering
    if (_selectedGender != null && _selectedGender != 'All') {
      filtered = filtered.where((record) {
        final cattle = _cattleMap[record.cattleBusinessId];
        return cattle?.gender?.toLowerCase() == _selectedGender?.toLowerCase();
      }).toList();
    }

    // Core common filter 4: Cattle filtering (dependent on above filters)
    if (_selectedCattleId != null && _selectedCattleId != 'All') {
      filtered = filtered.where((record) =>
          record.cattleBusinessId == _selectedCattleId).toList();
    }

    // Filter by date range
    if (_startDate != null) {
      filtered = filtered.where((record) =>
          record.measurementDate?.isAfter(_startDate!) ?? false).toList();
    }
    if (_endDate != null) {
      filtered = filtered.where((record) =>
          record.measurementDate?.isBefore(_endDate!) ?? false).toList();
    }

    // Module-specific filter: Measurement method filtering
    if (_selectedMethod != null && _selectedMethod != 'All') {
      filtered = filtered.where((record) =>
          record.measurementMethod == _selectedMethod).toList();
    }

    // Module-specific filter: Measurement quality filtering
    if (_selectedQuality != null && _selectedQuality != 'All') {
      filtered = filtered.where((record) =>
          record.measurementQuality == _selectedQuality).toList();
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((record) {
        final cattle = _cattleMap[record.cattleBusinessId];
        final cattleName = cattle?.name?.toLowerCase() ?? '';
        final notes = record.notes?.toLowerCase() ?? '';
        final query = _searchQuery.toLowerCase();

        return cattleName.contains(query) ||
               notes.contains(query) ||
               record.measuredBy?.toLowerCase().contains(query) == true ||
               record.weight.toString().contains(query);
      }).toList();
    }

    // Apply Sorting
    if (_sortBy != null) {
      switch (_sortBy) {
        case 'Date':
          filtered.sort((a, b) {
            // Primary sort: measurement date
            final dateComparison = (a.measurementDate ?? DateTime(0))
                .compareTo(b.measurementDate ?? DateTime(0));

            // Secondary sort: if dates are the same, sort by creation time
            if (dateComparison == 0) {
              return (a.createdAt ?? DateTime(0))
                  .compareTo(b.createdAt ?? DateTime(0));
            }
            return dateComparison;
          });
          break;
        case 'Weight':
          filtered.sort((a, b) => a.weight.compareTo(b.weight));
          break;
        case 'Cattle':
          filtered.sort((a, b) {
            final cattleA = _cattleMap[a.cattleBusinessId]?.name ?? '';
            final cattleB = _cattleMap[b.cattleBusinessId]?.name ?? '';
            return cattleA.compareTo(cattleB);
          });
          break;
      }

      // Reverse if not ascending
      if (!_sortAscending) {
        filtered = filtered.reversed.toList();
      }
    } else {
      // Default sorting by date in descending order if no specific sort is selected
      filtered.sort((a, b) {
        // Primary sort: measurement date (newest first)
        final dateComparison = (b.measurementDate ?? DateTime(0))
            .compareTo(a.measurementDate ?? DateTime(0));

        // Secondary sort: if dates are the same, sort by creation time (newest first)
        if (dateComparison == 0) {
          return (b.createdAt ?? DateTime(0))
              .compareTo(a.createdAt ?? DateTime(0));
        }
        return dateComparison;
      });
    }

    setState(() {
      _filteredWeightRecords = filtered;
    });
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _isSelectionMode
          ? AppBarConfig.standard(
              title: '${_selectedRecords.length} selected',
              actions: [
                IconButton(
                  icon: const Icon(Icons.select_all),
                  onPressed: _selectAll,
                  tooltip: 'Select All',
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: _deleteSelected,
                  tooltip: 'Delete Selected',
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: _exitSelectionMode,
                  tooltip: 'Cancel',
                ),
              ],
            )
          : AppBarConfig.standard(
              title: 'Weight Management',
              actions: _buildAppBarActions(),
            ),
      body: Column(
        children: [
          // Use the reusable tab bar widget
          ReusableTabBar(
            tabController: _tabController,
            tabs: _tabs,
            context: context,
          ),
          // TabBarView
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      WeightAnalyticsTab(
                        weightRecords: _allWeightRecords,
                        cattleMap: _cattleMap,
                        onClearFilters: _clearFilters,
                      ),
                      WeightRecordsTab(
                        allWeightRecords: _allWeightRecords,
                        filteredWeightRecords: _filteredWeightRecords,
                        cattleMap: _cattleMap,
                        animalTypeMap: _animalTypeMap,
                        breedMap: _breedMap,
                        allCattle: _allCattle,
                        isSelectionMode: _isSelectionMode,
                        selectedRecords: _selectedRecords,
                        onRecordTap: _onRecordTap,
                        onRecordLongPress: _onRecordLongPress,
                        onSelectionChanged: _toggleRecordSelection,
                        onRefresh: _refreshData,
                        onEditRecord: _editWeightRecord,
                        onDeleteRecord: _deleteWeightRecord,
                        startDate: _startDate,
                        endDate: _endDate,
                        selectedAnimalType: _selectedAnimalType,
                        selectedBreed: _selectedBreed,
                        selectedGender: _selectedGender,
                        selectedCattleId: _selectedCattleId,
                        selectedMethod: _selectedMethod,
                        selectedQuality: _selectedQuality,
                        sortBy: _sortBy,
                        sortAscending: _sortAscending,
                        searchQuery: _searchQuery,
                        onStartDateChanged: (date) {
                          setState(() => _startDate = date);
                          _applyFilters();
                        },
                        onEndDateChanged: (date) {
                          setState(() => _endDate = date);
                          _applyFilters();
                        },
                        onAnimalTypeChanged: (animalType) {
                          setState(() {
                            _selectedAnimalType = animalType;
                            // Clear dependent filters when animal type changes
                            _selectedBreed = null;
                            _selectedCattleId = null;
                          });
                          _applyFilters();
                        },
                        onBreedChanged: (breed) {
                          setState(() {
                            _selectedBreed = breed;
                            // Clear dependent cattle filter when breed changes
                            _selectedCattleId = null;
                          });
                          _applyFilters();
                        },
                        onGenderChanged: (gender) {
                          setState(() {
                            _selectedGender = gender;
                            // Clear dependent cattle filter when gender changes
                            _selectedCattleId = null;
                          });
                          _applyFilters();
                        },
                        onCattleChanged: (cattleId) {
                          setState(() => _selectedCattleId = cattleId);
                          _applyFilters();
                        },
                        onMethodChanged: (method) {
                          setState(() => _selectedMethod = method);
                          _applyFilters();
                        },
                        onQualityChanged: (quality) {
                          setState(() => _selectedQuality = quality);
                          _applyFilters();
                        },
                        onSortByChanged: (sortBy) {
                          setState(() => _sortBy = sortBy);
                          _applyFilters();
                        },
                        onSortAscendingChanged: (ascending) {
                          setState(() => _sortAscending = ascending);
                          _applyFilters();
                        },
                        onSearchChanged: (query) {
                          setState(() => _searchQuery = query);
                          _applyFilters();
                        },
                        onClearFilters: _clearFilters,
                        onApplyFilters: _applyFilters,
                      ),
                      WeightInsightsTab(
                        weightRecords: _allWeightRecords,
                        cattleMap: _cattleMap,
                      ),
                    ],
                  ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 1: // Records tab (now at index 1)
        return FabStyles.add(
          onPressed: _addWeightRecord,
          tooltip: 'Add Weight Record',
          // Use green color for FAB as per user preference
        );
      default:
        return null;
    }
  }

  List<Widget> _buildAppBarActions() {
    return [
      IconButton(
        icon: const Icon(Icons.refresh),
        onPressed: _refreshData,
        tooltip: 'Refresh',
      ),
    ];
  }

  // Removed old tab building methods - now using separate tab components

  // Action methods
  void _addWeightRecord() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WeightFormDialog(
          cattle: _allCattle,
          onRecordAdded: _refreshData,
        ),
      ),
    );
  }



  void _onRecordTap(WeightRecordIsar record) {
    if (_isSelectionMode) {
      _toggleRecordSelection(record, !_selectedRecords.contains(record.businessId));
    } else {
      _navigateToCattleWeightDetail(record);
    }
  }

  void _navigateToCattleWeightDetail(WeightRecordIsar record) {
    final cattle = _cattleMap[record.cattleBusinessId];
    if (cattle != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CattleWeightDetailScreen(
            cattle: cattle,
            onRefresh: _refreshData,
          ),
        ),
      );
    }
  }

  void _editWeightRecord(WeightRecordIsar record) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WeightFormDialog(
          cattle: _allCattle,
          existingRecord: record,
          onRecordAdded: _refreshData,
        ),
      ),
    );
  }

  void _deleteWeightRecord(WeightRecordIsar record) async {
    final cattle = _cattleMap[record.cattleBusinessId];
    final cattleName = cattle?.name ?? 'Unknown Cattle';

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Weight Record'),
        content: Text(
          'Are you sure you want to delete the weight record for $cattleName (${record.weight.toStringAsFixed(1)} kg)?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _weightService.deleteWeightRecord(record.businessId!);
        await _refreshData();

        if (mounted) {
          MessageUtils.showSuccess(context, 'Weight record deleted successfully');
        }
      } catch (e) {
        if (mounted) {
          MessageUtils.showError(context, 'Error deleting record: $e');
        }
      }
    }
  }

  void _onRecordLongPress(WeightRecordIsar record) {
    if (!_isSelectionMode) {
      setState(() {
        _isSelectionMode = true;
        _selectedRecords.add(record.businessId!);
      });
    }
  }

  void _clearFilters() {
    setState(() {
      _startDate = null;
      _endDate = null;

      // Clear core common filters
      _selectedAnimalType = null;
      _selectedBreed = null;
      _selectedGender = null;
      _selectedCattleId = null;

      // Clear module-specific filters
      _selectedMethod = null;
      _selectedQuality = null;

      _sortBy = null;
      _sortAscending = true;  // Reset to ascending (more intuitive)
      _searchQuery = '';
    });
    _applyFilters();
  }

  void _toggleRecordSelection(WeightRecordIsar record, bool selected) {
    setState(() {
      if (selected) {
        _selectedRecords.add(record.businessId!);
      } else {
        _selectedRecords.remove(record.businessId!);
        if (_selectedRecords.isEmpty) {
          _isSelectionMode = false;
        }
      }
    });
  }

  void _selectAll() {
    setState(() {
      _selectedRecords.addAll(
        _filteredWeightRecords.map((r) => r.businessId!).toSet(),
      );
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedRecords.clear();
    });
  }

  void _deleteSelected() async {
    if (_selectedRecords.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Weight Records'),
        content: Text('Are you sure you want to delete ${_selectedRecords.length} selected record(s)?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        for (final recordId in _selectedRecords) {
          await _weightService.deleteWeightRecord(recordId);
        }

        setState(() {
          _isSelectionMode = false;
          _selectedRecords.clear();
        });

        await _refreshData();

        if (mounted) {
          MessageUtils.showSuccess(context, 'Records deleted successfully');
        }
      } catch (e) {
        if (mounted) {
          MessageUtils.showError(context, 'Error deleting records: $e');
        }
      }
    }
  }




}
