import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/cattle_milk_record.dart';
import '../../Milk Records/models/milk_record.dart';
import '../../../services/milk_record_service.dart';
import 'package:uuid/uuid.dart';

class CattleMilkService {
  final MilkRecordService _milkRecordService = MilkRecordService();
  static const String _storageKey = 'cattle_milk_records';

  Future<List<CattleMilkRecord>> getCattleMilkRecords(String cattleId) async {
    final prefs = await SharedPreferences.getInstance();
    final recordsJson = prefs.getStringList('${_storageKey}_$cattleId') ?? [];
    
    return recordsJson
        .map((json) => CattleMilkRecord.fromMap(jsonDecode(json)))
        .where((record) => record.cattleId == cattleId)
        .toList();
  }

  Future<void> addMilkRecord(CattleMilkRecord record) async {
    final prefs = await SharedPreferences.getInstance();
    final records = await getCattleMilkRecords(record.cattleId);
    
    records.add(record);
    
    final recordsJson = records.map((r) => jsonEncode(r.toMap())).toList();
    await prefs.setStringList('${_storageKey}_${record.cattleId}', recordsJson);

    // Add morning record
    if (record.morningYield > 0) {
      await _milkRecordService.addMilkRecord(MilkRecord(
        id: const Uuid().v4(),
        cattleId: record.cattleId,
        quantity: record.morningYield,
        date: record.date,
        shift: 'Morning',
        notes: record.notes,
        morningQuantity: record.morningYield,
        eveningQuantity: 0.0,
        fatContent: 0.0,
        totalRevenue: 0.0,
      ));
    }

    // Add evening record
    if (record.eveningYield > 0) {
      await _milkRecordService.addMilkRecord(MilkRecord(
        id: const Uuid().v4(),
        cattleId: record.cattleId,
   