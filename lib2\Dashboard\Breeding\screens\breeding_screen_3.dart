import 'package:flutter/material.dart';
import '../../../routes/app_routes.dart';

class BreedingScreen extends StatelessWidget {
  const BreedingScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Breeding Management'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.breedingReport,
            ),
            tooltip: 'View Breeding Reports',
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: Show breeding record form
        },
        backgroundColor: const Color(0xFF2E7D32),
        child: const Icon(Icons.add),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          Card(
            child: ListTile(
              leading: const Icon(Icons.favorite, color: Colors.pink),
              title: const Text('Breeding Records'),
              subtitle: const Text('View and manage breeding records'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: Navigate to breeding records list
              },
            ),
          ),
          Card(
            child: ListTile(
              leading: const Icon(Icons.pregnant_woman, color: Colors.purple),
              title: const Text('Pregnancy Records'),
              subtitle: const Text('Track pregnancy status'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: Navigate to pregnancy records
              },
            ),
          ),
          Card(
            child: ListTile(
              leading: const Icon(Icons.calendar_today, color: Colors.blue),
              title: const Text('Heat Calendar'),
              subtitle: const Text('Track heat cycles'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: Navigate to heat calendar
              },
            ),
          ),
          Card(
            child: ListTile(
              leading: const Icon(Icons.pets, color: Colors.orange),
              title: const Text('Sire Directory'),
              subtitle: const Text('Manage breeding bulls'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: Navigate to sire directory
              },
            ),
          ),
        ],
      ),
    );
  }
}
