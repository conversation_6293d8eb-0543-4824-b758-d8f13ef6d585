import 'package:flutter/material.dart';

/// Responsive helper class for handling different screen sizes and breakpoints
class ResponsiveHelper {
  // Breakpoint constants
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  // Screen size categories
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }

  static bool isSmallMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < 360;
  }

  static bool isLargeMobile(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= 360 && width < mobileBreakpoint;
  }

  // Screen dimensions
  static double screenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  static double screenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  static double safeAreaHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.height - 
           mediaQuery.padding.top - 
           mediaQuery.padding.bottom;
  }

  static double availableHeight(BuildContext context, {bool includeAppBar = true}) {
    final mediaQuery = MediaQuery.of(context);
    double height = mediaQuery.size.height - 
                   mediaQuery.padding.top - 
                   mediaQuery.padding.bottom;
    
    if (includeAppBar) {
      height -= kToolbarHeight;
    }
    
    return height;
  }

  // Grid calculations
  static int getGridCrossAxisCount(BuildContext context, {
    int mobileCount = 2,
    int tabletCount = 3,
    int desktopCount = 4,
  }) {
    if (isMobile(context)) return mobileCount;
    if (isTablet(context)) return tabletCount;
    return desktopCount;
  }

  static double getGridChildAspectRatio(BuildContext context, {
    double mobileRatio = 1.0,
    double tabletRatio = 1.2,
    double desktopRatio = 1.3,
  }) {
    if (isMobile(context)) return mobileRatio;
    if (isTablet(context)) return tabletRatio;
    return desktopRatio;
  }

  // Responsive values
  static T getResponsiveValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    if (isDesktop(context) && desktop != null) return desktop;
    if (isTablet(context) && tablet != null) return tablet;
    return mobile;
  }

  // Dialog sizing
  static double getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (isMobile(context)) {
      return screenWidth * 0.95; // 95% of screen width on mobile
    } else if (isTablet(context)) {
      return screenWidth * 0.7; // 70% of screen width on tablet
    } else {
      return 600; // Fixed width on desktop
    }
  }

  static double getDialogMaxHeight(BuildContext context) {
    return MediaQuery.of(context).size.height * 0.8;
  }

  // Font scaling
  static double getScaledFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return baseFontSize * 0.9; // Smaller fonts for very small screens
    } else if (screenWidth > 600) {
      return baseFontSize * 1.1; // Slightly larger fonts for larger screens
    }
    return baseFontSize;
  }

  // Padding and margins
  static EdgeInsets getResponsivePadding(BuildContext context, {
    double mobile = 16.0,
    double? tablet,
    double? desktop,
  }) {
    final padding = getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet ?? mobile * 1.5,
      desktop: desktop ?? mobile * 2,
    );
    return EdgeInsets.all(padding);
  }

  static EdgeInsets getResponsiveHorizontalPadding(BuildContext context, {
    double mobile = 16.0,
    double? tablet,
    double? desktop,
  }) {
