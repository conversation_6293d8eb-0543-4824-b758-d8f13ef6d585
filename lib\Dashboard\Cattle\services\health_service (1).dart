import '../models/health_record.dart';
import '../models/medication.dart';
import '../models/vaccination.dart';
import '../../../services/database_helper.dart';

class HealthService {
  final DatabaseHelper _dbHelper;

  HealthService() : _dbHelper = DatabaseHelper.instance;

  Future<List<HealthRecord>> getHealthRecords(String cattleId) async {
    final records = await _dbHelper.getHealthRecords(cattleId);
    return records.map((record) => HealthRecord.fromMap(record)).toList();
  }

  Future<void> addHealthRecord(HealthRecord record) async {
    await _dbHelper.addHealthRecord(record.toMap());
  }

  Future<List<Medication>> getMedications(String cattleId) async {
    final medications = await _dbHelper.getMedications(cattleId);
    return medications.map((med) => Medication.fromMap(med)).toList();
  }

  Future<void> addMedication(Medication medication) async {
    await _dbHelper.addMedication(medication.toMap());
  }

  Future<List<Vaccination>> getVaccinations(String cattleId) async {
    final vaccinations = await _dbHelper.getVaccinations(cattleId);
    return vaccinations.map((vac) => Vaccination.fromMap(vac)).toList();
  }

  Future<void> addVaccination(Vaccination vaccination) async {
    await _dbHelper.addVaccination(vaccination.toMap());
  }
}
