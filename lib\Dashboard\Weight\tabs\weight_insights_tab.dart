import 'package:flutter/material.dart';
import '../models/weight_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../widgets/index.dart';

class WeightInsightsTab extends StatefulWidget {
  final List<WeightRecordIsar> weightRecords;
  final Map<String, CattleIsar> cattleMap;

  const WeightInsightsTab({
    Key? key,
    required this.weightRecords,
    required this.cattleMap,
  }) : super(key: key);

  @override
  State<WeightInsightsTab> createState() => _WeightInsightsTabState();
}

class _WeightInsightsTabState extends State<WeightInsightsTab> {
  static const _insightsColor = Color(0xFF8E44AD); // Purple

  // Expanded color palette to prevent repetition within sections
  static const _performanceColors = [
    Color(0xFF1976D2), // Blue
    Color(0xFF27AE60), // Green
    Color(0xFF8E44AD), // Purple
  ];

  static const _healthColors = [
    Color(0xFF16A085), // Teal
    Color(0xFF00796B), // Dark Teal
    Color(0xFFD32F2F), // Red
  ];

  static const _trendColors = [
    Color(0xFF7B1FA2), // Deep Purple
    Color(0xFF303F9F), // Indigo
    Color(0xFF388E3C), // Dark Green
  ];

  static const _recommendationColors = [
    Color(0xFF1976D2), // Blue
    Color(0xFF27AE60), // Green
    Color(0xFF8E44AD), // Purple
    Color(0xFF16A085), // Teal
  ];

  @override
  Widget build(BuildContext context) {
    if (widget.weightRecords.isEmpty) {
      return _buildEmptyState();
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(_getResponsivePadding()),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInsightsHeader(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildPerformanceInsights(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildHealthInsights(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildTrendInsights(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildRecommendations(),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 300),
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 32),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: _insightsColor.withAlpha(76)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: EmptyState.custom(
        icon: Icons.insights,
        message: 'No Insights Available',
        subtitle: 'Add more weight records to generate insights',
        color: _insightsColor,
        hasData: false,
      ),
    );
  }

  Widget _buildInsightsHeader() {

    
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [_insightsColor, _insightsColor.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.insights, color: Colors.white, size: 28),
              SizedBox(width: 12),
              Text(
                'Weight Insights',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Analysis based on ${widget.weightRecords.length} weight records across ${widget.cattleMap.length} cattle',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Last updated: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceInsights() {
    final performanceData = _analyzePerformance();

    return _buildInsightSection(
      'Performance Insights',
      Icons.trending_up,
      _performanceColors[0],
      [
        _buildInsightCard(
          'Top Performer',
          performanceData['topPerformer'] ?? 'No data',
          'Highest weight gain rate',
          _performanceColors[0],
        ),
        _buildInsightCard(
          'Average Daily Gain',
          '${performanceData['avgDailyGain']?.toStringAsFixed(2) ?? '0'} kg/day',
          'Across all cattle',
          _performanceColors[1],
        ),
        _buildInsightCard(
          'Growth Trend',
          performanceData['growthTrend'] ?? 'Stable',
          'Overall herd performance',
          _performanceColors[2],
        ),
      ],
    );
  }

  Widget _buildHealthInsights() {
    final healthData = _analyzeHealth();

    return _buildInsightSection(
      'Health Insights',
      Icons.health_and_safety,
      _healthColors[0],
      [
        _buildInsightCard(
          'Body Condition',
          healthData['avgBodyCondition']?.toStringAsFixed(1) ?? 'N/A',
          'Average BCS score',
          _healthColors[0],
        ),
        _buildInsightCard(
          'Weight Consistency',
          healthData['consistency'] ?? 'Good',
          'Measurement reliability',
          _healthColors[1],
        ),
        _buildInsightCard(
          'Health Alerts',
          '${healthData['alerts'] ?? 0}',
          'Cattle needing attention',
          _healthColors[2],
        ),
      ],
    );
  }

  Widget _buildTrendInsights() {
    final trendData = _analyzeTrends();

    return _buildInsightSection(
      'Trend Analysis',
      Icons.show_chart,
      _trendColors[0],
      [
        _buildInsightCard(
          'Seasonal Pattern',
          trendData['seasonalPattern'] ?? 'No pattern',
          'Weight gain by season',
          _trendColors[0],
        ),
        _buildInsightCard(
          'Measurement Frequency',
          trendData['frequency'] ?? 'Regular',
          'Recording consistency',
          _trendColors[1],
        ),
        _buildInsightCard(
          'Data Quality',
          trendData['dataQuality'] ?? 'Good',
          'Measurement accuracy',
          _trendColors[2],
        ),
      ],
    );
  }

  Widget _buildRecommendations() {
    final recommendations = _generateRecommendations();
    
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: _recommendationColors[0],
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb, color: _recommendationColors[0], size: 24),
              const SizedBox(width: 8),
              Text(
                'Recommendations',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _recommendationColors[0],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...recommendations.map((rec) => _buildRecommendationItem(rec)),
        ],
      ),
    );
  }

  Widget _buildInsightSection(String title, IconData icon, Color color, List<Widget> cards) {
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildResponsiveCardLayout(cards),
        ],
      ),
    );
  }

  Widget _buildResponsiveCardLayout(List<Widget> cards) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 600) {
      // Mobile: Stack vertically
      return Column(
        children: cards.map((card) => 
          Container(
            width: double.infinity,
            margin: const EdgeInsets.only(bottom: 8),
            child: card,
          )
        ).toList(),
      );
    } else if (screenWidth < 900) {
      // Tablet: 2 columns
      final rows = <Widget>[];
      for (int i = 0; i < cards.length; i += 2) {
        rows.add(
          Row(
            children: [
              Expanded(child: cards[i]),
              if (i + 1 < cards.length) ...[
                SizedBox(width: _getResponsivePadding() * 0.5),
                Expanded(child: cards[i + 1]),
              ] else
                const Expanded(child: SizedBox()),
            ],
          ),
        );
        if (i + 2 < cards.length) {
          rows.add(SizedBox(height: _getResponsivePadding() * 0.5));
        }
      }
      return Column(children: rows);
    } else {
      // Desktop: 3 columns
      final rows = <Widget>[];
      for (int i = 0; i < cards.length; i += 3) {
        rows.add(
          Row(
            children: [
              Expanded(child: cards[i]),
              if (i + 1 < cards.length) ...[
                SizedBox(width: _getResponsivePadding() * 0.5),
                Expanded(child: cards[i + 1]),
              ] else
                const Expanded(child: SizedBox()),
              if (i + 2 < cards.length) ...[
                SizedBox(width: _getResponsivePadding() * 0.5),
                Expanded(child: cards[i + 2]),
              ] else
                const Expanded(child: SizedBox()),
            ],
          ),
        );
        if (i + 3 < cards.length) {
          rows.add(SizedBox(height: _getResponsivePadding() * 0.5));
        }
      }
      return Column(children: rows);
    }
  }

  Widget _buildInsightCard(String title, String value, String subtitle, Color color) {
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding() * 0.75),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationItem(Map<String, dynamic> recommendation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(_getResponsivePadding() * 0.75),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: recommendation['color']),
      ),
      child: Row(
        children: [
          Icon(
            recommendation['icon'],
            color: recommendation['color'],
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  recommendation['title'],
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: recommendation['color'],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  recommendation['description'],
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }



  Map<String, dynamic> _analyzePerformance() {
    if (widget.weightRecords.isEmpty) return {};

    // Calculate performance metrics
    final cattlePerformance = <String, double>{};
    
    for (final cattle in widget.cattleMap.values) {
      final cattleRecords = widget.weightRecords
          .where((r) => r.cattleBusinessId == cattle.businessId)
          .toList();
      
      if (cattleRecords.length >= 2) {
        cattleRecords.sort((a, b) => (a.measurementDate ?? DateTime.now())
            .compareTo(b.measurementDate ?? DateTime.now()));
        
        final firstWeight = cattleRecords.first.weight;
        final lastWeight = cattleRecords.last.weight;
        final days = (cattleRecords.last.measurementDate ?? DateTime.now())
            .difference(cattleRecords.first.measurementDate ?? DateTime.now())
            .inDays;
        
        if (days > 0) {
          final dailyGain = (lastWeight - firstWeight) / days;
          final cattleName = cattle.name ?? 'Unknown';
          final tagId = cattle.tagId ?? '';
          final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;
          cattlePerformance[displayName] = dailyGain;
        }
      }
    }

    String topPerformer = 'No data';
    if (cattlePerformance.isNotEmpty) {
      topPerformer = cattlePerformance.entries
          .reduce((a, b) => a.value > b.value ? a : b)
          .key;
    }
    
    final avgDailyGain = cattlePerformance.values.isEmpty 
        ? 0.0 
        : cattlePerformance.values.reduce((a, b) => a + b) / cattlePerformance.length;

    final growthTrend = avgDailyGain > 0.5 ? 'Excellent' 
                      : avgDailyGain > 0.2 ? 'Good'
                      : avgDailyGain > 0 ? 'Moderate'
                      : 'Declining';

    return {
      'topPerformer': topPerformer,
      'avgDailyGain': avgDailyGain,
      'growthTrend': growthTrend,
    };
  }

  Map<String, dynamic> _analyzeHealth() {
    if (widget.weightRecords.isEmpty) return {};

    final bodyConditionScores = widget.weightRecords
        .where((r) => r.bodyConditionScore != null)
        .map((r) => r.bodyConditionScore!)
        .toList();

    final avgBodyCondition = bodyConditionScores.isEmpty 
        ? null 
        : bodyConditionScores.reduce((a, b) => a + b) / bodyConditionScores.length;

    // Analyze measurement consistency
    final measurementMethods = widget.weightRecords
        .map((r) => r.measurementMethod)
        .where((m) => m != null)
        .toSet();
    
    final consistency = measurementMethods.length <= 2 ? 'Excellent' 
                       : measurementMethods.length <= 3 ? 'Good' 
                       : 'Variable';

    // Count health alerts (simplified logic)
    final alerts = widget.weightRecords
        .where((r) => r.healthStatus == 'sick' || r.bodyConditionScore != null && r.bodyConditionScore! < 3)
        .length;

    return {
      'avgBodyCondition': avgBodyCondition,
      'consistency': consistency,
      'alerts': alerts,
    };
  }

  Map<String, dynamic> _analyzeTrends() {
    if (widget.weightRecords.isEmpty) return {};

    // Analyze seasonal patterns
    final seasonalGains = <String, List<double>>{
      'Spring': [],
      'Summer': [],
      'Fall': [],
      'Winter': [],
    };

    for (final record in widget.weightRecords) {
      if (record.measurementDate != null && record.weightGain != null) {
        final month = record.measurementDate!.month;
        final season = month >= 3 && month <= 5 ? 'Spring'
                     : month >= 6 && month <= 8 ? 'Summer'
                     : month >= 9 && month <= 11 ? 'Fall'
                     : 'Winter';
        seasonalGains[season]!.add(record.weightGain!);
      }
    }

    // Find best season with safe handling of empty collections
    String bestSeason = 'No pattern';
    final validSeasons = seasonalGains.entries
        .where((e) => e.value.isNotEmpty)
        .map((e) => MapEntry(e.key, e.value.reduce((a, b) => a + b) / e.value.length))
        .toList();

    if (validSeasons.isNotEmpty) {
      bestSeason = validSeasons.reduce((a, b) => a.value > b.value ? a : b).key;
    }

    // Analyze measurement frequency
    final dates = widget.weightRecords
        .map((r) => r.measurementDate)
        .where((d) => d != null)
        .map((d) => d!)
        .toList();

    dates.sort();

    final intervals = <int>[];
    for (int i = 1; i < dates.length; i++) {
      intervals.add(dates[i].difference(dates[i-1]).inDays);
    }

    final avgInterval = intervals.isEmpty ? 0.0 : intervals.reduce((a, b) => a + b) / intervals.length;
    final frequency = avgInterval <= 7 ? 'Weekly'
                    : avgInterval <= 14 ? 'Bi-weekly'
                    : avgInterval <= 30 ? 'Monthly'
                    : 'Irregular';

    // Analyze data quality
    final qualityScores = widget.weightRecords
        .where((r) => r.measurementQuality != null)
        .map((r) => r.measurementQuality!)
        .toList();
    
    final excellentCount = qualityScores.where((q) => q == 'excellent').length;
    final dataQuality = qualityScores.isEmpty ? 'Unknown'
                       : excellentCount / qualityScores.length > 0.8 ? 'Excellent'
                       : excellentCount / qualityScores.length > 0.6 ? 'Good'
                       : 'Needs Improvement';

    return {
      'seasonalPattern': bestSeason,
      'frequency': frequency,
      'dataQuality': dataQuality,
    };
  }

  List<Map<String, dynamic>> _generateRecommendations() {
    final recommendations = <Map<String, dynamic>>[];
    int colorIndex = 0;

    if (widget.weightRecords.length < 10) {
      recommendations.add({
        'title': 'Increase Measurement Frequency',
        'description': 'Record weights more regularly for better trend analysis',
        'icon': Icons.schedule,
        'color': _recommendationColors[colorIndex % _recommendationColors.length],
      });
      colorIndex++;
    }

    final recentRecords = widget.weightRecords
        .where((r) => r.measurementDate != null &&
                     r.measurementDate!.isAfter(DateTime.now().subtract(const Duration(days: 30))))
        .length;

    if (recentRecords < 5) {
      recommendations.add({
        'title': 'Update Recent Records',
        'description': 'Add more recent weight measurements for current insights',
        'icon': Icons.update,
        'color': _recommendationColors[colorIndex % _recommendationColors.length],
      });
      colorIndex++;
    }

    final bodyConditionRecords = widget.weightRecords
        .where((r) => r.bodyConditionScore != null)
        .length;

    if (bodyConditionRecords < widget.weightRecords.length * 0.5) {
      recommendations.add({
        'title': 'Include Body Condition Scores',
        'description': 'Add BCS measurements for comprehensive health tracking',
        'icon': Icons.health_and_safety,
        'color': _recommendationColors[colorIndex % _recommendationColors.length],
      });
      colorIndex++;
    }

    if (recommendations.isEmpty) {
      recommendations.add({
        'title': 'Great Job!',
        'description': 'Your weight tracking is comprehensive and up-to-date',
        'icon': Icons.check_circle,
        'color': _recommendationColors[0],
      });
    }

    return recommendations;
  }

  double _getResponsivePadding() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 12.0;
    if (screenWidth < 1200) return 16.0;
    return 20.0;
  }

  double _getResponsiveSpacing() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 16.0;
    if (screenWidth < 1200) return 20.0;
    return 24.0;
  }
}
