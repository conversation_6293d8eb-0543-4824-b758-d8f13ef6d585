import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/weight_report_data.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class WeightSummaryTab extends StatelessWidget {
  final WeightReportData reportData;

  const WeightSummaryTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final summaryData = reportData.summaryData;
    final trends = reportData.weightTrends;

    return SingleChildScrollView(
      child: Padding(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCard(summaryData),
            SizedBox(height: ResponsiveSpacing.getLG(context)),
            _buildWeightTrendsChart(trends),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(Map<String, dynamic> summaryData) {
    return ResponsiveCard(
      child: Padding(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Weight Summary',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            _buildSummaryRow('Average Weight',
                '${summaryData['averageWeight']?.toStringAsFixed(1)} kg'),
            _buildSummaryRow('Max Weight',
                '${summaryData['maxWeight']?.toStringAsFixed(1)} kg'),
            _buildSummaryRow('Min Weight',
                '${summaryData['minWeight']?.toStringAsFixed(1)} kg'),
            _buildSummaryRow('Average Daily Gain',
                '${summaryData['averageDailyGain']?.toStringAsFixed(2)} kg/day'),
            _buildSummaryRow(
                'Total Records', summaryData['totalRecords'].toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: ResponsiveTheme.getSubtitleStyle(context),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.blue,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeightTrendsChart(Map<String, dynamic> trends) {
    return ResponsiveCard(
      child: Padding(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Weight Trends',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            SizedBox(
              height: 300,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children:
                    (trends as Map<String, List<dynamic>>).entries.map((entry) {
                  return Container(
                    width: 300,
                    margin: const EdgeInsets.only(right: 16),
                    child: _buildCategoryChart(entry.key, entry.value),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryChart(String category, List<dynamic> records) {
    final spots = records.map((record) {
      final date = record['date'] as DateTime;
      final weight = record['weight'] as double;
      return FlSpot(
        date.millisecondsSinceEpoch.toDouble(),
        weight,
      );
    }).toList();

    if (spots.isEmpty) {
      return const Center(child: Text('No data available'));
    }

    return LineChart(
      LineChartData(
        gridData: const FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: const AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: Side