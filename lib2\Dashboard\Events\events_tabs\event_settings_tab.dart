import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logging/logging.dart';

import '../../../constants/app_icons.dart';

class EventSettingsTab extends StatefulWidget {
  const EventSettingsTab({Key? key}) : super(key: key);

  @override
  State<EventSettingsTab> createState() => _EventSettingsTabState();
}

class _EventSettingsTabState extends State<EventSettingsTab> {
  static final Logger _logger = Logger('EventSettingsTab');
  bool _isLoading = true;

  // Notification Settings
  bool _enableNotifications = true;
  bool _enablePushNotifications = false;
  bool _enableEmailNotifications = false;
  bool _enableSMSNotifications = false;

  // Auto-Generation Settings
  bool _enableAutoGeneration = true;
  bool _breedingEvents = true;
  bool _healthFollowups = true;
  bool _vaccinationSchedule = true;
  bool _milkReminders = false;
  bool _weightReminders = true;
  bool _heatCyclePredictions = true;

  // Reminder Settings
  final List<int> _defaultReminderMinutes = [1440, 720]; // 1 day, 12 hours
  int _maxNotificationsToKeep = 100;
  int _autoDeleteAfterDays = 30;

  // Display Settings
  String _defaultCalendarView = 'month';
  bool _showCompletedEvents = true;
  bool _showAutoGeneratedEvents = true;
  String _eventSortOrder = 'date_asc';

  // Responsive helper methods
  double _getResponsiveFontSize() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 11.0; // Small screens
    } else if (screenWidth < 600) {
      return 12.0; // Medium screens
    } else {
      return 13.0; // Large screens
    }
  }

  double _getResponsiveIconSize() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 20.0; // Small screens
    } else if (screenWidth < 600) {
      return 22.0; // Medium screens
    } else {
      return 24.0; // Large screens
    }
  }

  double _getResponsivePadding() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 12.0; // Small screens
    } else if (screenWidth < 600) {
      return 16.0; // Medium screens
    } else {
      return 20.0; // Large screens
    }
  }

  double _getResponsiveSpacing() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 16.0; // Small screens
    } else if (screenWidth < 600) {
      return 20.0; // Medium screens
    } else {
      return 24.0; // Large screens
    }
  }
  
  @override
  void initState() {
    super.initState();
    _logger.info('⚙️ EventSettingsTab: Initializing settings...');
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      _logger.info('📖 EventSettingsTab: Loading settings from SharedPreferences...');
      final prefs = await SharedPreferences.getInstance();
      
      setState(() {
        // Notification Settings
        _enableNotifications = prefs.getBool('event_notifications_enabled') ?? true;
        _enablePushNotifications = prefs.getBool('event_push_notifications') ?? false;
        _enableEmailNotifications = prefs.getBool('event_email_notifications') ?? false;
        _enableSMSNotifications = prefs.getBool('event_sms_notifications') ?? false;
        
        // Auto-Generation Settings
        _enableAutoGeneration = prefs.getBool('event_auto_generation') ?? true;
        _breedingEvents = prefs.getBool('auto_breeding_events') ?? true;
        _healthFollowups = prefs.getBool('auto_health_followups') ?? true;
        _vaccinationSchedule = prefs.getBool('auto_vaccination_schedule') ?? true;
        _milkReminders = prefs.getBool('auto_milk_reminders') ?? false;
        _weightReminders = prefs.getBool('auto_weight_reminders') ?? true;
        _heatCyclePredictions = prefs.getBool('auto_heat_cycle_predictions') ?? true;
        
        // Reminder Settings
        _maxNotificationsToKeep = prefs.getInt('max_notifications_to_keep') ?? 100;
        _autoDeleteAfterDays = prefs.getInt('auto_delete_after_days') ?? 30;
        
        // Display Settings
        _defaultCalendarView = prefs.getString('default_calendar_view') ?? 'month';
        _showCompletedEvents = prefs.getBool('show_completed_events') ?? true;
        _showAutoGeneratedEvents = prefs.getBool('show_auto_generated_events') ?? true;
        _eventSortOrder = prefs.getString('event_sort_order') ?? 'date_asc';
        
        _isLoading = false;
      });
      _logger.info('✅ EventSettingsTab: Settings loaded successfully');
    } catch (e) {
      _logger.severe('❌ EventSettingsTab: Error loading settings: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    try {
      _logger.info('💾 EventSettingsTab: Saving settings to SharedPreferences...');
      final prefs = await SharedPreferences.getInstance();
      
      // Notification Settings
      await prefs.setBool('event_notifications_enabled', _enableNotifications);
      await prefs.setBool('event_push_notifications', _enablePushNotifications);
      await prefs.setBool('event_email_notifications', _enableEmailNotifications);
      await prefs.setBool('event_sms_notifications', _enableSMSNotifications);
      
      // Auto-Generation Settings
      await prefs.setBool('event_auto_generation', _enableAutoGeneration);
      await prefs.setBool('auto_breeding_events', _breedingEvents);
      await prefs.setBool('auto_health_followups', _healthFollowups);
      await prefs.setBool('auto_vaccination_schedule', _vaccinationSchedule);
      await prefs.setBool('auto_milk_reminders', _milkReminders);
      await prefs.setBool('auto_weight_reminders', _weightReminders);
      await prefs.setBool('auto_heat_cycle_predictions', _heatCyclePredictions);
      
      // Reminder Settings
      await prefs.setInt('max_notifications_to_keep', _maxNotificationsToKeep);
      await prefs.setInt('auto_delete_after_days', _autoDeleteAfterDays);
      
      // Display Settings
      await prefs.setString('default_calendar_view', _defaultCalendarView);
      await prefs.setBool('show_completed_events', _showCompletedEvents);
      await prefs.setBool('show_auto_generated_events', _showAutoGeneratedEvents);
      await prefs.setString('event_sort_order', _eventSortOrder);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Settings saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to save settings'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(_getResponsivePadding()),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Notification Settings
          _buildNotificationSettings(),
          SizedBox(height: _getResponsiveSpacing()),

          // Auto-Generation Settings
          _buildAutoGenerationSettings(),
          SizedBox(height: _getResponsiveSpacing()),

          // Reminder Settings
          _buildReminderSettings(),
          SizedBox(height: _getResponsiveSpacing()),

          // Display Settings
          _buildDisplaySettings(),
          SizedBox(height: _getResponsiveSpacing()),

          // Data Management
          _buildDataManagement(),
          SizedBox(height: _getResponsiveSpacing() + 8),

          // Save Button
          _buildSaveButton(),
        ],
      ),
    );
  }

  Widget _buildNotificationSettings() {
    return _buildSettingsSection(
      'Notification Settings',
      Icons.notifications,
      const Color(0xFF2E7D32), // Dark Green
      [
        _buildSwitchTileWithIcon(
          'Enable Notifications',
          'Receive notifications for events',
          Icons.notifications_active,
          const Color(0xFF2E7D32), // Green
          _enableNotifications,
          (value) => setState(() => _enableNotifications = value),
        ),
        _buildSwitchTileWithIcon(
          'Push Notifications',
          'Receive push notifications on your device',
          Icons.phone_android,
          const Color(0xFF3498DB), // Blue
          _enablePushNotifications,
          _enableNotifications ? (value) => setState(() => _enablePushNotifications = value) : null,
        ),
        _buildSwitchTileWithIcon(
          'Email Notifications',
          'Receive notifications via email',
          Icons.email,
          const Color(0xFF8E44AD), // Purple
          _enableEmailNotifications,
          _enableNotifications ? (value) => setState(() => _enableEmailNotifications = value) : null,
        ),
        _buildSwitchTileWithIcon(
          'SMS Notifications',
          'Receive notifications via SMS',
          Icons.sms,
          const Color(0xFFE74C3C), // Red
          _enableSMSNotifications,
          _enableNotifications ? (value) => setState(() => _enableSMSNotifications = value) : null,
        ),
      ],
    );
  }

  Widget _buildAutoGenerationSettings() {
    return _buildSettingsSection(
      'Auto-Generation Settings',
      Icons.auto_awesome,
      const Color(0xFF8E44AD), // Purple
      [
        _buildSwitchTileWithIcon(
          'Enable Auto-Generation',
          'Automatically generate events based on module activities',
          Icons.auto_awesome,
          const Color(0xFF8E44AD), // Purple
          _enableAutoGeneration,
          (value) => setState(() => _enableAutoGeneration = value),
        ),
        _buildSwitchTileWithIcon(
          'Breeding Events',
          'Auto-generate pregnancy checks and calving events',
          AppIcons.breeding,
          const Color(0xFFE74C3C), // Red
          _breedingEvents,
          _enableAutoGeneration ? (value) => setState(() => _breedingEvents = value) : null,
        ),
        _buildSwitchTileWithIcon(
          'Health Follow-ups',
          'Auto-generate follow-up health events',
          AppIcons.health,
          const Color(0xFF27AE60), // Green
          _healthFollowups,
          _enableAutoGeneration ? (value) => setState(() => _healthFollowups = value) : null,
        ),
        _buildSwitchTileWithIcon(
          'Vaccination Schedule',
          'Auto-generate vaccination reminders',
          AppIcons.vaccination,
          const Color(0xFF3498DB), // Blue
          _vaccinationSchedule,
          _enableAutoGeneration ? (value) => setState(() => _vaccinationSchedule = value) : null,
        ),
        _buildSwitchTileWithIcon(
          'Milk Recording Reminders',
          'Auto-generate daily milk recording reminders',
          AppIcons.milk,
          const Color(0xFF16A085), // Teal
          _milkReminders,
          _enableAutoGeneration ? (value) => setState(() => _milkReminders = value) : null,
        ),
        _buildSwitchTileWithIcon(
          'Weight Measurement Reminders',
          'Auto-generate monthly weight measurement reminders',
          AppIcons.weight,
          const Color(0xFF9B59B6), // Purple
          _weightReminders,
          _enableAutoGeneration ? (value) => setState(() => _weightReminders = value) : null,
        ),
        _buildSwitchTileWithIcon(
          'Heat Cycle Predictions',
          'Auto-generate heat cycle prediction events',
          Icons.thermostat,
          const Color(0xFF1976D2), // Dark Blue
          _heatCyclePredictions,
          _enableAutoGeneration ? (value) => setState(() => _heatCyclePredictions = value) : null,
        ),
      ],
    );
  }

  Widget _buildReminderSettings() {
    return _buildSettingsSection(
      'Reminder Settings',
      Icons.alarm,
      const Color(0xFF1976D2), // Blue (different from other sections)
      [
        _buildListTile(
          'Default Reminder Times',
          '1 day and 12 hours before event',
          Icons.schedule,
          const Color(0xFF2E7D32), // Green
          _showReminderTimesDialog,
        ),
        _buildListTile(
          'Max Notifications to Keep',
          '$_maxNotificationsToKeep notifications',
          Icons.storage,
          const Color(0xFF8E44AD), // Purple
          () => _showNumberPickerDialog(
            'Max Notifications',
            _maxNotificationsToKeep,
            50,
            500,
            (value) => setState(() => _maxNotificationsToKeep = value),
          ),
        ),
        _buildListTile(
          'Auto-delete After',
          '$_autoDeleteAfterDays days',
          Icons.auto_delete,
          const Color(0xFFE74C3C), // Red
          () => _showNumberPickerDialog(
            'Auto-delete After (days)',
            _autoDeleteAfterDays,
            7,
            365,
            (value) => setState(() => _autoDeleteAfterDays = value),
          ),
        ),
      ],
    );
  }

  Widget _buildDisplaySettings() {
    return _buildSettingsSection(
      'Display Settings',
      Icons.display_settings,
      const Color(0xFF16A085), // Teal (different from other sections)
      [
        _buildListTile(
          'Default Calendar View',
          _defaultCalendarView.toUpperCase(),
          Icons.calendar_view_month,
          const Color(0xFF3498DB), // Blue
          _showCalendarViewDialog,
        ),
        _buildSwitchTileWithIcon(
          'Show Completed Events',
          'Display completed events in lists',
          Icons.check_circle,
          const Color(0xFF27AE60), // Green
          _showCompletedEvents,
          (value) => setState(() => _showCompletedEvents = value),
        ),
        _buildSwitchTileWithIcon(
          'Show Auto-Generated Events',
          'Display auto-generated events in main lists',
          Icons.auto_awesome,
          const Color(0xFF8E44AD), // Purple
          _showAutoGeneratedEvents,
          (value) => setState(() => _showAutoGeneratedEvents = value),
        ),
        _buildListTile(
          'Event Sort Order',
          _getEventSortOrderText(),
          Icons.sort,
          const Color(0xFF9B59B6), // Purple
          _showSortOrderDialog,
        ),
      ],
    );
  }

  Widget _buildDataManagement() {
    return _buildSettingsSection(
      'Data Management',
      Icons.storage,
      const Color(0xFF8E44AD), // Purple (different from other sections)
      [
        _buildActionTile(
          'Export Events',
          'Export all events to CSV file',
          Icons.file_download,
          const Color(0xFF27AE60), // Green
          _exportEvents,
        ),
        _buildActionTile(
          'Import Events',
          'Import events from CSV file',
          Icons.file_upload,
          const Color(0xFF3498DB), // Blue
          _importEvents,
        ),
        _buildActionTile(
          'Clear Completed Events',
          'Remove all completed events',
          Icons.clear_all,
          const Color(0xFF1976D2), // Dark Blue
          _clearCompletedEvents,
        ),
        _buildActionTile(
          'Reset All Settings',
          'Reset all event settings to default',
          Icons.restore,
          const Color(0xFFE74C3C), // Red
          _resetAllSettings,
        ),
      ],
    );
  }

  Widget _buildSettingsSection(String title, IconData icon, Color color, List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFE0E0E0), // Solid light grey instead of alpha
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(_getResponsivePadding()),
            decoration: BoxDecoration(
              color: color,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: Colors.white, size: _getResponsiveIconSize()), // Changed to white
                SizedBox(width: _getResponsivePadding() * 0.5),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize() + 6,
                    fontWeight: FontWeight.bold,
                    color: Colors.white, // Added white text color
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }



  Widget _buildSwitchTileWithIcon(String title, String subtitle, IconData icon, Color iconColor, bool value, ValueChanged<bool>? onChanged) {
    return SwitchListTile(
      title: Text(
        title,
        style: TextStyle(fontSize: _getResponsiveFontSize() + 2),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(fontSize: _getResponsiveFontSize()),
      ),
      secondary: Icon(icon, color: iconColor, size: _getResponsiveIconSize()),
      value: value,
      onChanged: onChanged,
      activeColor: Colors.white, // White thumb on colored track
      activeTrackColor: iconColor, // Use icon color for track
      inactiveThumbColor: const Color(0xFF9E9E9E), // Solid dark grey instead of light alpha
      inactiveTrackColor: const Color(0xFFE0E0E0), // Solid light grey instead of alpha
      contentPadding: EdgeInsets.symmetric(
        horizontal: _getResponsivePadding(),
        vertical: 4,
      ),
    );
  }

  Widget _buildListTile(String title, String subtitle, IconData icon, Color iconColor, VoidCallback onTap) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(fontSize: _getResponsiveFontSize() + 2),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(fontSize: _getResponsiveFontSize()),
      ),
      leading: Icon(icon, color: iconColor, size: _getResponsiveIconSize()),
      trailing: Icon(Icons.chevron_right, size: _getResponsiveIconSize()),
      onTap: onTap,
      contentPadding: EdgeInsets.symmetric(
        horizontal: _getResponsivePadding(),
        vertical: 4,
      ),
    );
  }

  Widget _buildActionTile(String title, String subtitle, IconData icon, Color iconColor, VoidCallback onTap) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(fontSize: _getResponsiveFontSize() + 2),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(fontSize: _getResponsiveFontSize()),
      ),
      leading: Icon(icon, color: iconColor, size: _getResponsiveIconSize()),
      onTap: onTap,
      contentPadding: EdgeInsets.symmetric(
        horizontal: _getResponsivePadding(),
        vertical: 4,
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _saveSettings,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF27AE60), // Success Green
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: _getResponsivePadding()),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          'Save Settings',
          style: TextStyle(
            fontSize: _getResponsiveFontSize() + 4,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  String _getEventSortOrderText() {
    switch (_eventSortOrder) {
      case 'date_asc':
        return 'Date (Ascending)';
      case 'date_desc':
        return 'Date (Descending)';
      case 'priority_high':
        return 'Priority (High to Low)';
      case 'priority_low':
        return 'Priority (Low to High)';
      case 'title_asc':
        return 'Title (A to Z)';
      case 'title_desc':
        return 'Title (Z to A)';
      default:
        return 'Date (Ascending)';
    }
  }

  void _showReminderTimesDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Default Reminder Times',
          style: TextStyle(fontSize: _getResponsiveFontSize() + 4),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Select when to receive reminders before events:',
              style: TextStyle(fontSize: _getResponsiveFontSize()),
            ),
            const SizedBox(height: 16),
            CheckboxListTile(
              title: Text('1 day before', style: TextStyle(fontSize: _getResponsiveFontSize())),
              value: _defaultReminderMinutes.contains(1440),
              onChanged: (value) {
                setState(() {
                  if (value == true && !_defaultReminderMinutes.contains(1440)) {
                    _defaultReminderMinutes.add(1440);
                  } else if (value == false) {
                    _defaultReminderMinutes.remove(1440);
                  }
                });
                Navigator.pop(context);
              },
            ),
            CheckboxListTile(
              title: Text('12 hours before', style: TextStyle(fontSize: _getResponsiveFontSize())),
              value: _defaultReminderMinutes.contains(720),
              onChanged: (value) {
                setState(() {
                  if (value == true && !_defaultReminderMinutes.contains(720)) {
                    _defaultReminderMinutes.add(720);
                  } else if (value == false) {
                    _defaultReminderMinutes.remove(720);
                  }
                });
                Navigator.pop(context);
              },
            ),
            CheckboxListTile(
              title: Text('1 hour before', style: TextStyle(fontSize: _getResponsiveFontSize())),
              value: _defaultReminderMinutes.contains(60),
              onChanged: (value) {
                setState(() {
                  if (value == true && !_defaultReminderMinutes.contains(60)) {
                    _defaultReminderMinutes.add(60);
                  } else if (value == false) {
                    _defaultReminderMinutes.remove(60);
                  }
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Done', style: TextStyle(fontSize: _getResponsiveFontSize())),
          ),
        ],
      ),
    );
  }

  void _showNumberPickerDialog(String title, int currentValue, int min, int max, Function(int) onChanged) {
    int selectedValue = currentValue;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(
            title,
            style: TextStyle(fontSize: _getResponsiveFontSize() + 4),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Current value: $selectedValue',
                style: TextStyle(fontSize: _getResponsiveFontSize() + 2),
              ),
              const SizedBox(height: 16),
              Slider(
                value: selectedValue.toDouble(),
                min: min.toDouble(),
                max: max.toDouble(),
                divisions: (max - min) ~/ 10,
                label: selectedValue.toString(),
                onChanged: (value) {
                  setDialogState(() {
                    selectedValue = value.round();
                  });
                },
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: selectedValue > min ? () {
                      setDialogState(() {
                        selectedValue = (selectedValue - 10).clamp(min, max);
                      });
                    } : null,
                    child: Text('-10', style: TextStyle(fontSize: _getResponsiveFontSize())),
                  ),
                  ElevatedButton(
                    onPressed: selectedValue < max ? () {
                      setDialogState(() {
                        selectedValue = (selectedValue + 10).clamp(min, max);
                      });
                    } : null,
                    child: Text('+10', style: TextStyle(fontSize: _getResponsiveFontSize())),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Cancel', style: TextStyle(fontSize: _getResponsiveFontSize())),
            ),
            TextButton(
              onPressed: () {
                onChanged(selectedValue);
                Navigator.pop(context);
              },
              child: Text('Save', style: TextStyle(fontSize: _getResponsiveFontSize())),
            ),
          ],
        ),
      ),
    );
  }

  void _showCalendarViewDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Default Calendar View',
          style: TextStyle(fontSize: _getResponsiveFontSize() + 4),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: Text('Month', style: TextStyle(fontSize: _getResponsiveFontSize())),
              value: 'month',
              groupValue: _defaultCalendarView,
              onChanged: (value) {
                setState(() {
                  _defaultCalendarView = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: Text('Two Weeks', style: TextStyle(fontSize: _getResponsiveFontSize())),
              value: 'twoWeeks',
              groupValue: _defaultCalendarView,
              onChanged: (value) {
                setState(() {
                  _defaultCalendarView = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: Text('Week', style: TextStyle(fontSize: _getResponsiveFontSize())),
              value: 'week',
              groupValue: _defaultCalendarView,
              onChanged: (value) {
                setState(() {
                  _defaultCalendarView = value!;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showSortOrderDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Event Sort Order',
          style: TextStyle(fontSize: _getResponsiveFontSize() + 4),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: Text('Date (Ascending)', style: TextStyle(fontSize: _getResponsiveFontSize())),
              value: 'date_asc',
              groupValue: _eventSortOrder,
              onChanged: (value) {
                setState(() {
                  _eventSortOrder = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: Text('Date (Descending)', style: TextStyle(fontSize: _getResponsiveFontSize())),
              value: 'date_desc',
              groupValue: _eventSortOrder,
              onChanged: (value) {
                setState(() {
                  _eventSortOrder = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: Text('Priority (High to Low)', style: TextStyle(fontSize: _getResponsiveFontSize())),
              value: 'priority_high',
              groupValue: _eventSortOrder,
              onChanged: (value) {
                setState(() {
                  _eventSortOrder = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: Text('Priority (Low to High)', style: TextStyle(fontSize: _getResponsiveFontSize())),
              value: 'priority_low',
              groupValue: _eventSortOrder,
              onChanged: (value) {
                setState(() {
                  _eventSortOrder = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: Text('Title (A to Z)', style: TextStyle(fontSize: _getResponsiveFontSize())),
              value: 'title_asc',
              groupValue: _eventSortOrder,
              onChanged: (value) {
                setState(() {
                  _eventSortOrder = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: Text('Title (Z to A)', style: TextStyle(fontSize: _getResponsiveFontSize())),
              value: 'title_desc',
              groupValue: _eventSortOrder,
              onChanged: (value) {
                setState(() {
                  _eventSortOrder = value!;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _exportEvents() {
    // Implementation for exporting events
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export functionality will be implemented')),
    );
  }

  void _importEvents() {
    // Implementation for importing events
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Import functionality will be implemented')),
    );
  }

  void _clearCompletedEvents() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Clear Completed Events',
          style: TextStyle(fontSize: _getResponsiveFontSize() + 4),
        ),
        content: Text(
          'Are you sure you want to remove all completed events? This action cannot be undone.',
          style: TextStyle(fontSize: _getResponsiveFontSize()),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel', style: TextStyle(fontSize: _getResponsiveFontSize())),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Implementation for clearing completed events
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Clear completed events functionality will be implemented',
                    style: TextStyle(fontSize: _getResponsiveFontSize()),
                  ),
                ),
              );
            },
            child: Text(
              'Clear',
              style: TextStyle(
                color: Colors.red,
                fontSize: _getResponsiveFontSize(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _resetAllSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Reset All Settings',
          style: TextStyle(fontSize: _getResponsiveFontSize() + 4),
        ),
        content: Text(
          'Are you sure you want to reset all event settings to default? This action cannot be undone.',
          style: TextStyle(fontSize: _getResponsiveFontSize()),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel', style: TextStyle(fontSize: _getResponsiveFontSize())),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Reset all settings to default
              setState(() {
                _enableNotifications = true;
                _enablePushNotifications = false;
                _enableEmailNotifications = false;
                _enableSMSNotifications = false;
                _enableAutoGeneration = true;
                _breedingEvents = true;
                _healthFollowups = true;
                _vaccinationSchedule = true;
                _milkReminders = false;
                _weightReminders = true;
                _heatCyclePredictions = true;
                _maxNotificationsToKeep = 100;
                _autoDeleteAfterDays = 30;
                _defaultCalendarView = 'month';
                _showCompletedEvents = true;
                _showAutoGeneratedEvents = true;
                _eventSortOrder = 'date_asc';
              });
              _saveSettings();
            },
            child: Text(
              'Reset',
              style: TextStyle(
                color: Colors.red,
                fontSize: _getResponsiveFontSize(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
