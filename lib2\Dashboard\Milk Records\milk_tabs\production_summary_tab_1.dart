import 'package:flutter/material.dart';
import '../../../services/database_helper.dart';
import '../../../services/milk_record_service.dart';
import '../../Cattle/models/cattle.dart';
import 'package:fl_chart/fl_chart.dart';
import 'dart:math';

class ProductionSummaryTab extends StatefulWidget {
  const ProductionSummaryTab({Key? key}) : super(key: key);

  @override
  ProductionSummaryTabState createState() => ProductionSummaryTabState();
}

class ProductionSummaryTabState extends State<ProductionSummaryTab> {
  String _selectedPeriod = 'Daily';
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String? _selectedCattle;
  List<Cattle> _cattleList = [];
  final MilkRecordService _milkRecordService = MilkRecordService();
  Map<DateTime, double> _productionData = {};

  @override
  void initState() {
    super.initState();
    _loadCattleList();
    _loadProductionData();
  }

  Future<void> _loadCattleList() async {
    final cattle = await DatabaseHelper.instance.getCattle();
    if (mounted) {
      setState(() {
        _cattleList = cattle;
      });
    }
  }

  Future<void> _loadProductionData() async {
    Map<DateTime, double> data;
    switch (_selectedPeriod) {
      case 'Daily':
        data = await _milkRecordService.getDailyProduction(_startDate, _endDate,
            cattleId: _selectedCattle);
        break;
      case 'Weekly':
        data = await _milkRecordService.getWeeklyProduction(
            _startDate, _endDate,
            cattleId: _selectedCattle);
        break;
      case 'Monthly':
        data = await _milkRecordService.getMonthlyProduction(
            _startDate, _endDate,
            cattleId: _selectedCattle);
        break;
      default:
        data = {};
    }

    if (mounted) {
      setState(() {
        _productionData = Map.fromEntries(
            data.entries.toList()..sort((a, b) => a.key.compareTo(b.key)));
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Filters',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: ConstrainedBox(
                          constraints: const BoxConstraints(maxWidth: 300),
                          child: DropdownButtonFormField<String>(
                            isExpanded: true,
                            decoration: const InputDecoration(
                              labelText: 'Period',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            ),
                            value: _selectedPeriod,
                            items: const [
                              DropdownMenuItem(
                                value: 'Daily',
                                child: Text('Daily'),
                              ),
                              DropdownMenuItem(
                                value: 'Weekly',
                                child: Text('Weekly'),
                              ),
                              DropdownMenuItem(
                                value: 'Monthly',
                                child: Text('Monthly'),
                              ),
                            ],
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _selectedPeriod = value;
                                });
                                _loadProductionData();
                              }
                            },
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        flex: 1,
                        child: ConstrainedBox(
                          constraints: const BoxConstraints(maxWidth: 300),
                          child: DropdownButtonFormField<String>(
                            isExpanded: true,
                            decoration: const InputDecoration(
                              labelText: 'Cattle',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            ),
                            value: _selectedCattle,
                            items: _cattleList
                                .map((cattle) => DropdownMenuItem(
                                      value: cattle.id,
                                      child: Text(
                                          '${cattle.tagId} - ${cattle.name}',
                                          overflow: TextOverflow.ellipsis,
                                      ),
                                    ))
                                .toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedCattle = value;
                              });
                              _loadProductionData();
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextButton.icon(
                          onPressed: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: _startDate,
                              firstDate: DateTime(2000),
                              lastDate: DateTime.now(),
                            );
                            if (date != null) {
                              setState(() {
                                _startDate = date;
                              });
                              _loadProductionData();
                            }
                          },
                          icon: const Icon(Icons.calendar_today),
                          label: Text(
                              'Start: ${_startDate.toString().split(' ')[0]}'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextButton.icon(
                          onPressed: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: _endDate,
                              firstDate: DateTime(2000),
                              lastDate: DateTime.now(),
                            );
                            if (date != null) {
                              setState(() {
                                _endDate = date;
                              });
                              _loadProductionData();
                            }
                          },
                          icon: const Icon(Icons.calendar_today),
                          label:
                              Text('End: ${_endDate.toString().split(' ')[0]}'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Production Summary',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: _productionData.isEmpty
                          ? const Center(
                              child: Text(
                                  'No data available for the selected period'),
                            )
                          : LineChart(
                              LineChartData(
                                gridData: const FlGridData(show: true),
                                titlesData: FlTitlesData(
                                  leftTitles: const AxisTitles(
                                    sideTitles: SideTitles(
                                      showTitles: true,
                                      reservedSize: 40,
                                      interval: 5,
                                    ),
                                  ),
                                  bottomTitles: AxisTitles(
                                    sideTitles: SideTitles(
                                      showTitles: true,
                                      interval: max(1, (_productionData.length / 8).floor()).toDouble(),
                                      getTitlesWidget: (value, meta) {
                                        if (value.toInt() >= 0 &&
                                            value.toInt() < _productionData.length) {
                                          final date = _productionData.keys
                                              .elementAt(value.toInt());
                                          return Padding(
                                            padding: const EdgeInsets.only(top: 8.0),
                                            child: Transform.rotate(
                                              angle: -0.5,
                                              child: Text(
                                                '${date.day}/${date.month}',
                                                style: const TextStyle(fontSize: 10),
                                              ),
                                            ),
                                          );
                                        }
                                        return const SizedBox();
                                      },
                                    ),
                                  ),
                                  rightTitles: const AxisTitles(
                                    sideTitles: SideTitles(showTitles: false),
                                  ),
                                  topTitles: const AxisTitles(
                                    sideTitles: SideTitles(showTitles: false),
                                  ),
                                ),
                                borderData: FlBorderData(
                                  show: true,
                                  border: Border.all(color: Colors.grey),
                                ),
                                lineBarsData: [
                                  LineChartBarData(
                                    spots: _productionData.isEmpty
                                        ? []
                                        : List.generate(
                                            _productionData.length,
                                            (index) {
                                              try {
                                                return FlSpot(
                                                  index.toDouble(),
                                                  _productionData.values.elementAt(index),
                                                );
                                              } catch (e) {
                                                return FlSpot(index.toDouble(), 0);
                                              }
                                            },
                                          ),
                                    isCurved: true,
                                    color: Theme.of(context).primaryColor,
                                    barWidth: 3,
                                    dotData: FlDotData(
                                      show: _productionData.length <= 30,
                                    ),
                                    belowBarData: BarAreaData(
                                      show: true,
                                      color: Theme.of(context)
                                          .primaryColor
                                          .withAlpha(25),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
