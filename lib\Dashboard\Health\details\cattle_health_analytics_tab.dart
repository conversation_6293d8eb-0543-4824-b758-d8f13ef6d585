import 'package:flutter/material.dart';

import 'package:intl/intl.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/health_record_isar.dart';
import '../models/treatment_record_isar.dart';
import '../models/vaccination_record_isar.dart';



class CattleHealthAnalyticsTab extends StatefulWidget {
  final CattleIsar cattle;
  final List<HealthRecordIsar> healthRecords;
  final List<TreatmentRecordIsar> treatmentRecords;
  final List<VaccinationRecordIsar> vaccinationRecords;

  const CattleHealthAnalyticsTab({
    Key? key,
    required this.cattle,
    required this.healthRecords,
    required this.treatmentRecords,
    required this.vaccinationRecords,
  }) : super(key: key);

  @override
  State<CattleHealthAnalyticsTab> createState() => _CattleHealthAnalyticsTabState();
}

class _CattleHealthAnalyticsTabState extends State<CattleHealthAnalyticsTab> {
  DateTime? _startDate;
  DateTime? _endDate;
  
  List<HealthRecordIsar> _filteredHealthRecords = [];
  List<TreatmentRecordIsar> _filteredTreatmentRecords = [];
  List<VaccinationRecordIsar> _filteredVaccinationRecords = [];

  @override
  void initState() {
    super.initState();
    _applyFilters();
  }

  @override
  void didUpdateWidget(CattleHealthAnalyticsTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.healthRecords != widget.healthRecords ||
        oldWidget.treatmentRecords != widget.treatmentRecords ||
        oldWidget.vaccinationRecords != widget.vaccinationRecords) {
      _applyFilters();
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredHealthRecords = widget.healthRecords.where((record) {
        if (_startDate != null && record.date != null && record.date!.isBefore(_startDate!)) return false;
        if (_endDate != null && record.date != null && record.date!.isAfter(_endDate!)) return false;
        return true;
      }).toList();

      _filteredTreatmentRecords = widget.treatmentRecords.where((record) {
        if (_startDate != null && record.startDate != null && record.startDate!.isBefore(_startDate!)) return false;
        if (_endDate != null && record.startDate != null && record.startDate!.isAfter(_endDate!)) return false;
        return true;
      }).toList();

      _filteredVaccinationRecords = widget.vaccinationRecords.where((record) {
        if (_startDate != null && record.vaccinationDate != null && record.vaccinationDate!.isBefore(_startDate!)) return false;
        if (_endDate != null && record.vaccinationDate != null && record.vaccinationDate!.isAfter(_endDate!)) return false;
        return true;
      }).toList();
    });
  }

  void _clearFilters() {
    setState(() {
      _startDate = null;
      _endDate = null;
    });
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Filters
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      final picked = await showDatePicker(
                        context: context,
                        initialDate: _startDate ?? DateTime.now(),
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now(),
                      );
                      if (picked != null) {
                        setState(() {
                          _startDate = picked;
                        });
                        _applyFilters();
                      }
                    },
                    icon: const Icon(Icons.date_range),
                    label: Text(_startDate != null
                        ? 'From: ${DateFormat('MMM dd').format(_startDate!)}'
                        : 'Start Date'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      final picked = await showDatePicker(
                        context: context,
                        initialDate: _endDate ?? DateTime.now(),
                        firstDate: _startDate ?? DateTime(2020),
                        lastDate: DateTime.now(),
                      );
                      if (picked != null) {
                        setState(() {
                          _endDate = picked;
                        });
                        _applyFilters();
                      }
                    },
                    icon: const Icon(Icons.date_range),
                    label: Text(_endDate != null
                        ? 'To: ${DateFormat('MMM dd').format(_endDate!)}'
                        : 'End Date'),
                  ),
                ),
                if (_startDate != null || _endDate != null)
                  IconButton(
                    onPressed: _clearFilters,
                    icon: const Icon(Icons.clear),
                    tooltip: 'Clear filters',
                  ),
              ],
            ),
          ),

          // Analytics content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHealthOverview(),
                  const SizedBox(height: 24),
                  _buildHealthTrends(),
                  const SizedBox(height: 24),
                  _buildTreatmentAnalysis(),
                  const SizedBox(height: 24),
                  _buildVaccinationStatus(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHealthOverview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.health_and_safety, color: Colors.red[600]),
                const SizedBox(width: 8),
                const Text(
                  'Health Overview',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Health Records',
                    _filteredHealthRecords.length.toString(),
                    Icons.medical_information,
                    Colors.red,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'Treatments',
                    _filteredTreatmentRecords.length.toString(),
                    Icons.medication,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'Vaccinations',
                    _filteredVaccinationRecords.length.toString(),
                    Icons.vaccines,
                    Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildHealthTrends() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: Colors.purple[600]),
                const SizedBox(width: 8),
                const Text(
                  'Health Trends',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: const Center(
                child: Text('Health trends chart will be implemented here'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTreatmentAnalysis() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  'Treatment Analysis',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: const Center(
                child: Text('Treatment analysis chart will be implemented here'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVaccinationStatus() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.vaccines, color: Colors.green[600]),
                const SizedBox(width: 8),
                const Text(
                  'Vaccination Status',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: const Center(
                child: Text('Vaccination status chart will be implemented here'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
