import 'package:flutter/material.dart';
import '../models/cattle.dart';

class AddBreedingRecordDialog extends StatefulWidget {
  final Function(BreedingRecord) onAdd;

  const AddBreedingRecordDialog({Key? key, required this.onAdd}) : super(key: key);

  @override
  State<AddBreedingRecordDialog> createState() => _AddBreedingRecordDialogState();
}

class _AddBreedingRecordDialogState extends State<AddBreedingRecordDialog> {
  final _formKey = GlobalKey<FormState>();
  DateTime _selectedDate = DateTime.now();
  String _selectedType = 'Natural';
  final TextEditingController _partnerController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  @override
  void dispose() {
    _partnerController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Breeding Record'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: const Text('Breeding Date'),
                subtitle: Text(
                  '${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')}',
                ),
                trailing: const Icon(Icons.calendar_today),
                onTap: () => _selectDate(context),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'Breeding Type',
                  border: OutlineInputBorder(),
                ),
                items: ['Natural', 'Artificial']
                    .map((type) => DropdownMenuItem(
                          value: type,
                          child: Text(type),
                        ))
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _partnerController,
                decoration: const InputDecoration(
                  labelText: 'Partner (Optional)',
                  border: OutlineInputBorder(),
                  hintText: 'Enter partner tag ID or bull name',
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'Notes (Optional)',
                  border: OutlineInputBorder(),
                  hintText: 'Enter any additional notes',
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final record = BreedingRecord(
                date: _selectedDate,
                type: _selectedType,
                partner: _partnerController.text.isEmpty
                    ? null
                    : _partnerController.text,
                notes: _notesController.text.isEmpty
                    ? null
                    : _notesController.text,
              );
              widget.onAdd(record);
              Navigator.of(context).pop();
            }
          },
          child: const Text('Add Record'),
        ),
      ],
    );
  }
}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       