import 'package:flutter/material.dart';
import '../utils/responsive_helper.dart';
import '../utils/responsive_layout.dart';
import '../theme/responsive_theme.dart';

class EmptyState extends StatelessWidget {
  final IconData icon;
  final String message;
  final String subtitle;

  const EmptyState({
    Key? key,
    required this.icon,
    required this.message,
    required this.subtitle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ResponsiveContainer(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: ResponsiveHelper.getResponsiveValue(
                context,
                mobile: 56.0,
                tablet: 64.0,
                desktop: 72.0,
              ),
              color: ResponsiveTheme.primaryColor.withOpacity(0.5),
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: ResponsiveSpacing.getMD(context)),
            ResponsiveText(
              message,
              style: ResponsiveTheme.getTitleStyle(context).co