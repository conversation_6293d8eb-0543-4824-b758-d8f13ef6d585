import 'package:flutter/material.dart';
import '../widgets/dashboard_menu_item.dart';
import '../services/database_helper.dart';
import 'Farm Setup/models/farm.dart';
import 'Farm Setup/screens/farm_setup_screen.dart';
import 'Cattle/screens/cattle_records_screen.dart';
import 'Reports/screens/reports_screen.dart';
import 'Transactions/screens/transactions_screen.dart';
import 'Milk Records/screens/milk_records_screen.dart';
import 'Events/screens/events_screen.dart';
import 'Breeding/screens/breeding_screen.dart';
import 'Health/screens/health_screen.dart';
import 'widgets/farm_selection_drawer.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  Farm? _selectedFarm;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSelectedFarm();
    _setupFarmChangeListener();
  }

  @override
  void dispose() {
    _dbHelper.removeListener(_onFarmChanged);
    super.dispose();
  }

  void _setupFarmChangeListener() {
    _dbHelper.addListener(_onFarmChanged);
  }

  void _onFarmChanged() {
    _loadSelectedFarm();
  }

  Future<void> _loadSelectedFarm() async {
    try {
      setState(() => _isLoading = true);
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId != null) {
        final farms = await _dbHelper.getFarms();
        setState(() {
          _selectedFarm = farms.firstWhere(
            (farm) => farm.id == selectedFarmId,
            orElse: () => farms.first,
          );
          _isLoading = false;
        });
      } else {
        setState(() => _isLoading = false);
      }
    } catch (e) {
      debugPrint('Error loading selected farm: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    final selectedFarmName = _selectedFarm?.name ?? 'My Cattle Manager';
    const mainColor = Color(0xFF2E7D32);
    final screenSize = MediaQuery.of(context).size;
    final availableHeight = screenSize.height -
        MediaQuery.of(context).padding.top -
        MediaQuery.of(context).padding.bottom -
        kToolbarHeight -
        32;

    int crossAxisCount = screenSize.width > 600 ? 3 : 2;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          selectedFarmName,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: mainColor,
        elevation: 0,
        leading: Builder(
          builder: (context) => IconButton(
            icon: const Icon(Icons.menu_outlined, color: Colors.white),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined, color: Colors.white),
            onPressed: () {
              // Handle notifications button tap
            },
          ),
        ],
      ),
      drawer: const FarmSelectionDrawer(),
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            const buttonHeight = 48.0;
            final buttonPadding = availableHeight * 0.02;
            final gridHeight = constraints.maxHeight - (buttonHeight + buttonPadding * 3);
            final itemWidth = (constraints.maxWidth - ((crossAxisCount + 1) * 12)) / crossAxisCount;
            final itemHeight = (gridHeight - 24) / 4;

            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: GridView.count(
                        padding: const EdgeInsets.all(12),
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        crossAxisCount: crossAxisCount,
                        mainAxisSpacing: 12.0,
                        crossAxisSpacing: 12.0,
                        childAspectRatio: itemWidth / itemHeight,
                        children: [
                          DashboardMenuItem(
                            title: 'Cattle Records',
                            icon: Icons.pets,
                            color: Colors.blue,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const CattleRecordsScreen(),
                                ),
                              );
                            },
                          ),
                          DashboardMenuItem(
                            title: 'Milk Records',
                            icon: Icons.local_drink,
                            color: Colors.green,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const MilkRecordsScreen(),
                                ),
                              );
                            },
                          ),
                          DashboardMenuItem(
                            title: 'Breeding',
                            icon: Icons.favorite,
                            color: Colors.pink,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const BreedingScreen(),
                                ),
                              );
                            },
                          ),
                          DashboardMenuItem(
                            title: 'Health',
                            icon: Icons.medical_services,
                            color: Colors.teal,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const HealthScreen(),
                                ),
                              );
                            },
                          ),
                          DashboardMenuItem(
                            title: 'Events',
                            icon: Icons.event,
                            color: Colors.orange,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const EventsScreen(),
                                ),
                              );
                            },
                          ),
                          DashboardMenuItem(
                            title: 'Transactions',
                            icon: Icons.account_balance_wallet,
                            color: Colors.purple,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const TransactionsScreen(),
                                ),
                              );
                            },
                          ),
                          DashboardMenuItem(
                            title: 'Reports',
                            icon: Icons.bar_chart,
                            color: Colors.red,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const ReportsScreen(),
                                ),
                              );
                            },
                          ),
                          DashboardMenuItem(
                            title: 'Farm Setup',
                            icon: Icons.settings,
                            color: Colors.deepPurple,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const FarmSetupScreen(),
                                ),
                              );
                            },
            