import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../Dashboard/Milk Records/models/milk_record.dart';

class CsvExportService {
  static const String csvHeader = 'Date,Cattle ID,Shift,Quantity (Liters),Notes\n';

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  String _convertRecordToCsv(MilkRecord record) {
    return '${_formatDate(record.date)},${record.cattleId},${record.shift},${record.quantity},${record.notes ?? ''}\n';
  }

  Future<String> exportMilkRecords(List<MilkRecord> records) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'milk_records_${DateTime.now().millisecondsSinceEpoch}.csv';
      final file = File('${directory.path}\\$fileName');

      final StringBuffer csvContent = StringBuffer();
      csvContent.write(csvHeader);

      for (final record in records) {
        csvContent.write(_convertRecordToCsv(record));
      }

      await file.writeAsString(csvContent.toString());
      return file.path;
    } catch (e) {
      throw Exception('Failed to export milk records: $e');
    }
  }
}                                                                                                                                                                                                                                                                                                                                                                                                       