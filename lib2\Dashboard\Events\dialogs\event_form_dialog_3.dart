import 'package:flutter/material.dart';
import '../models/event.dart';
import '../../../services/database_helper.dart';

class EventFormDialog extends StatefulWidget {
  final FarmEvent? event; // Pass existing event for editing

  const EventFormDialog({Key? key, this.event}) : super(key: key);

  @override
  State<EventFormDialog> createState() => _EventFormDialogState();
}

class _EventFormDialogState extends State<EventFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late DateTime _selectedDate;
  late TimeOfDay _selectedTime;
  EventType _selectedType = EventType.miscellaneous;
  EventPriority _selectedPriority = EventPriority.medium;
  String? _selectedCattleId;
  List<Map<String, dynamic>> _cattleList = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.event?.title ?? '');
    _descriptionController =
        TextEditingController(text: widget.event?.description ?? '');
    _selectedDate = widget.event?.date ?? DateTime.now();
    _selectedTime = widget.event?.time ?? TimeOfDay.now();
    _selectedType = widget.event?.type ?? EventType.miscellaneous;
    _selectedPriority = widget.event?.priority ?? EventPriority.medium;
    _selectedCattleId = widget.event?.cattleId;
    _loadCattleList();
  }

  Future<void> _loadCattleList() async {
    try {
      final db = DatabaseHelper.instance;
      final cattle = await db.queryAllRows('cattle');
      setState(() {
        _cattleList = cattle;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading cattle list: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
    );
    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.event == null ? 'Add New Event' : 'Edit Event'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Event Title',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ListTile(
                      title: const Text('Date'),
                      subtitle: Text(
                        '${_selectedDate.year}-${_selectedDate.month}-${_selectedDate.day}',
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () => _selectDate(context),
                    ),
                  ),
                  Expanded(
                    child: ListTile(
                      title: const Text('Time'),
                      subtitle: Text(_selectedTime.format(context)),
                      trailing: const Icon(Icons.access_time),
                      onTap: () => _selectTime(context),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<EventType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'Event Type',
                  border: OutlineInputBorder(),
                ),
                items: EventType.values.map((type) {
                  String label = type.toString().split('.').last;
                  // Convert camelCase to Title Case
                  label = label
                      .replaceAllMapped(
                        RegExp(r'([A-Z])'),
                        (match) => ' ${match.group(1)}',
                      )
                      .trim();
                  label = label[0].toUpperCase() + label.substring(1);

                  return DropdownMenuItem(
                    value: type,
                    child: Text(label),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<EventPriority>(
                value: _selectedPriority,
                decoration: const InputDecoration(
                  labelText: 'Priority',
                  border: OutlineInputBorder(),
                ),
                items: EventPriority.values.map((priority) {
                  return DropdownMenuItem(
                    value: priority,
                    child: Text(priority.toString().split('.').last),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedPriority = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              if (_isLoading)
                const CircularProgressIndicator()
              else
                DropdownButtonFormField<String>(
                  value: _selectedCattleId,
                  decoration: const InputDecoration(
                    labelText: 'Select Cattle',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem<String>(
                      value: null,
                      child: Text('No Cattle Selected'),
                    ),
                    ..._cattleList.map((cattle) => DropdownMenuItem<String>(
                          value: cattle['id'] as String,
                          child: Text(
                              '${cattle['tagNumber']} - ${cattle['name']}'),
                        )),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedCattleId = value;
                    });
                  },
                ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final event = FarmEvent(
                id: widget.event?.id ??
                    DateTime.now().millisecondsSinceEpoch.toString(),
                title: _titleController.text,
                description: _descriptionController.text,
                date: _selectedDate,
                time: _selectedTime,
                type: _selectedType,
                priority: _selectedPriority,
                cattleId: _selectedCattleId,
                createdAt: widget.event?.createdAt ?? DateTime.now(),
              );
              Navigator.of(context).pop(event);
            }
          },
          child: Text(widget.event == null ? 'Add' : 'Save'),
        ),
      ],
    );
  }
}
