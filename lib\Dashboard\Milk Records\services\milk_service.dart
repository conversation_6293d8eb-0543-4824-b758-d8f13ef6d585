import 'package:uuid/uuid.dart';
import 'package:isar/isar.dart';
import '../../../services/database/database_helper.dart';
import '../models/milk_record_isar.dart';
import 'milk_sales_service.dart';
import '../../Notifications/services/notifications_handler.dart';
import '../../Notifications/models/notification_isar.dart';

class MilkService {
  static final MilkService _instance = MilkService._internal();
  factory MilkService() => _instance;
  MilkService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  final Uuid _uuid = const Uuid();
  final MilkSalesService _salesService = MilkSalesService();
  final NotificationsHandler _notificationsHandler = NotificationsHandler.instance;

  // CRUD Operations
  Future<String> addMilkRecord(MilkRecordIsar record) async {
    try {
      final businessId = _uuid.v4();
      record.businessId = businessId;
      record.createdAt = DateTime.now();
      record.updatedAt = DateTime.now();

      // Total yield is calculated automatically by the getter

      final isar = await _dbHelper.database;
      await isar.writeTxn(() async {
        await isar.milkRecordIsars.put(record);
      });

      return businessId;
    } catch (e) {
      throw Exception('Failed to add milk record: $e');
    }
  }

  Future<void> updateMilkRecord(MilkRecordIsar record) async {
    try {
      record.updatedAt = DateTime.now();

      // Total yield is calculated automatically by the getter

      final isar = await _dbHelper.database;
      await isar.writeTxn(() async {
        await isar.milkRecordIsars.put(record);
      });
    } catch (e) {
      throw Exception('Failed to update milk record: $e');
    }
  }

  Future<void> deleteMilkRecord(String businessId) async {
    try {
      final isar = await _dbHelper.database;
      await isar.writeTxn(() async {
        final record = await isar.milkRecordIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();
        if (record != null) {
          await isar.milkRecordIsars.delete(record.id);
        }
      });
    } catch (e) {
      throw Exception('Failed to delete milk record: $e');
    }
  }

  Future<MilkRecordIsar?> getMilkRecord(String businessId) async {
    try {
      final isar = await _dbHelper.database;
      return await isar.milkRecordIsars
          .filter()
          .businessIdEqualTo(businessId)
          .findFirst();
    } catch (e) {
      throw Exception('Failed to get milk record: $e');
    }
  }

  Future<List<MilkRecordIsar>> getMilkRecords() async {
    try {
      final isar = await _dbHelper.database;
      return await isar.milkRecordIsars
          .where()
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      throw Exception('Failed to get milk records: $e');
    }
  }

  Future<List<MilkRecordIsar>> getMilkRecordsByCattle(String cattleBusinessId) async {
    try {
      final isar = await _dbHelper.database;
      return await isar.milkRecordIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      throw Exception('Failed to get cattle milk records: $e');
    }
  }

  /// Alias for getMilkRecordsByCattle for backward compatibility
  Future<List<MilkRecordIsar>> getMilkRecordsForCattle(String cattleId) async {
    return await getMilkRecordsByCattle(cattleId);
  }

  Future<List<MilkRecordIsar>> getMilkRecordsByDateRange(
    DateTime startDate,
    DateTime endDate
  ) async {
    try {
      final isar = await _dbHelper.database;
      return await isar.milkRecordIsars
          .filter()
          .dateBetween(startDate, endDate)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      throw Exception('Failed to get milk records by date range: $e');
    }
  }

  Future<List<MilkRecordIsar>> getMilkRecordsForDate(DateTime date) async {
    try {
      // Normalize date to start and end of day
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final isar = await _dbHelper.database;
      return await isar.milkRecordIsars
          .filter()
          .dateBetween(startOfDay, endOfDay)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      throw Exception('Failed to get milk records for date: $e');
    }
  }

  // Analytics Methods
  Future<MilkRecordIsar?> getLatestMilkRecord(String cattleBusinessId) async {
    try {
      final isar = await _dbHelper.database;
      return await isar.milkRecordIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId)
          .sortByDateDesc()
          .findFirst();
    } catch (e) {
      throw Exception('Failed to get latest milk record: $e');
    }
  }

  Future<double> getAverageProduction(String cattleBusinessId, {int? lastNRecords}) async {
    try {
      final isar = await _dbHelper.database;
      var records = await isar.milkRecordIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId)
          .sortByDateDesc()
          .findAll();

      // Apply limit manually if specified
      if (lastNRecords != null && records.length > lastNRecords) {
        records = records.take(lastNRecords).toList();
      }

      if (records.isEmpty) return 0.0;

      final totalProduction = records.fold<double>(0.0, (sum, record) => sum + record.totalYield);
      return totalProduction / records.length;
    } catch (e) {
      throw Exception('Failed to calculate average production: $e');
    }
  }

  Future<double> getTotalProduction(String cattleBusinessId, {DateTime? startDate, DateTime? endDate}) async {
    try {
      final isar = await _dbHelper.database;
      var query = isar.milkRecordIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId);

      if (startDate != null && endDate != null) {
        query = query.dateBetween(startDate, endDate);
      }

      final records = await query.findAll();
      return records.fold<double>(0.0, (sum, record) => sum + record.totalYield);
    } catch (e) {
      throw Exception('Failed to calculate total production: $e');
    }
  }

  Future<Map<String, double>> getSessionDistribution(String cattleBusinessId) async {
    try {
      final records = await getMilkRecordsByCattle(cattleBusinessId);

      double morningTotal = 0.0;
      double eveningTotal = 0.0;

      for (final record in records) {
        morningTotal += record.morningAmount ?? 0.0;
        eveningTotal += record.eveningAmount ?? 0.0;
      }

      return {
        'morning': morningTotal,
        'evening': eveningTotal,
      };
    } catch (e) {
      throw Exception('Failed to get session distribution: $e');
    }
  }

  Future<Map<String, dynamic>> getProductionSummary(String cattleBusinessId) async {
    try {
      final records = await getMilkRecordsByCattle(cattleBusinessId);

      if (records.isEmpty) {
        return {
          'totalRecords': 0,
          'totalProduction': 0.0,
          'averageDaily': 0.0,
          'bestDay': 0.0,
          'productionTrend': 'No Data',
        };
      }

      final totalProduction = records.fold<double>(0.0, (sum, record) => sum + record.totalYield);
      final averageDaily = totalProduction / records.length;
      final bestDay = records.map((r) => r.totalYield).reduce((a, b) => a > b ? a : b);

      // Determine production trend
      String productionTrend = 'Stable';
      if (records.length >= 3) {
        final recent = records.take(3).toList();
        final yields = recent.map((r) => r.totalYield).toList();

        if (yields[0] > yields[1] && yields[1] > yields[2]) {
          productionTrend = 'Increasing';
        } else if (yields[0] < yields[1] && yields[1] < yields[2]) {
          productionTrend = 'Decreasing';
        } else {
          productionTrend = 'Variable';
        }
      }

      return {
        'totalRecords': records.length,
        'totalProduction': totalProduction,
        'averageDaily': averageDaily,
        'bestDay': bestDay,
        'productionTrend': productionTrend,
      };
    } catch (e) {
      throw Exception('Failed to get production summary: $e');
    }
  }

  // Integrated Analytics with Sales Data
  Future<Map<String, dynamic>> getIntegratedAnalytics({DateTime? startDate, DateTime? endDate}) async {
    try {
      // Get production analytics
      final records = startDate != null && endDate != null
          ? await getMilkRecordsByDateRange(startDate, endDate)
          : await getMilkRecords();

      final totalProduction = records.fold<double>(0.0, (sum, record) => sum + record.totalYield);

      // Get sales analytics
      final salesAnalytics = await _salesService.getSalesAnalytics(
        startDate: startDate,
        endDate: endDate
      );

      final totalSold = salesAnalytics['totalQuantity'] ?? 0.0;
      final totalRevenue = salesAnalytics['totalRevenue'] ?? 0.0;

      // Calculate efficiency metrics
      final salesEfficiency = totalProduction > 0 ? (totalSold / totalProduction) * 100 : 0.0;
      final avgPricePerLiter = totalSold > 0 ? totalRevenue / totalSold : 0.0;
      final unsoldMilk = totalProduction - totalSold;

      return {
        'totalProduction': totalProduction,
        'totalSold': totalSold,
        'totalRevenue': totalRevenue,
        'unsoldMilk': unsoldMilk,
        'salesEfficiency': salesEfficiency,
        'avgPricePerLiter': avgPricePerLiter,
        'productionRecords': records.length,
        'salesRecords': salesAnalytics['totalSales'] ?? 0,
      };
    } catch (e) {
      throw Exception('Failed to get integrated analytics: $e');
    }
  }

  Future<double> getAvailableMilkForDate(DateTime date) async {
    try {
      // Get production for the date
      final records = await getMilkRecordsByDateRange(date, date);
      final totalProduction = records.fold<double>(0.0, (sum, record) => sum + record.totalYield);

      // Get sales for the date
      final sales = await _salesService.getMilkSalesForDate(date);
      final totalSold = sales.fold<double>(0.0, (sum, sale) => sum + sale.quantity);

      return totalProduction - totalSold;
    } catch (e) {
      throw Exception('Failed to get available milk for date: $e');
    }
  }

  Future<Map<String, dynamic>> getProfitabilityAnalysis(String cattleBusinessId) async {
    try {
      final records = await getMilkRecordsByCattle(cattleBusinessId);

      if (records.isEmpty) {
        return {
          'totalProduction': 0.0,
          'estimatedRevenue': 0.0,
          'avgPricePerLiter': 0.0,
          'profitability': 'No Data',
        };
      }

      final totalProduction = records.fold<double>(0.0, (sum, record) => sum + record.totalYield);

      // Get average price from recent sales
      final avgPrice = await _salesService.getAveragePrice();
      final estimatedRevenue = totalProduction * avgPrice;

      // Simple profitability assessment
      String profitability = 'Good';
      if (avgPrice < 30) {
        profitability = 'Low';
      } else if (avgPrice < 50) {
        profitability = 'Moderate';
      } else {
        profitability = 'High';
      }

      return {
        'totalProduction': totalProduction,
        'estimatedRevenue': estimatedRevenue,
        'avgPricePerLiter': avgPrice,
        'profitability': profitability,
      };
    } catch (e) {
      throw Exception('Failed to get profitability analysis: $e');
    }
  }

  // Notification Integration Methods
  Future<void> checkAndCreateProductionAlerts() async {
    try {
      final today = DateTime.now();
      // final yesterday = today.subtract(const Duration(days: 1)); // Unused variable

      // Get all milk records from the last 7 days for analysis
      final recentRecords = await getMilkRecordsByDateRange(
        today.subtract(const Duration(days: 7)),
        today
      );

      // Group by cattle
      final Map<String, List<MilkRecordIsar>> cattleRecords = {};
      for (final record in recentRecords) {
        final cattleId = record.cattleBusinessId ?? 'Unknown';
        cattleRecords[cattleId] = (cattleRecords[cattleId] ?? [])..add(record);
      }

      // Check each cattle for production issues
      for (final entry in cattleRecords.entries) {
        final cattleId = entry.key;
        final records = entry.value;

        await _checkLowProductionAlert(cattleId, records);
        await _checkMissedMilkingAlert(cattleId, records, today);
        await _checkZeroProductionAlert(cattleId, records, today);
      }
    } catch (e) {
      // Use proper logging instead of print
      // print('Error checking production alerts: $e');
    }
  }

  Future<void> _checkLowProductionAlert(String cattleId, List<MilkRecordIsar> records) async {
    if (records.length < 3) return; // Need at least 3 records for comparison

    // Get latest record and calculate average of previous records
    final sortedRecords = records..sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
    final latestRecord = sortedRecords.first;
    final previousRecords = sortedRecords.skip(1).take(5).toList(); // Last 5 records for average

    if (previousRecords.isEmpty) return;

    final latestProduction = latestRecord.totalYield;
    final avgProduction = previousRecords.fold<double>(0.0, (sum, r) => sum + r.totalYield) / previousRecords.length;

    // Alert if production dropped by more than 30%
    if (latestProduction < avgProduction * 0.7 && avgProduction > 0) {
      final dropPercentage = ((avgProduction - latestProduction) / avgProduction * 100);

      await _createProductionAlert(
        type: 'production',
        title: 'Low Milk Production Alert',
        message: 'Cattle $cattleId produced ${latestProduction.toStringAsFixed(1)}L, '
                '${dropPercentage.toStringAsFixed(1)}% below average (${avgProduction.toStringAsFixed(1)}L)',
        priority: dropPercentage > 50 ? 'high' : 'medium',
        cattleId: cattleId,
        recordId: latestRecord.businessId,
      );
    }
  }

  Future<void> _checkMissedMilkingAlert(String cattleId, List<MilkRecordIsar> records, DateTime today) async {
    // Check if there are any records for today
    final todayRecords = records.where((r) =>
      r.date != null &&
      r.date!.day == today.day &&
      r.date!.month == today.month &&
      r.date!.year == today.year
    ).toList();

    // Alert if no records today and it's after 10 AM
    if (todayRecords.isEmpty && today.hour > 10) {
      await _createProductionAlert(
        type: 'production',
        title: 'Missed Milking Alert',
        message: 'No milk records found for cattle $cattleId today. Please check if milking was missed.',
        priority: 'medium',
        cattleId: cattleId,
      );
    }
  }

  Future<void> _checkZeroProductionAlert(String cattleId, List<MilkRecordIsar> records, DateTime today) async {
    // Check for zero production records today
    final todayRecords = records.where((r) =>
      r.date != null &&
      r.date!.day == today.day &&
      r.date!.month == today.month &&
      r.date!.year == today.year &&
      r.totalYield == 0.0
    ).toList();

    if (todayRecords.isNotEmpty) {
      await _createProductionAlert(
        type: 'production',
        title: 'Zero Production Alert',
        message: 'Cattle $cattleId recorded zero milk production today. Please verify if this is correct.',
        priority: 'low',
        cattleId: cattleId,
        recordId: todayRecords.first.businessId,
      );
    }
  }

  Future<void> _createProductionAlert({
    required String type,
    required String title,
    required String message,
    required String priority,
    required String cattleId,
    String? recordId,
  }) async {
    try {
      // Check if similar alert already exists (avoid duplicates)
      final existingAlerts = await _notificationsHandler.getAllNotifications();
      final duplicateAlert = existingAlerts.where((n) =>
        n.cattleId == cattleId &&
        n.type == type &&
        n.title == title &&
        n.createdAt != null &&
        DateTime.now().difference(n.createdAt!).inHours < 24
      ).isNotEmpty;

      if (duplicateAlert) {
        return; // Skip duplicate
      }

      final notification = NotificationIsar(
        businessId: 'milk_alert_${DateTime.now().millisecondsSinceEpoch}_$cattleId',
        type: type,
        title: title,
        message: message,
        priority: priority,
        cattleId: cattleId,
        recordId: recordId,
        createdAt: DateTime.now(),
        isRead: false,
      );

      await _notificationsHandler.addNotification(notification);
    } catch (e) {
      // Use proper logging instead of print
      // print('Error creating production alert: $e');
    }
  }

  // Enhanced add method with alert checking
  Future<void> addMilkRecordWithAlerts(MilkRecordIsar record) async {
    await addMilkRecord(record);

    // Check for alerts after adding record
    await checkAndCreateProductionAlerts();
  }

  // Predictive Analytics Methods
  Future<Map<String, dynamic>> getPredictiveAnalytics({String? cattleId}) async {
    try {
      final endDate = DateTime.now();
      final startDate = endDate.subtract(const Duration(days: 30)); // Last 30 days

      final records = cattleId != null
          ? await getMilkRecordsByCattle(cattleId)
          : await getMilkRecordsByDateRange(startDate, endDate);

      if (records.length < 7) {
        return {
          'forecast': 'Insufficient data for prediction',
          'trend': 'Unknown',
          'confidence': 0.0,
          'nextWeekPrediction': 0.0,
          'seasonalPattern': 'Unknown',
          'recommendations': ['Collect more data for accurate predictions'],
        };
      }

      final forecast = _calculateProductionForecast(records);
      final trend = _analyzeTrend(records);
      final seasonalPattern = _analyzeSeasonalPattern(records);
      final recommendations = _generateRecommendations(records, forecast, trend);

      return {
        'forecast': forecast['description'],
        'trend': trend['direction'],
        'confidence': forecast['confidence'],
        'nextWeekPrediction': forecast['nextWeek'],
        'nextMonthPrediction': forecast['nextMonth'],
        'seasonalPattern': seasonalPattern,
        'trendPercentage': trend['percentage'],
        'recommendations': recommendations,
        'dataPoints': records.length,
      };
    } catch (e) {
      throw Exception('Failed to generate predictive analytics: $e');
    }
  }

  Map<String, dynamic> _calculateProductionForecast(List<MilkRecordIsar> records) {
    // Sort records by date
    final sortedRecords = records.where((r) => r.date != null).toList()
      ..sort((a, b) => a.date!.compareTo(b.date!));

    if (sortedRecords.length < 7) {
      return {
        'description': 'Insufficient data',
        'confidence': 0.0,
        'nextWeek': 0.0,
        'nextMonth': 0.0,
      };
    }

    // Simple linear regression for trend
    final productions = sortedRecords.map((r) => r.totalYield).toList();
    // final n = productions.length; // Unused variable

    // Calculate moving average for smoothing
    final movingAverage = <double>[];
    const windowSize = 7; // 7-day moving average

    for (int i = windowSize - 1; i < productions.length; i++) {
      final sum = productions.sublist(i - windowSize + 1, i + 1).fold<double>(0.0, (a, b) => a + b);
      movingAverage.add(sum / windowSize);
    }

    if (movingAverage.isEmpty) {
      return {
        'description': 'Insufficient data for moving average',
        'confidence': 0.0,
        'nextWeek': 0.0,
        'nextMonth': 0.0,
      };
    }

    // Calculate trend using linear regression on moving average
    final x = List.generate(movingAverage.length, (i) => i.toDouble());
    final y = movingAverage;

    final xMean = x.fold<double>(0.0, (a, b) => a + b) / x.length;
    final yMean = y.fold<double>(0.0, (a, b) => a + b) / y.length;

    double numerator = 0.0;
    double denominator = 0.0;

    for (int i = 0; i < x.length; i++) {
      numerator += (x[i] - xMean) * (y[i] - yMean);
      denominator += (x[i] - xMean) * (x[i] - xMean);
    }

    final slope = denominator != 0 ? numerator / denominator : 0.0;
    final intercept = yMean - slope * xMean;

    // Predict next week (7 days) and next month (30 days)
    final nextWeekIndex = x.length.toDouble();
    final nextMonthIndex = x.length + 23.0; // 30 days from last data point

    final nextWeekPrediction = slope * nextWeekIndex + intercept;
    final nextMonthPrediction = slope * nextMonthIndex + intercept;

    // Calculate confidence based on R-squared
    double ssRes = 0.0;
    double ssTot = 0.0;

    for (int i = 0; i < y.length; i++) {
      final predicted = slope * x[i] + intercept;
      ssRes += (y[i] - predicted) * (y[i] - predicted);
      ssTot += (y[i] - yMean) * (y[i] - yMean);
    }

    final rSquared = ssTot != 0 ? 1 - (ssRes / ssTot) : 0.0;
    final confidence = (rSquared * 100).clamp(0.0, 100.0);

    String description;
    if (slope > 0.1) {
      description = 'Production is trending upward';
    } else if (slope < -0.1) {
      description = 'Production is trending downward';
    } else {
      description = 'Production is stable';
    }

    return {
      'description': description,
      'confidence': confidence,
      'nextWeek': nextWeekPrediction.clamp(0.0, double.infinity),
      'nextMonth': nextMonthPrediction.clamp(0.0, double.infinity),
      'slope': slope,
    };
  }

  Map<String, dynamic> _analyzeTrend(List<MilkRecordIsar> records) {
    if (records.length < 14) {
      return {'direction': 'Unknown', 'percentage': 0.0};
    }

    // Compare first half vs second half
    final sortedRecords = records.where((r) => r.date != null).toList()
      ..sort((a, b) => a.date!.compareTo(b.date!));

    final midPoint = sortedRecords.length ~/ 2;
    final firstHalf = sortedRecords.sublist(0, midPoint);
    final secondHalf = sortedRecords.sublist(midPoint);

    final firstHalfAvg = firstHalf.fold<double>(0.0, (sum, r) => sum + r.totalYield) / firstHalf.length;
    final secondHalfAvg = secondHalf.fold<double>(0.0, (sum, r) => sum + r.totalYield) / secondHalf.length;

    final changePercentage = firstHalfAvg != 0
        ? ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100
        : 0.0;

    String direction;
    if (changePercentage > 5) {
      direction = 'Increasing';
    } else if (changePercentage < -5) {
      direction = 'Decreasing';
    } else {
      direction = 'Stable';
    }

    return {
      'direction': direction,
      'percentage': changePercentage,
    };
  }

  String _analyzeSeasonalPattern(List<MilkRecordIsar> records) {
    // Analyze by month to detect seasonal patterns
    final monthlyProduction = <int, List<double>>{};

    for (final record in records) {
      if (record.date == null) continue;

      final month = record.date!.month;
      monthlyProduction[month] = (monthlyProduction[month] ?? [])..add(record.totalYield);
    }

    if (monthlyProduction.length < 3) {
      return 'Insufficient data for seasonal analysis';
    }

    // Calculate average production per month
    final monthlyAverages = <int, double>{};
    for (final entry in monthlyProduction.entries) {
      final avg = entry.value.fold<double>(0.0, (sum, val) => sum + val) / entry.value.length;
      monthlyAverages[entry.key] = avg;
    }

    // Find peak and low months
    final sortedMonths = monthlyAverages.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    if (sortedMonths.length >= 2) {
      final peakMonth = _getMonthName(sortedMonths.first.key);
      final lowMonth = _getMonthName(sortedMonths.last.key);
      return 'Peak: $peakMonth, Low: $lowMonth';
    }

    return 'No clear seasonal pattern detected';
  }

  String _getMonthName(int month) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month - 1];
  }

  List<String> _generateRecommendations(
    List<MilkRecordIsar> records,
    Map<String, dynamic> forecast,
    Map<String, dynamic> trend
  ) {
    final recommendations = <String>[];

    // Based on trend
    final trendDirection = trend['direction'] as String;
    final trendPercentage = trend['percentage'] as double;

    if (trendDirection == 'Decreasing' && trendPercentage.abs() > 10) {
      recommendations.add('Production declining by ${trendPercentage.abs().toStringAsFixed(1)}% - check cattle health and nutrition');
      recommendations.add('Consider reviewing feed quality and quantity');
      recommendations.add('Schedule veterinary checkup for declining cattle');
    } else if (trendDirection == 'Increasing') {
      recommendations.add('Production increasing - maintain current feeding and care practices');
      recommendations.add('Monitor for optimal milking frequency');
    }

    // Based on forecast confidence
    final confidence = forecast['confidence'] as double;
    if (confidence < 50) {
      recommendations.add('Low prediction confidence - collect more consistent data');
      recommendations.add('Ensure regular recording of milk production');
    }

    // Based on production levels
    final recentAvg = records.take(7).fold<double>(0.0, (sum, r) => sum + r.totalYield) / 7;
    if (recentAvg < 10) {
      recommendations.add('Low average production - consider nutritional supplements');
    } else if (recentAvg > 25) {
      recommendations.add('High production levels - ensure adequate rest and nutrition');
    }

    // General recommendations
    recommendations.add('Maintain consistent milking schedule for optimal production');
    recommendations.add('Monitor cattle health indicators regularly');

    return recommendations.take(5).toList(); // Limit to 5 recommendations
  }
}
