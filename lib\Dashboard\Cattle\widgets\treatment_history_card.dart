import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'info_row.dart';

class TreatmentHistoryCard extends StatelessWidget {
  final List<Map<String, dynamic>> records;
  final String title;
  final String emptyMessage;
  final Function(Map<String, dynamic>)? onEdit;
  final Function(Map<String, dynamic>)? onDelete;
  final Function(Map<String, dynamic>)? onStatusTap;
  final Function(Map<String, dynamic>)? onCattleTap; // Add cattle tap callback
  // Optional cattle information - if provided, it will be shown in record headers
  final String? cattleName;
  final String? cattleId;

  const TreatmentHistoryCard({
    super.key,
    required this.records,
    this.title = 'Treatment History',
    this.emptyMessage = 'No treatment records found',
    this.onEdit,
    this.onDelete,
    this.onStatusTap,
    this.onCattleTap, // Add cattle tap callback
    this.cattleName,
    this.cattleId,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withAlpha(26), // Light background
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.blue.withAlpha(51),
                  child: const Icon(
                    Icons.healing,
                    color: Colors.blue,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ),
                Text(
                  '${records.length} ${records.length == 1 ? 'treatment' : 'treatments'}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.blue[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // Content
          if (records.isEmpty)
            Padding(
              padding: const EdgeInsets.all(32),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.healing_outlined,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      emptyMessage,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black54,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Add a treatment record to start tracking',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          else
            Flexible(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(10, 16, 10, 16),
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: records.length,
                  itemBuilder: (context, index) {
                    // Add divider between items
                    if (index > 0) {
                      return Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Divider(height: 32),
                          _buildTreatmentRecord(context, records[index]),
                        ],
                      );
                    }
                    return _buildTreatmentRecord(context, records[index]);
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTreatmentRecord(BuildContext context, Map<String, dynamic> record) {
    // Extract record data with null safety - updated for unified form structure
    final recordDate = DateTime.tryParse(record['date']?.toString() ?? record['startDate']?.toString() ?? '') ?? DateTime.now();
    final condition = record['condition']?.toString() ?? record['diagnosis']?.toString() ?? 'Unknown condition';
    final treatment = record['treatment']?.toString() ?? record['treatmentName']?.toString() ?? record['name']?.toString() ?? 'Unknown treatment';
    final medicine = record['medicine']?.toString() ?? record['medication']?.toString() ?? '';
    final dose = record['dose']?.toString() ?? record['dosage']?.toString() ?? '';
    final dosageUnit = record['dosageUnit']?.toString() ?? '';
    final veterinarian = record['veterinarian']?.toString() ?? 'Not specified';
    final cost = record['cost']?.toString() ?? '0.00';
    final notes = record['notes']?.toString() ?? '';

    // Use record's cattle info with fallback to the widget's cattle info
    final recordCattleId = record['cattleId']?.toString() ?? '';
    final recordCattleName = record['cattleName']?.toString() ?? '';
    final displayCattleId = recordCattleId.isNotEmpty ? recordCattleId : cattleId ?? '';
    final displayCattleName = recordCattleName.isNotEmpty ? recordCattleName : cattleName ?? '';

    // Use a default color for the header
    const statusColor = Colors.blue;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Record Header with status-based color - make entire header tappable
        GestureDetector(
          onTap: onCattleTap != null ? () => onCattleTap!(record) : null,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: statusColor.withAlpha(51), // 0.2 opacity (standardized)
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: statusColor.withAlpha(70), // 0.27 opacity (standardized)
                        child: Icon(
                          Icons.healing,
                          color: statusColor,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              DateFormat('MMMM dd, yyyy').format(recordDate),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold, // Standardized
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (displayCattleId.isNotEmpty || displayCattleName.isNotEmpty) ...[
                              const SizedBox(height: 4),
                              Text(
                                displayCattleName.isNotEmpty ? displayCattleName : 'Unknown',
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w600, // Standardized
                                  color: statusColor,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // More options menu (edit/delete)
                if (onEdit != null || onDelete != null)
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert),
                    onSelected: (value) {
                      if (value == 'edit' && onEdit != null) {
                        onEdit!(record);
                      } else if (value == 'delete' && onDelete != null) {
                        onDelete!(record);
                      }
                    },
                    itemBuilder: (context) => [
                      if (onEdit != null)
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit),
                              SizedBox(width: 8),
                              Text('Edit'),
                            ],
                          ),
                        ),
                      if (onDelete != null)
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 8),
                              Text('Delete', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                    ],
                  ),
              ],
            ),
          ),
        ),

        // Record Details
        Padding(
          padding: const EdgeInsets.all(16), // Standardized padding
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Condition/Diagnosis
              InfoRow(
                icon: Icons.local_hospital,
                label: 'Condition',
                value: condition,
                color: Colors.red,
              ),
              const SizedBox(height: 10),

              // Treatment
              InfoRow(
                icon: Icons.healing,
                label: 'Treatment',
                value: treatment,
                color: Colors.blue,
              ),
              const SizedBox(height: 10),

              // Medicine (if provided)
              if (medicine.isNotEmpty) ...[
                InfoRow(
                  icon: Icons.medication,
                  label: 'Medicine',
                  value: medicine,
                  color: Colors.purple,
                ),
                const SizedBox(height: 10),
              ],

              // Dosage (if provided)
              if (dose.isNotEmpty) ...[
                InfoRow(
                  icon: Icons.straighten,
                  label: 'Dosage',
                  value: dosageUnit.isNotEmpty ? '$dose $dosageUnit' : dose,
                  color: Colors.teal,
                ),
                const SizedBox(height: 10),
              ],

              // Date
              InfoRow(
                icon: Icons.date_range,
                label: 'Date',
                value: DateFormat('MMMM dd, yyyy').format(recordDate),
                color: Colors.indigo,
              ),
              const SizedBox(height: 10),

              // Veterinarian
              InfoRow(
                icon: Icons.person,
                label: 'Veterinarian',
                value: veterinarian,
                color: Colors.green,
              ),
              const SizedBox(height: 10),

              // Cost
              InfoRow(
                icon: Icons.monetization_on_outlined,
                label: 'Cost',
                value: '\$$cost',
                color: Colors.red,
              ),

              // Notes section
              if (notes.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Divider(),
                const SizedBox(height: 4),
                InfoRow(
                  icon: Icons.notes,
                  label: 'Notes',
                  value: notes,
                  color: Colors.deepPurple,
                  isMultiline: true,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
