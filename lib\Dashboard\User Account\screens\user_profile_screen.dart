import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:async';
import '../services/cloud_authentication_service.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_bar.dart';
import '../../../services/firebase_service.dart';

/// User profile management screen
class UserProfileScreen extends StatefulWidget {
  const UserProfileScreen({Key? key}) : super(key: key);

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  late final CloudAuthenticationService _authService;
  final _formKey = GlobalKey<FormState>();
  final _displayNameController = TextEditingController();
  final _phoneController = TextEditingController();

  bool _isEditing = false;
  File? _selectedImage;
  Map<String, dynamic>? _userProfile;
  String? _currentImageUrl;

  @override
  void initState() {
    super.initState();
    _authService = GetIt.instance<CloudAuthenticationService>();
    _loadUserProfile();
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _loadUserProfile() async {
    if (_authService.currentUser == null) return;

    // INSTANT LOAD: Show Firebase Auth data immediately (no loading state)
    setState(() {
      _displayNameController.text = _authService.currentUser?.displayName ?? '';
      _phoneController.text = _userProfile?['phoneNumber'] ?? ''; // Use cached data if available
      _currentImageUrl = _authService.currentUser?.photoURL ?? _userProfile?['photoURL'];
    });

    // BACKGROUND SYNC: Load from Firebase in background without blocking UI
    _loadFromFirebaseInBackground();
  }

  /// Load data from Firebase in background without affecting UI responsiveness
  void _loadFromFirebaseInBackground() async {
    if (_authService.currentUser == null) return;

    try {
      final userData = await FirebaseService.getUserData(_authService.currentUser!.uid).timeout(
        const Duration(seconds: 2),
      );

      if (userData != null && mounted) {
        // Only update if data is different to avoid unnecessary rebuilds
        final newFullName = userData['fullName'] ?? _authService.currentUser?.displayName ?? '';
        final newPhoneNumber = userData['phoneNumber'] ?? '';
        final newImageUrl = userData['photoURL'];

        if (newFullName != _displayNameController.text ||
            newPhoneNumber != _phoneController.text ||
            newImageUrl != _currentImageUrl) {
          setState(() {
            _userProfile = userData;
            _displayNameController.text = newFullName;
            _phoneController.text = newPhoneNumber;
            _currentImageUrl = newImageUrl;
          });
        }
      }
    } catch (e) {
      // Silent fail - user already has immediate data from Firebase Auth
      debugPrint('Background Firebase load failed: $e');
    }
  }

  Future<void> _showImagePickerOptions() async {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Select Profile Photo',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildImageOption(
                  'Camera',
                  Icons.camera_alt,
                  Colors.blue,
                  () => _pickImage(ImageSource.camera),
                ),
                _buildImageOption(
                  'Gallery',
                  Icons.photo_library,
                  Colors.green,
                  () => _pickImage(ImageSource.gallery),
                ),
                if (_selectedImage != null || _currentImageUrl != null)
                  _buildImageOption(
                    'Remove',
                    Icons.delete,
                    Colors.red,
                    _removeImage,
                  ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildImageOption(String label, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Icon(icon, color: color, size: 30),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
        });
      }
    } catch (e) {
      if (mounted) {
        MessageUtils.showError(context, 'Failed to pick image: $e');
      }
    }
  }

  void _removeImage() {
    setState(() {
      _selectedImage = null;
      _currentImageUrl = null;
    });
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;
    if (_authService.currentUser == null) {
      MessageUtils.showError(context, 'No user logged in');
      return;
    }

    // INSTANT SAVE: Update UI immediately and show success
    final updateData = {
      'fullName': _displayNameController.text.trim(),
      'phoneNumber': _phoneController.text.trim(),
      'updatedAt': DateTime.now().toIso8601String(),
    };

    if (_currentImageUrl != null) {
      updateData['photoURL'] = _currentImageUrl!;
    }

    // Update local state immediately
    setState(() {
      _isEditing = false;
      _selectedImage = null;
      _userProfile = updateData;
    });

    // Show immediate success message
    MessageUtils.showSuccess(context, 'Profile updated!');

    // BACKGROUND SYNC: Save to Firebase in background
    _saveToFirebaseInBackground(updateData);
  }

  /// Save to Firebase in background without blocking UI
  void _saveToFirebaseInBackground(Map<String, dynamic> updateData) async {
    if (_authService.currentUser == null) return;

    try {
      final user = _authService.currentUser!;

      // Handle image upload in background if needed
      if (_selectedImage != null) {
        try {
          final imageUrl = await _uploadProfileImage();
          if (imageUrl != null) {
            updateData['photoURL'] = imageUrl;
            if (mounted) {
              setState(() {
                _currentImageUrl = imageUrl;
                _userProfile = updateData;
              });
            }
          }
        } catch (e) {
          debugPrint('Background image upload failed: $e');
        }
      }

      // Try to update display name in Firebase Auth
      try {
        if (updateData['fullName'] != user.displayName) {
          await user.updateDisplayName(updateData['fullName']).timeout(
            const Duration(seconds: 2),
          );
        }
      } catch (e) {
        debugPrint('Background display name update failed: $e');
      }

      // Try to save to Firebase
      try {
        await FirebaseService.updateUserData(user.uid, updateData).timeout(
          const Duration(seconds: 3),
        );
        debugPrint('Profile synced to Firebase successfully');
      } catch (e) {
        debugPrint('Background Firebase save failed: $e');
        // Save locally as fallback
        await _saveProfileLocally(updateData);

        if (mounted) {
          // Show subtle notification that it's saved locally
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profile saved locally, will sync when online'),
              duration: Duration(seconds: 2),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Background save error: $e');
      // Ensure local save as final fallback
      await _saveProfileLocally(updateData);
    }
  }

  /// Save profile data locally as fallback when Firebase is unavailable
  Future<void> _saveProfileLocally(Map<String, dynamic> updateData) async {
    try {
      // You can implement local storage here using SharedPreferences or Isar
      // For now, we'll just update the local state
      _userProfile = {...(_userProfile ?? {}), ...updateData};
      debugPrint('Profile saved locally: $updateData');
    } catch (e) {
      debugPrint('Local save failed: $e');
      rethrow;
    }
  }



  Future<String?> _uploadProfileImage() async {
    if (_selectedImage == null) return null;

    try {
      // This is a placeholder for Firebase Storage upload
      // In a real implementation, you would upload to Firebase Storage
      // For now, we'll simulate a faster upload
      await Future.delayed(const Duration(milliseconds: 800)); // Much faster simulation

      // Return a placeholder URL
      // In real implementation: return the Firebase Storage download URL
      return 'https://placeholder.com/profile_${_authService.currentUser!.uid}.jpg';
    } catch (e) {
      throw Exception('Failed to upload image: $e');
    }
  }

  Widget _buildProfileHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF2196F3),
            const Color(0xFF1976D2),
            const Color(0xFF0D47A1),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF2196F3).withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 2,
          ),
        ],
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // Enhanced Profile Avatar
            Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white.withValues(alpha: 0.3),
                        Colors.white.withValues(alpha: 0.1),
                      ],
                    ),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.4),
                      width: 3,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: CircleAvatar(
                    radius: 40,
                    backgroundColor: Colors.white.withValues(alpha: 0.2),
                    backgroundImage: _getProfileImage(),
                    child: _getProfileImage() == null
                        ? Icon(
                            Icons.person,
                            size: 40,
                            color: Colors.white,
                          )
                        : null,
                  ),
                ),
                if (_isEditing)
                  Positioned(
                    bottom: 2,
                    right: 2,
                    child: GestureDetector(
                      onTap: _showImagePickerOptions,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 6,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.camera_alt,
                          size: 18,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(width: 20),

            // Enhanced User Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _displayNameController.text.isNotEmpty
                        ? _displayNameController.text
                        : _authService.currentUserDisplayName ?? 'User',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: 0.5,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _authService.userEmail ?? '',
                      style: const TextStyle(
                        fontSize: 13,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  if (_phoneController.text.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.15),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.phone,
                            size: 14,
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            _phoneController.text,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.white.withValues(alpha: 0.9),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  if (_authService.isEmailVerified) ...[
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.shield_outlined,
                          size: 16,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Verified Account',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.white.withValues(alpha: 0.8),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),

            // Enhanced Verification Badge
            if (_authService.isEmailVerified)
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.shade400,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.verified,
                  size: 18,
                  color: Colors.white,
                ),
              ),
          ],
        ),
      ),
    );
  }

  ImageProvider? _getProfileImage() {
    if (_selectedImage != null) {
      return FileImage(_selectedImage!);
    } else if (_currentImageUrl != null && _currentImageUrl!.isNotEmpty) {
      return NetworkImage(_currentImageUrl!);
    }
    return null;
  }

  Widget _buildProfileForm() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Profile Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.primaryColor,
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: _isEditing
                        ? Colors.red.withValues(alpha: 0.1)
                        : AppConstants.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    onPressed: () {
                      setState(() {
                        _isEditing = !_isEditing;
                        if (!_isEditing) {
                          _selectedImage = null;
                          _loadUserProfile(); // Reset form
                        }
                      });
                    },
                    icon: Icon(
                      _isEditing ? Icons.close : Icons.edit,
                      color: _isEditing ? Colors.red : AppConstants.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Full Name Field (Required)
            _buildTextField(
              controller: _displayNameController,
              label: 'Full Name *',
              icon: Icons.person_outline,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter your full name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Email Field (Read-only)
            _buildTextField(
              controller: TextEditingController(text: _authService.userEmail ?? ''),
              label: 'Email Address',
              icon: Icons.email_outlined,
              enabled: false,
            ),
            const SizedBox(height: 16),

            // Phone Field (Required)
            _buildTextField(
              controller: _phoneController,
              label: 'Mobile Number *',
              icon: Icons.phone_outlined,
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter your mobile number';
                }
                return null;
              },
            ),

            if (_isEditing) ...[
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton.icon(
                  onPressed: _saveProfile, // Always enabled for instant save
                  icon: const Icon(
                    Icons.save,
                    size: 20,
                    color: Colors.white,
                  ),
                  label: const Text(
                    'Save Changes',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    int maxLines = 1,
    bool enabled = true,
  }) {
    return TextFormField(
      controller: controller,
      enabled: enabled && _isEditing,
      maxLines: maxLines,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppConstants.primaryColor, width: 2),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade200),
        ),
        filled: !(enabled && _isEditing),
        fillColor: (enabled && _isEditing) ? null : Colors.grey.shade50,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
      validator: validator,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBarConfig.withBack(
        title: 'Profile',
        context: context,
        actions: [
          if (!_isEditing)
            IconButton(
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
              icon: const Icon(Icons.edit),
              tooltip: 'Edit Profile',
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildProfileHeader(),
            _buildProfileForm(),
            const SizedBox(height: 32), // Bottom padding
          ],
        ),
      ),
    );
  }
}
