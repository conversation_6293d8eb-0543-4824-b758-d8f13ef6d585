geVersion": "3.0"
    },
    {
      "name": "file_selector_windows",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "fixnum",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1",
      "packageUri": "lib/",
      "languageVersion": "3.1"
    },
    {
      "name": "fl_chart",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fl_chart-0.70.2",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "flutter",
      "rootUri": "file:///C:/flutter/packages/flutter",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "flutter_lints",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-2.0.3",
      "packageUri": "lib/",
      "languageVersion": "2.19"
    },
    {
      "name": "flutter_localizations",
      "rootUri": "file:///C:/flutter/packages/flutter_localizations",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "flutter_plugin_android_lifecycle",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.24",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "flutter_secure_storage",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage-9.2.4",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "flutter_secure_storage_linux",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.2",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "flutter_secure_storage_macos",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "flutter_secure_storage_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "flutter_secure_storage_web",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "flutter_secure_storage_windows",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "flutter_svg",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_svg-2.0.17",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "flutter_test",
      "rootUri": "file:///C:/flutter/packages/flutter_test",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "flutter_web_plugins",
      "rootUri": "file:///C:/flutter/packages/flutter_web_plugins",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "font_awesome_flutter",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/font_awesome_flutter-10.8.0",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "frontend_server_client",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/frontend_server_client-4.0.0",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "geolocator",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator-10.1.1",
      "packageUri": "lib/",
      "languageVersion": "2.15"
    },
    {
      "name": "geolocator_android",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_android-4.6.1",
      "packageUri": "lib/",
      "languageVersion": "2.15"
    },
    {
      "name": "geolocator_apple",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_apple-2.3.9",
      "packageUri": "lib/",
      "languageVersion": "2.15"
    },
    {
      "name": "geolocator_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_platform_interface-4.2.4",
      "packageUri": "lib/",
      "languageVersion": "2.15"
    },
    {
      "name": "geolocator_web",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_web-2.2.1",
      "packageUri": "lib/",
      "languageVersion": "2.15"
    },
    {
      "name": "geolocator_windows",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_windows-0.2.3",
      "packageUri": "lib/",
      "languageVersion": "2.15"
    },
    {
      "name": "glob",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/glob-2.1.3",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "google_identity_services_web",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_identity_services_web-0.3.3",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "google_sign_in",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in-6.2.2",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "google_sign_in_android",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_android-6.1.35",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "google_sign_in_ios",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_ios-5.8.0",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "google_sign_in_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_platform_interface-2.4.5",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "google_sign_in_web",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_sign_in_web-0.12.4+3",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "googleapis",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/googleapis-12.0.0",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "googleapis_auth",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/googleapis_auth-1.6.0",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "graphs",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/graphs-2.3.2",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "hive",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/hive-2.2.3",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "hive_flutter",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/hive_flutter-1.1.0",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "hive_generator",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/hive_generator-2.0.1",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "html",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/html-0.15.5",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "http",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.3.0",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "http_multi_server",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_multi_server-3.2.2",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "http_parser",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "image",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image-4.3.0",
      "packageUri": "lib/",
      "languageVersion": "2.15"
    },
    {
      "name": "image_picker",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker-1.1.2",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "image_picker_android",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+21",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "image_picker_for_web",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "image_picker_ios",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+2",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "image_picker_linux",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+1",
      "packageUri": "lib/",
      "languageVersion": "2.19"
    },
    {
      "name": "image_picker_macos",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+2",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "image_picker_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.1",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "image_picker_windows",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1",
      "packageUri": "lib/",
      "languageVersion": "2.19"
    },
    {
      "name": "intl",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.19.0",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "io",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/io-1.0.5",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "jiffy",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/jiffy-6.3.2",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "js",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.6.7",
      "packageUri": "lib/",
      "languageVersion": "2.19"
    },
    {
      "name": "json_annotation",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "leak_tracker",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.7",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "leak_tracker_flutter_testing",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.8",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "leak_tracker_testing",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "lints",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-2.1.1",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "logging",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "macros",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/macros-0.1.3-main.0",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "matcher",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.16+1",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "material_color_utilities",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1",
      "packageUri": "lib/",
      "languageVersion": "2.17"
    },
    {
      "name": "meta",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.15.0",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "mime",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mime-1.0.6",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "nested",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "package_config",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_config-2.1.1",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "path",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.0",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "path_parsing",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_parsing-1.1.0",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "path_provider",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "path_provider_android",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.15",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "path_provider_foundation",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "path_provider_linux",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1",
      "packageUri": "lib/",
      "languageVersion": "2.19"
    },
    {
      "name": "path_provider_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "path_provider_windows",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "pdf",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pdf-3.11.3",
      "packageUri": "lib/",
      "languageVersion": "2.19"
    },
    {
      "name": "pdf_widget_wrapper",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pdf_widget_wrapper-1.0.4",
      "packageUri": "lib/",
      "languageVersion": "2.18"
    },
    {
      "name": "petitparser",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.0.2",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "platform",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "plugin_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "pool",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pool-1.5.1",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "printing",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/printing-5.14.2",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "process_run",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/process_run-1.2.2+1",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "provider",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.2",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "pub_semver",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pub_semver-2.1.5",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "pubspec_parse",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pubspec_parse-1.5.0",
      "packageUri": "lib/",
      "languageVersion": "3.6"
    },
    {
      "name": "qr",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/qr-3.0.2",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "qr_code_scanner",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/qr_code_scanner-1.0.1",
      "packageUri": "lib/",
      "languageVersion": "2.17"
    },
    {
      "name": "qr_flutter",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/qr_flutter-4.1.0",
      "packageUri": "lib/",
      "languageVersion": "2.19"
    },
    {
      "name": "share_plus",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus-7.2.2",
      "packageUri": "lib/",
      "languageVersion": "2.18"
    },
    {
      "name": "share_plus_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus_platform_interface-3.4.0",
      "packageUri": "lib/",
      "languageVersion": "2.18"
    },
    {
      "name": "shared_preferences",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.2",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "shared_preferences_android",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.6",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "shared_preferences_foundation",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "shared_preferences_linux",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "shared_preferences_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "shared_preferences_web",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "shared_preferences_windows",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "shelf",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf-1.4.2",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "shelf_web_socket",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf_web_socket-3.0.0",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sky_engine",
      "rootUri": "file:///C:/flutter/bin/cache/pkg/sky_engine",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "source_gen",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_gen-1.5.0",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "source_helper",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_helper-1.3.5",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "source_span",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.0",
      "packageUri": "lib/",
      "languageVersion": "2.18"
    },
    {
      "name": "sprintf",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "sqflite",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite-2.4.1",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sqflite_android",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_android-2.4.0",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sqflite_common",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common-2.5.4+6",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sqflite_common_ffi",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common_ffi-2.3.4+4",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sqflite_common_ffi_web",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common_ffi_web-0.4.5+4",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sqflite_darwin",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.1+1",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sqflite_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_platform_interface-2.4.0",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sqlite3",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqlite3-2.7.4",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "stack_trace",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.0",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "stream_channel",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.2",
      "packageUri": "lib/",
      "languageVersion": "2.19"
    },
    {
      "name": "stream_transform",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1",
      "packageUri": "lib/",
      "languageVersion": "3.1"
    },
    {
      "name": "string_scanner",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.3.0",
      "packageUri": "lib/",
      "languageVersion": "3.1"
    },
    {
      "name": "syncfusion_flutter_core",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/syncfusion_flutter_core-28.2.6",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "syncfusion_flutter_pdf",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/syncfusion_flutter_pdf-28.2.6",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "syncfusion_flutter_xlsio",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/syncfusion_flutter_xlsio-28.2.6",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "syncfusion_officecore",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/syncfusion_officecore-28.2.6",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "synchronized",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/synchronized-3.3.0+3",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "term_glyph",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.1",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "test_api",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.3",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "timing",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timing-1.0.2",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "typed_data",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "universal_html",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/universal_html-2.2.4",
      "packageUri": "lib/",
      "languageVersion": "2.17"
    },
    {
      "name": "universal_io",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/universal_io-2.2.2",
      "packageUri": "lib/",
      "languageVersion": "2.17"
    },
    {
      "name": "url_launcher_linux",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "url_launcher_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2",
      "packageUri": "lib/",
      "languageVersion": "3.1"
    },
    {
      "name": "url_launcher_web",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.0",
      "packageUri": "lib/",
      "languageVersion": "3.6"
    },
    {
      "name": "url_launcher_windows",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "uuid",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "vector_graphics",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics-1.1.18",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "vector_graphics_codec",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics_codec-1.1.13",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "vector_graphics_compiler",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics_compiler-1.1.16",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "vector_math",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4",
      "packageUri": "lib/",
      "languageVersion": "2.14"
    },
    {
      "name": "vm_service",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-14.3.0",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "watcher",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/watcher-1.1.1",
      "packageUri": "lib/",
      "languageVersion": "3.1"
    },
    {
      "name": "web",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.0",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "web_socket",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket-0.1.6",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "web_socket_channel",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket_channel-3.0.2",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "win32",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.10.1",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "xdg_directories",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "xml",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xml-6.5.0",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "yaml",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/yaml-3.1.3",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "cattle_manager",
      "rootUri": "../",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    }
  ],
  "generated": "2025-06-24T04:13:48.376177Z",
  "generator": "pub",
  "generatorVersion": "3.6.1",
  "flutterRoot": "file:///C:/flutter",
  "flutterVersion": "3.27.3",
  "pubCache": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache"
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                