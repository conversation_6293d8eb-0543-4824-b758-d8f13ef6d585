import 'package:flutter/material.dart';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'package:intl/intl.dart';
import '../models/backup_settings.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../services/database_helper.dart';
import '../../../services/google_drive_service.dart';
import 'package:googleapis/drive/v3.dart' as drive;

class DataBackupScreen extends StatefulWidget {
  const DataBackupScreen({Key? key}) : super(key: key);

  @override
  State<DataBackupScreen> createState() => _DataBackupScreenState();
}

class _DataBackupScreenState extends State<DataBackupScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  final GoogleDriveService _driveService = GoogleDriveService();
  late BackupSettings _settings;
  bool _isLoading = true;
  bool _isProcessing = false;
  bool _isGoogleDriveInitialized = false;

  Future<void> _initializeGoogleDrive() async {
    if (_settings.backupLocation == 'cloud') {
      setState(() => _isProcessing = true);
      _isGoogleDriveInitialized = await _driveService.initialize();
      setState(() => _isProcessing = false);

      if (!_isGoogleDriveInitialized && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to initialize Google Drive')),
        );
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _settings = BackupSettings(); // Initialize with default values
    _loadSettings().then((_) => _initializeGoogleDrive());
  }

  Future<void> _loadSettings() async {
    try {
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) {
        setState(() {
          _settings = BackupSettings();
          _isLoading = false;
        });
        return;
      }

      final prefs = await SharedPreferences.getInstance();
      final key = '${selectedFarmId}_backup_settings';
      final settingsJson = prefs.getString(key);

      setState(() {
        if (settingsJson != null) {
          _settings = BackupSettings.fromMap(
            Map<String, dynamic>.from(json.decode(settingsJson)),
          );
        } else {
          _settings = BackupSettings();
        }
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading backup settings: $e');
      setState(() {
        _settings = BackupSettings();
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    try {
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) return;

      final prefs = await SharedPreferences.getInstance();
      final key = '${selectedFarmId}_backup_settings';
      await prefs.setString(key, jsonEncode(_settings.toMap()));
    } catch (e) {
      debugPrint('Error saving backup settings: $e');
    }
  }

  Future<Map<String, dynamic>> _getAllFarmData() async {
    try {
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) {
        throw Exception('No farm selected');
      }

      final prefs = await SharedPreferences.getInstance();
      final allKeys =
          prefs.getKeys().where((key) => key.startsWith('${selectedFarmId}_'));
      final data = <String, dynamic>{};

      for (final key in allKeys) {
        final value = prefs.get(key);
        if (value != null) {
          data[key] = value;
        }
      }

      return data;
    } catch (e) {
      debugPrint('Error getting farm data: $e');
      rethrow;
    }
  }

  Future<void> _restoreFarmData(Map<String, dynamic> data) async {
    try {
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) {
        throw Exception('No farm selected');
      }

      final prefs = await SharedPreferences.getInstance();
      for (final entry in data.entries) {
        if (entry.key.startsWith('${selectedFarmId}_')) {
          if (entry.value is String) {
            await prefs.setString(entry.key, entry.value as String);
          } else if (entry.value is List<String>) {
            await prefs.setStringList(entry.key, entry.value as List<String>);
          }
        }
      }
    } catch (e) {
      debugPrint('Error restoring farm data: $e');
      rethrow;
    }
  }

  Future<void> _createBackup() async {
    setState(() => _isProcessing = true);
    try {
      final data = await _getAllFarmData();
      final backupJson = jsonEncode(data);
      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      final selectedFarmId = await _dbHelper.getSelectedFarmId();

      if (_settings.backupLocation == 'cloud' && _isGoogleDriveInitialized) {
        final fileId = await _driveService.uploadBackup(selectedFarmId!, data);
        if (fileId == null) throw Exception('Failed to upload to Google Drive');
      } else {
        final directory = await getApplicationDocumentsDirectory();
        final fileName = 'farm_backup_$timestamp.json';
        final file = File('${directory.path}/$fileName');
        await file.writeAsString(backupJson);

        await Share.shareXFiles(
          [XFile(file.path)],
          text: 'Farm Backup $timestamp',
        );
      }

      setState(() {
        _settings = _settings.copyWith(lastBackupDate: DateTime.now());
      });
      await _saveSettings();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Backup created successfully')),
        );
      }
    } catch (e) {
      debugPrint('Error creating backup: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error creating backup')),
        );
      }
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  Future<void> _exportData(String format) async {
    setState(() => _isProcessing = true);
    try {
      final data = await _getAllFarmData();
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      late final String fileName;
      late final String content;

      if (format == 'csv') {
        fileName = 'farm_export_$timestamp.csv';
        content = data.entries.map((e) => '${e.key},${e.value}').join('\n');
      } else {
        fileName = 'farm_export_$timestamp.json';
        content = jsonEncode(data);
      }

      final file = File('${directory.path}/$fileName');
      await file.writeAsString(content);

      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Farm Data Export $timestamp',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Data exported successfully')),
        );
      }
    } catch (e) {
      debugPrint('Error exporting data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error exporting data')),
        );
      }
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  Future<void> _restoreBackup() async {
    try {
      Map<String, dynamic>? backupData;

      if (_settings.backupLocation == 'cloud' && _isGoogleDriveInitialized) {
        final backups = await _driveService.listBackups();
        if (backups.isEmpty) {
          throw Exception('No backups found in Google Drive');
        }

        final selectedBackup = await showDialog<drive.File>(
          context: mounted ? context : throw Exception('Widget is unmounted'),
          builder: (context) => AlertDialog(
            title: const Text('Select Backup'),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: backups.length,
                itemBuilder: (context, index) => ListTile(
                  title: Text(backups[index].name ?? 'Unnamed Backup'),
                  subtitle: Text(
                      backups[index].createdTime?.toString() ?? 'Unknown date'),
                  onTap: () => Navigator.pop(context, backups[index]),
                ),
              ),
            ),
          ),
        );

        if (selectedBackup == null) return;
        setState(() => _isProcessing = true);
        backupData = await _driveService.downloadBackup(selectedBackup.id!);
        if (backupData == null) throw Exception('Failed to download backup');
      } else {
        final result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: ['json'],
        );

        if (result == null) return;
        setState(() => _isProcessing = true);
        final file = File(result.files.single.path!);
        final backupJson = await file.readAsString();
        backupData = jsonDecode(backupJson) as Map<String, dynamic>;
      }

      await _restoreFarmData(backupData);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Backup restored successfully')),
        );
      }
    } catch (e) {
      debugPrint('Error restoring backup: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error restoring backup')),
        );
      }
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  Future<void> _resetFarmData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Farm Data'),
        content: const Text(
          'Are you sure you want to reset all farm data? '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Reset'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() => _isProcessing = true);
    try {
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) {
        throw Exception('No farm selected');
      }

      final prefs = await SharedPreferences.getInstance();
      final allKeys =
          prefs.getKeys().where((key) => key.startsWith('${selectedFarmId}_'));
      for (final key in allKeys) {
        await prefs.remove(key);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Farm data reset successfully')),
        );
      }
    } catch (e) {
      debugPrint('Error resetting farm data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error resetting farm data')),
        );
      }
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Data Backup'),
      ),
      body: Stack(
        children: [
          ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Backup Settings',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      if (_settings.backupLocation == 'cloud') ...[
                        ListTile(
                          leading: Icon(
                            _isGoogleDriveInitialized
                                ? Icons.cloud_done
                                : Icons.cloud_off,
                            color: _isGoogleDriveInitialized
                                ? Colors.green
                                : Colors.grey,
                          ),
                          title: Text(
                            _isGoogleDriveInitialized
                                ? 'Google Drive Connected'
                                : 'Connect Google Drive',
                          ),
                          subtitle: Text(
                            _isGoogleDriveInitialized
                                ? 'Click to disconnect'
                                : 'Click to connect your Google account',
                          ),
                          onTap: () async {
                            if (_isGoogleDriveInitialized) {
                              await _driveService.signOut();
                              setState(() {
                                _isGoogleDriveInitialized = false;
                              });
                            } else {
                              await _initializeGoogleDrive();
                            }
                          },
                        ),
                        const Divider(),
                      ],
                      SwitchListTile(
                        title: const Text('Auto Backup'),
                        subtitle: const Text(
                            'Automatically backup data periodically'),
                        value: _settings.autoBackupEnabled,
                        onChanged: (value) {
                          setState(() {
                            _settings =
                                _settings.copyWith(autoBackupEnabled: value);
                          });
                          _saveSettings();
                        },
                      ),
                      if (_settings.autoBackupEnabled) ...[
                        ListTile(
                          title: const Text('Backup Frequency'),
                          subtitle: Text(
                              'Every ${_settings.backupFrequencyDays} days'),
                          trailing: const Icon(Icons.chevron_right),
                          onTap: () async {
                            final days = await showDialog<int>(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: const Text('Backup Frequency'),
                                content: DropdownButtonFormField<int>(
                                  value: _settings.backupFrequencyDays,
                                  items: [1, 3, 7, 14, 30].map((days) {
                                    return DropdownMenuItem(
                                      value: days,
                                      child: Text('$days days'),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    Navigator.pop(context, value);
                                  },
                                ),
                              ),
                            );
                            if (days != null) {
                              setState(() {
                                _settings = _settings.copyWith(
                                  backupFrequencyDays: days,
                                );
                              });
                              _saveSettings();
                            }
                          },
                        ),
                        ListTile(
                          title: const Text('Backup Location'),
                          subtitle: Text(
                            _settings.backupLocation == 'local'
                                ? 'Local Storage'
                                : 'Cloud Storage',
                          ),
                          trailing: const Icon(Icons.chevron_right),
                          onTap: () async {
                            final location = await showDialog<String>(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: const Text('Backup Location'),
                                content: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    RadioListTile<String>(
                                      title: const Text('Local Storage'),
                                      value: 'local',
                                      groupValue: _settings.backupLocation,
                                      onChanged: (value) {
                                        Navigator.pop(context, value);
                                      },
                                    ),
                                    RadioListTile<String>(
                                      title: const Text('Google Drive'),
                                      subtitle: const Text(
                                          'Automatic cloud backup and sync'),
                                      value: 'cloud',
                                      groupValue: _settings.backupLocation,
                                      onChanged: (value) {
                                        Navigator.pop(context, value);
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            );
                            if (location != null) {
                              setState(() {
                                _settings = _settings.copyWith(
                                  backupLocation: location,
                                );
                              });
                              _saveSettings();
                            }
                          },
                        ),
                      ],
                      if (_settings.lastBackupDate != null)
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            'Last backup: ${DateFormat.yMMMd().add_jm().format(_settings.lastBackupDate!)}',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Backup & Export',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ListTile(
                        leading: const Icon(Icons.backup),
                        title: const Text('Create Backup'),
                        subtitle: const Text(
                            'Create a full backup of your farm data'),
                        onTap: _createBackup,
                      ),
                      ListTile(
                        leading: const Icon(Icons.restore),
                        title: const Text('Restore Backup'),
                        subtitle: const Text('Restore data from a backup file'),
                        onTap: _restoreBackup,
                      ),
                      const Divider(),
                      ListTile(
                        leading: const Icon(Icons.file_download),
                        title: const Text('Export as CSV'),
                        subtitle: const Text('Export farm data in CSV format'),
                        onTap: () => _exportData('csv'),
                      ),
                      ListTile(
                        leading: const Icon(Icons.file_download),
                        title: const Text('Export as JSON'),
                        subtitle: const Text('Export farm data in JSON format'),
                        onTap: () => _exportData('json'),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Danger Zone',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ListTile(
                        leading:
                            const Icon(Icons.delete_forever, color: Colors.red),
                        title: const Text(
                         