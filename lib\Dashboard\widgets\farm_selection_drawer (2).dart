import 'package:flutter/material.dart';
import '../Farm Setup/models/farm.dart';
import '../Farm Setup/screens/farm_info_screen.dart';
import '../Reports/screens/reports_screen.dart';
import '../Notifications/screens/notifications_screen.dart';
import '../../services/database_helper.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/responsive_layout.dart';
import '../../theme/responsive_theme.dart';

class FarmSelectionDrawer extends StatefulWidget {
  const FarmSelectionDrawer({Key? key}) : super(key: key);

  @override
  State<FarmSelectionDrawer> createState() => _FarmSelectionDrawerState();
}

class _FarmSelectionDrawerState extends State<FarmSelectionDrawer> {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  List<Farm> _farms = [];
  Farm? _selectedFarm;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFarms();
    _setupFarmChangeListener();
  }

  @override
  void dispose() {
    _dbHelper.removeListener(_onFarmChanged);
    super.dispose();
  }

  void _setupFarmChangeListener() {
    _dbHelper.addListener(_onFarmChanged);
  }

  void _onFarmChanged() {
    _loadFarms();
  }

  Future<void> _loadFarms() async {
    try {
      final farms = await _dbHelper.getFarms();
      final selectedFarmId = await _dbHelper.getSelectedFarmId();

      if (!mounted) return;

      setState(() {
        _farms = farms;
        if (selectedFarmId != null && farms.isNotEmpty) {
          _selectedFarm = farms.firstWhere(
            (farm) => farm.id == selectedFarmId,
            orElse: () => farms.first,
          );
        } else if (farms.isNotEmpty) {
          _selectedFarm = farms.first;
        } else {
          _selectedFarm = null;
        }
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading farms: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _selectFarm(Farm farm) async {
    try {
      setState(() => _isLoading = true);
      await _dbHelper.setSelectedFarmId(farm.id);
      setState(() {
        _selectedFarm = farm;
        _isLoading = false;
      });
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      debugPrint('Error selecting farm: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? iconColor,
    bool showDivider = false,
  }) {
    return Column(
      children: [
        ResponsiveListTile(
          leading: Icon(
            icon,
            color: iconColor ?? ResponsiveTheme.primaryColor,
            size: ResponsiveHelper.getIconSize(context),
          ),
          title: ResponsiveText(
            title,
            style: ResponsiveTheme.getSubtitleStyle(context),
          ),
          onTap: onTap,
        ),
        if (showDivider) const Divider(height: 1),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Drawer(
        child: Center(child: CircularProgressIndicator()),
      );
    }

    return Drawer(
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).padding.top + 16,
                bottom: 16,
              ),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black,
                  ],
                ),
              ),
              child: Column(
                children: [
                  const CircleAvatar(
                    radius: 40,
                    backgroundColor: Colors.white,
                    child: Icon(
                      Icons.person,
                      size: 40,
                      color: Colors.black54,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    _selectedFarm?.name ?? 'Select Farm',
                    style: const TextStyle(
                      color: Colors.black87,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    'Manage Your Farm',
                    style: TextStyle(
                      color: Colors.black54,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  const SizedBox(height: 8),
                  ExpansionTile(
                    leading:
                        const Icon(Icons.business, color: Color(0xFF2E7D32)),
                    title: const Text(
                      'My Farms',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    children: _farms.map((farm) {
                      final isSelected = farm.id == _selectedFarm?.id;
                      return ListTile(
                        leading: const Icon(Icons.agriculture),
                        title: Text(farm.name),
                        selected: isSelected,
                        selectedTileColor:
                            Theme.of(context).primaryColor.withAlpha(25),
                        onTap: () => _selectFarm(farm),
                      );
                    }).toList(),
                  ),
                  const Divider(height: 1),
                  _buildDrawerItem(
                    icon: Icons.dashboard,
                    title: 'Dashboard',
                    onTap: () => Navigator.pop(context),
                    showDivider: true,
                  ),
                  _buildDrawerItem(
                    icon: Icons.settings,
                    title: 'Farm Setup',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const FarmInfoScreen()),
                      );
                    },
                    showDivider: true,
                  ),
                  _buildDrawerItem(
                    icon: Icons.analytics,
                    title: 'Reports & Analytics',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ReportsScreen(),
                        ),
                      );
                    },
                    showDivider: true,
                  ),
                  _buildDrawerItem(
                    icon: Icons.notifications,
                    title: 'Notifications',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const NotificationsScreen(),
                        ),
                      );
                    },
                    showDivider: true,
                  ),
                  _buildDrawerItem(
                    icon: Icons.help,
                    title: 'Help & Support',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Add Help screen
                    },
                    showDivider: true,
                  ),
                  const Divider(height: 1),
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'Account',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  _buildDrawerItem(
                    icon: Icons.person,
                    title: 'Profile',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Add Profile screen
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.settings,
                    title: 'Settings',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Add Settings screen
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.logout,
                    title: 'Logout',
                    onTap: () {
                      // Handle logout
                    },
                    iconColor: Colors.red,
                  ),
                ],
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Version 1.0.0',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
