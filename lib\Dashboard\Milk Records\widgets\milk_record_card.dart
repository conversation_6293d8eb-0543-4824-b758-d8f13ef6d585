import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/milk_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';

class MilkRecordCard extends StatelessWidget {
  final MilkRecordIsar record;
  final CattleIsar? cattle;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool compact;

  const MilkRecordCard({
    Key? key,
    required this.record,
    this.cattle,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.primaryColor;

    return Card(
      margin: EdgeInsets.only(bottom: compact ? 8 : 12),
      elevation: isSelected ? 4 : 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: isSelected 
                ? Border.all(color: primaryColor, width: 2) 
                : null,
          ),
          child: Padding(
            padding: EdgeInsets.all(compact ? 12 : 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Row
                Row(
                  children: [
                    // Record Icon
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.water_drop,
                        color: primaryColor,
                        size: compact ? 20 : 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    
                    // Record Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Record #${record.recordId ?? 'N/A'}',
                            style: TextStyle(
                              fontSize: compact ? 14 : 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            DateFormat('MMM dd, yyyy').format(record.date ?? DateTime.now()),
                            style: TextStyle(
                              fontSize: compact ? 12 : 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Actions
                    if (!isSelectionMode && (onEdit != null || onDelete != null))
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (onEdit != null)
                            IconButton(
                              onPressed: onEdit,
                              icon: const Icon(Icons.edit),
                              color: primaryColor,
                              iconSize: compact ? 20 : 24,
                            ),
                          if (onDelete != null)
                            IconButton(
                              onPressed: onDelete,
                              icon: const Icon(Icons.delete),
                              color: Colors.red,
                              iconSize: compact ? 20 : 24,
                            ),
                        ],
                      ),
                    
                    // Selection checkbox
                    if (isSelectionMode)
                      Checkbox(
                        value: isSelected,
                        onChanged: (_) => onTap?.call(),
                        activeColor: primaryColor,
                      ),
                  ],
                ),
                
                if (!compact) ...[
                  const SizedBox(height: 12),
                  
                  // Production Details
                  Row(
                    children: [
                      Expanded(
                        child: _buildProductionItem(
                          'Morning',
                          '${record.morningAmount?.toStringAsFixed(1) ?? '0.0'} L',
                          Icons.wb_sunny,
                          Colors.orange,
                          compact,
                        ),
                      ),
                      Expanded(
                        child: _buildProductionItem(
                          'Evening',
                          '${record.eveningAmount?.toStringAsFixed(1) ?? '0.0'} L',
                          Icons.nights_stay,
                          Colors.indigo,
                          compact,
                        ),
                      ),
                      Expanded(
                        child: _buildProductionItem(
                          'Total',
                          '${record.totalYield.toStringAsFixed(1)} L',
                          Icons.water_drop,
                          primaryColor,
                          compact,
                        ),
                      ),
                    ],
                  ),
                ],
                
                // Cattle Info (if available and not compact)
                if (!compact && cattle != null) ...[
                  const SizedBox(height: 12),
                  const Divider(),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.pets, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 8),
                      Text(
                        'Cattle: ${cattle!.name ?? cattle!.tagId ?? 'Unknown'}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ],
                
                // Notes (if available and not compact)
                if (!compact && record.notes != null && record.notes!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.note, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          record.notes!,
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey[600],
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProductionItem(
    String label, 
    String value, 
    IconData icon, 
    Color color,
    bool compact,
  ) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: compact ? 6 : 8, 
        vertical: compact ? 4 : 6,
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: compact ? 16 : 20),
          SizedBox(height: compact ? 2 : 4),
          Text(
            value,
            style: TextStyle(
              fontSize: compact ? 12 : 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: compact ? 10 : 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
