import 'package:uuid/uuid.dart';

class UserRole {
  final String id;
  final String name;
  final List<String> permissions;

  UserRole({
    String? id,
    required this.name,
    required this.permissions,
  }) : id = id ?? const Uuid().v4();

  factory UserRole.fromMap(Map<String, dynamic> map) {
    return UserRole(
      id: map['id'] as String,
      name: map['name'] as String,
      permissions: List<String>.from(map['permissions'] as List),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'permissions': permissions,
    };
  }

  UserRole copyWith({
    String? name,
    List<String>? permissions,
  }) {
    return UserRole(
      id: id,
      name: name ?? this.name,
      permissions: permissions ?? List.from(this.permissions),
    );
  }

  static List<String> get allPermissions => [
    'manage_cattle',
    'view_cattle',
    'manage_milk_records',
    'view_milk_records',
    'manage_health_records',
    'view_health_records',
    'manage_breeding_records',
    'view_breeding_records',
    'manage_transactions',
    'view_transactions',
    'manage_users',
    'view_reports',
    'manage_farm_settings',
  ];

  static List<UserRole> get defaultRoles => [
    UserRole(
      name: 'Admin',
      permissions: allPermissions,
    ),
    UserRole(
      name: 'Manager',
      permissions: [
        'view_cattle',
        'manage_cattle',
        'view_milk_records',
        'manage_milk_records',
        'view_health_records',
        'manage_health_records',
        'view_breeding_records',
        'manage_breeding_records',
        'view_transactions',
        'manage_transactions',
        'view_reports',
      ],
    ),
    UserRole(
      name: 'Worker',
      permissions: [
        'view_cattle',
        'view_milk_records',
        'manage_milk_records',
        'view_health_records',
        'view_breeding_records',
      ],
    ),
    UserRole(
      name: 'Veterinarian',
      permissions: [
        'view_cattle',
        'view_health_records',
        'manage_health_records',
        'view_breeding_records',
        'manage_breeding_records',
      ],
    ),
  ];
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              