import 'package:flutter/material.dart';
import '../models/event.dart';
import '../dialogs/event_form_dialog.dart';
import '../../../services/database_helper.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class AllEventsTab extends StatefulWidget {
  const AllEventsTab({Key? key}) : super(key: key);

  @override
  State<AllEventsTab> createState() => _AllEventsTabState();
}

class _AllEventsTabState extends State<AllEventsTab> {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  String _selectedType = 'all';
  String _searchQuery = '';
  List<FarmEvent> _events = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadEvents();
    _setupFarmChangeListener();
  }

  @override
  void dispose() {
    _dbHelper.removeListener(_onF