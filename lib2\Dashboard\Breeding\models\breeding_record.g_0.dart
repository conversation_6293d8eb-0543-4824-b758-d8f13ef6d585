import 'package:google_sign_in/google_sign_in.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:http/http.dart' as http;
import 'package:logging/logging.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      drive.DriveApi.driveFileScope,
    ],
  );
  final _storage = const FlutterSecureStorage();
  final _logger = Logger('AuthService');
  GoogleSignInAccount? _currentUser;
  AuthClient? _authClient;

  factory AuthService() {
    return _instance;
  }

  AuthService._internal();

  GoogleSignInAccount? get currentUser => _currentUser;
  AuthClient? get authClient => _authClient;

  Future<bool> initialize() async {
    try {
      // Try to sign in silently with cached credentials
      _currentUser = await _googleSignIn.signInSilently();
      if (_currentUser != null) {
        await _getAuthClient();
        return true;
      }
      return false;
    } catch (e) {
      _logger.warning('Error initializing auth service: $e');
      return false;
    }
  }

  Future<bool> signIn() async {
    try {
      _currentUser = await _googleSignIn.signIn();
      if (_currentUser != null) {
        await _getAuthClient();
        return true;
      }
      return false;
    } catch (e) {
      _logger.warning('Error signing in: $e');
      return false;
    }
  }

  Future<void> signOut() async {
    try {
     