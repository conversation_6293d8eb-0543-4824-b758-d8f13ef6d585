import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../models/transaction.dart';
import '../models/category.dart';
import '../transactions_tabs/summary_tab.dart';
import '../transactions_tabs/transactions_list_tab.dart';
import '../dialogs/transaction_form_dialog.dart';
import 'package:excel/excel.dart' as excel_lib;
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:share_plus/share_plus.dart';

class TransactionsScreen extends StatefulWidget {
  const TransactionsScreen({Key? key}) : super(key: key);

  @override
  TransactionsScreenState createState() => TransactionsScreenState();
}

class TransactionsScreenState extends State<TransactionsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<Transaction> transactions = [];
  List<Transaction> filteredTransactions = [];
  late Box<Transaction> transactionsBox;
  late Box<Category> incomeCategoriesBox;
  late Box<Category> expenseCategoriesBox;

  // Logger setup
  final Logger _logger = Logger('TransactionsScreen');

  // Filter states
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedType;
  final List<String> _selectedCategories = [];
  String? _selectedPaymentMethod;
  String? _amountRange;
  String? _sortBy;
  final bool _sortAscending = true;
  final String _searchQuery = '';

  // Sort states

  final TextEditingController _startDateController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();

  bool _isSelectionMode = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _initHive();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _startDateController.dispose();
    _endDateController.dispose();
    super.dispose();
  }

  Future<void> _initHive() async {
    transactionsBox = await Hive.openBox<Transaction>('transactions');
    incomeCategoriesBox = await Hive.openBox<Category>('income_categories');
    expenseCategoriesBox = await Hive.openBox<Category>('expense_categories');
    _loadTransactions();
  }

  void _applyFilters() {
    setState(() {
      filteredTransactions = transactions.where((transaction) {
        // Date filter
        if (_startDate != null && _endDate != null) {
          if (transaction.date.isBefore(_startDate!) ||
              transaction.date.isAfter(_endDate!)) {
            return false;
          }
        }

        // Type filter
        if (_selectedType != null &&
            _selectedType != 'All Types' &&
            transaction.type != _selectedType) {
          return false;
        }

        // Category filter
        if (_selectedCategories.isNotEmpty &&
            !_selectedCategories.contains(transaction.category)) {
          return false;
        }

        // Amount filter
        if (_amountRange != null && _amountRange != 'All Amounts') {
          final amount = transaction.amount;
          switch (_amountRange) {
            case 'Under 1000':
              if (amount >= 1000) return false;
              break;
            case '1000-5000':
              if (amount < 1000 || amount > 5000) return false;
              break;
            case '5000-10000':
              if (amount < 5000 || amount > 10000) return false;
              break;
            case 'Over 10000':
              if (amount <= 10000) return false;
              break;
          }
        }

        // Payment Method filter
        if (_selectedPaymentMethod != null &&
            _selectedPaymentMethod != 'All Methods' &&
            transaction.paymentMethod != _selectedPaymentMethod) {
          return false;
        }

        // Search query
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          return transaction.type.toLowerCase().contains(query) ||
              transaction.category.toLowerCase().contains(query) ||
              (transaction.description.toLowerCase().contains(query)) ||
              transaction.amount.toString().contains(query) ||
              (transaction.paymentMethod?.toLowerCase().contains(query) ??
                  false);
        }

        return true;
      }).toList();

      // Apply sorting
      if (_sortBy != null) {
        filteredTransactions.sort((a, b) {
          int comparison;
          switch (_sortBy) {
            case 'Date':
              comparison = a.date.compareTo(b.date);
              break;
            case 'Amount':
              comparison = a.amount.compareTo(b.amount);
              break;
            case 'Category':
              comparison = a.category.compareTo(b.category);
              break;
            default:
              comparison = 0;
          }
          return _sortAscending ? comparison : -comparison;
        });
      }
    });
  }

  Future<void> _loadTransactions() async {
    try {
      setState(() {
        transactions = transactionsBox.values.toList();
        filteredTransactions = List.from(transactions);
        _applyFilters();
      });
    } catch (e) {
      _logger.severe('Failed to load transactions', e);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load transactions: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _addTransaction(Transaction transaction) async {
    try {
      await transactionsBox.add(transaction);
      setState(() {
        transactions = transactionsBox.values.toList();
        filteredTransactions = List.from(transactions);
        _applyFilters();
      });

      // Safely show SnackBar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Transaction saved successfully'),
            backgroundColor: Color(0xFF2E7D32),
          ),
        );
      }
    } catch (e) {
      // Use logger instead of print
      _logger.severe('Error saving transaction', e);

      // Safely show error SnackBar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error saving transaction'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleSaveTransaction(Transaction transaction) async {
    await _addTransaction(transaction);
  }

  Future<void> _exportData(String value) async {
    try {
      final directory = await getExternalStorageDirectory();
      if (directory == null) {
        throw Exception('Could not access external storage');
      }

      String filePath;
      switch (value) {
        case 'excel':
          filePath = await _exportToExcel(directory.path);
          break;
        case 'csv':
          filePath = await _exportToCsv(directory.path);
          break;
        case 'pdf':
          filePath = await _exportToPdf(directory.path);
          break;
        default:
          return;
      }

      // Share the file
      await Share.shareXFiles([XFile(filePath)]);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Exported successfully to ${value.toUpperCase()}')),
        );
      }
    } catch (e) {
      _logger.severe('Export error', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to export: $e')),
        );
      }
    }
  }

  Future<String> _exportToExcel(String dirPath) async {
    final excel = excel_lib.Excel.createExcel();
    final sheet = excel['Transactions'];

    // Add headers
    final headers = [
      'Date',
      'Type',
      'Category',
      'Amount',
      'Payment Method',
      'Description'
    ];
    for (var i = 0; i < headers.length; i++) {
      final cell = sheet.cell(
          excel_lib.CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
      cell.value = excel_lib.TextCellValue(headers[i]);
      cell.cellStyle = excel_lib.CellStyle(bold: true);
    }

    // Add data
    var row = 1;
    for (var transaction in filteredTransactions) {
      final cells = [
        DateFormat('yyyy-MM-dd').format(transaction.date),
        transaction.type,
        transaction.category,
        '\$${transaction.amount.toStringAsFixed(2)}',
        transaction.paymentMethod ?? '',
        transaction.description
      ];

      for (var i = 0; i < cells.length; i++) {
        final cell = sheet.cell(excel_lib.CellIndex.indexByColumnRow(
            columnIndex: i, rowIndex: row));
        cell.value = excel_lib.TextCellValue(cells[i]);
      }
      row++;
    }

    // Save file
    final bytes = excel.save();
    if (bytes == null) throw Exception('Failed to generate Excel file');

    final file = File(
        '$dirPath/transactions_${DateTime.now().millisecondsSinceEpoch}.xlsx');
    await file.writeAsBytes(bytes);
    return file.path;
  }

  Future<String> _exportToCsv(String dirPath) async {
    final csvData = StringBuffer();

    // Headers
    csvData.writeln('Date,Type,Category,Amount,Payment Method,Description');

    // Data
    for (var transaction in filteredTransactions) {
      csvData.writeln('${DateFormat('yyyy-MM-dd').format(transaction.date)},'
          '${transaction.type},'
          '${transaction.category},'
          '\$${transaction.amount.toStringAsFixed(2)},'
          '${transaction.paymentMethod ?? ''},'
          '${transaction.description}');
    }

    // Save file
    final file = File(
        '$dirPath/transactions_${DateTime.now().millisecondsSinceEpoch}.csv');
    await file.writeAsString(csvData.toString());
    return file.path;
  }

  Future<String> _exportToPdf(String dirPath) async {
    final pdf = pw.Document();

    // Add page with transaction table
    pdf.addPage(
      pw.Page(
        build: (context) => pw.TableHelper.fromTextArray(
          headers: [
            'Date',
            'Type',
            'Category',
            'Amount',
            'Payment Method',
            'Description'
          ],
          data: filteredTransactions.map((transaction) {
            return [
              DateFormat('yyyy-MM-dd').format(transaction.date),
              transaction.type,
              transaction.category,
              transaction.amount.toString(),
              transaction.paymentMethod,
              transaction.description,
            ];
          }).toList(),
          headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
          headerDecoration: const pw.BoxDecoration(color: PdfColors.grey300),
          cellAlignments: {
            0: pw.Alignment.centerLeft,
            1: pw.Alignment.center,
            2: pw.Alignment.center,
            3: pw.Alignment.centerRight,
            4: pw.Alignment.center,
            5: pw.Alignment.centerLeft,
          },
        ),
      ),
    );

    // Save file
    final file = File(
        '$dirPath/transactions_${DateTime.now().millisecondsSinceEpoch}.pdf');
    await file.writeAsBytes(await pdf.save());
    return file.path;
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          toolbarHeight: 40, // Reduced height
          title: const Text('Transactions'),
          backgroundColor: const Color(0xFF2E7D32),
          actions: [
            IconButton(
              icon: Icon(_isSelectionMode ? Icons.close : Icons.delete_outline),
              onPressed: () {
                setState(() {
                  _isSelectionMode = !_isSelectionMode;
                });
              },
            ),
            PopupMenuButton<String>(
              icon: const Icon(Icons.download),
              tooltip: 'Export Data',
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'excel',
                  child: ListTile(
                    leading: Icon(Icons.table_chart),
                    title: Text('Export as Excel'),
                  ),
                ),
                const PopupMenuItem(
                  value: 'csv',
                  child: ListTile(
                    leading: Icon(Icons.description),
                    title: Text('Export as CSV'),
                  ),
                ),
                const PopupMenuItem(
                  value: 'pdf',
                  child: ListTile(
                    leading: Icon(Icons.picture_as_pdf),
                    title: Text('Export as PDF'),
                  ),
                ),
              ],
              onSelected: (value) async {
                _exportData(value);
              },
            ),
          ],
          bottom: TabBar(
            labelColor: Colors.white,
            unselectedLabelColor: Colors.green[100],
            indicatorColor: Colors.white,
            labelStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.normal,
              fontSize: 12,
            ),
            tabs: const [
              Tab(
                text: 'Summary',
                icon: Icon(Icons.pie_chart_outline),
                height: 56,
              ),
              Tab(
                text: 'Transactions',
                icon: Icon(Icons.list_alt_rounded),
                height: 56,
              ),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            const TransactionsSummaryTab(),
            TransactionsListTab(isSelectionMode: _isSelectionMode),
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => TransactionFormDialog(
                onSave: _handleSaveTransaction,
              ),
            );
          },
          backgroundColor: const Color(0xFF2E7D32),
          child: const Icon(Icons.add),
        ),
      ),
    );
  }
}
