import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../models/cattle.dart';
import '../models/cattle_event.dart';
import '../services/cattle_event_service.dart';
import '../../Events/models/event.dart';
import '../../../widgets/loading_indicator.dart';
import '../../../widgets/empty_state.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class EventsTab extends StatefulWidget {
  final Cattle cattle;

  const EventsTab({
    Key? key,
    required this.cattle,
  }) : super(key: key);

  @override
  State<EventsTab> createState() => _EventsTabState();
}

class _EventsTabState extends State<EventsTab> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final CattleEventService _eventService = CattleEventService();
  List<CattleEvent> _events = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadEvents();
  }

  Future<void> _loadEvents() async {
    setState(() => _isLoading = true);
    try {
      final events = await _eventService.getCattleEvents(widget.cattle.id);
      setState(() {
        _events = events;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading events: $e')),
        );
      }
    }
  }

  Future<void> _showAddEventDialog() async {
    final event = await showDialog<CattleEvent>(
      context: context,
      builder: (context) => AddEventDialog(cattleId: widget.cattle.id),
    );

    if (event != null) {
      setState(() => _isLoading = true);
      try {
        await _eventService.addEvent(event);
        if (event.isRecurring && event.recurringInterval != null) {
          await _eventService.createRecurringEvent(event);
        }
        await _loadEvents();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error adding event: $e')),
          );
        }
      }
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Timeline'),
              Tab(text: 'Categories'),
              Tab(text: 'Reminders'),
            ],
          ),
          Expanded(
            child: _isLoading
                ? const LoadingIndicator()
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildTimelineTab(),
                      _buildCategoriesTab(),
                      _buildRemindersTab(),
                    ],
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddEventDialog,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTimelineTab() {
    if (_events.isEmpty) {
      return const EmptyState(
        icon: Icons.event_busy,
        message: 'No events found',
        subtitle: 'Add your first event using the + button',
      );
    }

    final sortedEvents = List<CattleEvent>.from(_events)
      ..sort((a, b) => b.date.compareTo(a.date));

    return ListView.builder(
      itemCount: sortedEvents.length,
      itemBuilder: (context, index) {
        final event = sortedEvents[index];
        return EventCard(
          event: event,
          onComplete: () => _markEventComplete(event.id),
          onDelete: () => _deleteEvent(event.id),
        );
      },
    );
  }

  Widget _buildCategoriesTab() {
    final eventsByType = <EventType, List<CattleEvent>>{};
    for (final type in EventType.values) {
      eventsByType[type] = _events.where((e) => e.type == type).toList();
    }

    return ListView.builder(
      itemCount: eventsByType.length,
      itemBuilder: (context, index) {
        final type = EventType.values[index];
        final events = eventsByType[type] ?? [];
        if (events.isEmpty) return const SizedBox.shrink();

        return ExpansionTile(
          title: Text(type.toString().split('.').last),
          children: events.map((event) => EventCard(
            event: event,
            onComplete: () => _markEventComplete(event.id),
            onDelete: () => _deleteEvent(event.id),
          )).toList(),
        );
      },
    );
  }

  Widget _buildRemindersTab() {
    final now = DateTime.now();
    final upcoming = _events.where((e) => 
      !e.isCompleted && e.date.isAfter(now)
    ).toList()..sort((a, b) => a.date.compareTo(b.date));

    final overdue = _events.where((e) =>
      !e.isCompleted && e.date.isBefore(now)
    ).toList()..sort((a, b) => b.date.compareTo(a.date));

    return ListView(
      children: [
        if (overdue.isNotEmpty) ...[
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(
              'Overdue',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
          ),
          ...overdue.map((event) => EventCard(
            event: event,
            onComplete: () => _markEventComplete(event.id),
            onDelete: () => _deleteEvent(event.id),
            isOverdue: true,
          )),
        ],
        if (upcoming.isNotEmpty) ...[
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(
              'Upcoming',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
          ),
          ...upcoming.map((event) => EventCard(
            event: event,
            onComplete: () => _markEventComplete(event.id),
            onDelete: () => _deleteEvent(event.id),
          )),
        ],
        if (overdue.isEmpty && upcoming.isEmpty)
          const EmptyState(
            icon: Icons.notifications_none,
            message: 'No reminders',
            subtitle: 'All caught up!',
          ),
      ],
    );
  }

  Future<void> _markEventComplete(String eventId) async {
    try {
      await _eventService.markEventComplete(widget.cattle.id, eventId);
      await _loadEvents();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error marking event complete: $e')),
        );
      }
    }
  }

  Future<void> _deleteEvent(String eventId) async {
    try {
      await _eventService.deleteEvent(widget.cattle.id, eventId);
      await _loadEvents();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting event: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}

class EventCard extends StatelessWidget {
  final CattleEvent event;
  final VoidCallback onComplete;
  final VoidCallback onDelete;
  final bool isOverdue;

  const EventCard({
    Key? key,
    required this.event,
    required this.onComplete,
    required this.onDelete,
    this.isOverdue = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(
          event.title,
          style: TextStyle(
            decoration: event.isCompleted ? TextDecoration.lineThrough : null,
            color: isOverdue ? Colors.red : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(event.description),
            const SizedBox(height: 4),
            Text(
              '${event.date.toString().split('.')[0]} at ${event.time.format(context)}',
              style: theme.textTheme.bodySmall,
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!event.isCompleted)
              IconButton(
                icon: const Icon(Icons.check),
                onPressed: onComplete,
                color: theme.primaryColor,
              ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: onDelete,
              color: Colors.red,
            ),
          ],
        ),
      ),
    );
  }
}

class AddEventDialog extends StatefulWidget {
  final String cattleId;

  const AddEventDialog({
    Key? key,
    required this.cattleId,
  }) : super(key: key);

  @override
  State<AddEventDialog> createState() => _AddEventDialogState();
}

class _AddEventDialogState extends State<AddEventDialog> {
  final _formKey = GlobalKey<FormState>();
  late DateTime _selectedDate;
  late TimeOfDay _selectedTime;
  String _title = '';
  String _description = '';
  EventType _type = EventType.miscellaneous;
  EventPriority _priority = EventPriority.medium;
  bool _isRecurring = false;
  int _recurringIntervalDays = 30;
  DateTime? _reminderDate;

  @override
  void initState() {
    super.initState();
    _selectedDate = DateTime.now();
    _selectedTime = TimeOfDay.now();
  }

  Future<void> _pickDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );
    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  Future<void> _pickTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
    );
    if (time != null) {
      setState(() => _selectedTime = time);
    }
  }

  Future<void> _pickReminderDate() async {
    final now = DateTime.now();
    final date = await showDatePicker(
      context: context,
      initialDate: _reminderDate ?? now,
      firstDate: now.subtract(const Duration(days: 365)),
      lastDate: _selectedDate,
    );
    if (date != null) {
      setState(() => _reminderDate = date);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Event'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                decoration: const InputDecoration(labelText: 'Title'),
                validator: (value) =>
                    value?.isEmpty ?? true ? 'Please enter a title' : null,
                onSaved: (value) => _title = value ?? '',
              ),
              TextFormField(
                decoration: const InputDecoration(labelText: 'Description'),
                maxLines: 3,
                onSaved: (value) => _description = value ?? '',
              ),
              SizedBox(height: ResponsiveSpacing.getMD(context)),
              ListTile(
                title: const Text('Date'),
                subtitle: Text(_selectedDate.toString().split(' ')[0]),
                trailing: const Icon(Icons.calendar_today),
                onTap: _pickDate,
              ),
              ListTile(
                title: const Text('Time'),
                subtitle: Text(_selectedTime.format(context)),
                trailing: const Icon(Icons.access_time),
                onTap: _pickTime,
              ),
              DropdownButtonFormField<EventType>(
                decoration: const InputDecoration(labelText: 'Event Type'),
                value: _type,
                items: EventType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.toString().split('.').last),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) setState(() => _type = value);
                },
              ),
              DropdownButtonFormField<EventPriority>(
                decoration: const InputDecoration(labelText: 'Priority'),
                value: _priority,
                items: EventPriority.values.map((priority) {
                  return DropdownMenuItem(
                    value: priority,
                    child: Text(priority.toString().split('.').last),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) setState(() => _priority = value);
                },
              ),
              SwitchListTile(
                title: const Text('Recurring Event'),
                value: _isRecurring,
                onChanged: (value) => setState(() => _isRecurring = value),
              ),
              if (_isRecurring)
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Repeat every (days)',
                    helperText: 'Enter number of days between repetitions',
                  ),
                  keyboardType: TextInputType.number,
                  initialValue: _recurringIntervalDays.toString(),
                  validator: (value) {
                    if (value == null || int.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                    return null;
                  },
                  onSaved: (value) => _recurringIntervalDays = int.parse(value!),
                ),
              ListTile(
                title: const Text('Set Reminder'),
                subtitle: Text(_reminderDate?.toString().split(' ')[0] ??
                    'No reminder set'),
                trailing: const Icon(Icons.notification_add),
                onTap: _pickReminderDate,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState?.validate() ?? false) {
              _formKey.currentState?.save();
              final event = CattleEvent(
                id: const Uuid().v4(),
                cattleId: widget.cattleId,
                title: _title,
                description: _description,
                date: _selectedDate,
                time: _selectedTime,
                type: _type,
                priority: _priority,
                reminderDate: _reminderDate,
                isRecurring: _isRecurring,
                recurringInterval: _isRecurring
                    ? Duration(days: _recurringIntervalDays)
                    : null,
              );
              Navigator.of(context).pop(event);
            }
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
