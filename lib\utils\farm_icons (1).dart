),
                  ),
                  const SizedBox(height: 16),
                  ..._alertTypes.map((type) => ListTile(
                    title: Text(type.name),
                    subtitle: Text(type.description),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Switch(
                          value: type.enabled,
                          onChanged: (value) {
                            setState(() {
                              final index = _alertTypes.indexWhere((t) => t.id == type.id);
                              _alertTypes[index] = type.copyWith(enabled: value);
                            });
                            _saveSettings();
                          },
                        ),
                        IconButton(
                          icon: const Icon(Icons.settings),
                          onPressed: type.enabled ? () => _configureAlertType(type) : null,
                        ),
                      ],
                    ),
                  )),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                