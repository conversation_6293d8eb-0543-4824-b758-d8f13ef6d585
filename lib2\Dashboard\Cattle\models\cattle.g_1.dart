// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cattle.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CattleAdapter extends TypeAdapter<Cattle> {
  @override
  final int typeId = 0;

  @override
  Cattle read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Cattle(
      id: fields[0] as String,
      tagId: fields[1] as String,
      name: fields[2] as String,
      animalTypeId: fields[3] as String,
      breedId: fields[4] as String,
      gender: fields[5] as String,
      source: fields[6] as String,
      motherTagId: fields[7] as String?,
      dateOfBirth: fields[8] as DateTime?,
      purchaseDate: fields[9] as DateTime?,
      purchasePrice: fields[10] as double?,
      weight: fields[11] as double?,
      color: fields[12] as String?,
      notes: fields[13] as String?,
      photoPath: fields[14] as String?,
      createdAt: fields[15] as DateTime,
      updatedAt: fields[16] as DateTime,
      reproductiveStatus: fields[17] as String?,
      lastHeatDate: fields[18] as DateTime?,
      isPregnant: fields[19] as bool?,
      breedingDate: fields[20] as DateTime?,
      breedingHistory: (fields[21] as List?)?.cast<BreedingRecord>(),
      offspring: (fields[22] as List?)?.cast<Cattle>(),
      category: fields[23] as String?,
      status: fields[24] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Cattle obj) {
    writer
      ..writeByte(25)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.tagId)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.animalTypeId)
      ..writeByte(4)
      ..write(obj.breedId)
      ..writeByte(5)
      ..write(obj.gender)
      ..writeByte(6)
      ..write(obj.source)
      ..writeByte(7)
      ..write(obj.motherTagId)
      ..writeByte(8)
      ..write(obj.dateOfBirth)
      ..writeByte(9)
      ..write(obj.purchaseDate)
      ..writeByte(10)
      ..write(obj.purchasePrice)
      ..writeByte(11)
      ..write(obj.weight)
      ..writeByte(12)
      ..write(obj.color)
      ..writeByte(13)
      ..write(obj.notes)
      ..writeByte(14)
      ..write(obj.photoPath)
      ..writeByte(15)
      ..write(obj.createdAt)
      ..writeByte(16)
      ..write(obj.updatedAt)
      ..writeByte(17)
      ..write(obj.reproductiveStatus)
      ..writeByte(18)
      ..write(obj.lastHeatDate)
      ..writeByte(19)
      ..write(obj.isPregnant)
      ..writeByte(20)
      ..write(obj.breedingDate)
      ..writeByte(21)
      ..write(obj.breedingHistory)
      ..writeByte(22)
      ..write(obj.offspring)
      ..writeByte(23)
      ..write(obj.category)
      ..writeByte(24)
      ..write(obj.status);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CattleAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
