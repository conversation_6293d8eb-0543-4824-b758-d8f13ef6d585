import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../Breeding/models/breeding_record.dart';
import 'report_data.dart';
import 'chart_data.dart';

class BreedingReportData extends ReportData {
  final List<BreedingRecord> records;
  final String? status;
  final String? cattleId;
  final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');
  final dateFormat = DateFormat('yyyy-MM-dd');

  BreedingReportData({
    required this.records,
    this.status,
    this.cattleId,
    super.startDate,
    super.endDate,
  });

  List<BreedingRecord> get filteredRecords {
    return records.where((record) {
      final recordDate = record.date;
      if (startDate != null && recordDate.isBefore(startDate!)) return false;
      if (endDate != null && recordDate.isAfter(endDate!)) return false;
      if (status != null && record.status != status) return false;
      if (cattleId != null && record.cattleId != cattleId) return false;
      return true;
    }).toList()
      ..sort((a, b) {
        final aDate = a.date;
        final bDate = b.date;
        return bDate.compareTo(aDate);
      });
  }

  @override
  String get reportTitle => 'Breeding Report';

  @override
  List<DataColumn> get tableColumns => const [
        DataColumn(label: Text('Date')),
        DataColumn(label: Text('Cattle ID')),
        DataColumn(label: Text('Bull ID/Type')),
        DataColumn(label: Text('Method')),
        DataColumn(label: Text('Status')),
        DataColumn(label: Text('Expected Date')),
        DataColumn(label: Text('Cost')),
      ];

  @override
  List<DataRow> get tableRows => filteredRecords.map((record) {
        final date = record.date;
        final expectedDate = record.expectedDate;

        return DataRow(
          cells: [
            DataCell(Text(dateFormat.format(date))),
            DataCell(Text(record.cattleId)),
            DataCell(Text(record.bullIdOrType)),
            DataCell(Text(record.method)),
            DataCell(Text(record.status)),
            DataCell(Text(
                expectedDate != null ? dateFormat.format(expectedDate) : '')),
            DataCell(Text(currencyFormat.format(record.cost))),
          ],
        );
      }).toList();

  @override
  Map<String, double> get summaryData {
    final records = filteredRecords;
    if (records.isEmpty) {
      return {
        'Total Breedings': 0,
        'Successful': 0,
        'Pending': 0,
        'Failed': 0,
        'Natural': 0,
        'Artificial': 0,
        'Total Cost': 0,
        'Success Rate': 0,
      };
    }

    var successful = 0;
    var pending = 0;
    var failed = 0;
    var natural = 0;
    var artificial = 0;
    var totalCost = 0.0;

    for (var record in records) {
      switch (record.status.toLowerCase()) {
        case 'successful':
          successful++;
          break;
        case 'pending':
          pending++;
          break;
        case 'failed':
          failed++;
          break;
      }

      if (record.method.toLowerCase() == 'natural') {
        natural++;
      } else {
        artificial++;
      }

      totalCost += record.cost;
    }

    final successRate =
        records.isNotEmpty ? (successful / records.length) * 100 : 0.0;

    return {
      'Total Breedings': records.length.toDouble(),
      'Successful': successful.toDouble(),
      'Pending': pending.toDouble(),
      'Failed': failed.toDouble(),
      'Natural': natural.toDouble(),
      'Artificial': artificial.toDouble(),
      'Total Cost': totalCost,
      'Success Rate': successRate,
    };
  }

  @override
  List<ChartData> get chartData {
    final Map<String, int> breedingsByStatus = {
      'Successful': 0,
      'Pending': 0,
      'Failed': 0,
    };

    for (var record in filteredRecords) {
      breedingsByStatus[record.status] =
          (breedingsByStatus[record.status] ?? 0) + 1;
    }

    return breedingsByStatus.entries.map((entry) {
      return ChartData(
        date: DateTime.now(), // Not used for pie chart
        value: entry.value.toDouble(),
        label: entry.key,
      );
    }).toList();
  }

  List<ChartData> get breedingTrend {
    final Map<DateTime, int> breedingsByDate = {};
    for (var record in filteredRecords) {
      final date =
          DateTime(record.date.year, record.date.month, record.date.day);
      breedingsByDate[date] = (breedingsByDate[date] ?? 0) + 1;
    }

    return breedingsByDate.entries.map((entry) {
      return ChartData(
        date: entry.key,
        value: entry.value.toDouble(),
        label: 'Breedings',
      );
    }).toList()
      ..sort((a, b) {
        final aDate = a.date ?? DateTime.fromMillisecondsSinceEpoch(0);
        final bDate = b.date ?? DateTime.fromMillisecondsSinceEpoch(0);
        return aDate.compareTo(bDate);
      });
  }

  Map<String, List<BreedingRecord>> get upcomingDeliveries {
    final now = DateTime.now();
    final records = filteredRecords
        .where((record) =>
            record.status.toLowerCase() == 'successful' &&
            record.expectedDate != null &&
            record.expectedDate!.isAfter(now))
        .toList();

    return {
      'Due This Week': records
          .where((r) => r.expectedDate!.difference(now).inDays <= 7)
          .toList(),
      'Due This Month': records
          .where((r) =>
              r.expectedDate!.difference(now).inDays <= 30 &&
              r.expectedDate!.difference(now).inDays > 7)
          .toList(),
      'Due Later': records
          .where((r) => r.expectedDate!.difference(now).inDays > 30)
          .toList(),
    };
  }
}
