import 'package:hive/hive.dart';
import '../models/transaction.dart';

class TransactionService {
  static const String _boxName = 'transactions';

  Future<Box<Transaction>> _getBox() async {
    if (!Hive.isBoxOpen(_boxName)) {
      await Hive.openBox<Transaction>(_boxName);
    }
    return Hive.box<Transaction>(_boxName);
  }

  Future<List<Transaction>> getTransactions() async {
    final box = await _getBox();
    return box.values.toList();
  }

  Future<void> addTransaction(Transaction transaction) async {
    final box = await _getBox();
    await box.add(transaction);
  }

  Future<void> updateTransaction(int index, Transaction transaction) async {
    final box = await _getBox();
    await box.putAt(index, transaction);
  }

  Future<void> deleteTransaction(int index) async {
    final box = await _getBox();
    await box.deleteAt(index);
  }
}
                                                                                                                