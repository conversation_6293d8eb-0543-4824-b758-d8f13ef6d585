class AlertChannel {
  final String id;
  final String name;
  final bool enabled;
  final Map<String, dynamic> config;

  AlertChannel({
    required this.id,
    required this.name,
    this.enabled = true,
    required this.config,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'enabled': enabled,
      'config': config,
    };
  }

  factory AlertChannel.fromMap(Map<String, dynamic> map) {
    return AlertChannel(
      id: map['id'] as String,
      name: map['name'] as String,
      enabled: map['enabled'] as bool? ?? true,
      config: Map<String, dynamic>.from(map['config'] as Map),
    );
  }

  AlertChannel copyWith({
    String? name,
    bool? enabled,
    Map<String, dynamic>? config,
  }) {
    return AlertChannel(
      id: id,
      name: name ?? this.name,
      enabled: enabled ?? this.enabled,
      config: config ?? Map<String, dynamic>.from(this.config),
    );
  }

  static List<AlertChannel> get defaultChannels => [
    AlertChannel(
      id: 'push',
      name: 'Push Notifications',
      config: {'token': ''},
    ),
    Al<PERSON><PERSON>han<PERSON>(
      id: 'email',
      name: 'Email',
      config: {'email': ''},
    ),
    AlertChannel(
      id: 'sms',
      name: 'SMS',
      config: {'phone': ''},
    ),
  ];
}

class AlertType {
  final String id;
  final String name;
  final String description;
  final bool enabled;
  final List<String> enabledChannels;
  final Map<String, dynamic> config;

  AlertType({
    required this.id,
    required this.name,
    required this.description,
    this.enabled = true,
    List<String>? enabledChannels,
    Map<String, dynamic>? config,
  }) : enabledChannels = enabledChannels ?? ['push'],
       config = config ?? {};

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'enabled': enabled,
      'enabledChannels': enabledChannels,
      'config': config,
    };
  }

  factory AlertType.fromMap(Map<String, dynamic> map) {
    return AlertType(
      id: map['id'] as String,
      name: map['name'] as String,
      description: map['description'] as String,
      enabled: map['enabled'] as bool? ?? true,
      enabledChannels: List<String>.from(map['enabledChannels'] as List? ?? ['push']),
      config: Map<String, dynamic>.from(map['config'] as Map? ?? {}),
    );
  }

  AlertType copyWith({
    String? name,
    String? description,
    bool? enabled,
    List<String>? enabledChannels,
    Map<String, dynamic>? config,
  }) {
    return AlertType(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      enabled: enabled ?? this.enabled,
      enabledChannels: enabledChannels ?? List<String>.from(this.enabledChannels),
      config: config ?? Map<String, dynamic>.from(this.config),
    );
  }

  static List<AlertType> get defaultAlertTypes => [
    AlertType(
      id: 'health_check',
      name: 'Health Check Reminders',
      description: 'Get notified when cattle health checks are due',
      config: {
        'days_before': 3,
        'repeat_interval': 24, // hours
      },
    ),
    AlertType(
      id: 'milk_collection',
      name: 'Milk Collection Reminders',
      description: 'Get reminders for scheduled milk collection times',
      config: {
        'morning_time': '06:00',
        'evening_time': '18:00',
        'remind_before': 30, // minutes
      },
    ),
    AlertType(
      id: 'vaccination',
      name: 'Vaccination Due',
      description: 'Get notified when vaccinations are due',
      config: {
        'days_before': 7,
        'repeat_interval': 24, // hours
      },
    ),
    AlertType(
      id: 'breeding',
      name: 'Breeding Events',
      description: 'Get notified about breeding-related events',
      config: {
        'heat_detection': true,
        'pregnancy_check': true,
        'calving_due': true,
        'days_before': 7,
      },
    ),
    AlertType(
      id: 'low_inventory',
      name: 'Low Inventory Alerts',
      description: 'Get notified when feed or medicine inventory is low',
      config: {
        'threshold_percentage': 20,
      },
    ),
    AlertType(
      id: 'abnormal_milk',
      n