#!/bin/sh
#
# An example hook script to block unannotated tags from entering.
# Called by "git receive-pack" with arguments: refname sha1-old sha1-new
#
# To enable this hook, rename this file to "update".
#
# Config
# ------
# hooks.allowunannotated
#   This boolean sets whether unannotated tags will be allowed into the
#   repository.  By default they won't be.
# hooks.allowdeletetag
#   This boolean sets whether deleting tags will be allowed in the
#   repository.  By default they won't be.
# hooks.allowmodifytag
#   This boolean sets whether a tag may be modified after creation. By default
#   it won't be.
# hooks.allowdeletebranch
#   This boolean sets whether deleting branches will be allowed in the
#   repository.  By default they won't be.
# hooks.denycreatebranch
#   This boolean sets whether remotely creating branches will be denied
#   in the repository.  By default this is allowed.
#

# --- Command line
refname="$1"
oldrev="$2"
newrev="$3"

# --- Safety check
if [ -z "$GIT_DIR" ]; then
	echo "Don't run this script from the command line." >&2
	echo " (if you want, you could supply GIT_DIR then run" >&2
	echo "  $0 <ref> <oldrev> <newrev>)" >&2
	exit 1
fi

if [ -z "$refname" -o -z "$oldrev" -o -z "$newrev" ]; then
	echo "usage: $0 <ref> <oldrev> <newrev>" >&2
	exit 1
fi

# --- Config
allowunannotated=$(git config --type=bool hooks.allowunannotated)
allowdeletebranch=$(git config --type=bool hooks.allowdeletebranch)
denycreatebranch=$(git config --type=bool hooks.denycreatebranch)
allowdeletetag=$(git config --type=bool hooks.allowdeletetag)
allowmodifytag=$(git config --type=bool hooks.allowmodifytag)

# check for no description
projectdesc=$(sed -e '1q' "$GIT_DIR/description")
case "$projectdesc" in
"Unnamed repository"* | "")
	echo "*** Project description file hasn't been set" >&2
	exit 1
	;;
esac

# --- Check types
# if $newrev is 0000...0000, it's a commit to delete a ref.
zero=$(git hash-object --stdin </dev/null | tr '[0-9a-f]' '0')
if [ "$newrev" = "$zero" ]; then
	newrev_type=delete
else
	newrev_type=$(git cat-file -t $newrev)
fi

case "$refname","$newrev_type" in
	refs/tags/*,commit)
		# un-annotated tag
		short_refname=${refname##refs/tags/}
		if [ "$allowunannotated" != "true" ]; then
			echo "*** The un-annotated tag, $short_refname, is not allowed in this repository" >&2
			echo "*** Use 'git tag [ -a | -s ]' for tags you want to propagate." >&2
			exit 1
		fi
		;;
	refs/tags/*,delete)
		# delete tag
		if [ "$allowdeletetag" != "true" ]; then
			echo "*** Deleting a tag is not allowed in this repository" >&2
			exit 1
		fi
		;;
	refs/tags/*,tag)
		# annotated tag
		if [ "$allowmodifytag" != "true" ] && git rev-parse $refname > /dev/null 2>&1
		then
			echo "*** Tag '$refname' already exists." >&2
			echo "*** Modifying a tag is not allowed in this repository." >&2
			exit 1
		fi
		;;
	refs/heads/*,commit)
		# branch
		if [ "$oldrev" = "$zero" -a "$denycreatebranch" = "true" ]; then
			echo "*** Creating a branch is not allowed in this repository" >&2
			exit 1
		fi
		;;
	refs/heads/*,delete)
		# delete branch
		if [ "$allowdeletebranch" != "true" ]; then
			echo "*** Deleting a branch is not allowed in this repository" >&2
			exit 1
		fi
		;;
	refs/remotes/*,commit)
		# tracking branch
		;;
	refs/remotes/*,delete)
		# delete tracking branch
		if [ "$allowdeletebranch" != "true" ]; then
			echo "*** Deleting a tracking branch is not allowed in this repository" >&2
			exit 1
		fi
		;;
	*)
		# Anything else (is there anything else?)
		echo "*** Update hook: unknown type of update to ref $refname of type $newrev_type" >&2
		exit 1
		;;
esac

# --- Finished
exit 0
                                                                                                                                                                                                                                                                                                                                                                                                                                                              0000000000000000000000000000000000000000 d18c40146764b3425172edd3fd7a295e0c9eafa8 ask2206230 <<EMAIL>> 1739444610 +0500	branch: Created from HEAD
d18c40146764b3425172edd3fd7a295e0c9eafa8 0e891285f66279b72ff728f5c568a179a37a16d8 ask2206230 <<EMAIL>> 1739444691 +0500	commit (merge): Fix merge conflict in README.md
0e891285f66279b72ff728f5c568a179a37a16d8 91744cbe9f1011e9a2b693a8acbce2495c12287d ask2206230 <<EMAIL>> 1739454065 +0500	commit: Version 2.1: Updated import paths and fixed issues
91744cbe9f1011e9a2b693a8acbce2495c12287d b4537d0b2fb1b131283f348c810a00f177b05732 ask2206230 <<EMAIL>> 1739596771 +0500	merge update-v2.18.0: Fast-forward
b4537d0b2fb1b131283f348c810a00f177b05732 0bbdd4e7f3ed102d695ed76dfdb363a3f1b4ef07 ask2206230 <<EMAIL>> 1739619750 +0500	commit: Fixed PDF export header alignments and styling for all sections (Transaction List, Financial Overview, Income/Expense Categories)
0bbdd4e7f3ed102d695ed76dfdb363a3f1b4ef07 146a541e37093137b19fd287465d7853ddbab089 ask2206230 <<EMAIL>> 1739619892 +0500	commit: Updated CHANGELOG.md with V2.19 changes
146a541e37093137b19fd287465d7853ddbab089 5570fee2f451b2f44c20ff77fc61867e78d4330f ask2206230 <<EMAIL>> 1739620223 +0500	commit: Restored complete version history in README.md including V2.15-V2.17
5570fee2f451b2f44c20ff77fc61867e78d4330f 2e8cb0d41eb75853ea3df2c43e46f0688798ca81 ask2206230 <<EMAIL>> 1739661190 +0500	pul