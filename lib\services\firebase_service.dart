import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import '../firebase_options.dart';

/// Firebase service for initializing and managing Firebase services
class FirebaseService {
  static final Logger _logger = Logger();
  static bool _isInitialized = false;
  
  /// Firebase Auth instance
  static FirebaseAuth get auth => FirebaseAuth.instance;
  
  /// Firestore instance
  static FirebaseFirestore get firestore => FirebaseFirestore.instance;
  
  /// Firebase Storage instance
  static FirebaseStorage get storage => FirebaseStorage.instance;
  
  /// Whether Firebase is initialized
  static bool get isInitialized => _isInitialized;
  
  /// Initialize Firebase
  static Future<bool> initialize() async {
    try {
      if (_isInitialized) {
        _logger.i('Firebase already initialized');
        return true;
      }

      _logger.i('Initializing Firebase...');

      // Check if Firebase is already initialized (by backup services or other parts)
      try {
        Firebase.app(); // This will throw if not initialized
        _logger.i('Firebase already initialized by another service');
      } catch (e) {
        // Firebase not initialized, initialize it now
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
        _logger.i('Firebase initialized with default options');
      }

      // Configure Firestore settings (only if not already configured)
      try {
        if (!kIsWeb) {
          // Use the new settings approach instead of deprecated enablePersistence
          firestore.settings = const Settings(persistenceEnabled: true);
        }
      } catch (e) {
        _logger.w('Firestore persistence already enabled or failed: $e');
      }

      // Configure Auth settings
      try {
        await auth.setSettings(
          appVerificationDisabledForTesting: kDebugMode,
          forceRecaptchaFlow: false,
        );
      } catch (e) {
        _logger.w('Auth settings configuration failed: $e');
      }

      _isInitialized = true;
      _logger.i('Firebase service initialized successfully');
      return true;
    } catch (e) {
      _logger.e('Failed to initialize Firebase service: $e');
      return false;
    }
  }
  

  
  /// Check if user is signed in
  static bool get isUserSignedIn => auth.currentUser != null;
  
  /// Get current user
  static User? get currentUser => auth.currentUser;
  
  /// Get current user ID
  static String? get currentUserId => auth.currentUser?.uid;
  
  /// Sign out current user
  static Future<void> signOut() async {
    try {
      await auth.signOut();
      _logger.i('User signed out successfully');
    } catch (e) {
      _logger.e('Error signing out: $e');
      rethrow;
    }
  }
  
  /// Delete current user account
  static Future<void> deleteAccount() async {
    try {
      final user = auth.currentUser;
      if (user != null) {
        await user.delete();
        _logger.i('User account deleted successfully');
      }
    } catch (e) {
      _logger.e('Error deleting account: $e');
      rethrow;
    }
  }
  
  /// Listen to authentication state changes
  static Stream<User?> get authStateChanges => auth.authStateChanges();
  
  /// Listen to user changes (including profile updates)
  static Stream<User?> get userChanges => auth.userChanges();
  
  /// Get user document reference
  static DocumentReference<Map<String, dynamic>> getUserDocument(String userId) {
    return firestore.collection('users').doc(userId);
  }
  
  /// Get user data from Firestore
  static Future<Map<String, dynamic>?> getUserData(String userId) async {
    try {
      final doc = await getUserDocument(userId).get();
      return doc.data();
    } catch (e) {
      _logger.e('Error getting user data: $e');
      return null;
    }
  }
  
  /// Update user data in Firestore
  static Future<void> updateUserData(String userId, Map<String, dynamic> data) async {
    try {
      await getUserDocument(userId).set(data, SetOptions(merge: true));
      _logger.i('User data updated successfully');
    } catch (e) {
      _logger.e('Error updating user data: $e');
      rethrow;
    }
  }
  
  /// Create user document in Firestore
  static Future<void> createUserDocument(String userId, Map<String, dynamic> userData) async {
    try {
      await getUserDocument(userId).set({
        ...userData,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      _logger.i('User document created successfully');
    } catch (e) {
      _logger.e('Error creating user document: $e');
      rethrow;
    }
  }

  /// Create or update user document in Firestore (preserves existing data)
  static Future<void> createOrUpdateUserDocument(String userId, Map<String, dynamic> userData, {Map<String, dynamic>? defaultsForNewUser}) async {
    try {
      final existingDoc = await getUserDocument(userId).get();

      if (existingDoc.exists) {
        // Document exists - merge with existing data
        await getUserDocument(userId).set({
          ...userData,
          'updatedAt': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
        _logger.i('User document updated successfully (merged with existing data)');
      } else {
        // Document doesn't exist - create new one with defaults
        final newUserData = {
          ...userData,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        };

        // Add defaults for new users if provided
        if (defaultsForNewUser != null) {
          for (final entry in defaultsForNewUser.entries) {
            if (!newUserData.containsKey(entry.key)) {
              newUserData[entry.key] = entry.value;
            }
          }
        }

        await getUserDocument(userId).set(newUserData);
        _logger.i('User document created successfully with defaults');
      }
    } catch (e) {
      _logger.e('Error creating/updating user document: $e');
      rethrow;
    }
  }
  
  /// Check if user document exists
  static Future<bool> userDocumentExists(String userId) async {
    try {
      final doc = await getUserDocument(userId).get();
      return doc.exists;
    } catch (e) {
      _logger.e('Error checking user document: $e');
      return false;
    }
  }

  /// Get user farms collection reference
  static CollectionReference<Map<String, dynamic>> getUserFarmsCollection(String userId) {
    return firestore.collection('users').doc(userId).collection('farms');
  }

  /// Get user cattle collection reference
  static CollectionReference<Map<String, dynamic>> getUserCattleCollection(String userId) {
    return firestore.collection('users').doc(userId).collection('cattle');
  }

  /// Get user farm data from Firestore
  static Future<List<Map<String, dynamic>>> getUserFarms(String userId) async {
    try {
      final snapshot = await getUserFarmsCollection(userId).get();
      return snapshot.docs.map((doc) => {'id': doc.id, ...doc.data()}).toList();
    } catch (e) {
      _logger.e('Error getting user farms: $e');
      return [];
    }
  }

  /// Get user cattle data from Firestore
  static Future<List<Map<String, dynamic>>> getUserCattle(String userId) async {
    try {
      final snapshot = await getUserCattleCollection(userId).get();
      return snapshot.docs.map((doc) => {'id': doc.id, ...doc.data()}).toList();
    } catch (e) {
      _logger.e('Error getting user cattle: $e');
      return [];
    }
  }

  /// Save farm data to Firestore with ownership validation
  static Future<void> saveFarmToCloud(String userId, Map<String, dynamic> farmData) async {
    try {
      final farmId = farmData['farmBusinessId'] ?? farmData['id'];
      if (farmId == null) throw Exception('Farm ID is required');

      // Ensure ownership fields are set correctly
      final enhancedFarmData = {
        ...farmData,
        'ownerUserId': farmData['ownerUserId'] ?? userId, // Ensure owner is set
        'createdByUserId': farmData['createdByUserId'] ?? userId, // Ensure creator is set
        'updatedAt': FieldValue.serverTimestamp(),
        'syncedAt': FieldValue.serverTimestamp(), // Track when it was synced
      };

      await getUserFarmsCollection(userId).doc(farmId).set(
        enhancedFarmData,
        SetOptions(merge: true)
      );

      _logger.i('Farm data saved to cloud: $farmId (Owner: ${enhancedFarmData['ownerUserId']})');
    } catch (e) {
      _logger.e('Error saving farm to cloud: $e');
      rethrow;
    }
  }

  /// Save cattle data to Firestore
  static Future<void> saveCattleToCloud(String userId, Map<String, dynamic> cattleData) async {
    try {
      final cattleId = cattleData['businessId'] ?? cattleData['id'];
      if (cattleId == null) throw Exception('Cattle ID is required');

      await getUserCattleCollection(userId).doc(cattleId).set({
        ...cattleData,
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
      _logger.i('Cattle data saved to cloud: $cattleId');
    } catch (e) {
      _logger.e('Error saving cattle to cloud: $e');
      rethrow;
    }
  }

  /// Check if user has existing data in Firestore
  static Future<bool> hasExistingUserData(String userId) async {
    try {
      final userDoc = await getUserDocument(userId).get();
      if (!userDoc.exists) return false;

      // Check if user has farms or cattle data
      final farmsSnapshot = await getUserFarmsCollection(userId).limit(1).get();
      final cattleSnapshot = await getUserCattleCollection(userId).limit(1).get();

      return farmsSnapshot.docs.isNotEmpty || cattleSnapshot.docs.isNotEmpty;
    } catch (e) {
      _logger.e('Error checking existing user data: $e');
      return false;
    }
  }

  /// Get user farm statistics from cloud
  static Future<Map<String, dynamic>> getUserFarmStats(String userId) async {
    try {
      final farmsSnapshot = await getUserFarmsCollection(userId).get();
      final cattleSnapshot = await getUserCattleCollection(userId).get();

      final totalFarms = farmsSnapshot.docs.length;
      final totalCattle = cattleSnapshot.docs.length;

      // Calculate farm-specific stats
      int totalCapacity = 0;
      int activeFarms = 0;
      int sharedFarms = 0;

      for (final farmDoc in farmsSnapshot.docs) {
        final farmData = farmDoc.data();
        totalCapacity += (farmData['capacity'] as int?) ?? 0;
        if (farmData['isActive'] == true) activeFarms++;
        if (farmData['isShared'] == true) sharedFarms++;
      }

      return {
        'totalFarms': totalFarms,
        'totalCattle': totalCattle,
        'totalCapacity': totalCapacity,
        'activeFarms': activeFarms,
        'sharedFarms': sharedFarms,
        'lastSyncAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      _logger.e('Error getting user farm stats: $e');
      return {
        'totalFarms': 0,
        'totalCattle': 0,
        'totalCapacity': 0,
        'activeFarms': 0,
        'sharedFarms': 0,
        'error': e.toString(),
      };
    }
  }

  /// Delete a farm from cloud
  static Future<void> deleteFarmFromCloud(String userId, String farmId) async {
    try {
      // Delete the farm document
      await getUserFarmsCollection(userId).doc(farmId).delete();

      // Delete associated cattle
      final cattleQuery = getUserCattleCollection(userId)
          .where('farmBusinessId', isEqualTo: farmId);
      final cattleSnapshot = await cattleQuery.get();

      final batch = firestore.batch();
      for (final cattleDoc in cattleSnapshot.docs) {
        batch.delete(cattleDoc.reference);
      }
      await batch.commit();

      _logger.i('Farm and associated data deleted from cloud: $farmId');
    } catch (e) {
      _logger.e('Error deleting farm from cloud: $e');
      rethrow;
    }
  }
  
  /// Get server timestamp
  static FieldValue get serverTimestamp => FieldValue.serverTimestamp();
  
  /// Batch write operations
  static WriteBatch batch() => firestore.batch();
  
  /// Run transaction
  static Future<T> runTransaction<T>(
    TransactionHandler<T> updateFunction, {
    Duration timeout = const Duration(seconds: 30),
  }) {
    return firestore.runTransaction(updateFunction, timeout: timeout);
  }
}
