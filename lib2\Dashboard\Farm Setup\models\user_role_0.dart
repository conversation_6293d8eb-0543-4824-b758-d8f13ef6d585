import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import '../../Breeding/models/pregnancy_record.dart';
import '../models/pregnancy_report_data.dart';
import '../report_tabs/pregnancy_summary_tab.dart';
import '../report_tabs/pregnancy_details_tab.dart';
import 'package:intl/intl.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class PregnanciesReportScreen extends StatefulWidget {
  const PregnanciesReportScreen({Key? key}) : super(key: key);

  @override
  PregnanciesReportScreenState createState() => PregnanciesReportScreenState();
}

class PregnanciesReportScreenState extends State<PregnanciesReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PregnancyReportData reportData;
  DateTime? startDate;
  DateTime? endDate;
  String? selectedStage;
  bool isLoading = true;
  String? errorMessage;

  final List<String> stages = [
    'First Trimester',
    'Second Trimester',
    'Third Trimester',
    'Due'
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      final pregnancyBox = await Hive.openBox<PregnancyRecord>('pregnancy_records');

      setState(() {
        reportData = PregnancyReportData(
          pregnancies: pregnancyBox.values.toList(),
          stage: selectedStage,
          startDate: startDate ?? DateTime.now().subtract(const Duration(days: 365)),
          endDate: endDate ?? DateTime.now(),
        );
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load data: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pregnancy Report'),
        backgroundColor: const Color(0xFF2E7D32),
  