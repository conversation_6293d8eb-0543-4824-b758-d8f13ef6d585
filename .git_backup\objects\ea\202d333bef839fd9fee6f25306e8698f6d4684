"name": "http",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.3.0",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "http_multi_server",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_multi_server-3.2.2",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "http_parser",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "image",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image-4.3.0",
      "packageUri": "lib/",
      "languageVersion": "2.15"
    },
    {
      "name": "image_picker",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker-1.1.2",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "image_picker_android",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+21",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "image_picker_for_web",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "image_picker_ios",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+2",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "image_picker_linux",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+1",
      "packageUri": "lib/",
      "languageVersion": "2.19"
    },
    {
      "name": "image_picker_macos",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+2",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "image_picker_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.1",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "image_picker_windows",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1",
      "packageUri": "lib/",
      "languageVersion": "2.19"
    },
    {
      "name": "intl",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.19.0",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "io",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/io-1.0.5",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "jiffy",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/jiffy-6.3.2",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "js",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.6.7",
      "packageUri": "lib/",
      "languageVersion": "2.19"
    },
    {
      "name": "json_annotation",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "leak_tracker",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.7",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "leak_tracker_flutter_testing",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.8",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "leak_tracker_testing",
      "rootUri": "file:///C:/Users/<USER>