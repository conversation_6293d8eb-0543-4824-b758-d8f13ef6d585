> addWatermark(pw.Document document, String watermarkText) async {
    // Create a new document
    final newDocument = pw.Document();

    // Add a single page with watermark
    newDocument.addPage(
      pw.Page(
        build: (context) {
          return pw.Stack(
            children: [
              // Watermark
              pw.Center(
                child: pw.Transform.rotate(
                  angle: -0.5,
                  child: pw.Text(
  