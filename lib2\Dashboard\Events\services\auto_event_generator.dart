import 'package:logging/logging.dart';
import 'package:uuid/uuid.dart';
import '../models/event_isar.dart';
import '../../../services/database/database_helper.dart';

/// Service for automatically generating events based on module activities
class AutoEventGenerator {
  static final Logger _logger = Logger('AutoEventGenerator');
  static AutoEventGenerator? _instance;
  
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  
  // Singleton pattern
  static AutoEventGenerator get instance {
    _instance ??= AutoEventGenerator._internal();
    return _instance!;
  }
  
  AutoEventGenerator._internal();
  
  /// Generate pregnancy check event after breeding
  Future<void> generatePregnancyCheckEvent(String cattleId, Map<String, dynamic> breedingData) async {
    try {
      // Validate cattle ID first
      if (cattleId.isEmpty) {
        _logger.warning('Cannot generate pregnancy check: Empty cattle ID');
        return;
      }

      // Verify cattle exists in database
      final cattle = await _dbHelper.cattleHandler.getCattleById(cattleId);
      if (cattle == null) {
        _logger.warning('Cannot generate pregnancy check: Cattle with ID $cattleId not found');
        return;
      }

      final breedingDate = breedingData['date'] as DateTime?;
      if (breedingDate == null) {
        _logger.warning('Cannot generate pregnancy check: No breeding date provided');
        return;
      }

      _logger.info('Generating pregnancy check for cattle ${cattle.tagId} (ID: $cattleId)');

      // Schedule pregnancy check 30-45 days after breeding
      final pregnancyCheckDate = breedingDate.add(const Duration(days: 35));

      final event = EventIsar()
        ..businessId = 'auto_pregnancy_check_${const Uuid().v4()}'
        ..cattleId = cattleId
        ..title = 'Pregnancy Check'
        ..type = EventTypeEmbedded()
        ..priority = EventPriority.high
        ..eventDate = pregnancyCheckDate
        ..notes = 'Auto-generated pregnancy check for ${cattle.tagId} after breeding on ${breedingDate.toIso8601String().split('T')[0]}'
        ..isAutoGenerated = true
        ..source = EventSource.autoGenerated
        ..sourceModule = 'breeding'
        ..relatedRecordId = breedingData['recordId'] as String?
        ..reminderMinutes = [1440, 720] // 1 day and 12 hours before
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      event.type!.fromEventType(EventType.pregnancyCheck);

      await _dbHelper.eventsHandler.addEvent(event);
      _logger.info('Generated pregnancy check event for cattle ${cattle.tagId} (ID: $cattleId)');
    } catch (e) {
      _logger.severe('Error generating pregnancy check event: $e');
    }
  }
  
  /// Generate expected calving event
  Future<void> generateExpectedCalvingEvent(String cattleId, Map<String, dynamic> breedingData) async {
    try {
      final breedingDate = breedingData['date'] as DateTime?;
      if (breedingDate == null) return;
      
      // Calculate expected calving date (approximately 280 days for cattle)
      final expectedCalvingDate = breedingDate.add(const Duration(days: 280));
      
      final event = EventIsar()
        ..businessId = 'auto_calving_${const Uuid().v4()}'
        ..cattleId = cattleId
        ..title = 'Expected Calving'
        ..type = EventTypeEmbedded()
        ..priority = EventPriority.critical
        ..eventDate = expectedCalvingDate
        ..notes = 'Auto-generated expected calving date based on breeding on ${breedingDate.toIso8601String().split('T')[0]}'
        ..isAutoGenerated = true
        ..source = EventSource.autoGenerated
        ..sourceModule = 'breeding'
        ..relatedRecordId = breedingData['recordId'] as String?
        ..reminderMinutes = [10080, 2880, 1440] // 1 week, 2 days, 1 day before
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();
      
      event.type!.fromEventType(EventType.calving);
      
      await _dbHelper.eventsHandler.addEvent(event);
      _logger.info('Generated expected calving event for cattle $cattleId');
    } catch (e) {
      _logger.severe('Error generating expected calving event: $e');
    }
  }
  
  /// Generate follow-up health check event
  Future<void> generateFollowUpHealthEvent(String cattleId, Map<String, dynamic> healthData) async {
    try {
      final healthType = healthData['type'] as String?;
      final healthDate = healthData['date'] as DateTime?;
      
      if (healthDate == null) return;
      
      DateTime followUpDate;
      String followUpTitle;
      EventPriority priority = EventPriority.medium;

      // Determine follow-up schedule based on health record type
      switch (healthType?.toLowerCase()) {
        case 'vaccination':
          followUpDate = healthDate.add(const Duration(days: 365)); // Annual
          followUpTitle = 'Annual Vaccination';
          priority = EventPriority.high;
          break;
        case 'treatment':
          followUpDate = healthDate.add(const Duration(days: 14)); // 2 weeks
          followUpTitle = 'Treatment Follow-up';
          priority = EventPriority.high;
          break;
        case 'health check':
          followUpDate = healthDate.add(const Duration(days: 180)); // 6 months
          followUpTitle = 'Routine Health Check';
          priority = EventPriority.medium;
          break;
        case 'deworming':
          followUpDate = healthDate.add(const Duration(days: 90)); // 3 months
          followUpTitle = 'Deworming';
          priority = EventPriority.medium;
          break;
        default:
          return; // No follow-up needed
      }
      
      final event = EventIsar()
        ..businessId = 'auto_followup_${const Uuid().v4()}'
        ..cattleId = cattleId
        ..title = followUpTitle
        ..type = EventTypeEmbedded()
        ..priority = priority
        ..eventDate = followUpDate
        ..notes = 'Auto-generated follow-up for $healthType on ${healthDate.toIso8601String().split('T').first}'
        ..isAutoGenerated = true
        ..source = EventSource.autoGenerated
        ..sourceModule = 'health'
        ..relatedRecordId = healthData['recordId'] as String?
        ..reminderMinutes = [2880, 1440] // 2 days and 1 day before
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();
      
      // Set appropriate event type
      switch (healthType?.toLowerCase()) {
        case 'vaccination':
          event.type!.fromEventType(EventType.vaccination);
          break;
        case 'treatment':
          event.type!.fromEventType(EventType.treatment);
          break;
        case 'deworming':
          event.type!.fromEventType(EventType.deworming);
          break;
        default:
          event.type!.fromEventType(EventType.healthCheckup);
      }
      
      await _dbHelper.eventsHandler.addEvent(event);
      _logger.info('Generated follow-up health event for cattle $cattleId');
    } catch (e) {
      _logger.severe('Error generating follow-up health event: $e');
    }
  }
  
  /// Generate initial health check for new cattle
  Future<void> generateInitialHealthCheckEvent(String cattleId) async {
    try {
      // Validate cattle ID first
      if (cattleId.isEmpty) {
        _logger.warning('Cannot generate initial health check: Empty cattle ID');
        return;
      }

      // Verify cattle exists in database
      final cattle = await _dbHelper.cattleHandler.getCattleById(cattleId);
      if (cattle == null) {
        _logger.warning('Cannot generate initial health check: Cattle with ID $cattleId not found');
        return;
      }

      _logger.info('Generating initial health check for cattle ${cattle.tagId} (ID: $cattleId)');

      // Schedule initial health check 7 days after cattle registration
      final healthCheckDate = DateTime.now().add(const Duration(days: 7));

      final event = EventIsar()
        ..businessId = 'auto_initial_health_${const Uuid().v4()}'
        ..cattleId = cattleId
        ..title = 'Initial Health Check'
        ..type = EventTypeEmbedded()
        ..priority = EventPriority.high
        ..eventDate = healthCheckDate
        ..notes = 'Auto-generated initial health check for new cattle ${cattle.tagId}'
        ..isAutoGenerated = true
        ..source = EventSource.autoGenerated
        ..sourceModule = 'cattle'
        ..reminderMinutes = [1440, 720] // 1 day and 12 hours before
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      event.type!.fromEventType(EventType.healthCheckup);

      await _dbHelper.eventsHandler.addEvent(event);
      _logger.info('Generated initial health check event for cattle ${cattle.tagId} (ID: $cattleId)');
    } catch (e) {
      _logger.severe('Error generating initial health check event: $e');
    }
  }
  
  /// Generate vaccination schedule for new cattle
  Future<void> generateVaccinationSchedule(String cattleId) async {
    try {
      // Validate cattle ID first
      if (cattleId.isEmpty) {
        _logger.warning('Cannot generate vaccination schedule: Empty cattle ID');
        return;
      }

      // Verify cattle exists in database
      final cattle = await _dbHelper.cattleHandler.getCattleById(cattleId);
      if (cattle == null) {
        _logger.warning('Cannot generate vaccination schedule: Cattle with ID $cattleId not found');
        return;
      }

      _logger.info('Generating vaccination schedule for cattle ${cattle.tagId} (ID: $cattleId)');

      final now = DateTime.now();

      // Basic vaccination schedule
      final vaccinations = [
        {'name': 'Primary Vaccination', 'days': 14, 'priority': EventPriority.high},
        {'name': 'Booster Vaccination', 'days': 45, 'priority': EventPriority.high},
        {'name': 'Annual Vaccination', 'days': 365, 'priority': EventPriority.medium},
      ];

      for (final vaccination in vaccinations) {
        final vaccinationDate = now.add(Duration(days: vaccination['days'] as int));

        final event = EventIsar()
          ..businessId = 'auto_vaccination_${const Uuid().v4()}'
          ..cattleId = cattleId
          ..title = vaccination['name'] as String
          ..type = EventTypeEmbedded()
          ..priority = vaccination['priority'] as EventPriority
          ..eventDate = vaccinationDate
          ..notes = 'Auto-generated vaccination schedule for new cattle ${cattle.tagId}'
          ..isAutoGenerated = true
          ..source = EventSource.autoGenerated
          ..sourceModule = 'cattle'
          ..reminderMinutes = [2880, 1440] // 2 days and 1 day before
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();

        event.type!.fromEventType(EventType.vaccination);

        await _dbHelper.eventsHandler.addEvent(event);
      }
      
      _logger.info('Generated vaccination schedule for cattle $cattleId');
    } catch (e) {
      _logger.severe('Error generating vaccination schedule: $e');
    }
  }
  
  /// Generate milk recording reminders
  Future<void> generateMilkRecordingReminders(String cattleId) async {
    try {
      final now = DateTime.now();
      
      // Generate daily milk recording reminders for the next 30 days
      for (int i = 1; i <= 30; i++) {
        final reminderDate = now.add(Duration(days: i));
        
        final event = EventIsar()
          ..businessId = 'auto_milk_reminder_${cattleId}_$i'
          ..cattleId = cattleId
          ..title = 'Milk Recording Reminder'
          ..type = EventTypeEmbedded()
          ..priority = EventPriority.low
          ..eventDate = reminderDate
          ..notes = 'Auto-generated daily milk recording reminder'
          ..isAutoGenerated = true
          ..source = EventSource.autoGenerated
          ..sourceModule = 'milk'
          ..reminderMinutes = [60] // 1 hour before
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();
        
        event.type!.fromEventType(EventType.milkRecording);
        
        await _dbHelper.eventsHandler.addEvent(event);
      }
      
      _logger.info('Generated milk recording reminders for cattle $cattleId');
    } catch (e) {
      _logger.severe('Error generating milk recording reminders: $e');
    }
  }
  
  /// Generate weight measurement reminders
  Future<void> generateWeightMeasurementReminders(String cattleId) async {
    try {
      final now = DateTime.now();
      
      // Generate monthly weight measurement reminders for the next 12 months
      for (int i = 1; i <= 12; i++) {
        final reminderDate = DateTime(now.year, now.month + i, now.day);
        
        final event = EventIsar()
          ..businessId = 'auto_weight_reminder_${cattleId}_$i'
          ..cattleId = cattleId
          ..title = 'Weight Measurement'
          ..type = EventTypeEmbedded()
          ..priority = EventPriority.medium
          ..eventDate = reminderDate
          ..notes = 'Auto-generated monthly weight measurement reminder'
          ..isAutoGenerated = true
          ..source = EventSource.autoGenerated
          ..sourceModule = 'weight'
          ..reminderMinutes = [1440] // 1 day before
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();
        
        event.type!.fromEventType(EventType.weightMeasurement);
        
        await _dbHelper.eventsHandler.addEvent(event);
      }
      
      _logger.info('Generated weight measurement reminders for cattle $cattleId');
    } catch (e) {
      _logger.severe('Error generating weight measurement reminders: $e');
    }
  }
  
  /// Generate heat cycle prediction events
  Future<void> generateHeatCyclePredictions(String cattleId, DateTime lastHeatDate) async {
    try {
      // Cattle heat cycle is typically 21 days
      final nextHeatDate = lastHeatDate.add(const Duration(days: 21));
      
      final event = EventIsar()
        ..businessId = 'auto_heat_cycle_${const Uuid().v4()}'
        ..cattleId = cattleId
        ..title = 'Expected Heat Cycle'
        ..type = EventTypeEmbedded()
        ..priority = EventPriority.high
        ..eventDate = nextHeatDate
        ..notes = 'Auto-generated heat cycle prediction based on last heat on ${lastHeatDate.toIso8601String().split('T').first}'
        ..isAutoGenerated = true
        ..source = EventSource.autoGenerated
        ..sourceModule = 'breeding'
        ..reminderMinutes = [1440, 720] // 1 day and 12 hours before
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();
      
      event.type!.fromEventType(EventType.heatCycle);
      
      await _dbHelper.eventsHandler.addEvent(event);
      _logger.info('Generated heat cycle prediction for cattle $cattleId');
    } catch (e) {
      _logger.severe('Error generating heat cycle prediction: $e');
    }
  }
  
  /// Generate dry-off event before expected calving
  Future<void> generateDryOffEvent(String cattleId, DateTime expectedCalvingDate) async {
    try {
      // Dry off 60 days before expected calving
      final dryOffDate = expectedCalvingDate.subtract(const Duration(days: 60));
      
      final event = EventIsar()
        ..businessId = 'auto_dry_off_${const Uuid().v4()}'
        ..cattleId = cattleId
        ..title = 'Dry Off'
        ..type = EventTypeEmbedded()
        ..priority = EventPriority.high
        ..eventDate = dryOffDate
        ..notes = 'Auto-generated dry-off event 60 days before expected calving on ${expectedCalvingDate.toIso8601String().split('T')[0]}'
        ..isAutoGenerated = true
        ..source = EventSource.autoGenerated
        ..sourceModule = 'breeding'
        ..reminderMinutes = [2880, 1440] // 2 days and 1 day before
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();
      
      event.type!.fromEventType(EventType.dryOff);
      
      await _dbHelper.eventsHandler.addEvent(event);
      _logger.info('Generated dry-off event for cattle $cattleId');
    } catch (e) {
      _logger.severe('Error generating dry-off event: $e');
    }
  }
}
