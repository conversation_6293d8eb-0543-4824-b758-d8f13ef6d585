import 'package:flutter/material.dart';
import '../../../routes/app_routes.dart';

class MilkScreen extends StatelessWidget {
  const MilkScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Milk Production'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.milkReport,
            ),
            tooltip: 'View Milk Reports',
          ),
        ],
      ),
      body: const Center(
        child: Text('Milk Production Screen'),
      ),
    );
  }
}
