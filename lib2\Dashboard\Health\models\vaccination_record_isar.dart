import 'package:isar/isar.dart';
import 'package:json_annotation/json_annotation.dart';

part 'vaccination_record_isar.g.dart';

/// Represents a vaccination record in the Isar database
@collection
@JsonSerializable()
class VaccinationRecordIsar {
  factory VaccinationRecordIsar.fromJson(Map<String, dynamic> json) =>
      _$VaccinationRecordIsarFromJson(json);
  Map<String, dynamic> toJson() => _$VaccinationRecordIsarToJson(this);

  /// Isar database ID
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) for the vaccination record - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// ID of the farm this record belongs to
  @Index()
  String? farmBusinessId;

  /// ID of the cattle this vaccination record is for
  @Index()
  String? cattleBusinessId;

  /// Tag ID of the cattle for quick reference
  @Index()
  String? cattleTagId;

  /// Name of the cattle for quick reference
  String? cattleName;

  /// Name of the vaccine
  String? vaccineName;

  /// Type/category of vaccine (based on animal type)
  @Index()
  String? vaccineType;

  /// Manufacturer of the vaccine
  String? manufacturer;

  /// Batch/lot number of the vaccine
  String? batchNumber;

  /// Date of vaccination
  DateTime? vaccinationDate;

  /// Date when the vaccine expires
  DateTime? expiryDate;

  /// Dosage amount administered
  double? dosageAmount;

  /// Dosage unit (ml, cc, doses)
  String? dosageUnit;

  /// Route of administration (Intramuscular, Subcutaneous, Oral, Nasal)
  String? route;

  /// Site of injection (Left neck, Right neck, Left hip, Right hip)
  String? injectionSite;

  /// Veterinarian who administered the vaccine
  String? veterinarian;

  /// Person who administered the vaccine
  String? administeredBy;

  /// Cost of the vaccination
  double? cost;

  /// Supplier or clinic where vaccine was obtained
  String? supplier;

  /// Next vaccination due date
  DateTime? nextDueDate;

  /// Frequency of vaccination (Annual, Semi-annual, One-time, As needed)
  String? frequency;

  /// Whether this is a booster shot
  bool isBooster = false;

  /// Whether this is a core vaccine (essential) or non-core (optional)
  bool isCoreVaccine = true;

  /// Reaction observed after vaccination
  String? reaction;

  /// Severity of reaction (None, Mild, Moderate, Severe)
  String? reactionSeverity;

  /// Temperature after vaccination (if monitored)
  double? postVaccinationTemperature;

  /// Additional notes about the vaccination
  String? notes;

  /// Whether a reminder is set for next vaccination
  bool reminderSet = false;

  /// Whether the vaccination is part of a vaccination program
  String? vaccinationProgram;

  /// Series number if part of a multi-dose series
  int? seriesNumber;

  /// Total doses in the series
  int? totalDosesInSeries;

  /// Whether the vaccination series is complete
  bool seriesComplete = false;

  /// Withdrawal period for milk (in days)
  int? milkWithdrawalDays;

  /// Withdrawal period for meat (in days)
  int? meatWithdrawalDays;

  /// Date when milk withdrawal period ends
  DateTime? milkWithdrawalEndDate;

  /// Date when meat withdrawal period ends
  DateTime? meatWithdrawalEndDate;

  /// Certificate number if applicable
  String? certificateNumber;

  /// Date when the record was created
  DateTime? createdAt;

  /// Date when the record was last updated
  DateTime? updatedAt;

  /// User who created the record
  String? createdBy;

  /// User who last updated the record
  String? updatedBy;

  /// Default constructor
  VaccinationRecordIsar();

  /// Generate a business ID for the vaccination record
  static String generateBusinessId(String cattleId, DateTime vaccinationDate) {
    final timestamp = vaccinationDate.millisecondsSinceEpoch;
    return 'vaccination_${cattleId}_$timestamp';
  }

  /// Factory constructor for creating a new vaccination record
  factory VaccinationRecordIsar.create({
    required String cattleBusinessId,
    required String cattleTagId,
    required String cattleName,
    required String farmBusinessId,
    required String vaccineName,
    String? vaccineType,
    String? manufacturer,
    String? batchNumber,
    required DateTime vaccinationDate,
    DateTime? expiryDate,
    required double dosageAmount,
    required String dosageUnit,
    String? route,
    String? injectionSite,
    String? veterinarian,
    String? administeredBy,
    double? cost,
    String? supplier,
    DateTime? nextDueDate,
    String? frequency,
    bool isBooster = false,
    bool isCoreVaccine = true,
    String? reaction,
    String? reactionSeverity,
    double? postVaccinationTemperature,
    String? notes,
    bool reminderSet = false,
    String? vaccinationProgram,
    int? seriesNumber,
    int? totalDosesInSeries,
    bool seriesComplete = false,
    int? milkWithdrawalDays,
    int? meatWithdrawalDays,
    String? certificateNumber,
    String? createdBy,
  }) {
    // Calculate withdrawal end dates
    DateTime? milkWithdrawalEnd;
    DateTime? meatWithdrawalEnd;
    if (milkWithdrawalDays != null) {
      milkWithdrawalEnd = vaccinationDate.add(Duration(days: milkWithdrawalDays));
    }
    if (meatWithdrawalDays != null) {
      meatWithdrawalEnd = vaccinationDate.add(Duration(days: meatWithdrawalDays));
    }

    final record = VaccinationRecordIsar()
      ..businessId = generateBusinessId(cattleBusinessId, vaccinationDate)
      ..farmBusinessId = farmBusinessId
      ..cattleBusinessId = cattleBusinessId
      ..cattleTagId = cattleTagId
      ..cattleName = cattleName
      ..vaccineName = vaccineName
      ..vaccineType = vaccineType
      ..manufacturer = manufacturer
      ..batchNumber = batchNumber
      ..vaccinationDate = vaccinationDate
      ..expiryDate = expiryDate
      ..dosageAmount = dosageAmount
      ..dosageUnit = dosageUnit
      ..route = route
      ..injectionSite = injectionSite
      ..veterinarian = veterinarian
      ..administeredBy = administeredBy
      ..cost = cost
      ..supplier = supplier
      ..nextDueDate = nextDueDate
      ..frequency = frequency
      ..isBooster = isBooster
      ..isCoreVaccine = isCoreVaccine
      ..reaction = reaction
      ..reactionSeverity = reactionSeverity
      ..postVaccinationTemperature = postVaccinationTemperature
      ..notes = notes
      ..reminderSet = reminderSet
      ..vaccinationProgram = vaccinationProgram
      ..seriesNumber = seriesNumber
      ..totalDosesInSeries = totalDosesInSeries
      ..seriesComplete = seriesComplete
      ..milkWithdrawalDays = milkWithdrawalDays
      ..meatWithdrawalDays = meatWithdrawalDays
      ..milkWithdrawalEndDate = milkWithdrawalEnd
      ..meatWithdrawalEndDate = meatWithdrawalEnd
      ..certificateNumber = certificateNumber
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now()
      ..createdBy = createdBy;

    return record;
  }

  /// Create a copy with updated values
  VaccinationRecordIsar copyWith({
    String? businessId,
    String? farmBusinessId,
    String? cattleBusinessId,
    String? cattleTagId,
    String? cattleName,
    String? vaccineName,
    String? vaccineType,
    String? manufacturer,
    String? batchNumber,
    DateTime? vaccinationDate,
    DateTime? expiryDate,
    double? dosageAmount,
    String? dosageUnit,
    String? route,
    String? injectionSite,
    String? veterinarian,
    String? administeredBy,
    double? cost,
    String? supplier,
    DateTime? nextDueDate,
    String? frequency,
    bool? isBooster,
    bool? isCoreVaccine,
    String? reaction,
    String? reactionSeverity,
    double? postVaccinationTemperature,
    String? notes,
    bool? reminderSet,
    String? vaccinationProgram,
    int? seriesNumber,
    int? totalDosesInSeries,
    bool? seriesComplete,
    int? milkWithdrawalDays,
    int? meatWithdrawalDays,
    DateTime? milkWithdrawalEndDate,
    DateTime? meatWithdrawalEndDate,
    String? certificateNumber,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) {
    final record = VaccinationRecordIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..farmBusinessId = farmBusinessId ?? this.farmBusinessId
      ..cattleBusinessId = cattleBusinessId ?? this.cattleBusinessId
      ..cattleTagId = cattleTagId ?? this.cattleTagId
      ..cattleName = cattleName ?? this.cattleName
      ..vaccineName = vaccineName ?? this.vaccineName
      ..vaccineType = vaccineType ?? this.vaccineType
      ..manufacturer = manufacturer ?? this.manufacturer
      ..batchNumber = batchNumber ?? this.batchNumber
      ..vaccinationDate = vaccinationDate ?? this.vaccinationDate
      ..expiryDate = expiryDate ?? this.expiryDate
      ..dosageAmount = dosageAmount ?? this.dosageAmount
      ..dosageUnit = dosageUnit ?? this.dosageUnit
      ..route = route ?? this.route
      ..injectionSite = injectionSite ?? this.injectionSite
      ..veterinarian = veterinarian ?? this.veterinarian
      ..administeredBy = administeredBy ?? this.administeredBy
      ..cost = cost ?? this.cost
      ..supplier = supplier ?? this.supplier
      ..nextDueDate = nextDueDate ?? this.nextDueDate
      ..frequency = frequency ?? this.frequency
      ..isBooster = isBooster ?? this.isBooster
      ..isCoreVaccine = isCoreVaccine ?? this.isCoreVaccine
      ..reaction = reaction ?? this.reaction
      ..reactionSeverity = reactionSeverity ?? this.reactionSeverity
      ..postVaccinationTemperature = postVaccinationTemperature ?? this.postVaccinationTemperature
      ..notes = notes ?? this.notes
      ..reminderSet = reminderSet ?? this.reminderSet
      ..vaccinationProgram = vaccinationProgram ?? this.vaccinationProgram
      ..seriesNumber = seriesNumber ?? this.seriesNumber
      ..totalDosesInSeries = totalDosesInSeries ?? this.totalDosesInSeries
      ..seriesComplete = seriesComplete ?? this.seriesComplete
      ..milkWithdrawalDays = milkWithdrawalDays ?? this.milkWithdrawalDays
      ..meatWithdrawalDays = meatWithdrawalDays ?? this.meatWithdrawalDays
      ..milkWithdrawalEndDate = milkWithdrawalEndDate ?? this.milkWithdrawalEndDate
      ..meatWithdrawalEndDate = meatWithdrawalEndDate ?? this.meatWithdrawalEndDate
      ..certificateNumber = certificateNumber ?? this.certificateNumber
      ..createdAt = createdAt ?? this.createdAt
      ..updatedAt = updatedAt ?? DateTime.now()
      ..createdBy = createdBy ?? this.createdBy
      ..updatedBy = updatedBy ?? this.updatedBy;

    return record;
  }

  @override
  String toString() {
    return 'VaccinationRecordIsar{id: $id, businessId: $businessId, cattleTagId: $cattleTagId, vaccineName: $vaccineName, vaccinationDate: $vaccinationDate}';
  }
}
