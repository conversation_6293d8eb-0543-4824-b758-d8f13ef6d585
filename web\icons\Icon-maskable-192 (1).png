
                  child: Text(
                    widget.cattle == null ? 'Add Cattle' : 'Edit Cattle',
                    style: _headerStyle,
                  ),
                ),
                const SizedBox(height: 24),

                // Animal Type Dropdown
                DropdownButtonFormField<String>(
                  value: _selectedAnimalTypeId,
                  decoration: const InputDecoration(
                    labelText: 'Animal Type',
                    border: OutlineInputBorder(),
                  ),
                  items:
                      widget.animalTypes.map<DropdownMenuItem<String>>((type) {
                    return DropdownMenuItem(
                      value: type.id,
                      child: Text(type.name),
                    );
                  }).toList(),
                  onChanged: _onAnimalTypeChanged,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select an animal type';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Breed Dropdown (filtered by animal type)
                if (_selectedAnimalTypeId != null)
                  DropdownButtonFormField<String>(
                    value: _selectedBreedId,
                    decoration: const InputDecoration(
                      labelText: 'Breed',
                      border: OutlineInputBorder(),
                    ),
                    items: widget.breeds
                        .where((breed) =>
                            breed.animalTypeId == _selectedAnimalTypeId)
                        .map<DropdownMenuItem<String>>((breed) {
                      return DropdownMenuItem(
                        value: breed.id,
                        child: Text(breed.name),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedBreedId = value;
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select a breed';
                      }
                      return null;
                    },
                  ),
                if (_selectedAnimalTypeId != null) const SizedBox(height: 16),

                // Name Field
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Name',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Tag ID Field with Auto-generate Toggle
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: 60,
                        child: TextFormField(
                          controller: _tagIdController,
                          enabled: !_autoGenerateTagId,
                          decoration: InputDecoration(
                            labelText: 'Tag ID',
                            border: const OutlineInputBorder(),
                            helperText: !_autoGenerateTagId
                                ? 'Format: ${_getAnimalTypePrefix()}N (N = number)'
                                : null,
                          ),
                          validator: (value) {
                            if (!_autoGenerateTagId) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter a tag ID';
                              }
                              if (!_isValidTagIdFormat(value)) {
                                return 'Invalid tag ID format';
                              }
                            }
                            return null;
                          },
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text('Auto', style: TextStyle(fontSize: 14)),
                        Switch(
                          value: _autoGenerateTagId,
                          activeColor: Colors.white,
                          activeTrackColor: const Color(0xFF2E7D32),
                          inactiveTrackColor: Colors.grey.shade300,
                          inactiveThumbColor: Colors.grey.shade50,
                          onChanged: (value) {
                            setState(() {
                              _autoGenerateTagId = value;
                              if (value) {
                                _tagIdController.text = '';
                              }
                            });
                          },
                        ),
                      ],
           