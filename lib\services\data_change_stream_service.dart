import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:logging/logging.dart';

/// Enum for different types of data changes
enum DataChangeType {
  farmSwitch,
  cattleAdded,
  cattleUpdated,
  cattleDeleted,
  breedingAdded,
  breedingUpdated,
  breedingDeleted,
  healthAdded,
  healthUpdated,
  healthDeleted,
  milkAdded,
  milkUpdated,
  milkDeleted,
  weightAdded,
  weightUpdated,
  weightDeleted,
  farmSetupChanged,
  animalTypeChanged,
  breedChanged,
  eventTypeChanged,
  transactionCategoryChanged,
}

/// Data change event that contains information about what changed
class DataChangeEvent {
  final DataChangeType type;
  final String? entityId;
  final Map<String, dynamic>? data;
  final DateTime timestamp;

  DataChangeEvent({
    required this.type,
    this.entityId,
    this.data,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  @override
  String toString() {
    return 'DataChangeEvent(type: $type, entityId: $entityId, timestamp: $timestamp)';
  }
}

/// Global service for broadcasting data changes across the application
class DataChangeStreamService extends ChangeNotifier {
  static final DataChangeStreamService _instance = DataChangeStreamService._internal();
  factory DataChangeStreamService() => _instance;
  DataChangeStreamService._internal();

  final Logger _logger = Logger('DataChangeStreamService');
  
  // Main stream controller for all data changes
  final StreamController<DataChangeEvent> _dataChangeController = 
      StreamController<DataChangeEvent>.broadcast();

  // Specific stream controllers for different types of changes
  final StreamController<DataChangeEvent> _farmChangeController = 
      StreamController<DataChangeEvent>.broadcast();
  final StreamController<DataChangeEvent> _cattleChangeController = 
      StreamController<DataChangeEvent>.broadcast();
  final StreamController<DataChangeEvent> _breedingChangeController = 
      StreamController<DataChangeEvent>.broadcast();
  final StreamController<DataChangeEvent> _healthChangeController = 
      StreamController<DataChangeEvent>.broadcast();
  final StreamController<DataChangeEvent> _milkChangeController = 
      StreamController<DataChangeEvent>.broadcast();
  final StreamController<DataChangeEvent> _weightChangeController = 
      StreamController<DataChangeEvent>.broadcast();
  final StreamController<DataChangeEvent> _farmSetupChangeController = 
      StreamController<DataChangeEvent>.broadcast();

  // Getters for streams
  Stream<DataChangeEvent> get dataChangeStream => _dataChangeController.stream;
  Stream<DataChangeEvent> get farmChangeStream => _farmChangeController.stream;
  Stream<DataChangeEvent> get cattleChangeStream => _cattleChangeController.stream;
  Stream<DataChangeEvent> get breedingChangeStream => _breedingChangeController.stream;
  Stream<DataChangeEvent> get healthChangeStream => _healthChangeController.stream;
  Stream<DataChangeEvent> get milkChangeStream => _milkChangeController.stream;
  Stream<DataChangeEvent> get weightChangeStream => _weightChangeController.stream;
  Stream<DataChangeEvent> get farmSetupChangeStream => _farmSetupChangeController.stream;

  // Legacy compatibility getters for health module
  Stream<DataChangeEvent> get healthRecordStream => _healthChangeController.stream;
  Stream<DataChangeEvent> get treatmentStream => _healthChangeController.stream;
  Stream<DataChangeEvent> get vaccinationStream => _healthChangeController.stream;
  Stream<DataChangeEvent> get cattleStream => _cattleChangeController.stream;

  /// Broadcast a data change event
  void broadcastChange(DataChangeEvent event) {
    try {
      _logger.info('Broadcasting data change: $event');
      
      // Broadcast to main stream
      _dataChangeController.add(event);
      
      // Broadcast to specific streams based on type
      switch (event.type) {
        case DataChangeType.farmSwitch:
          _farmChangeController.add(event);
          break;
        case DataChangeType.cattleAdded:
        case DataChangeType.cattleUpdated:
        case DataChangeType.cattleDeleted:
          _cattleChangeController.add(event);
          break;
        case DataChangeType.breedingAdded:
        case DataChangeType.breedingUpdated:
        case DataChangeType.breedingDeleted:
          _breedingChangeController.add(event);
          break;
        case DataChangeType.healthAdded:
        case DataChangeType.healthUpdated:
        case DataChangeType.healthDeleted:
          _healthChangeController.add(event);
          break;
        case DataChangeType.milkAdded:
        case DataChangeType.milkUpdated:
        case DataChangeType.milkDeleted:
          _milkChangeController.add(event);
          break;
        case DataChangeType.weightAdded:
        case DataChangeType.weightUpdated:
        case DataChangeType.weightDeleted:
          _weightChangeController.add(event);
          break;
        case DataChangeType.farmSetupChanged:
        case DataChangeType.animalTypeChanged:
        case DataChangeType.breedChanged:
        case DataChangeType.eventTypeChanged:
        case DataChangeType.transactionCategoryChanged:
          _farmSetupChangeController.add(event);
          break;
      }
      
      // Notify listeners (for ChangeNotifier compatibility)
      notifyListeners();
      
    } catch (e) {
      _logger.severe('Error broadcasting data change: $e');
    }
  }

  /// Convenience methods for common data changes
  
  /// Legacy method - no longer needed for single farm per user
  @Deprecated('No longer needed - single farm per user')
  void notifyFarmSwitch(String? farmId, String? farmName) {
    // No-op for single farm per user
  }

  void notifyCattleAdded(String cattleId) {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.cattleAdded,
      entityId: cattleId,
    ));
  }

  void notifyCattleUpdated(String cattleId) {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.cattleUpdated,
      entityId: cattleId,
    ));
  }

  void notifyCattleDeleted(String cattleId) {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.cattleDeleted,
      entityId: cattleId,
    ));
  }

  void notifyBreedingAdded(String recordId) {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.breedingAdded,
      entityId: recordId,
    ));
  }

  void notifyBreedingUpdated(String recordId) {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.breedingUpdated,
      entityId: recordId,
    ));
  }

  void notifyBreedingDeleted(String recordId) {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.breedingDeleted,
      entityId: recordId,
    ));
  }

  void notifyHealthAdded(String recordId) {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.healthAdded,
      entityId: recordId,
    ));
  }

  void notifyHealthUpdated(String recordId) {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.healthUpdated,
      entityId: recordId,
    ));
  }

  void notifyHealthDeleted(String recordId) {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.healthDeleted,
      entityId: recordId,
    ));
  }

  void notifyMilkAdded(String recordId) {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.milkAdded,
      entityId: recordId,
    ));
  }

  void notifyMilkUpdated(String recordId) {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.milkUpdated,
      entityId: recordId,
    ));
  }

  void notifyMilkDeleted(String recordId) {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.milkDeleted,
      entityId: recordId,
    ));
  }

  void notifyWeightAdded(String recordId) {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.weightAdded,
      entityId: recordId,
    ));
  }

  void notifyWeightUpdated(String recordId) {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.weightUpdated,
      entityId: recordId,
    ));
  }

  void notifyWeightDeleted(String recordId) {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.weightDeleted,
      entityId: recordId,
    ));
  }

  void notifyFarmSetupChanged() {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.farmSetupChanged,
    ));
  }

  void notifyAnimalTypeChanged() {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.animalTypeChanged,
    ));
  }

  void notifyBreedChanged() {
    broadcastChange(DataChangeEvent(
      type: DataChangeType.breedChanged,
    ));
  }

  // Health-specific notification methods for backward compatibility
  void notifyHealthRecordAdded(String recordId) {
    notifyHealthAdded(recordId);
  }

  void notifyHealthRecordUpdated(String recordId) {
    notifyHealthUpdated(recordId);
  }

  void notifyHealthRecordDeleted(String recordId) {
    notifyHealthDeleted(recordId);
  }

  void notifyTreatmentAdded(String recordId) {
    notifyHealthAdded(recordId);
  }

  void notifyTreatmentUpdated(String recordId) {
    notifyHealthUpdated(recordId);
  }

  void notifyTreatmentDeleted(String recordId) {
    notifyHealthDeleted(recordId);
  }

  void notifyVaccinationAdded(String recordId) {
    notifyHealthAdded(recordId);
  }

  void notifyVaccinationUpdated(String recordId) {
    notifyHealthUpdated(recordId);
  }

  void notifyVaccinationDeleted(String recordId) {
    notifyHealthDeleted(recordId);
  }

  /// Dispose all stream controllers
  @override
  void dispose() {
    _dataChangeController.close();
    _farmChangeController.close();
    _cattleChangeController.close();
    _breedingChangeController.close();
    _healthChangeController.close();
    _milkChangeController.close();
    _weightChangeController.close();
    _farmSetupChangeController.close();
    super.dispose();
  }
}
