import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'chart_data_isar.dart';
import 'report_data_isar.dart';

part 'pregnancy_report_data_isar.g.dart';

@collection
class PregnancyReportDataIsar with ReportDataIsarMixin {
  Id id = Isar.autoIncrement;

  @override
  @Index()
  String? get reportType => super.reportType;
  @override
  set reportType(String? value) => super.reportType = value;

  @override
  @Index(unique: true)
  String? get businessId => super.businessId;
  @override
  set businessId(String? value) => super.businessId = value;

  // Status distribution for the pregnancy report
  @ignore
  Map<String, int> get statusDistribution {
    final result = <String, int>{};
    if (statusLabels != null && statusCounts != null) {
      for (int i = 0;
          i < statusLabels!.length && i < statusCounts!.length;
          i++) {
        result[statusLabels![i]] = statusCounts![i];
      }
    }
    return result;
  }

  // Stage distribution for the pregnancy report
  @ignore
  Map<String, int> get stageDistribution {
    final result = <String, int>{};
    if (trimesterLabels != null && trimesterCounts != null) {
      for (int i = 0;
          i < trimesterLabels!.length && i < trimesterCounts!.length;
          i++) {
        result[trimesterLabels![i]] = trimesterCounts![i];
      }
    }
    return result;
  }

  // Get all pregnancies filtered by the current report criteria
  @ignore
  List<PregnancyIsar> get filteredPregnancies {
    return _pregnancies
        .map((record) => PregnancyIsar(
              cattleId: record.cattleId ?? '',
              confirmedDate: record.startDate ?? DateTime.now(),
              expectedDueDate: record.dueDate ??
                  DateTime.now().add(const Duration(days: 280)),
              status: record.status ?? 'Unknown',
              trimester: _getTrimester(record),
              daysInPregnancy: record.startDate != null
                  ? DateTime.now().difference(record.startDate!).inDays
                  : 0,
              notes: record.notes,
            ))
        .toList();
  }

  // Mock method to create sample pregnancies for the report
  List<PregnancyIsar> createMockPregnancies() {
    // This is a placeholder implementation
    // In a real app, this data would be retrieved from a database
    final pregnancies = <PregnancyIsar>[];

    // Return empty list for now
    return pregnancies;
  }

  int? totalPregnancies;
  int? currentPregnancies;
  int? completedPregnancies;
  int? abortedPregnancies;
  double? averageGestationLength;
  double? pregnancySuccessRate;
  double? calvingSurvivalRate;

  // Status distribution
  List<String>? statusLabels;
  List<int>? statusCounts;
  List<int>? statusColors;

  // Trimester distribution
  List<String>? trimesterLabels;
  List<int>? trimesterCounts;
  List<int>? trimesterColors;

  // Monthly calving distribution
  List<DateTime>? calvingDates;
  List<int>? calvingCounts;

  // Store pregnancies for the getter
  List<dynamic> _pregnancies = [];

  /// Default constructor with name
  PregnancyReportDataIsar.empty();

  /// Factory constructor to create a new pregnancy report
  factory PregnancyReportDataIsar.create({
    required String title,
    DateTime? startDate,
    DateTime? endDate,
    String? filterCriteria,
    int? totalPregnancies,
    int? currentPregnancies,
    int? completedPregnancies,
    int? abortedPregnancies,
    double? averageGestationLength,
    double? pregnancySuccessRate,
    double? calvingSurvivalRate,
    List<String>? statusLabels,
    List<int>? statusCounts,
    List<int>? statusColors,
    List<String>? trimesterLabels,
    List<int>? trimesterCounts,
    List<int>? trimesterColors,
    List<DateTime>? calvingDates,
    List<int>? calvingCounts,
  }) {
    final report = PregnancyReportDataIsar();

    // Initialize the base report properties
    report.initializeReport(
      reportType: 'pregnancy',
      title: title,
      startDate: startDate,
      endDate: endDate,
      filterCriteria: filterCriteria,
    );

    // Set the pregnancy-specific properties
    report.totalPregnancies = totalPregnancies;
    report.currentPregnancies = currentPregnancies;
    report.completedPregnancies = completedPregnancies;
    report.abortedPregnancies = abortedPregnancies;
    report.averageGestationLength = averageGestationLength;
    report.pregnancySuccessRate = pregnancySuccessRate;
    report.calvingSurvivalRate = calvingSurvivalRate;
    report.statusLabels = statusLabels;
    report.statusCounts = statusCounts;
    report.statusColors = statusColors;
    report.trimesterLabels = trimesterLabels;
    report.trimesterCounts = trimesterCounts;
    report.trimesterColors = trimesterColors;
    report.calvingDates = calvingDates;
    report.calvingCounts = calvingCounts;

    return report;
  }

  /// Constructor used in pregnancies_report_screen.dart
  PregnancyReportDataIsar({
    List<dynamic>? pregnancies,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
    String? selectedStatus,
  }) {
    // Initialize the base report properties
    initializeReport(
      reportType: 'pregnancy',
      title: 'Pregnancy Report',
      startDate: startDate,
      endDate: endDate,
      filterCriteria: searchQuery,
    );

    // Store the pregnancies for filteredPregnancies getter
    _pregnancies = pregnancies ?? [];

    // Process pregnancy records
    if (pregnancies != null && pregnancies.isNotEmpty) {
      // Calculate summary statistics
      totalPregnancies = pregnancies.length;
      currentPregnancies =
          pregnancies.where((p) => p.status == 'Active').length;
      completedPregnancies =
          pregnancies.where((p) => p.status == 'Completed').length;
      abortedPregnancies =
          pregnancies.where((p) => p.status == 'Failed').length;

      // Calculate success rate
      if (completedPregnancies! + abortedPregnancies! > 0) {
        pregnancySuccessRate = ((completedPregnancies!.toDouble() /
                    (completedPregnancies! + abortedPregnancies!)) *
                100)
            .toDouble();
      } else {
        pregnancySuccessRate = 0;
      }

      // Calculate average gestation length for completed pregnancies
      final completedList =
          pregnancies.where((p) => p.status == 'Completed').toList();
      if (completedList.isNotEmpty) {
        int totalDays = 0;
        for (final pregnancy in completedList) {
          if (pregnancy.startDate != null && pregnancy.endDate != null) {
            final difference =
                pregnancy.endDate!.difference(pregnancy.startDate!);
            final int diffDays = difference.inDays;
            totalDays += diffDays;
          }
        }
        averageGestationLength = (totalDays / completedList.length).toDouble();
      }

      // Assume calving survival rate from data if available, or default to 95%
      calvingSurvivalRate = 95.0;

      // Create status distribution data
      final statusDistribution = <String, int>{
        'Active': currentPregnancies ?? 0,
        'Completed': completedPregnancies ?? 0,
        'Failed': abortedPregnancies ?? 0,
      };

      statusLabels = statusDistribution.keys.toList();
      statusCounts = statusDistribution.values.toList();

      // Generate colors for status
      statusColors = [
        Colors.blue
            .value, // ignore: deprecated_member_use (value is correct for storing ARGB int)
        Colors.green
            .value, // ignore: deprecated_member_use (value is correct for storing ARGB int)
        Colors.red
            .value, // ignore: deprecated_member_use (value is correct for storing ARGB int)
      ];

      // Create trimester distribution
      final trimesterData = <String, int>{
        'First Trimester': 0,
        'Second Trimester': 0,
        'Third Trimester': 0,
        'Due': 0,
      };

      for (final pregnancy in pregnancies.where((p) => p.status == 'Active')) {
        if (pregnancy.startDate != null) {
          final today = DateTime.now();
          final difference = today.difference(pregnancy.startDate!);
          final daysInPregnancy = difference.inDays;

          if (daysInPregnancy < 90) {
            trimesterData['First Trimester'] =
                (trimesterData['First Trimester']! + 1);
          } else if (daysInPregnancy < 180) {
            trimesterData['Second Trimester'] =
                (trimesterData['Second Trimester']! + 1);
          } else if (daysInPregnancy < 270) {
            trimesterData['Third Trimester'] =
                (trimesterData['Third Trimester']! + 1);
          } else {
            trimesterData['Due'] = (trimesterData['Due']! + 1);
          }
        }
      }

      trimesterLabels = trimesterData.keys.toList();
      trimesterCounts = trimesterData.values.toList();

      // Generate colors for trimesters
      trimesterColors = [
        Colors.blue
            .value, // ignore: deprecated_member_use (value is correct for storing ARGB int)
        Colors.green
            .value, // ignore: deprecated_member_use (value is correct for storing ARGB int)
        Colors.orange
            .value, // ignore: deprecated_member_use (value is correct for storing ARGB int)
        Colors.red
            .value, // ignore: deprecated_member_use (value is correct for storing ARGB int)
      ];

      // Create calving time series data for completed pregnancies
      if (completedList.isNotEmpty) {
        final dateMap = <DateTime, int>{};
        for (final pregnancy in completedList) {
          if (pregnancy.endDate != null) {
            final date =
                DateTime(pregnancy.endDate!.year, pregnancy.endDate!.month, 1);
            final currentValue = dateMap[date] ?? 0;
            dateMap[date] = currentValue + 1;
          }
        }

        final sortedDates = dateMap.keys.toList()..sort();
        calvingDates = sortedDates;
        calvingCounts = sortedDates.map((date) => dateMap[date]!).toList();
      }
    } else {
      // Set defaults
      totalPregnancies = 0;
      currentPregnancies = 0;
      completedPregnancies = 0;
      abortedPregnancies = 0;
      pregnancySuccessRate = 0;
      averageGestationLength = 0;
      calvingSurvivalRate = 0;
    }
  }

  @override
  List<DataColumn> getTableColumns() {
    return [
      const DataColumn(label: Text('Metric')),
      const DataColumn(label: Text('Value'), numeric: true),
    ];
  }

  @override
  List<DataRow> getTableRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('Total Pregnancies')),
        DataCell(Text('${totalPregnancies ?? 0}')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Current Pregnancies')),
        DataCell(Text('${currentPregnancies ?? 0}')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Completed Pregnancies')),
        DataCell(Text('${completedPregnancies ?? 0}')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Aborted Pregnancies')),
        DataCell(Text('${abortedPregnancies ?? 0}')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Average Gestation Length (days)')),
        DataCell(Text(averageGestationLength?.toStringAsFixed(1) ?? '0.0')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Pregnancy Success Rate')),
        DataCell(Text('${pregnancySuccessRate?.toStringAsFixed(1) ?? '0.0'}%')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Calving Survival Rate')),
        DataCell(Text('${calvingSurvivalRate?.toStringAsFixed(1) ?? '0.0'}%')),
      ]),
    ];
  }

  @override
  Map<String, dynamic> getSummaryData() {
    return {
      'Total Pregnancies': totalPregnancies ?? 0,
      'Current': currentPregnancies ?? 0,
      'Completed': completedPregnancies ?? 0,
      'Aborted': abortedPregnancies ?? 0,
      'Success Rate': '${pregnancySuccessRate?.toStringAsFixed(1) ?? '0.0'}%',
    };
  }

  @override
  List<ChartDataIsar> getChartData() {
    final result = <ChartDataIsar>[];

    // Add pregnancy status distribution chart data
    if (statusLabels != null && statusCounts != null && statusColors != null) {
      for (int i = 0;
          i < statusLabels!.length &&
              i < statusCounts!.length &&
              i < statusColors!.length;
          i++) {
        result.add(ChartDataIsar()
          ..label = statusLabels![i]
          ..value = statusCounts![i].toDouble()
          ..colorValue = statusColors![i]);
      }
    }

    return result;
  }

  // Helper method to get trimester distribution chart data
  List<ChartDataIsar> getTrimesterChartData() {
    final result = <ChartDataIsar>[];

    // Add trimester distribution chart data
    if (trimesterLabels != null &&
        trimesterCounts != null &&
        trimesterColors != null) {
      for (int i = 0;
          i < trimesterLabels!.length &&
              i < trimesterCounts!.length &&
              i < trimesterColors!.length;
          i++) {
        result.add(ChartDataIsar()
          ..label = trimesterLabels![i]
          ..value = trimesterCounts![i].toDouble()
          ..colorValue = trimesterColors![i]);
      }
    }

    return result;
  }

  // Helper method to get time series calving data
  List<ChartDataIsar> getCalvingTimeSeriesChartData() {
    final result = <ChartDataIsar>[];

    // Create time series chart data from calvingDates and calvingCounts
    if (calvingDates != null && calvingCounts != null) {
      for (int i = 0;
          i < calvingDates!.length && i < calvingCounts!.length;
          i++) {
        result.add(ChartDataIsar()
              ..date = calvingDates![i]
              ..value = calvingCounts![i].toDouble()
              ..colorValue = Colors.green
                  .value // ignore: deprecated_member_use (value is correct for storing ARGB int)
            );
      }
    }

    return result;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'totalPregnancies': totalPregnancies,
      'currentPregnancies': currentPregnancies,
      'completedPregnancies': completedPregnancies,
      'abortedPregnancies': abortedPregnancies,
      'averageGestationLength': averageGestationLength,
      'pregnancySuccessRate': pregnancySuccessRate,
      'calvingSurvivalRate': calvingSurvivalRate,
      'statusLabels': statusLabels,
      'statusCounts': statusCounts,
      'statusColors': statusColors,
      'trimesterLabels': trimesterLabels,
      'trimesterCounts': trimesterCounts,
      'trimesterColors': trimesterColors,
      'calvingDates':
          calvingDates?.map((date) => date.toIso8601String()).toList(),
      'calvingCounts': calvingCounts,
    });
    return map;
  }

  factory PregnancyReportDataIsar.fromMap(Map<String, dynamic> map) {
    final report = PregnancyReportDataIsar();

    // Initialize the base properties
    report.initFromMap(map);

    // Set pregnancy-specific properties
    report.totalPregnancies = map['totalPregnancies'] as int?;
    report.currentPregnancies = map['currentPregnancies'] as int?;
    report.completedPregnancies = map['completedPregnancies'] as int?;
    report.abortedPregnancies = map['abortedPregnancies'] as int?;
    report.averageGestationLength = map['averageGestationLength'] as double?;
    report.pregnancySuccessRate = map['pregnancySuccessRate'] as double?;
    report.calvingSurvivalRate = map['calvingSurvivalRate'] as double?;

    // Handle lists
    if (map['statusLabels'] != null) {
      report.statusLabels = List<String>.from(map['statusLabels'] as List);
    }

    if (map['statusCounts'] != null) {
      report.statusCounts = List<int>.from(map['statusCounts'] as List);
    }

    if (map['statusColors'] != null) {
      report.statusColors = List<int>.from(map['statusColors'] as List);
    }

    if (map['trimesterLabels'] != null) {
      report.trimesterLabels =
          List<String>.from(map['trimesterLabels'] as List);
    }

    if (map['trimesterCounts'] != null) {
      report.trimesterCounts = List<int>.from(map['trimesterCounts'] as List);
    }

    if (map['trimesterColors'] != null) {
      report.trimesterColors = List<int>.from(map['trimesterColors'] as List);
    }

    if (map['calvingDates'] != null) {
      report.calvingDates = (map['calvingDates'] as List)
          .map((dateStr) => DateTime.parse(dateStr as String))
          .toList();
    }

    if (map['calvingCounts'] != null) {
      report.calvingCounts = List<int>.from(map['calvingCounts'] as List);
    }

    return report;
  }

  // Helper method to determine trimester
  String _getTrimester(dynamic record) {
    if (record.startDate == null) return 'Unknown';

    final today = DateTime.now();
    final difference = today.difference(record.startDate!);
    final daysInPregnancy = difference.inDays;

    if (daysInPregnancy < 90) {
      return 'First Trimester';
    } else if (daysInPregnancy < 180) {
      return 'Second Trimester';
    } else if (daysInPregnancy < 270) {
      return 'Third Trimester';
    } else {
      return 'Due';
    }
  }
}

/// Class to represent a pregnancy in the pregnancy report
class PregnancyIsar {
  final String cattleId;
  final DateTime confirmedDate;
  final DateTime expectedDueDate;
  final String status;
  final String trimester;
  final int daysInPregnancy;
  final String? notes;

  PregnancyIsar({
    required this.cattleId,
    required this.confirmedDate,
    required this.expectedDueDate,
    required this.status,
    required this.trimester,
    required this.daysInPregnancy,
    this.notes,
  });
}
