import 'package:flutter/material.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class UpdatePregnancyStatusDialog extends StatefulWidget {
  final bool initialPregnancyStatus;
  final DateTime? initialBreedingDate;
  final Function(bool, DateTime?) onUpdate;

  const UpdatePregnancyStatusDialog({
    Key? key,
    required this.initialPregnancyStatus,
    this.initialBreedingDate,
    required this.onUpdate,
  }) : super(key: key);

  @override
  State<UpdatePregnancyStatusDialog> createState() =>
      _UpdatePregnancyStatusDialogState();
}

class _UpdatePregnancyStatusDialogState extends State<UpdatePregnancyStatusDialog> {
  late bool _isPregnant;
  DateTime? _breedingDate;

  @override
  void initState() {
    super.initState();
    _isPregnant = widget.initialPregnancyStatus;
    _breedingDate = widget.initialBreedingDate;
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _breedingDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _breedingDate = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Update Pregnancy Status'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SwitchListTile(
            title: const Text('Pregnant'),
            value: _isPregnant,
            onChanged: (value) {
              setState(() {
                _isPregnant = value;
              });
            },
          ),
          if (_isPregnant) ...[  
            SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
            ListTile(
              title: const Text('Breeding Date'),
              subtitle: Text(
                _breedingDate == null
                    ? 'Not set'
                    : '${_breedingDate!.year}-${_breedingDate!.month.toString().padLeft(2, '0')}-${_breedingDate!.day.toString().padLeft(2, '0')}',
              ),
              trailing: const Icon(Icons.calendar_today),
              onTap: () => _selectDate(context),
            ),
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_isPregnant && _breedingDate == null) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please select a breeding date'),
                ),
          