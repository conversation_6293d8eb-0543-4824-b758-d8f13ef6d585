import 'dart:io';
import 'dart:convert';
import 'package:get_it/get_it.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/logging_service.dart';
import '../models/farm_isar.dart';
import 'farm_setup_handler.dart';

/// Service for importing and exporting farm data in various formats
class DataImportExportService {
  static final LoggingService _logger = LoggingService.instance;
  final IsarService _isarService;
  final FarmSetupHandler _farmSetupHandler;

  // Singleton instance
  static final DataImportExportService _instance = DataImportExportService._internal();
  static DataImportExportService get instance => _instance;

  // Private constructor
  DataImportExportService._internal() :
    _isarService = GetIt.instance<IsarService>(),
    _farmSetupHandler = FarmSetupHandler.instance;

  /// Export farm data to JSON format
  Future<ExportResult> exportToJson(
    String outputPath, {
    List<String>? tableNames,
    Function(double)? onProgress,
  }) async {
    try {
      _logger.logInfo('Starting JSON export to: $outputPath');
      onProgress?.call(0.1);

      final exportData = <String, dynamic>{
        'exportInfo': {
          'version': '1.0',
          'exportDate': DateTime.now().toIso8601String(),
          'format': 'json',
        },
        'data': {},
      };

      // Export farms
      if (tableNames == null || tableNames.contains('farms')) {
        final farms = await _farmSetupHandler.getAllFarms();
        exportData['data']['farms'] = farms.map((f) => _farmToMap(f)).toList();
        onProgress?.call(0.3);
      }

      // Export cattle (placeholder - you'll need to implement based on your cattle model)
      if (tableNames == null || tableNames.contains('cattle')) {
        // final cattle = await _isarService.isar.cattleIsars.where().findAll();
        // exportData['data']['cattle'] = cattle.map((c) => _cattleToMap(c)).toList();
        exportData['data']['cattle'] = []; // Placeholder
        onProgress?.call(0.5);
      }

      // Export milk records (placeholder)
      if (tableNames == null || tableNames.contains('milkRecords')) {
        exportData['data']['milkRecords'] = []; // Placeholder
        onProgress?.call(0.7);
      }

      // Export breeding records (placeholder)
      if (tableNames == null || tableNames.contains('breedingRecords')) {
        exportData['data']['breedingRecords'] = []; // Placeholder
        onProgress?.call(0.8);
      }

      // Write to file
      final jsonString = const JsonEncoder.withIndent('  ').convert(exportData);
      final file = File(outputPath);
      await file.writeAsString(jsonString);
      onProgress?.call(1.0);

      final fileSize = await file.length();
      
      _logger.logInfo('JSON export completed successfully. Size: ${(fileSize / 1024 / 1024).toStringAsFixed(2)} MB');
      
      return ExportResult(
        success: true,
        outputPath: outputPath,
        format: 'json',
        fileSize: fileSize,
        recordCount: _countRecords(exportData['data']),
      );
    } catch (e) {
      _logger.logError('Error exporting to JSON: $e');
      return ExportResult(
        success: false,
        message: 'JSON export failed: $e',
      );
    }
  }

  /// Export farm data to CSV format (simplified without csv package)
  Future<ExportResult> exportToCsv(
    String outputDirectory, {
    List<String>? tableNames,
    Function(double)? onProgress,
  }) async {
    try {
      _logger.logInfo('Starting CSV export to: $outputDirectory');
      onProgress?.call(0.1);

      final dir = Directory(outputDirectory);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }

      int totalFiles = 0;
      int totalRecords = 0;

      // Export farms to CSV
      if (tableNames == null || tableNames.contains('farms')) {
        final farms = await _farmSetupHandler.getAllFarms();
        final csvData = _farmsToCsv(farms);
        final filePath = '$outputDirectory/farms.csv';
        await File(filePath).writeAsString(csvData);
        totalFiles++;
        totalRecords += farms.length;
        onProgress?.call(0.3);
      }

      // Export cattle to CSV (placeholder)
      if (tableNames == null || tableNames.contains('cattle')) {
        const csvData = 'id,name,tagId,breed,birthDate\n'; // Header only
        final filePath = '$outputDirectory/cattle.csv';
        await File(filePath).writeAsString(csvData);
        totalFiles++;
        onProgress?.call(0.5);
      }

      // Export milk records to CSV (placeholder)
      if (tableNames == null || tableNames.contains('milkRecords')) {
        const csvData = 'id,cattleId,date,morningMilk,eveningMilk,totalMilk\n'; // Header only
        final filePath = '$outputDirectory/milk_records.csv';
        await File(filePath).writeAsString(csvData);
        totalFiles++;
        onProgress?.call(0.7);
      }

      // Export breeding records to CSV (placeholder)
      if (tableNames == null || tableNames.contains('breedingRecords')) {
        const csvData = 'id,cattleId,date,method,outcome,notes\n'; // Header only
        final filePath = '$outputDirectory/breeding_records.csv';
        await File(filePath).writeAsString(csvData);
        totalFiles++;
        onProgress?.call(0.9);
      }

      onProgress?.call(1.0);

      _logger.logInfo('CSV export completed successfully. Files: $totalFiles, Records: $totalRecords');

      return ExportResult(
        success: true,
        outputPath: outputDirectory,
        format: 'csv',
        fileCount: totalFiles,
        recordCount: totalRecords,
      );
    } catch (e) {
      _logger.logError('Error exporting to CSV: $e');
      return ExportResult(
        success: false,
        message: 'CSV export failed: $e',
      );
    }
  }

  /// Import farm data from JSON format
  Future<ImportResult> importFromJson(
    String inputPath, {
    bool validateData = true,
    Function(double)? onProgress,
  }) async {
    try {
      _logger.logInfo('Starting JSON import from: $inputPath');
      onProgress?.call(0.1);

      final file = File(inputPath);
      if (!await file.exists()) {
        throw Exception('Import file not found: $inputPath');
      }

      final jsonString = await file.readAsString();
      final importData = jsonDecode(jsonString) as Map<String, dynamic>;
      onProgress?.call(0.2);

      // Validate import data structure
      if (validateData) {
        _validateImportData(importData);
      }
      onProgress?.call(0.3);

      int importedRecords = 0;
      final data = importData['data'] as Map<String, dynamic>;

      // Import farms
      if (data.containsKey('farms')) {
        final farmsData = data['farms'] as List<dynamic>;
        for (final farmData in farmsData) {
          final farm = _mapToFarm(farmData as Map<String, dynamic>);
          await _isarService.isar.writeTxn(() async {
            await _isarService.isar.farmIsars.put(farm);
          });
          importedRecords++;
        }
        onProgress?.call(0.6);
      }

      // Import other data types (placeholder)
      // You would implement similar logic for cattle, milk records, etc.

      onProgress?.call(1.0);

      _logger.logInfo('JSON import completed successfully. Records imported: $importedRecords');
      
      return ImportResult(
        success: true,
        inputPath: inputPath,
        format: 'json',
        recordsImported: importedRecords,
      );
    } catch (e) {
      _logger.logError('Error importing from JSON: $e');
      return ImportResult(
        success: false,
        message: 'JSON import failed: $e',
      );
    }
  }

  /// Import farm data from CSV format
  Future<ImportResult> importFromCsv(
    String inputPath,
    String dataType, {
    bool validateData = true,
    Function(double)? onProgress,
  }) async {
    try {
      _logger.logInfo('Starting CSV import from: $inputPath for $dataType');
      onProgress?.call(0.1);

      final file = File(inputPath);
      if (!await file.exists()) {
        throw Exception('Import file not found: $inputPath');
      }

      final csvString = await file.readAsString();
      final csvData = _parseCsv(csvString);
      onProgress?.call(0.3);

      if (csvData.isEmpty) {
        throw Exception('CSV file is empty');
      }

      final headers = csvData.first.map((e) => e.toString()).toList();
      final rows = csvData.skip(1).toList();

      int importedRecords = 0;

      switch (dataType.toLowerCase()) {
        case 'farms':
          for (final row in rows) {
            final farmData = _csvRowToMap(headers, row);
            final farm = _mapToFarm(farmData);
            await _isarService.isar.writeTxn(() async {
              await _isarService.isar.farmIsars.put(farm);
            });
            importedRecords++;
            onProgress?.call(0.3 + (importedRecords / rows.length) * 0.6);
          }
          break;
        // Add cases for other data types
        default:
          throw Exception('Unsupported data type: $dataType');
      }

      onProgress?.call(1.0);

      _logger.logInfo('CSV import completed successfully. Records imported: $importedRecords');
      
      return ImportResult(
        success: true,
        inputPath: inputPath,
        format: 'csv',
        recordsImported: importedRecords,
      );
    } catch (e) {
      _logger.logError('Error importing from CSV: $e');
      return ImportResult(
        success: false,
        message: 'CSV import failed: $e',
      );
    }
  }

  /// Convert farm object to map for export
  Map<String, dynamic> _farmToMap(FarmIsar farm) {
    return {
      'id': farm.id,
      'businessId': farm.farmBusinessId,
      'name': farm.name,
      'location': farm.location,
      'createdAt': farm.createdAt?.toIso8601String(),
      'updatedAt': farm.updatedAt?.toIso8601String(),
    };
  }

  /// Convert map to farm object for import
  FarmIsar _mapToFarm(Map<String, dynamic> data) {
    return FarmIsar()
      ..farmBusinessId = data['businessId'] as String?
      ..name = data['name'] as String?
      ..location = data['location'] as String?
      ..createdAt = data['createdAt'] != null
          ? DateTime.parse(data['createdAt'] as String)
          : DateTime.now()
      ..updatedAt = data['updatedAt'] != null
          ? DateTime.parse(data['updatedAt'] as String)
          : DateTime.now();
  }

  /// Convert farms to CSV format (simplified without csv package)
  String _farmsToCsv(List<FarmIsar> farms) {
    final buffer = StringBuffer();

    // Add headers
    buffer.writeln('id,businessId,name,location,createdAt,updatedAt');

    // Add data rows
    for (final farm in farms) {
      buffer.writeln([
        farm.id,
        _escapeCsvField(farm.farmBusinessId ?? ''),
        _escapeCsvField(farm.name ?? ''),
        _escapeCsvField(farm.location ?? ''),
        farm.createdAt?.toIso8601String() ?? '',
        farm.updatedAt?.toIso8601String() ?? '',
      ].join(','));
    }

    return buffer.toString();
  }

  /// Escape CSV field if it contains special characters
  String _escapeCsvField(String field) {
    if (field.contains(',') || field.contains('"') || field.contains('\n')) {
      return '"${field.replaceAll('"', '""')}"';
    }
    return field;
  }

  /// Simple CSV parser (replace with proper CSV library in production)
  List<List<String>> _parseCsv(String csvString) {
    final lines = csvString.split('\n');
    final result = <List<String>>[];

    for (final line in lines) {
      if (line.trim().isEmpty) continue;

      final fields = <String>[];
      final chars = line.split('');
      bool inQuotes = false;
      String currentField = '';

      for (int i = 0; i < chars.length; i++) {
        final char = chars[i];

        if (char == '"') {
          if (inQuotes && i + 1 < chars.length && chars[i + 1] == '"') {
            currentField += '"';
            i++; // Skip next quote
          } else {
            inQuotes = !inQuotes;
          }
        } else if (char == ',' && !inQuotes) {
          fields.add(currentField);
          currentField = '';
        } else {
          currentField += char;
        }
      }

      fields.add(currentField);
      result.add(fields);
    }

    return result;
  }

  /// Convert CSV row to map
  Map<String, dynamic> _csvRowToMap(List<String> headers, List<dynamic> row) {
    final map = <String, dynamic>{};
    for (int i = 0; i < headers.length && i < row.length; i++) {
      map[headers[i]] = row[i];
    }
    return map;
  }

  /// Validate import data structure
  void _validateImportData(Map<String, dynamic> importData) {
    if (!importData.containsKey('exportInfo')) {
      throw Exception('Invalid import file: missing export info');
    }

    if (!importData.containsKey('data')) {
      throw Exception('Invalid import file: missing data section');
    }

    final exportInfo = importData['exportInfo'] as Map<String, dynamic>;
    if (!exportInfo.containsKey('version')) {
      throw Exception('Invalid import file: missing version info');
    }
  }

  /// Count total records in export data
  int _countRecords(Map<String, dynamic> data) {
    int count = 0;
    for (final value in data.values) {
      if (value is List) {
        count += value.length;
      }
    }
    return count;
  }
}

/// Result class for export operations
class ExportResult {
  final bool success;
  final String? outputPath;
  final String format;
  final int fileSize;
  final int fileCount;
  final int recordCount;
  final String? message;

  ExportResult({
    required this.success,
    this.outputPath,
    this.format = '',
    this.fileSize = 0,
    this.fileCount = 1,
    this.recordCount = 0,
    this.message,
  });
}

/// Result class for import operations
class ImportResult {
  final bool success;
  final String? inputPath;
  final String format;
  final int recordsImported;
  final String? message;

  ImportResult({
    required this.success,
    this.inputPath,
    this.format = '',
    this.recordsImported = 0,
    this.message,
  });
}
