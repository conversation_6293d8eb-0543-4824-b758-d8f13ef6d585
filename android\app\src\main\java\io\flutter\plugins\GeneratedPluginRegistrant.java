# Cattle Manager App

A comprehensive Flutter application for managing cattle operations. This application helps farmers and ranchers efficiently manage their cattle inventory, health records, breeding information, and more.

## Current Version: V2.27

### Latest Updates
- Enhanced pie chart UI and layout
  - Improved spacing between title, chart, and legends
  - Optimized chart size and label positioning
  - Added consistent color scheme for categories
- Refined legend display
  - Vertically aligned legend items
  - Added both percentage and currency values
  - Improved readability with consistent spacing
- Added date range dropdown at the top of the Summary tab
- Implemented smooth date range filtering with loading indicator
- Improved line chart axis titles and readability
- Added blue separator lines for chart axes
- Enhanced visual consistency and performance in Summary tab

### Color Scheme
- Type: Blue (dynamic green/red for values)
- Category: Green
- Date: Orange
- Payment Method: Purple
- Amount: Dynamic (green/red based on transaction type)

### Features
- Cattle inventory management
- Health records tracking
- Breeding information
- Transaction management
- Category management
- Reporting and analytics

## Version History

### [V2.27] - 2025-02-19
- **Pie Chart Enhancements**
  - Reduced chart radius for better label visibility
  - Centered chart within card with proper padding
  - Optimized label positioning to prevent overlap
  - Implemented consistent color scheme for categories

- **Legend Improvements**
  - Reorganized legends into vertical layout
  - Added both percentage and currency information
  - Enhanced legend readability with proper spacing
  - Improved color consistency between chart and legends

- **UI Refinements**
  - Increased card height for better content spacing
  - Added proper spacing between chart elements
  - Improved overall visual hierarchy

### [V2.25] - 2025-02-16
- **UI Enhancements**
  - Refined transactions list header design
  - Added icons to column headers
  - Improved visual hierarchy and spacing
  - Implemented subtle row dividers

- **Layout Improvements**
  - Centered column headers
  - Enhanced text and icon styling
  - Improved responsiveness

- **Code Optimizations**
  - Refactored transaction list widget
  - Improved code maintainability
  - Enhanced visual consistency

### [V2.22]
- Added date range dropdown at the top of the Summary tab
- Implemented smooth date range filtering with loading indicator
- Improved line chart axis titles and readability
- Added blue separator lines for chart axes
- Enhanced visual consistency and performance in Summary tab

## Installation
1. Ensure Flutter SDK is installed
2. Clone the repository
3. Run `flutter pub get`
4. Launch with `flutter run -d chrome`

## Contributing
Please read our contribution guidelines before submitting pull requests.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      