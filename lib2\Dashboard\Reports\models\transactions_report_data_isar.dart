import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'package:intl/intl.dart';
import '../../Transactions/models/transaction_isar.dart';
import 'chart_data_isar.dart';
import 'report_data_isar.dart';

part 'transactions_report_data_isar.g.dart';

@collection
class TransactionsReportDataIsar with ReportDataIsarMixin {
  Id id = Isar.autoIncrement;

  @override
  @Index()
  String? get reportType => super.reportType;
  @override
  set reportType(String? value) => super.reportType = value;

  @override
  @Index(unique: true)
  String? get businessId => super.businessId;
  @override
  set businessId(String? value) => super.businessId = value;

  // Format for displaying currency values
  @ignore
  NumberFormat get currencyFormat =>
      NumberFormat.currency(locale: 'en_US', symbol: '\$');

  // Get all transactions filtered by the current report criteria
  @ignore
  List<TransactionIsar> get filteredTransactions {
    return createMockTransactions();
  }

  // Get category data for charts
  @ignore
  Map<String, double> get categoryData {
    final result = <String, double>{};
    if (categoryNames != null &&
        incomeByCategory != null &&
        expenseByCategory != null) {
      for (int i = 0;
          i < categoryNames!.length && i < incomeByCategory!.length;
          i++) {
        result[categoryNames![i]] = incomeByCategory![i];
      }
    }
    return result;
  }

  // Get daily transaction data
  @ignore
  List<DailyTransactionData> get dailyData {
    final result = <DailyTransactionData>[];
    if (transactionDates != null &&
        incomeValues != null &&
        expenseValues != null) {
      for (int i = 0;
          i < transactionDates!.length &&
              i < incomeValues!.length &&
              i < expenseValues!.length;
          i++) {
        result.add(DailyTransactionData(
          date: transactionDates![i],
          income: incomeValues![i],
          expense: expenseValues![i],
        ));
      }
    }
    return result;
  }

  // Mock method to create sample transactions for the report
  List<TransactionIsar> createMockTransactions() {
    // This is a placeholder implementation
    // In a real app, this data would be retrieved from a database
    final transactions = <TransactionIsar>[];

    // Return empty list for now
    return transactions;
  }

  int? totalTransactions;
  double? totalIncome;
  double? totalExpense;
  double? netBalance;
  double? averageTransactionAmount;

  // Income vs Expense by category
  List<String>? categoryNames;
  List<double>? incomeByCategory;
  List<double>? expenseByCategory;
  List<int>? categoryColors;

  // Monthly transaction data
  List<DateTime>? transactionDates;
  List<double>? incomeValues;
  List<double>? expenseValues;

  TransactionsReportDataIsar();

  /// Factory constructor to create a new transactions report
  factory TransactionsReportDataIsar.create({
    required String title,
    DateTime? startDate,
    DateTime? endDate,
    String? filterCriteria,
    int? totalTransactions,
    double? totalIncome,
    double? totalExpense,
    double? netBalance,
    double? averageTransactionAmount,
    List<String>? categoryNames,
    List<double>? incomeByCategory,
    List<double>? expenseByCategory,
    List<int>? categoryColors,
    List<DateTime>? transactionDates,
    List<double>? incomeValues,
    List<double>? expenseValues,
  }) {
    final report = TransactionsReportDataIsar();

    // Initialize the base report properties
    report.initializeReport(
      reportType: 'transaction',
      title: title,
      startDate: startDate,
      endDate: endDate,
      filterCriteria: filterCriteria,
    );

    // Set the transaction-specific properties
    report.totalTransactions = totalTransactions;
    report.totalIncome = totalIncome;
    report.totalExpense = totalExpense;
    report.netBalance = netBalance;
    report.averageTransactionAmount = averageTransactionAmount;
    report.categoryNames = categoryNames;
    report.incomeByCategory = incomeByCategory;
    report.expenseByCategory = expenseByCategory;
    report.categoryColors = categoryColors;
    report.transactionDates = transactionDates;
    report.incomeValues = incomeValues;
    report.expenseValues = expenseValues;

    return report;
  }

  @override
  List<DataColumn> getTableColumns() {
    return [
      const DataColumn(label: Text('Category')),
      const DataColumn(label: Text('Income'), numeric: true),
      const DataColumn(label: Text('Expense'), numeric: true),
      const DataColumn(label: Text('Net'), numeric: true),
    ];
  }

  @override
  List<DataRow> getTableRows() {
    final rows = <DataRow>[];

    // Generate rows for each category
    if (categoryNames != null &&
        incomeByCategory != null &&
        expenseByCategory != null) {
      for (int i = 0;
          i < categoryNames!.length &&
              i < incomeByCategory!.length &&
              i < expenseByCategory!.length;
          i++) {
        final income = incomeByCategory![i];
        final expense = expenseByCategory![i];
        final net = income - expense;

        rows.add(DataRow(cells: [
          DataCell(Text(categoryNames![i])),
          DataCell(Text('\$${income.toStringAsFixed(2)}')),
          DataCell(Text('\$${expense.toStringAsFixed(2)}')),
          DataCell(Text('\$${net.toStringAsFixed(2)}')),
        ]));
      }
    }

    // Add totals row
    rows.add(DataRow(cells: [
      const DataCell(
          Text('Total', style: TextStyle(fontWeight: FontWeight.bold))),
      DataCell(Text('\$${totalIncome?.toStringAsFixed(2) ?? '0.00'}',
          style: const TextStyle(fontWeight: FontWeight.bold))),
      DataCell(Text('\$${totalExpense?.toStringAsFixed(2) ?? '0.00'}',
          style: const TextStyle(fontWeight: FontWeight.bold))),
      DataCell(Text('\$${netBalance?.toStringAsFixed(2) ?? '0.00'}',
          style: const TextStyle(fontWeight: FontWeight.bold))),
    ]));

    return rows;
  }

  @override
  Map<String, dynamic> getSummaryData() {
    return {
      'Total Transactions': totalTransactions ?? 0,
      'Total Income': '\$${totalIncome?.toStringAsFixed(2) ?? '0.00'}',
      'Total Expenses': '\$${totalExpense?.toStringAsFixed(2) ?? '0.00'}',
      'Net Balance': '\$${netBalance?.toStringAsFixed(2) ?? '0.00'}',
    };
  }

  @override
  List<ChartDataIsar> getChartData() {
    final result = <ChartDataIsar>[];

    // Add income by category chart data
    if (categoryNames != null &&
        incomeByCategory != null &&
        categoryColors != null) {
      for (int i = 0;
          i < categoryNames!.length &&
              i < incomeByCategory!.length &&
              i < categoryColors!.length;
          i++) {
        result.add(ChartDataIsar()
          ..label = categoryNames![i]
          ..value = incomeByCategory![i]
          ..colorValue = Colors.primaries[i % Colors.primaries.length].r
              .toInt()); // Use .r instead of .red
      }
    }

    return result;
  }

  // Helper method to get expense chart data
  List<ChartDataIsar> getExpenseChartData() {
    final result = <ChartDataIsar>[];

    // Add expense by category chart data
    if (categoryNames != null &&
        expenseByCategory != null &&
        categoryColors != null) {
      for (int i = 0;
          i < categoryNames!.length &&
              i < expenseByCategory!.length &&
              i < categoryColors!.length;
          i++) {
        result.add(ChartDataIsar()
          ..label = categoryNames![i]
          ..value = expenseByCategory![i]
          ..colorValue = Colors.primaries[i % Colors.primaries.length].r
              .toInt()); // Use .r instead of .red
      }
    }

    return result;
  }

  // Helper method to get time series income/expense data
  List<List<ChartDataIsar>> getTimeSeriesChartData() {
    final incomeData = <ChartDataIsar>[];
    final expenseData = <ChartDataIsar>[];

    // Create time series chart data for income
    if (transactionDates != null && incomeValues != null) {
      for (int i = 0;
          i < transactionDates!.length && i < incomeValues!.length;
          i++) {
        incomeData.add(ChartDataIsar()
          ..date = transactionDates![i]
          ..value = incomeValues![i]
          ..colorValue = Colors.green.r.toInt()); // Use .r instead of .red
      }
    }

    // Create time series chart data for expense
    if (transactionDates != null && expenseValues != null) {
      for (int i = 0;
          i < transactionDates!.length && i < expenseValues!.length;
          i++) {
        expenseData.add(ChartDataIsar()
          ..date = transactionDates![i]
          ..value = expenseValues![i]
          ..colorValue = Colors.red.r.toInt()); // Use .r instead of .red
      }
    }

    return [incomeData, expenseData];
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'totalTransactions': totalTransactions,
      'totalIncome': totalIncome,
      'totalExpense': totalExpense,
      'netBalance': netBalance,
      'averageTransactionAmount': averageTransactionAmount,
      'categoryNames': categoryNames,
      'incomeByCategory': incomeByCategory,
      'expenseByCategory': expenseByCategory,
      'categoryColors': categoryColors,
      'transactionDates':
          transactionDates?.map((date) => date.toIso8601String()).toList(),
      'incomeValues': incomeValues,
      'expenseValues': expenseValues,
    });
    return map;
  }

  factory TransactionsReportDataIsar.fromMap(Map<String, dynamic> map) {
    final report = TransactionsReportDataIsar();

    // Initialize the base properties
    report.initFromMap(map);

    // Set transaction-specific properties
    report.totalTransactions = map['totalTransactions'] as int?;
    report.totalIncome = map['totalIncome'] as double?;
    report.totalExpense = map['totalExpense'] as double?;
    report.netBalance = map['netBalance'] as double?;
    report.averageTransactionAmount =
        map['averageTransactionAmount'] as double?;

    // Handle lists
    if (map['categoryNames'] != null) {
      report.categoryNames = List<String>.from(map['categoryNames'] as List);
    }

    if (map['incomeByCategory'] != null) {
      report.incomeByCategory =
          List<double>.from(map['incomeByCategory'] as List);
    }

    if (map['expenseByCategory'] != null) {
      report.expenseByCategory =
          List<double>.from(map['expenseByCategory'] as List);
    }

    if (map['categoryColors'] != null) {
      report.categoryColors = List<int>.from(map['categoryColors'] as List);
    }

    if (map['transactionDates'] != null) {
      report.transactionDates = (map['transactionDates'] as List)
          .map((dateStr) => DateTime.parse(dateStr as String))
          .toList();
    }

    if (map['incomeValues'] != null) {
      report.incomeValues = List<double>.from(map['incomeValues'] as List);
    }

    if (map['expenseValues'] != null) {
      report.expenseValues = List<double>.from(map['expenseValues'] as List);
    }

    return report;
  }
}

/// Class to represent daily transaction data for charts
class DailyTransactionData {
  final DateTime date;
  final double income;
  final double expense;

  DailyTransactionData({
    required this.date,
    required this.income,
    required this.expense,
  });

  double get net => income - expense;
}
