import 'package:flutter/material.dart';
import '../../../constants/app_bar.dart';
import '../../Notifications/models/notification_settings_isar.dart';
import '../../Notifications/services/notifications_handler.dart';
import '../../../utils/message_utils.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  final NotificationsHandler _notificationsHandler = NotificationsHandler.instance;
  
  NotificationSettingsIsar? _settings;
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      setState(() => _isLoading = true);
      
      final settings = await _notificationsHandler.getNotificationSettings();
      
      setState(() {
        _settings = settings ?? NotificationSettingsIsar();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        MessageUtils.showError(context, 'Error loading settings: $e');
      }
    }
  }

  Future<void> _saveSettings() async {
    if (_settings == null) return;
    
    try {
      setState(() => _isSaving = true);
      
      _settings!.updatedAt = DateTime.now();
      await _notificationsHandler.saveNotificationSettings(_settings!);
      
      setState(() => _isSaving = false);
      
      if (mounted) {
        MessageUtils.showSuccess(context, 'Settings saved successfully');
      }
    } catch (e) {
      setState(() => _isSaving = false);
      if (mounted) {
        MessageUtils.showError(context, 'Error saving settings: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarConfig.withBack(
        title: 'Notification Settings',
        context: context,
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveSettings,
              tooltip: 'Save Settings',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildSettingsContent(),
    );
  }

  Widget _buildSettingsContent() {
    if (_settings == null) {
      return const Center(
        child: Text('Failed to load settings'),
      );
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildGeneralSettings(),
        const SizedBox(height: 24),
        _buildNotificationTypes(),
        const SizedBox(height: 24),
        _buildMaintenanceSettings(),
        const SizedBox(height: 24),
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildGeneralSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: const Color(0xFF2E7D32)),
                const SizedBox(width: 8),
                Text(
                  'General Settings',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Enable Notifications'),
              subtitle: const Text('Turn on/off all notifications'),
              value: _settings!.notificationsEnabled,
              activeColor: const Color(0xFF2E7D32),
              onChanged: (value) {
                setState(() {
                  _settings!.notificationsEnabled = value;
                });
              },
            ),
            SwitchListTile(
              title: const Text('In-App Notifications'),
              subtitle: const Text('Show notifications within the app'),
              value: _settings!.inAppNotificationsEnabled,
              activeColor: const Color(0xFF2E7D32),
              onChanged: _settings!.notificationsEnabled ? (value) {
                setState(() {
                  _settings!.inAppNotificationsEnabled = value;
                });
              } : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationTypes() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.category, color: const Color(0xFF1976D2)),
                const SizedBox(width: 8),
                Text(
                  'Notification Types',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF1976D2),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTypeSwitch(
              'Health Notifications',
              'Vaccination reminders, health alerts',
              Icons.medical_services,
              const Color(0xFF1976D2),
              _settings!.healthNotificationsEnabled,
              (value) => setState(() => _settings!.healthNotificationsEnabled = value),
            ),
            _buildTypeSwitch(
              'Breeding Notifications',
              'Heat cycles, pregnancy updates',
              Icons.pets,
              const Color(0xFF7B1FA2),
              _settings!.breedingNotificationsEnabled,
              (value) => setState(() => _settings!.breedingNotificationsEnabled = value),
            ),
            _buildTypeSwitch(
              'Milk Notifications',
              'Production alerts, quality issues',
              Icons.water_drop,
              const Color(0xFF388E3C),
              _settings!.milkNotificationsEnabled,
              (value) => setState(() => _settings!.milkNotificationsEnabled = value),
            ),
            _buildTypeSwitch(
              'Event Notifications',
              'Upcoming events, reminders',
              Icons.event,
              const Color(0xFF00796B),
              _settings!.eventsNotificationsEnabled,
              (value) => setState(() => _settings!.eventsNotificationsEnabled = value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeSwitch(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value && _settings!.notificationsEnabled,
        activeColor: color,
        onChanged: _settings!.notificationsEnabled ? onChanged : null,
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildMaintenanceSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.cleaning_services, color: const Color(0xFFD32F2F)),
                const SizedBox(width: 8),
                Text(
                  'Maintenance',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFFD32F2F),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: Icon(Icons.storage, color: const Color(0xFFD32F2F)),
              title: const Text('Max Notifications to Keep'),
              subtitle: Text('Currently: ${_settings!.maxNotificationsToKeep}'),
              trailing: DropdownButton<int>(
                value: _settings!.maxNotificationsToKeep,
                items: [50, 100, 200, 500].map((value) {
                  return DropdownMenuItem(
                    value: value,
                    child: Text('$value'),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _settings!.maxNotificationsToKeep = value;
                    });
                  }
                },
              ),
              contentPadding: EdgeInsets.zero,
            ),
            ListTile(
              leading: Icon(Icons.auto_delete, color: const Color(0xFFD32F2F)),
              title: const Text('Auto-delete Read After'),
              subtitle: Text('Currently: ${_settings!.autoDeleteReadAfterDays} days'),
              trailing: DropdownButton<int>(
                value: _settings!.autoDeleteReadAfterDays,
                items: [7, 14, 30, 60, 90].map((value) {
                  return DropdownMenuItem(
                    value: value,
                    child: Text('$value days'),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _settings!.autoDeleteReadAfterDays = value;
                    });
                  }
                },
              ),
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            icon: const Icon(Icons.delete_sweep),
            label: const Text('Clear All Read Notifications'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFD32F2F),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            onPressed: _clearAllReadNotifications,
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            icon: const Icon(Icons.refresh),
            label: const Text('Reset to Defaults'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            onPressed: _resetToDefaults,
          ),
        ),
      ],
    );
  }

  Future<void> _clearAllReadNotifications() async {
    try {
      await _notificationsHandler.deleteAllReadNotifications();
      if (mounted) {
        MessageUtils.showSuccess(context, 'All read notifications cleared');
      }
    } catch (e) {
      if (mounted) {
        MessageUtils.showError(context, 'Error clearing notifications: $e');
      }
    }
  }

  void _resetToDefaults() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('Are you sure you want to reset all notification settings to defaults?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _settings = NotificationSettingsIsar();
              });
              _saveSettings();
            },
            style: TextButton.styleFrom(foregroundColor: const Color(0xFFD32F2F)),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
