import 'package:flutter/material.dart';

enum EventType {
  breeding,
  pregnancyCheck,
  dryOff,
  calving,
  vaccination,
  healthCheckup,
  weightMeasurement,
  deworming,
  purchased,
  sold,
  miscellaneous,
  custom
}

enum EventPriority {
  low,
  medium,
  high
}

class CustomEventType {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;

  CustomEventType({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon.codePoint,
      'color': (color.r.toInt() << 16) | (color.g.toInt() << 8) | color.b.toInt() | (color.a.toInt() << 24),
    };
  }

  factory CustomEventType.fromMap(Map<String, dynamic> map) {
    return CustomEventType(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      icon: IconData(map['icon'], fontFamily: 'MaterialIcons'),
      color: Color(map['color']),
    );
  }

  Color getLightColor() {
    return HSLColor.fromColor(color).withLightness(0.8).toColor();
  }
}

class FarmEvent {
  final String id;
  final String title;
  final String description;
  final DateTime date;
  final TimeOfDay time;
  final EventType type;
  final String? customTypeId; // ID of custom event type if type is custom
  final EventPriority priority;
  final String? cattleId;
  final bool isCompleted;
  final DateTime createdAt;
  final DateTime? completedAt;

  FarmEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.date,
    required this.time,
    required this.type,
    this.customTypeId,
    required this.priority,
    this.cattleId,
    this.isCompleted = false,
    required this.createdAt,
    this.completedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'date': date.toIso8601String(),
      'time': {'hour': time.hour, 'minute': time.minute},
      'type': type.toString(),
      'customTypeId': customTypeId,
      'priority': priority.toString(),
      'cattleId': cattleId,
      'isCompleted': isCompleted,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
    };
  }

  factory FarmEvent.fromMap(Map<String, dynamic> map) {
    return FarmEvent(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      date: DateTime.parse(map['date']),
      time: TimeOfDay(
        hour: map['time']['hour'],
        minute: map['time']['minute'],
      ),
      type: EventType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => EventType.miscellaneous,
      ),
      customTypeId: map['customTypeId'],
      priority: EventPriority.values.firstWhere(
        (e) => e.toString() == map['priority'],
        orElse: () => EventPriority.medium,
      ),
      cattleId: map['cattleId'],
      isCompleted: map['isCompleted'],
      createdAt: DateTime.parse(map['createdAt']),
      completedAt: map['completedAt'] != null
          ? DateTime.parse(map['completedAt'])
          : null,
    );
  }

  FarmEvent copyWith({
    String? title,
    String? description,
    DateTime? date,
    TimeOfDay? time,
    EventType? type,
    String? customTypeId,
    EventPriority? priority,
    String? cattleId,
    bool? isCompleted,
    DateTime? completedAt,
  }) {
    return FarmEvent(
      id: id,
      title: title ?? this.title,
      description: description ?? this.description,
      date: date ?? this.date,
      time: time ?? this.time,
      type: type ?? this.type,
      customTypeId: customTypeId ?? this.customTypeId,
      priority: priority ?? this.priority,
      cattleId: cattleId ?? this.cattleId,
      isCompleted: isCompleted ?? this.isCompleted,
      createdAt: createdAt,
      completedAt: completedAt ?? this.completedAt,
    );
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      