x ISO8859_9 SJIS output_logits != nullptr TO Abort at %s:%d: %s
 Total allocation for buffer  Vertex %d is not unit length util/geometry/mutable_s2shape_index.cc , adding  Loop %d: empty loops are not allowed Returning all points (max_results/max_distance/region not set) Growing hashtable overflows size_type GPU Compilation nullptr NN API returned error %s at line %d while %s for tensor '%s'.
 ANEURALNETWORKS_BAD_STATE Failed to rename to %s, error: %s ANeuralNetworksMemoryDesc_free ANeuralNetworksMemory_createFromDesc ANeuralNetworksEvent_createFromSyncFenceFd ANeuralNetworksEvent_getSyncFenceFd RESIZE_BILINEAR SOFTMAX MAXIMUM NEG PACK FLOOR_MOD RANGE GATHER_ND DENSIFY BUCKETIZE SIGN failed to associate variable tensors with tensor %d: only kTfLiteFloat32 variable tensors are supported invalid stride width %d in node #%d unexpected number of shape dimensions (%d) in tensor #%d in %s node #%d: expected at least a 1D tensor unexpected number of dimensions %d in the output shape in node %d failed to setup XNNPACK runtime failed to invoke XNNPACK runtime Only uint8_t and int16_t outputs are supported with uint8_t inputs currently, got %s. 16.0 / 256 spectrogram_row.size() NumDimensions(rhs_data) >= 2 input_to_input_weights->dims->data[0] output_gate_bias->dims->data[0] bw_recurrent_to_output_weights->dims->size fw_num_units Cast Densify_output batch_dims <= axis op_data->else_subgraph_index < subgraphs->size() Logical ops only support bool type. NumDimensions(weight) cell_layer_norm_coefficients->dims->data[0] NumDimensions(input_tensor) Invalid sigma value for soft NMS: %f third_party/tensorflow/lite/kernels/pack.cc input0->type RandomUniform third_party/tensorflow/lite/kernels/range.cc third_party/tensorflow/lite/kernels/slice.cc num_splits != 0 output_flat_size NumElements(op_context.begin) input->quantization.type != kTfLiteNoQuantization output_values->type third_party/tensorflow/lite/kernels/unique.cc processor CPU revision CPU architecture fastmult SHW-M380S x3 sphinx hi3650 marlin ATM op_data != nullptr num_threads should be >= 0 or just -1 to let TFLite runtime set the value. No buffers in the model.
 failed to prepare ResourceVariable ./third_party/tensorflow/lite/experimental/resource/static_hashtable.h barhopper.deep_learning.BarcodeDetectorClientOptions acceleration.HangDetectionSettings third_party/absl/status/statusor.cc ./third_party/protobuf/extension_set_inl.h Can't  String field Stack trace:
 *** Check failure stack trace: ***
 only integers, absl::LogSeverity enumerators, and DFATAL are accepted \/ ReaderTryLock failed  Check (v & (kMuWait | kMuWrWait)) != kMuWrWait failed: %s: Mutex corrupt: waiting writer with no waiters: %p sum >= a  #types= September Dec stoi %s failed to release mutex sp ISO8859_8 code128MinCodeLength codabarMinCodeLength code39UseCheckDigit InitializeTfliteRuntime() photos/vision/barhopper/deep_learning/detector/barcode_detector_client.cc ", "shortToken": " DCG Features Tag third_party/halide/halide/src/runtime/tracing.cpp:131 halide_abort_if_false() failed: success && "Could not write to trace file"
  dimensions  is null, but the pipeline will access it on the host. ,  Printer buffer allocation failed.
 IsValid() Should never be called Shape %d has invalid dimension: %d  vertex ids and  Edge %d crosses edge %d InitializeWithFallback already called AllocateTensors failed, recreating interpreter without delegation and retrying.  plugin. Have you linked in the   timed out after  ABORTED type.googleapis.com/util.MessageSetPayload adding operand split_size > 0 ASharedMemory_create ANeuralNetworksCompilation_setPreference ANeuralNetworksEvent_free SL_ANeuralNetworksDiagnosticExecutionInfo_getHardwareExecutionTimeNanos HASHTABLE_LOOKUP CUSTOM SELECT_V2 invalid depth multiplier %d in node #%d unsupported non-default weights format in node #%d unsupported input-product-to-output scale in %s, node #%d unsupported number (%d) of scale quantization parameters for UINT8 tensor %d in XNNPACK delegate num_outputs failed to get XNNPACK profile information. input->type Only float32, uint8, int8 and int16 are supported currently, got %s. -128 third_party/tensorflow/lite/kernels/add.cc HaveSameShapes(input1, input) spectrogram_output.empty() || (spectrogram_output[0].size() == output_width) fw_recurrent_to_output_weights->type bw_aux_input_to_input_weights->dims->data[0] input_type filter->type SizeOfDimension(input, 0) Type %s with filter type %s not currently supported. nms_score_threshold SizeOfDimension(weights, 0) third_party/tensorflow/lite/kernels/embedding_lookup_sparse.cc Lookup size overflowed. is_optional_bias_float gather index out of bounds (NumElements(params) == 0 && NumElements(indices) == 0) || NumElements(params) > 0 NumElements(cond) NumDimensions(hash) output_layer_norm_coefficients != nullptr Found NMS op with invalid num inputs: %d op_context.constant_values->type optimized_ops::AveragePool(op_params, GetTensorShape(input), GetTensorData<uint8_t>(input), GetTensorShape(output), GetTensorData<uint8_t>(output)) NumDimensions(limit) num_input_elements SizeOfDimension(input, batch_dim) third_party/tensorflow/lite/kernels/sparse_to_dense.cc NumElements(default_value) Indices dimensions problem, got %d dimensions NumDimensions(op_context.begin) third_party/tensorflow/lite/kernels/strided_slice.cc StridedSlice op only supports 1D-5D input arrays. Type %s is currently not supported by StridedSlice. crc32 atomics stingray tostab12BA Amlogic Meson8B flounder Renesas tensor.data.raw != nullptr SetTensorParametersReadOnly is disallowed when graph is immutable. Unhandled fully-connected weights format. third_party/tensorflow/lite/arena_planner.cc Type %d is unsupported. Only float16, float32, float64, int8, int16, int32, int64, uint8, uint64, bool, complex64 and complex128 supported currently. dst != nullptr third_party/protobuf/wire_format_lite.cc waitp->thread->waitp == nullptr level >= 1 block not big enough for even one level  spec=' third_party/absl/base/internal/throw_delegate.cc l vector s28 width_ <= row_stride_ ISO8859_2 EUC_CN  upceMinConsistentLines width >= 0 && height >= 0 photos/vision/barhopper/deep_learning/decoder/oned_decoder_client.cc barhopper::deep_learning::OnedDecoderClient is created successfully. PerformNms(thresholded_barcodes, false, result) interpreter_builder(interpreter_out) == kTfLiteOk  ORG DCS TYPE BEGIN:VCALENDAR PASSWORD third_party/halide/halide/src/runtime/thread_pool_common.h:155 halide_abort_if_false() failed: bytes == limit && "Logic error in thread pool work queue initialization.\n"
 End produce float Buffer has both host and device dirty bits set.
 Chain %d of shape %d isn't closed One or more duplicate polygon edges detected Pass  BN_add(r.bn_.get(), a->bn_.get(), b->bn_.get()) Unknown NNAPI error code:  Unable to get graph execution plan.
 nnapi error: unable to open both library %s (%s) and library %s (%s)
 ANeuralNetworksModel_free SPACE_TO_DEPTH LESS NON_MAX_SUPPRESSION_V5 UNSORTED_SEGMENT_SUM invalid stride height %d in %s node #%