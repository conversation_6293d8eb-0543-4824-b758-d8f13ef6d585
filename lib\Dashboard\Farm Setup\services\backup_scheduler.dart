import 'dart:async';
import 'dart:io';
import '../../../services/logging_service.dart';
import '../models/backup_settings_isar.dart';
import 'farm_setup_handler.dart';
import 'backup_service.dart';

/// Automatic backup scheduler service
class BackupScheduler {
  static final LoggingService _logger = LoggingService.instance;
  final FarmSetupHandler _farmSetupHandler;
  final BackupService _backupService;
  
  Timer? _schedulerTimer;
  bool _isRunning = false;
  DateTime? _nextScheduledBackup;

  // Singleton instance
  static final BackupScheduler _instance = BackupScheduler._internal();
  static BackupScheduler get instance => _instance;

  // Private constructor
  BackupScheduler._internal() 
      : _farmSetupHandler = FarmSetupHandler.instance,
        _backupService = BackupService.instance;

  /// Start the backup scheduler
  Future<void> start() async {
    if (_isRunning) {
      _logger.info('Backup scheduler is already running');
      return;
    }

    try {
      _logger.info('Starting backup scheduler');
      _isRunning = true;
      
      // Check immediately if a backup is needed
      await _checkAndPerformBackup();
      
      // Schedule periodic checks every hour
      _schedulerTimer = Timer.periodic(
        const Duration(hours: 1),
        (_) => _checkAndPerformBackup(),
      );
      
      _logger.info('Backup scheduler started successfully');
    } catch (e) {
      _logger.severe('Error starting backup scheduler: $e');
      _isRunning = false;
    }
  }

  /// Stop the backup scheduler
  void stop() {
    if (!_isRunning) {
      _logger.info('Backup scheduler is not running');
      return;
    }

    _logger.info('Stopping backup scheduler');
    _schedulerTimer?.cancel();
    _schedulerTimer = null;
    _isRunning = false;
    _nextScheduledBackup = null;
    _logger.info('Backup scheduler stopped');
  }

  /// Check if backup is needed and perform it
  Future<void> _checkAndPerformBackup() async {
    try {
      final settings = await _farmSetupHandler.getBackupSettings();
      
      if (!settings.autoBackupEnabled) {
        _logger.info('Auto backup is disabled');
        return;
      }

      if (await _isBackupNeeded(settings)) {
        _logger.info('Automatic backup is needed, starting backup...');
        await _performScheduledBackup(settings);
      } else {
        _updateNextScheduledBackup(settings);
      }
    } catch (e) {
      _logger.severe('Error during scheduled backup check: $e');
    }
  }

  /// Check if a backup is needed based on settings
  Future<bool> _isBackupNeeded(BackupSettingsIsar settings) async {
    if (settings.lastBackupDate == null) {
      _logger.info('No previous backup found, backup needed');
      return true;
    }

    final daysSinceLastBackup = DateTime.now()
        .difference(settings.lastBackupDate!)
        .inDays;

    final isNeeded = daysSinceLastBackup >= settings.autoBackupFrequency;
    
    if (isNeeded) {
      _logger.info('Backup needed: $daysSinceLastBackup days since last backup (frequency: ${settings.autoBackupFrequency} days)');
    } else {
      _logger.info('Backup not needed: $daysSinceLastBackup days since last backup (frequency: ${settings.autoBackupFrequency} days)');
    }

    return isNeeded;
  }

  /// Perform the scheduled backup
  Future<void> _performScheduledBackup(BackupSettingsIsar settings) async {
    try {
      _logger.info('Starting scheduled backup');
      
      // Check if device has enough storage space
      if (!await _hasEnoughStorageSpace(settings.backupLocation)) {
        _logger.warning('Insufficient storage space for backup');
        return;
      }

      // Perform the backup
      final result = await _backupService.createBackup(
        includeMetadata: true,
        onProgress: (progress) {
          _logger.info('Backup progress: ${(progress * 100).toStringAsFixed(1)}%');
        },
      );

      if (result.success) {
        _logger.info('Scheduled backup completed successfully: ${result.backupPath}');
        
        // Send notification if needed (you might want to implement this)
        await _sendBackupNotification(true, result.message);
        
        // Update next scheduled backup time
        _updateNextScheduledBackup(settings);
      } else {
        _logger.severe('Scheduled backup failed: ${result.message}');
        await _sendBackupNotification(false, result.message);
      }
    } catch (e) {
      _logger.severe('Error during scheduled backup: $e');
      await _sendBackupNotification(false, 'Backup failed: $e');
    }
  }

  /// Check if there's enough storage space for backup
  Future<bool> _hasEnoughStorageSpace(String backupLocation) async {
    try {
      if (backupLocation.isEmpty) {
        return true; // Let the backup service handle default location
      }

      final directory = Directory(backupLocation);
      if (!await directory.exists()) {
        return true; // Directory will be created
      }

      // Get available space (this is a simplified check)
      // In a real implementation, you might want to use a plugin like disk_space
      
      // For now, just check if directory is accessible
      return true;
    } catch (e) {
      _logger.warning('Error checking storage space: $e');
      return false;
    }
  }

  /// Update the next scheduled backup time
  void _updateNextScheduledBackup(BackupSettingsIsar settings) {
    if (settings.lastBackupDate != null) {
      _nextScheduledBackup = settings.lastBackupDate!
          .add(Duration(days: settings.autoBackupFrequency));
      
      _logger.info('Next scheduled backup: $_nextScheduledBackup');
    }
  }

  /// Send backup notification (placeholder for future implementation)
  Future<void> _sendBackupNotification(bool success, String message) async {
    try {
      // This is a placeholder for notification implementation
      // You might want to use local notifications or in-app notifications
      _logger.info('Backup notification: ${success ? 'SUCCESS' : 'FAILURE'} - $message');
      
      // TODO: Implement actual notification system
      // Example: await NotificationService.instance.showNotification(
      //   title: success ? 'Backup Successful' : 'Backup Failed',
      //   body: message,
      // );
    } catch (e) {
      _logger.warning('Error sending backup notification: $e');
    }
  }

  /// Force a backup now (manual trigger)
  Future<BackupResult> forceBackupNow() async {
    try {
      _logger.info('Force backup triggered manually');
      
      final result = await _backupService.createBackup(
        includeMetadata: true,
        onProgress: (progress) {
          _logger.info('Manual backup progress: ${(progress * 100).toStringAsFixed(1)}%');
        },
      );

      if (result.success) {
        _logger.info('Manual backup completed successfully');
        
        // Update scheduler state
        final settings = await _farmSetupHandler.getBackupSettings();
        _updateNextScheduledBackup(settings);
      }

      return result;
    } catch (e) {
      _logger.severe('Error during manual backup: $e');
      return BackupResult(
        success: false,
        message: 'Manual backup failed: $e',
      );
    }
  }

  /// Get scheduler status
  Map<String, dynamic> getStatus() {
    return {
      'isRunning': _isRunning,
      'nextScheduledBackup': _nextScheduledBackup?.toIso8601String(),
      'schedulerActive': _schedulerTimer?.isActive ?? false,
    };
  }

  /// Get time until next backup
  Duration? getTimeUntilNextBackup() {
    if (_nextScheduledBackup == null) {
      return null;
    }
    
    final now = DateTime.now();
    if (_nextScheduledBackup!.isBefore(now)) {
      return Duration.zero;
    }
    
    return _nextScheduledBackup!.difference(now);
  }

  /// Check if backup is overdue
  bool isBackupOverdue() {
    if (_nextScheduledBackup == null) {
      return false;
    }

    return DateTime.now().isAfter(_nextScheduledBackup!);
  }

  /// Update scheduler when settings change (public method)
  Future<void> updateSchedulerSettings() async {
    try {
      final settings = await _farmSetupHandler.getBackupSettings();
      _updateNextScheduledBackup(settings);
      _logger.info('Scheduler settings updated successfully');
    } catch (e) {
      _logger.warning('Failed to update scheduler settings: $e');
    }
  }
}
