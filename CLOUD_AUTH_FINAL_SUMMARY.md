      morningYield: morningYield,
                  eveningYield: eveningYield,
                  notes: notes.isNotEmpty ? notes : null,
                );
                Navigator.pop(context);
                await _milkService.addMilkRecord(record);
                _loadData();
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveCard(
      child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Production Summary (7-day average)',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  SizedBox(height: ResponsiveSpacing.getMD(context)),
                  _buildSummaryRow('Morning', _summary['morningAverage'] ?? 0),
                  _buildSummaryRow('Evening', _summary['eveningAverage'] ?? 0),
                  const Divider(),
                  _buildSummaryRow('Total', _summary['totalAverage'] ?? 0),
                ],
              ),
            ),
          ),
          SizedBox(height: ResponsiveSpacing.getMD(context)),
          if (_records.isNotEmpty) ...[
            ResponsiveCard(
      child: Padding(
                padding: const EdgeInsets.all(16),
                child: SizedBox(
                  height: 200,
                  child: LineChart(
                    _buildLineChartData(),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, double value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text('${value.toStringAsFixed(1)} L'),
        ],
      ),
    );
  }

  LineChartData _buildLineChartData() {
    final spots = _records
        .take(7)
        .map((record) => FlSpot(
              record.date.millisecondsSinceEpoch.toDouble(),
              record.totalYield,
            ))
        .toList();

    return LineChartData(
      gridData: const FlGridData(show: false),
      titlesData: const FlTitlesData(show: false),
      borderData: FlBorderData(show: true),
      lineBarsData: [
        LineChartBarData(
          spots: spots,
          isCurved: true,
          color: Colors.blue,
          barWidth: 3,
          dotData: const FlDotData(show: true),
        ),
      ],
    );
  }

  Widget _buildRecordsTab() {
    return ListView.builder(
      itemCount: _records.length,
      itemBuilder: (context, index) {
        final record = _records[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ListTile(
            title: Text(DateFormat('yyyy-MM-dd').format(record.date)),
            subtitle: Text(
              'Morning: ${record.morningYield}L\nEvening: ${record.eveningYield}L',
            ),
            trailing: Text(
              'Total: ${record.totalYield}L',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAlertsTab() {
    final lowYieldRecords = _records
        .where((record) => record.totalYield < (_summary['totalAverage'] ?? 0) * 0.8)
        .toList();

    return ListView.builder(
      itemCount: lowYieldRecords.length,
      itemBuilder: (context, index) {
        final record = lowYieldRecords[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          color: Colors.red.shade50,
          child: ListTile(
            leading: const Icon(Icons.warning, color: Colors.red),
            title: Text('Low Yield on ${DateFormat('yyyy-MM-dd').format(record.date)}'),
            subtitle: Text(
              'Total yield (${record.totalYield}L) is below average',
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Scaffold(
      body: Column(
        children: [
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Summary'),
              Tab(text: 'Records'),
              Tab(text: 'Alerts'),
            ],
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildSummaryTab(),
                _buildRecordsTab(),
                _buildAlertsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddRecordDialog,
        child: const Icon(Icons.add),
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
