import 'package:flutter/material.dart';
import '../../../routes/app_routes.dart';

class CattleScreen extends StatelessWidget {
  const CattleScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cattle Management'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.cattleReport,
            ),
            tooltip: 'View Cattle Reports',
          ),
        ],
      ),
      body: const Center(
        child: Text('Cattle Management Screen'),
      ),
    );
  }
}
