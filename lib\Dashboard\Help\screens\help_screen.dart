import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_bar.dart';

class HelpScreen extends StatefulWidget {
  const HelpScreen({Key? key}) : super(key: key);

  @override
  State<HelpScreen> createState() => _HelpScreenState();
}

class _HelpScreenState extends State<HelpScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _filteredFaqs = [];
  bool _isSearching = false;

  final List<Map<String, dynamic>> _allFaqs = [
    {
      'question': 'How do I add a new animal to my farm?',
      'answer':
          'To add a new animal, go to the Dashboard and tap on the "Cattle" tab. Then tap the "+" button in the bottom right corner. Fill in the details and tap "Save".',
      'category': 'Cattle Management',
    },
    {
      'question': 'How do I record a breeding event?',
      'answer':
          'Navigate to the Breeding section, select the female animal, then tap "Add Breeding Record". Enter the breeding details including the sire information and save the record.',
      'category': 'Breeding',
    },
    {
      'question': 'How do I track vaccinations and treatments?',
      'answer':
          'Go to the Health section, select the animal, then tap "Add Health Record". Choose the type of treatment, enter the details, and save the record.',
      'category': 'Health Records',
    },
    {
      'question': 'How do I generate reports?',
      'answer':
          'Navigate to the Reports & Analytics section, select the type of report you want to generate, set any filters or parameters, and tap "Generate Report".',
      'category': 'Reports & Analytics',
    },
    {
      'question': 'How do I set up notifications for important events?',
      'answer':
          'Go to Settings > Notifications, and toggle on the types of notifications you want to receive. You can set custom reminders for vaccinations, heat cycles, and more.',
      'category': 'Settings & Account',
    },
    {
      'question': 'How do I create a new farm?',
      'answer':
          'From the Dashboard, open the side menu and tap "Farm Setup". Then tap the "+" button to add a new farm. Enter the farm details and tap "Save".',
      'category': 'Getting Started',
    },
    {
      'question': 'How do I switch between farms?',
      'answer':
          'Open the side menu, expand the "My Farms" section, and tap on the farm you want to switch to.',
      'category': 'Getting Started',
    },
    {
      'question': 'How do I track milk production?',
      'answer':
          'Go to the Milk section, select the animal, then tap "Add Milk Record". Enter the date, quantity, and quality parameters, then save the record.',
      'category': 'Cattle Management',
    },
    {
      'question': 'How do I monitor pregnancy progress?',
      'answer':
          'Navigate to the Breeding section, then tap on "Pregnancy Records". You\'ll see all active pregnancies with progress tracking and expected calving dates.',
      'category': 'Breeding',
    },
    {
      'question': 'How do I update my profile information?',
      'answer':
          'Open the side menu, tap on "User Account", then tap "Profile Information" to update your personal details.',
      'category': 'Settings & Account',
    },
  ];

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _isSearching = query.isNotEmpty;
      if (_isSearching) {
        _filteredFaqs = _allFaqs
            .where((faq) =>
                faq['question'].toString().toLowerCase().contains(query) ||
                faq['answer'].toString().toLowerCase().contains(query))
            .toList();
      } else {
        _filteredFaqs = [];
      }
    });
  }

  void _navigateToCategory(String category) {
    setState(() {
      _isSearching = true;
      _filteredFaqs = _allFaqs
          .where((faq) => faq['category'].toString() == category)
          .toList();
      _searchController.text = ''; // Clear the search field
    });

    // Show a snackbar with the category name
    MessageUtils.showInfo(context, 'Showing help topics for: $category');
  }

  Future<void> _launchEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      queryParameters: {
        'subject': 'Support Request',
        'body': 'I need help with the following issue:\n\n',
      },
    );

    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        if (mounted) {
          MessageUtils.showError(context, 'Could not launch email client');
        }
      }
    } catch (e) {
      if (mounted) {
        MessageUtils.showError(context, 'Error: $e');
      }
    }
  }

  Future<void> _launchPhone() async {
    final Uri phoneUri = Uri.parse('tel:+15551234567');

    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        if (mounted) {
          MessageUtils.showError(context, 'Could not launch phone app');
        }
      }
    } catch (e) {
      if (mounted) {
        MessageUtils.showError(context, 'Error: $e');
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarConfig.withBack(
        title: 'Help & Support',
        context: context,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildSearchBar(),
          const SizedBox(height: 24),
          if (!_isSearching) _buildHelpCategories(context),
          if (!_isSearching) const SizedBox(height: 24),
          _isSearching ? _buildSearchResults() : _buildFAQSection(context),
          const SizedBox(height: 24),
          _buildContactSupport(context),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return TextField(
      controller: _searchController,
      decoration: InputDecoration(
        hintText: 'Search for help topics...',
        prefixIcon: const Icon(Icons.search),
        suffixIcon: _isSearching
            ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  _searchController.clear();
                  setState(() {
                    _isSearching = false;
                  });
                },
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        filled: true,
        fillColor: Color(0xFFF8F9FA),
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_filteredFaqs.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(
                Icons.search_off,
                size: 48,
                color: Color(0xFF2E7D32),
              ),
              SizedBox(height: 16),
              Text(
                'No results found',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Try different keywords or browse the categories below',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.black87),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            'Search Results (${_filteredFaqs.length})',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _filteredFaqs.length,
          itemBuilder: (context, index) {
            final faq = _filteredFaqs[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8.0),
              child: ExpansionTile(
                title: Text(
                  faq['question'] as String,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                subtitle: Text(
                  'Category: ${faq['category']}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.black87,
                  ),
                ),
                children: [
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                    child: Text(faq['answer'] as String),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildHelpCategories(BuildContext context) {
    final categories = [
      {
        'title': 'Getting Started',
        'icon': Icons.play_circle_outline,
        'color': const Color(0xFF1976D2), // Blue
      },
      {
        'title': 'Cattle Management',
        'icon': Icons.pets,
        'color': const Color(0xFF2E7D32), // Green
      },
      {
        'title': 'Breeding',
        'icon': Icons.favorite,
        'color': const Color(0xFFD32F2F), // Red
      },
      {
        'title': 'Health Records',
        'icon': Icons.medical_services,
        'color': const Color(0xFF7B1FA2), // Purple
      },
      {
        'title': 'Reports & Analytics',
        'icon': Icons.bar_chart,
        'color': const Color(0xFF00796B), // Teal
      },
      {
        'title': 'Settings & Account',
        'icon': Icons.settings,
        'color': const Color(0xFF1565C0), // Deep Blue
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Help Categories',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        LayoutBuilder(
          builder: (context, constraints) {
            final screenWidth = constraints.maxWidth;
            int crossAxisCount = screenWidth > 600 ? 3 : 2;
            double childAspectRatio = screenWidth > 600 ? 1.3 : 1.5;

            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: childAspectRatio,
              ),
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final category = categories[index];
                return Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: InkWell(
                    onTap: () => _navigateToCategory(category['title'] as String),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            category['icon'] as IconData,
                            size: 32,
                            color: category['color'] as Color,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            category['title'] as String,
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildFAQSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Frequently Asked Questions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 5, // Show only the first 5 FAQs
          itemBuilder: (context, index) {
            final faq = _allFaqs[index];
            return ExpansionTile(
              title: Text(
                faq['question'] as String,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                ),
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  child: Text(faq['answer'] as String),
                ),
              ],
            );
          },
        ),
        // "View All" button
        Center(
          child: TextButton.icon(
            icon: const Icon(Icons.list_alt),
            label: const Text('View All FAQs'),
            onPressed: () {
              setState(() {
                _isSearching = true;
                _filteredFaqs = List.from(_allFaqs);
                _searchController.text = ''; // Clear the search field
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildContactSupport(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Contact Support',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                const Text(
                  'Need more help? Our support team is ready to assist you.',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 16),
                Center(
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.email),
                      label: const Text('Email Support'),
                      onPressed: _launchEmail,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 16),
                GestureDetector(
                  onTap: _launchPhone,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.phone,
                          size: 20,
                          color: Theme.of(context)
                              .colorScheme
                              .primary
                              .withAlpha(
                                (Theme.of(context).colorScheme.primary.a * 0.7).toInt()
                              )),
                      const SizedBox(width: 8),
                      Text(
                        'Call us: +****************',
                        style: TextStyle(
                          color: Theme.of(context)
                              .colorScheme
                              .primary
                              .withAlpha(
                                (Theme.of(context).colorScheme.primary.a * 0.7).toInt()
                              ),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.access_time,
                        size: 20, color: Colors.black87),
                    const SizedBox(width: 8),
                    Text(
                      'Available: Mon-Fri, 9AM-5PM',
                      style: TextStyle(
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
