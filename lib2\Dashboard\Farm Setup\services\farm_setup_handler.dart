import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter/foundation.dart';

import 'dart:io';
import 'package:uuid/uuid.dart';

import '../models/breed_category_isar.dart';
import '../models/animal_type_isar.dart';
import '../models/farm_isar.dart';
import '../models/alert_settings_isar.dart';
import '../models/backup_settings_isar.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';
import '../../../Dashboard/Events/models/event_type_isar.dart';
import '../../../services/cloud_data_sync_service.dart';
import '../../../Dashboard/User Account/services/cloud_authentication_service.dart';
import '../models/currency_settings_isar.dart';
import '../models/milk_settings_isar.dart';
import '../../Transactions/models/category_isar.dart';
import '../models/user_role_isar.dart';
import '../models/farm_user_isar.dart';
import '../../../Dashboard/Cattle/models/cattle_isar.dart';
import '../../Transactions/models/transaction_isar.dart';
// Removed multi-farm services - single farm per user

/// Consolidated handler for all Farm Setup module database operations
class FarmSetupHandler extends ChangeNotifier {
  static final Logger _logger = Logger('FarmSetupHandler');
  final IsarService _isarService;

  // Singleton instance
  static final FarmSetupHandler _instance = FarmSetupHandler._internal();
  static FarmSetupHandler get instance => _instance;

  // Private constructor
  FarmSetupHandler._internal() : _isarService = GetIt.instance<IsarService>();

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  // Selected farm ID
  String? _selectedFarmId;

  //=== FARM METHODS ===//

  /// Get the user's farm (single farm per user)
  Future<FarmIsar?> getUserFarm() async {
    try {
      _logger.info('🔍 Getting user farm...');

      final farms = await getAllFarms();
      if (farms.isNotEmpty) {
        final farm = farms.first;
        _logger.info('🔍 User farm: ${farm.name} (${farm.farmBusinessId})');
        return farm;
      }

      _logger.info('🔍 No farm found for user');
      return null;
    } catch (e) {
      _logger.severe('Error getting user farm: $e');
      throw DatabaseException('Failed to retrieve user farm', e.toString());
    }
  }

  /// Legacy method for backward compatibility
  @Deprecated('Use getUserFarm() instead - single farm per user')
  Future<FarmIsar?> getActiveFarm() async {
    return getUserFarm();
  }

  /// Get all farms for the current user (single farm per user)
  Future<List<FarmIsar>> getAllFarms() async {
    try {
      _logger.info('📋 Getting user farm (single farm per user)...');

      // Single farm per user - just get all farms in the user's database
      final farms = await _isar.farmIsars
          .where()
          .sortByName()
          .findAll();

      _logger.info('📋 Found ${farms.length} farm(s) for current user');

      if (farms.isNotEmpty) {
        final farm = farms.first;
        _logger.info('📋 User farm: ${farm.name} (${farm.farmBusinessId})');

        // Fix any cattle that don't have farm association
        await _fixCattleFarmAssociation(farm.farmBusinessId);
      }

      return farms;
    } catch (e) {
      _logger.severe('❌ Error getting user farm: $e');
      throw DatabaseException('Failed to get user farm', e.toString());
    }
  }

  /// Fix cattle that don't have farm association (migration helper)
  Future<void> _fixCattleFarmAssociation(String? farmBusinessId) async {
    if (farmBusinessId == null) return;

    try {
      // Get all cattle without farm association
      final cattleWithoutFarm = await _isar.cattleIsars
          .filter()
          .farmBusinessIdIsNull()
          .findAll();

      if (cattleWithoutFarm.isNotEmpty) {
        _logger.info('🔧 Fixing ${cattleWithoutFarm.length} cattle without farm association');

        await _isar.writeTxn(() async {
          for (final cattle in cattleWithoutFarm) {
            cattle.farmBusinessId = farmBusinessId;
            await _isar.cattleIsars.put(cattle);
          }
        });

        _logger.info('✅ Fixed farm association for ${cattleWithoutFarm.length} cattle');
      }
    } catch (e) {
      _logger.warning('⚠️ Error fixing cattle farm association: $e');
    }
  }

  /// Get selected farm ID
  Future<String> getSelectedFarmId() async {
    try {
      if (_selectedFarmId != null) {
        return _selectedFarmId!;
      }

      // If no selected farm ID, get the ID of the first farm
      final farm = await getActiveFarm();
      if (farm != null) {
        _selectedFarmId = farm.id.toString();
        return _selectedFarmId!;
      }

      return '0'; // Default ID if no farm exists
    } catch (e) {
      _logger.severe('Error getting selected farm ID: $e');
      throw DatabaseException(
          'Failed to retrieve selected farm ID', e.toString());
    }
  }

  /// Legacy method - no longer needed for single farm per user
  @Deprecated('No longer needed - single farm per user')
  Future<void> setActiveFarm(String farmId) async {
    _logger.info('🎯 setActiveFarm called but ignored - single farm per user');
    // No-op for single farm per user
  }



  //=== BREED CATEGORIES ===//

  /// Get all breed categories
  Future<List<BreedCategoryIsar>> getAllBreedCategories() async {
    try {
      final breedCategories =
          await _isar.breedCategoryIsars.where().sortByName().findAll();

      return breedCategories;
    } catch (e) {
      _logger.severe('Error retrieving breed categories: $e');
      throw DatabaseException(
          'Failed to retrieve breed categories', e.toString());
    }
  }

  /// Get breed categories for a specific animal type
  Future<List<BreedCategoryIsar>> getBreedCategoriesForAnimalType(
      String animalTypeId) async {
    try {
      if (animalTypeId.isEmpty) {
        throw ValidationException('Animal type ID is required');
      }

      final breedCategories = await _isar.breedCategoryIsars
          .filter()
          .animalTypeIdEqualTo(animalTypeId)
          .sortByName()
          .findAll();

      return breedCategories;
    } catch (e) {
      _logger.severe(
          'Error retrieving breed categories for animal type: $animalTypeId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to retrieve breed categories for animal type', e.toString());
    }
  }

  /// Add or update a breed category
  Future<void> addOrUpdateBreedCategory(BreedCategoryIsar category) async {
    try {
      await _validateBreedCategory(category);

      await _isar.writeTxn(() async {
        await _isar.breedCategoryIsars.put(category);
      });

      _logger.info('Successfully saved breed category: ${category.businessId}');
    } catch (e) {
      _logger.severe('Error saving breed category: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to save breed category', e.toString());
    }
  }

  /// Delete a breed category
  Future<void> deleteBreedCategory(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Breed category ID is required');
      }

      await _isar.writeTxn(() async {
        final category = await _isar.breedCategoryIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (category == null) {
          throw RecordNotFoundException(
              'Breed category not found: $businessId');
        }

        // Check if category is in use by any cattle
        final cattleCount = await _isar
            .collection<CattleIsar>()
            .filter()
            .breedIdEqualTo(businessId)
            .count();

        if (cattleCount > 0) {
          throw ValidationException(
              'Cannot delete breed category that is in use by $cattleCount cattle'); // Removed braces
        }

        // For now, we'll assume the category isn't in use
        await _isar.breedCategoryIsars.delete(category.id);
      });

      _logger.info('Successfully deleted breed category: $businessId');
    } catch (e) {
      _logger.severe('Error deleting breed category: $businessId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete breed category', e.toString());
    }
  }

  //=== ANIMAL TYPES ===//

  /// Get all animal types
  Future<List<AnimalTypeIsar>> getAllAnimalTypes() async {
    try {
      final animalTypes =
          await _isar.animalTypeIsars.where().sortByName().findAll();

      return animalTypes;
    } catch (e) {
      _logger.severe('Error retrieving animal types: $e');
      throw DatabaseException('Failed to retrieve animal types', e.toString());
    }
  }

  /// Add or update an animal type
  Future<void> addOrUpdateAnimalType(AnimalTypeIsar animalType) async {
    try {
      await _validateAnimalType(animalType);

      await _isar.writeTxn(() async {
        await _isar.animalTypeIsars.put(animalType);
      });

      _logger.info('Successfully saved animal type: ${animalType.businessId}');
    } catch (e) {
      _logger.severe('Error saving animal type: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to save animal type', e.toString());
    }
  }

  /// Delete an animal type
  Future<void> deleteAnimalType(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Animal type ID is required');
      }

      await _isar.writeTxn(() async {
        final animalType = await _isar.animalTypeIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (animalType == null) {
          throw RecordNotFoundException('Animal type not found: $businessId');
        }

        // Check if animal type is in use by breed categories
        final breedCategoryCount = await _isar.breedCategoryIsars
            .filter()
            .animalTypeIdEqualTo(businessId)
            .count();

        if (breedCategoryCount > 0) {
          throw ValidationException(
              'Cannot delete animal type that has breed categories');
        }

        // Check if animal type is in use by cattle
        final cattleCount = await _isar
            .collection<CattleIsar>()
            .filter()
            .animalTypeIdEqualTo(businessId)
            .count();

        if (cattleCount > 0) {
          throw ValidationException(
              'Cannot delete animal type that is in use by $cattleCount cattle'); // Removed braces
        }

        // If not in use, proceed with deletion
        await _isar.animalTypeIsars.delete(animalType.id);
      });

      _logger.info('Successfully deleted animal type: $businessId');
    } catch (e) {
      _logger.severe('Error deleting animal type: $businessId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete animal type', e.toString());
    }
  }

  //=== FARM CONFIGURATION ===//

  /// Get farm configuration - replaced with a simple Map implementation
  Future<Map<String, dynamic>> getFarmConfig() async {
    try {
      _logger.info('Starting getFarmConfig method');

      final farmId = _selectedFarmId ?? await getSelectedFarmId();

      // Return default config
      final config = {
        'farmName': 'Default Farm',
        'location': 'Default Location',
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      _logger.info('Farm location retrieved: ${config['location']}');
      return config;
    } catch (e) {
      _logger.severe('Error getting farm configuration: $e');
      throw DatabaseException(
          'Failed to retrieve farm configuration', e.toString());
    }
  }

  /// Save farm configuration - replaced with a simple logging implementation
  Future<void> saveFarmConfig(Map<String, dynamic> config) async {
    try {
      // Validate the config
      if (config['farmName'] == null || config['farmName'].isEmpty) {
        throw ValidationException('Farm name is required');
      }

      if (config['location'] == null || config['location'].isEmpty) {
        throw ValidationException('Farm location is required');
      }

      _logger.info('Successfully saved farm configuration');
    } catch (e) {
      _logger.severe('Error saving farm configuration: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to save farm configuration', e.toString());
    }
  }

  // Event Types methods
  Future<List<EventTypeIsar>> getEventTypes() async {
    try {
      final isarService = await IsarService.instance;
      final isar = isarService.isar;

      final eventTypes = await isar.eventTypeIsars.where().findAll();
      return eventTypes;
    } catch (e) {
      _logger.severe('Error getting event types: $e');
      throw DatabaseException('Failed to retrieve event types', e.toString());
    }
  }

  /// Save an event type
  Future<void> saveEventType(EventTypeIsar eventType) async {
    try {
      final isarService = await IsarService.instance;
      final isar = isarService.isar;

      await isar.writeTxn(() async {
        await isar.eventTypeIsars.put(eventType);
      });
    } catch (e) {
      _logger.severe('Error saving event type: $e');
      throw DatabaseException('Failed to save event type', e.toString());
    }
  }

  /// Delete an event type
  Future<void> deleteEventType(int id) async {
    try {
      final isarService = await IsarService.instance;
      final isar = isarService.isar;

      await isar.writeTxn(() async {
        await isar.eventTypeIsars.delete(id);
      });
    } catch (e) {
      _logger.severe('Error deleting event type: $e');
      throw DatabaseException('Failed to delete event type', e.toString());
    }
  }

  // Alert Settings methods
  Future<AlertSettingsIsar> getAlertSettings() async {
    try {
      // Get the current farm ID
      final currentFarmId = _selectedFarmId ?? await getSelectedFarmId();

      _logger.info('Getting alert settings for farm: $currentFarmId');

      // Access alert settings collection

      // First try to find settings for the current farm
      final settings = await _isar.alertSettingsIsars
          .filter()
          .farmBusinessIdEqualTo(currentFarmId)
          .findFirst();

      if (settings != null) {
        _logger.info('Found existing alert settings for farm $currentFarmId');
        return settings;
      }

      // If not found, create default settings for this farm
      _logger.info('Creating new alert settings for farm $currentFarmId');
      final newSettings = AlertSettingsIsar.create()
        ..farmBusinessId = currentFarmId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      // Save the new settings
      await _isar.writeTxn(() async {
        await _isar.alertSettingsIsars.put(newSettings);
      });

      return newSettings;
    } catch (e) {
      _logger.severe('Error retrieving alert settings: $e');
      throw DatabaseException(
          'Failed to retrieve alert settings', e.toString());
    }
  }

  Future<void> saveAlertSettings(AlertSettingsIsar settings) async {
    try {
      if (settings.farmBusinessId == null || settings.farmBusinessId!.isEmpty) {
        // If farmBusinessId is missing, set it to the current farm
        final currentFarmId = _selectedFarmId ?? await getSelectedFarmId();
        settings.farmBusinessId = currentFarmId;
        _logger.info('Setting missing farmBusinessId to: $currentFarmId');
      }

      // Update timestamp
      settings.updatedAt = DateTime.now();

      // Save to database
      _logger
          .info('Saving alert settings for farm: ${settings.farmBusinessId}');
      await _isar.writeTxn(() async {
        await _isar.alertSettingsIsars.put(settings);
      });

      _logger.info('Alert settings saved successfully');
    } catch (e) {
      _logger.severe('Error saving alert settings: $e');
      throw DatabaseException('Failed to save alert settings', e.toString());
    }
  }

  // Add the method to get backup settings
  Future<BackupSettingsIsar> getBackupSettings() async {
    try {
      // Get active farm ID
      final farmId = await getActiveFarmId();

      // Try to get existing backup settings for this farm
      final settings = await _isar.backupSettingsIsars
          .filter()
          .farmBusinessIdEqualTo(farmId)
          .findFirst();

      // If settings exist, return them
      if (settings != null) {
        return settings;
      }

      // Otherwise, create and save default settings with a valid path
      // We'll use a placeholder that will be replaced with a real path in the UI
      final defaultSettings = BackupSettingsIsar.create(
        farmBusinessId: farmId,
        autoBackupEnabled: true,
        autoBackupFrequency: 1,
        backupLocation:
            '', // Empty string will be populated with a real path in the UI
        lastBackupDate: null,
      );

      await _isar.writeTxn(() async {
        await _isar.backupSettingsIsars.put(defaultSettings);
      });

      return defaultSettings;
    } catch (e) {
      _logger.severe('Error getting backup settings: $e');
      throw DatabaseException('Failed to get backup settings', e.toString());
    }
  }

  // Add method to save settings
  Future<void> saveBackupSettings(BackupSettingsIsar settings) async {
    try {
      settings.updatedAt = DateTime.now();

      await _isar.writeTxn(() async {
        await _isar.backupSettingsIsars.put(settings);
      });

      _logger.info('Backup settings saved successfully');
    } catch (e) {
      _logger.severe('Error saving backup settings: $e');
      throw DatabaseException('Failed to save backup settings', e.toString());
    }
  }

  /// Update existing backup settings to use new default frequency (1 day instead of 7)
  Future<void> updateBackupFrequencyToNewDefault() async {
    try {
      final existingSettings = await _isar.backupSettingsIsars.where().findAll();

      for (final settings in existingSettings) {
        // Only update if it's still using the old default of 7 days
        if (settings.autoBackupFrequency == 7) {
          _logger.info('Updating backup frequency from 7 days to 1 day for settings: ${settings.businessId}');

          settings.autoBackupFrequency = 1;
          settings.backupFrequencyDays = 1;
          settings.updatedAt = DateTime.now();

          await _isar.writeTxn(() async {
            await _isar.backupSettingsIsars.put(settings);
          });

          _logger.info('Backup frequency updated successfully');
        }
      }
    } catch (e) {
      _logger.warning('Error updating backup frequency to new default: $e');
      // Don't throw - this is not critical for app functionality
    }
  }

  // Add other farms methods
  Future<String> getActiveFarmId() async {
    try {
      // Implementation would be specific to how active farm is stored
      return "default_farm_id";
    } catch (e) {
      _logger.severe('Error getting active farm ID: $e');
      throw DatabaseException('Failed to get active farm ID', e.toString());
    }
  }

  /// Add a new farm
  Future<void> addFarm(FarmIsar farm) async {
    try {
      _logger.info('Adding farm: ${farm.name} (${farm.farmBusinessId})');

      // Validate the farm data
      if (farm.name == null || farm.name!.isEmpty) {
        throw ValidationException('Farm name is required');
      }

      // Save the farm
      await _isar.writeTxn(() async {
        await _isar.farmIsars.put(farm);
      });

      _logger.info('✅ Successfully added farm to database: ${farm.name}');

      // Sync to cloud if user is authenticated
      await _syncFarmToCloudIfAuthenticated(farm);

      _logger.info('Successfully added farm: ${farm.name}');
    } catch (e) {
      _logger.severe('❌ Error adding farm: $e');
      throw DatabaseException('Failed to add farm', e.toString());
    }
  }

  /// Update an existing farm
  Future<void> updateFarm(FarmIsar farm) async {
    try {
      // Validate the farm data
      if (farm.name == null || farm.name!.isEmpty) {
        throw ValidationException('Farm name is required');
      }

      if (farm.farmBusinessId == null || farm.farmBusinessId!.isEmpty) {
        throw ValidationException('Farm ID is required for updates');
      }

      // Save the farm
      await _isar.writeTxn(() async {
        await _isar.farmIsars.put(farm);
      });

      // If this was the selected farm, update the selected farm ID
      if (_selectedFarmId == farm.farmBusinessId) {
        _selectedFarmId = farm.farmBusinessId;
      }

      // Sync to cloud if user is authenticated
      await _syncFarmToCloudIfAuthenticated(farm);

      _logger.info('Successfully updated farm: ${farm.name}');
    } catch (e) {
      _logger.severe('Error updating farm: $e');
      throw DatabaseException('Failed to update farm', e.toString());
    }
  }

  /// Save a farm (add new or update existing)
  Future<void> saveFarm(FarmIsar farm) async {
    try {
      // Validate the farm data
      if (farm.name == null || farm.name!.isEmpty) {
        throw ValidationException('Farm name is required');
      }

      // Generate business ID if not present
      if (farm.farmBusinessId == null || farm.farmBusinessId!.isEmpty) {
        farm.farmBusinessId = const Uuid().v4();
        farm.createdAt = DateTime.now();
      }

      // Single farm per user - no ownership tracking needed
      _logger.info('🔐 Single farm per user - no ownership tracking needed');

      farm.updatedAt = DateTime.now();

      // Save the farm
      await _isar.writeTxn(() async {
        await _isar.farmIsars.put(farm);
      });

      // Sync to cloud if user is authenticated
      await _syncFarmToCloudIfAuthenticated(farm);

      _logger.info('✅ Successfully saved farm: ${farm.name}');
    } catch (e) {
      _logger.severe('❌ Error saving farm: $e');
      throw DatabaseException('Failed to save farm', e.toString());
    }
  }

  /// Helper method to sync farm to cloud if user is authenticated
  Future<void> _syncFarmToCloudIfAuthenticated(FarmIsar farm) async {
    try {
      final authService = GetIt.instance<CloudAuthenticationService>();
      final currentUser = authService.currentUser;

      if (currentUser != null) {
        final cloudSyncService = CloudDataSyncService.instance;
        await cloudSyncService.syncFarmToCloud(currentUser.uid, farm);
        _logger.info('🌩️ Farm synced to cloud: ${farm.name}');
      } else {
        _logger.info('ℹ️ User not authenticated, skipping cloud sync for farm: ${farm.name}');
      }
    } catch (e) {
      _logger.warning('⚠️ Failed to sync farm to cloud: $e');
      // Don't throw error - local save should succeed even if cloud sync fails
    }
  }

  /// Check if a farm can be deleted
  Future<bool> canDeleteFarm(String farmId) async {
    try {
      // Check if this is the currently selected farm
      final selectedFarmId = await getSelectedFarmId();
      if (farmId == selectedFarmId || farmId == _selectedFarmId) {
        return false; // Cannot delete selected farm
      }

      // Check if this is the only farm remaining
      final allFarms = await getAllFarms();
      if (allFarms.length <= 1) {
        return false; // Cannot delete the last farm
      }

      return true; // Farm can be deleted
    } catch (e) {
      _logger.severe('Error checking if farm can be deleted: $e');
      return false; // Default to not allowing deletion on error
    }
  }

  /// Get the reason why a farm cannot be deleted (for UI feedback)
  Future<String?> getFarmDeletionBlockReason(String farmId) async {
    try {
      // Check if this is the currently selected farm
      final selectedFarmId = await getSelectedFarmId();
      if (farmId == selectedFarmId || farmId == _selectedFarmId) {
        return 'Cannot delete the currently selected farm. Please select a different farm first.';
      }

      // Check if this is the only farm remaining
      final allFarms = await getAllFarms();
      if (allFarms.length <= 1) {
        return 'Cannot delete the last remaining farm. At least one farm must exist.';
      }

      return null; // No blocking reason, farm can be deleted
    } catch (e) {
      _logger.severe('Error getting farm deletion block reason: $e');
      return 'Unable to determine if farm can be deleted due to an error.';
    }
  }

  Future<void> deleteFarm(String farmId) async {
    FarmIsar? farmToDelete;

    try {
      // Check if this is the currently selected farm
      final selectedFarmId = await getSelectedFarmId();
      if (farmId == selectedFarmId || farmId == _selectedFarmId) {
        throw ValidationException('Cannot delete the currently selected farm. Please select a different farm first.');
      }

      // Check if this is the only farm remaining
      final allFarms = await getAllFarms();
      if (allFarms.length <= 1) {
        throw ValidationException('Cannot delete the last remaining farm. At least one farm must exist.');
      }

      // Get the farm details before deletion (needed for database cleanup)
      final isar = _isar;
      farmToDelete = await isar.farmIsars
          .filter()
          .farmBusinessIdEqualTo(farmId)
          .findFirst();

      if (farmToDelete == null) {
        throw RecordNotFoundException('Farm not found: $farmId');
      }

      // Delete the farm from the database
      await isar.writeTxn(() async {
        await isar.farmIsars.delete(farmToDelete!.id);
        _logger.info('Successfully deleted farm from database: ${farmToDelete.name} (${farmToDelete.farmBusinessId})');
      });

      // Clean up the associated database file
      _logger.info('Cleaning up database file for deleted farm: ${farmToDelete.name}');
      final dbCleanupSuccess = await IsarService.deleteFarmDatabase(
        null, // userId - will use guest for now
        farmToDelete.farmBusinessId,
        farmToDelete.name,
      );

      if (dbCleanupSuccess) {
        _logger.info('Successfully cleaned up database file for farm: ${farmToDelete.name}');
      } else {
        _logger.warning('Failed to clean up database file for farm: ${farmToDelete.name} - file may remain orphaned');
        // Don't throw error here as the farm record is already deleted
      }

    } catch (e) {
      _logger.severe('Error deleting farm: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete farm', e.toString());
    }
  }

  //=== PRIVATE HELPER METHODS ===//

  /// Validate breed category
  Future<void> _validateBreedCategory(BreedCategoryIsar category) async {
    if (category.name == null || category.name!.isEmpty) {
      throw ValidationException('Breed category name is required');
    }

    if (category.animalTypeId == null || category.animalTypeId!.isEmpty) {
      throw ValidationException('Animal type ID is required');
    }

    // Check if animal type exists
    final animalType = await _isar.animalTypeIsars
        .filter()
        .businessIdEqualTo(category.animalTypeId!)
        .findFirst();

    if (animalType == null) {
      throw ValidationException('Invalid animal type ID');
    }
  }

  /// Validate animal type
  Future<void> _validateAnimalType(AnimalTypeIsar animalType) async {
    if (animalType.name == null || animalType.name!.isEmpty) {
      throw ValidationException('Animal type name is required');
    }

    // Check for duplicate names
    final existing = await _isar.animalTypeIsars
        .filter()
        .nameEqualTo(animalType.name!)
        .and()
        .not()
        .businessIdEqualTo(animalType.businessId ?? '')
        .findFirst();

    if (existing != null) {
      throw ValidationException('Animal type with this name already exists');
    }
  }

  /// Get currency settings for a specific farm
  Future<CurrencySettingsIsar> getCurrencySettings() async {
    try {
      // Get the current farm ID
      final currentFarmId = _selectedFarmId ?? await getSelectedFarmId();

      _logger.info('Getting currency settings for farm: $currentFarmId');

      // First try to find settings for the current farm
      final settings = await _isar.currencySettingsIsars
          .filter()
          .farmIdEqualTo(currentFarmId)
          .findFirst();

      // If found, return the settings
      if (settings != null) {
        _logger.info(
            'Found currency settings for farm $currentFarmId: ${settings.currencyCode}, ${settings.currencySymbol}');
        return settings;
      }

      // If not found, try to find any currency settings
      final defaultSettings =
          await _isar.currencySettingsIsars.where().findFirst();

      // If any settings exist, copy them for the current farm
      if (defaultSettings != null) {
        _logger.info('Using default currency settings for farm $currentFarmId');
        final newSettings = CurrencySettingsIsar()
          ..farmId = currentFarmId
          ..currencyCode = defaultSettings.currencyCode
          ..currencySymbol = defaultSettings.currencySymbol
          ..symbolBeforeAmount = defaultSettings.symbolBeforeAmount
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();

        // Save the new settings for this farm
        await _isar.writeTxn(() async {
          await _isar.currencySettingsIsars.put(newSettings);
        });

        return newSettings;
      }

      // If no settings exist at all, create default
      _logger.info(
          'Creating new default currency settings for farm $currentFarmId');
      final newDefaultSettings = CurrencySettingsIsar()
        ..farmId = currentFarmId
        ..currencyCode = 'USD'
        ..currencySymbol = '\$'
        ..symbolBeforeAmount = true
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      // Save the default settings
      await _isar.writeTxn(() async {
        await _isar.currencySettingsIsars.put(newDefaultSettings);
      });

      return newDefaultSettings;
    } catch (e) {
      _logger.severe('Error retrieving currency settings: $e');
      throw DatabaseException(
          'Failed to retrieve currency settings', e.toString());
    }
  }

  /// Save currency settings
  Future<void> saveCurrencySettings(CurrencySettingsIsar settings) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.currencySettingsIsars.put(settings);
      });

      _logger.info(
          'Successfully saved currency settings for farm ${settings.farmId}');
    } catch (e) {
      _logger.severe('Error saving currency settings: $e');
      throw DatabaseException('Failed to save currency settings', e.toString());
    }
  }

  /// Get milk settings for the user (single farm per user)
  Future<MilkSettingsIsar> getMilkSettings() async {
    try {
      _logger.info('Getting milk settings for user...');

      // First try to find any existing settings
      final settings = await _isar.milkSettingsIsars.where().findFirst();

      // If found, return the settings
      if (settings != null) {
        _logger.info('Found existing milk settings');
        return settings;
      }

      // If no settings exist, create default
      _logger.info('Creating new default milk settings');
      final newDefaultSettings = MilkSettingsIsar.create(
        farmBusinessId: '', // Not needed for single farm per user
        unit: 'liters',
        regularRate: 1.0,
        premiumRate: 1.5,
        bulkRate: 0.8,
      );

      // Save the default settings
      await _isar.writeTxn(() async {
        await _isar.milkSettingsIsars.put(newDefaultSettings);
      });

      return newDefaultSettings;
    } catch (e) {
      _logger.severe('Error retrieving milk settings: $e');
      throw DatabaseException('Failed to retrieve milk settings', e.toString());
    }
  }

  /// Save milk settings
  Future<void> saveMilkSettings(MilkSettingsIsar settings) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.milkSettingsIsars.put(settings);
      });

      _logger.info(
          'Successfully saved milk settings for farm ${settings.farmBusinessId}');
    } catch (e) {
      _logger.severe('Error saving milk settings: $e');
      throw DatabaseException('Failed to save milk settings', e.toString());
    }
  }

  /// Get all expense categories
  Future<List<CategoryIsar>> getExpenseCategories() async {
    try {
      final categories = await _isar.categoryIsars
          .filter()
          .typeEqualTo('Expense')
          .sortByName()
          .findAll();
      return categories;
    } catch (e) {
      _logger.severe('Error getting expense categories: $e');
      throw DatabaseException(
          'Failed to retrieve expense categories', e.toString());
    }
  }

  /// Get all income categories
  Future<List<CategoryIsar>> getIncomeCategories() async {
    try {
      final categories = await _isar.categoryIsars
          .filter()
          .typeEqualTo('Income')
          .sortByName()
          .findAll();
      return categories;
    } catch (e) {
      _logger.severe('Error getting income categories: $e');
      throw DatabaseException(
          'Failed to retrieve income categories', e.toString());
    }
  }

  /// Create a new category
  Future<void> createCategory(CategoryIsar category) async {
    try {
      if (category.name.isEmpty) {
        throw ValidationException('Category name is required');
      }

      // Check for duplicate names within the same type (case-insensitive)
      final existing = await _isar.categoryIsars
          .filter()
          .nameEqualTo(category.name, caseSensitive: false)
          .and()
          .typeEqualTo(category.type)
          .findFirst();

      if (existing != null) {
        _logger.warning(
            'Attempted to create duplicate category "${category.name}" (Category already exists: ${existing.name})');
        throw ValidationException('A category with this name already exists');
      }

      await _isar.writeTxn(() async {
        await _isar.categoryIsars.put(category);
      });

      _logger.info(
          'Successfully created ${category.type} category: ${category.name}');
    } catch (e) {
      _logger.severe('Error creating category: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to create category', e.toString());
    }
  }

  /// Update an existing category
  Future<void> updateCategory(CategoryIsar category) async {
    try {
      if (category.name.isEmpty) {
        throw ValidationException('Category name is required');
      }

      // Check for duplicate names within the same type, excluding this category (case-insensitive)
      final existing = await _isar.categoryIsars
          .filter()
          .nameEqualTo(category.name, caseSensitive: false)
          .and()
          .typeEqualTo(category.type)
          .and()
          .not()
          .idEqualTo(category.id)
          .findFirst();

      if (existing != null) {
        _logger.warning(
            'Attempted to update category to duplicate name "${category.name}" (Conflicting category: ${existing.name})');
        throw ValidationException('A category with this name already exists');
      }

      await _isar.writeTxn(() async {
        await _isar.categoryIsars.put(category);
      });

      _logger.info('Successfully updated category: ${category.name}');
    } catch (e) {
      _logger.severe('Error updating category: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to update category', e.toString());
    }
  }

  /// Delete a category
  Future<void> deleteCategory(String categoryId) async {
    try {
      if (categoryId.isEmpty) {
        throw ValidationException('Category ID is required');
      }

      _logger.info('Attempting to delete category with ID: $categoryId');

      await _isar.writeTxn(() async {
        // Find the category by its business ID (UUID) instead of trying to parse as int
        final category = await _isar.categoryIsars
            .filter()
            .categoryIdEqualTo(categoryId)
            .findFirst();

        if (category == null) {
          _logger.warning('Category not found with ID: $categoryId');
          throw RecordNotFoundException(
              'Category not found with ID: $categoryId');
        }

        _logger.info(
            'Found category to delete: ${category.name} (ID: ${category.id}, businessID: ${category.categoryId})');

        // Once we have the category with the internal Isar ID, we can delete it
        final success = await _isar.categoryIsars.delete(category.id);
        if (!success) {
          _logger.severe('Failed to delete category, operation returned false');
          throw DatabaseException(
              'Failed to delete category', 'Delete operation did not succeed');
        }

        _logger.info(
            'Successfully deleted category with name: ${category.name}, ID: ${category.id}');
      });

      // Verify the category is actually gone
      final verifyDeleted = await _isar.categoryIsars
          .filter()
          .categoryIdEqualTo(categoryId)
          .findFirst();

      if (verifyDeleted != null) {
        _logger.severe(
            'Category still exists after deletion! Name: ${verifyDeleted.name}');
      } else {
        _logger.info('Verified category is no longer in database');
      }

      _logger.info('Successfully deleted category: $categoryId');
    } catch (e) {
      _logger.severe('Error deleting category: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete category', e.toString());
    }
  }

  // For category handler - check if category is used in transactions
  Future<bool> isTransactionCategoryInUse(String categoryId) async {
    try {
      if (categoryId.isEmpty) {
        throw ValidationException('Category ID is required');
      }

      // Check if category is in use by any transactions
      final transactionCount = await _isar
          .collection<TransactionIsar>()
          .filter()
          .categoryEqualTo(categoryId)
          .count();

      _logger.info(
          'Found $transactionCount transactions using category $categoryId');
      return transactionCount > 0;
    } catch (e) {
      _logger.severe('Error checking if transaction category is in use: $e');
      throw DatabaseException(
          'Failed to check if transaction category is in use', e.toString());
    }
  }

  Future<List<FarmIsar>> getUnselectedFarms() async {
    try {
      final selectedFarmId = await getSelectedFarmId();
      if (selectedFarmId.isEmpty) {
        return getAllFarms();
      }

      // Use Isar's built-in filtering with indexes
      final unselectedFarms = await _isar.farmIsars
          .filter()
          .not()
          .farmBusinessIdEqualTo(selectedFarmId)
          .and()
          .not()
          .idEqualTo(int.tryParse(selectedFarmId) ?? -1)
          .sortByName()
          .findAll();

      return unselectedFarms;
    } catch (e) {
      _logger.severe('Error retrieving unselected farms: $e');
      throw DatabaseException(
          'Failed to retrieve unselected farms', e.toString());
    }
  }

  Future<List<CategoryIsar>> getExpenseCategoriesExcluding(
      String categoryId) async {
    try {
      if (categoryId.isEmpty) {
        throw ValidationException('Category ID is required');
      }

      // Use proper Isar query with indexed fields
      final categories = await _isar.categoryIsars
          .filter()
          .typeEqualTo('Expense') // Case sensitive, was 'expense'
          .and()
          .not()
          .categoryIdEqualTo(
              categoryId) // Use categoryId (string) instead of ID (int)
          .sortByName()
          .findAll();

      return categories;
    } catch (e) {
      _logger.severe(
          'Error retrieving expense categories excluding: $categoryId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to retrieve expense categories', e.toString());
    }
  }

  Future<bool> backupDatabase(String backupFilePath, {Function(double)? onProgress}) async {
    try {
      _logger.info('Starting database backup to: $backupFilePath');

      // Get the database file path - handle nullable path
      final dbPath = _isar.path;
      if (dbPath == null) {
        throw Exception('Database path is null');
      }

      final dbFile = File(dbPath);
      if (!await dbFile.exists()) {
        throw Exception('Database file not found at: $dbPath');
      }

      // Create backup directory if it doesn't exist
      final backupFile = File(backupFilePath);
      final backupDir = Directory(backupFile.parent.path);
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      // Report initial progress
      onProgress?.call(0.1);

      // Ensure we have a clean copy by closing the database first
      await _isarService.close();
      onProgress?.call(0.3);

      // Get file size for progress tracking
      final fileSize = await dbFile.length();

      // Copy the database file with progress tracking
      final sourceStream = dbFile.openRead();
      final sink = backupFile.openWrite();

      int bytesWritten = 0;
      await for (final chunk in sourceStream) {
        sink.add(chunk);
        bytesWritten += chunk.length;
        final progress = 0.3 + (bytesWritten / fileSize) * 0.6; // 30% to 90%
        onProgress?.call(progress);
      }

      await sink.close();
      onProgress?.call(0.9);

      // Verify the backup file was created successfully
      if (!await backupFile.exists()) {
        throw Exception('Backup file was not created successfully');
      }

      // Verify file size matches
      final backupSize = await backupFile.length();
      if (backupSize != fileSize) {
        throw Exception('Backup file size mismatch. Original: $fileSize, Backup: $backupSize');
      }

      // Reopen the database
      await _isarService.initialize();

      // Add a small delay to ensure database is fully ready
      await Future.delayed(const Duration(milliseconds: 500));

      onProgress?.call(1.0);

      _logger.info('Database backup created successfully at: $backupFilePath (Size: ${(backupSize / 1024 / 1024).toStringAsFixed(2)} MB)');
      return true;
    } catch (e) {
      _logger.severe('Error creating database backup: $e');

      // Make sure the database is open again
      if (!_isarService.isInitialized) {
        try {
          await _isarService.initialize();
          // Add a small delay to ensure database is fully ready
          await Future.delayed(const Duration(milliseconds: 500));
        } catch (initError) {
          _logger.severe('Error reinitializing database after backup failure: $initError');
        }
      }

      return false;
    }
  }

  Future<bool> restoreDatabase(String backupFilePath, {Function(double)? onProgress}) async {
    try {
      _logger.info('Starting database restore from: $backupFilePath');

      final backupFile = File(backupFilePath);
      if (!await backupFile.exists()) {
        throw Exception('Backup file not found: $backupFilePath');
      }

      // Validate backup file before proceeding
      final isValid = await validateBackupFile(backupFilePath);
      if (!isValid) {
        throw Exception('Invalid backup file format or corrupted file');
      }

      onProgress?.call(0.1);

      // Get the database file path - handle nullable path
      final dbPath = _isar.path;
      if (dbPath == null) {
        throw Exception('Database path is null');
      }

      // Create a backup of current database before restore
      final currentDbFile = File(dbPath);
      String? tempBackupPath;
      if (await currentDbFile.exists()) {
        tempBackupPath = '$dbPath.temp_backup_${DateTime.now().millisecondsSinceEpoch}';
        await currentDbFile.copy(tempBackupPath);
        _logger.info('Created temporary backup of current database at: $tempBackupPath');
      }

      onProgress?.call(0.3);

      try {
        // Close the current database
        await _isarService.close();
        onProgress?.call(0.5);

        // Get file size for progress tracking
        final backupSize = await backupFile.length();

        // Copy the backup file to the database location with progress tracking
        final sourceStream = backupFile.openRead();
        final sink = File(dbPath).openWrite();

        int bytesWritten = 0;
        await for (final chunk in sourceStream) {
          sink.add(chunk);
          bytesWritten += chunk.length;
          final progress = 0.5 + (bytesWritten / backupSize) * 0.4; // 50% to 90%
          onProgress?.call(progress);
        }

        await sink.close();
        onProgress?.call(0.9);

        // Reopen the database
        await _isarService.initialize();

        // Verify the restore was successful by checking if database opens properly
        final testQuery = await _isar.farmIsars.where().count();
        _logger.info('Database restore verification: Found $testQuery farms');

        onProgress?.call(1.0);

        // Clean up temporary backup if restore was successful
        if (tempBackupPath != null) {
          final tempFile = File(tempBackupPath);
          if (await tempFile.exists()) {
            await tempFile.delete();
            _logger.info('Cleaned up temporary backup file');
          }
        }

        _logger.info('Database restored successfully from: $backupFilePath (Size: ${(backupSize / 1024 / 1024).toStringAsFixed(2)} MB)');
        return true;

      } catch (e) {
        // Restore failed, try to recover from temporary backup
        if (tempBackupPath != null) {
          final tempFile = File(tempBackupPath);
          if (await tempFile.exists()) {
            try {
              await tempFile.copy(dbPath);
              await _isarService.initialize();
              _logger.info('Restored original database from temporary backup');
            } catch (recoveryError) {
              _logger.severe('Failed to recover original database: $recoveryError');
            }
          }
        }
        rethrow;
      }

    } catch (e) {
      _logger.severe('Error restoring database from backup: $e');

      // Make sure the database is open again
      if (!_isarService.isInitialized) {
        await _isarService.initialize();
      }

      return false;
    }
  }

  /// Validate backup file integrity and format
  Future<bool> validateBackupFile(String backupFilePath) async {
    try {
      final backupFile = File(backupFilePath);
      if (!await backupFile.exists()) {
        _logger.warning('Backup file does not exist: $backupFilePath');
        return false;
      }

      // Check file size (should not be empty)
      final fileSize = await backupFile.length();
      if (fileSize == 0) {
        _logger.warning('Backup file is empty: $backupFilePath');
        return false;
      }

      // Check file extension
      if (!backupFilePath.toLowerCase().endsWith('.isar')) {
        _logger.warning('Invalid backup file extension: $backupFilePath');
        return false;
      }

      // Try to read the file header to verify it's a valid Isar file
      final bytes = await backupFile.openRead(0, 16).first;
      if (bytes.length < 16) {
        _logger.warning('Backup file too small to be valid: $backupFilePath');
        return false;
      }

      // Basic Isar file signature check (simplified)
      // Isar files typically start with specific bytes
      final header = String.fromCharCodes(bytes.take(4));
      if (header != 'ISAR' && !bytes.any((b) => b != 0)) {
        _logger.warning('Invalid Isar file format: $backupFilePath');
        return false;
      }

      _logger.info('Backup file validation passed: $backupFilePath');
      return true;
    } catch (e) {
      _logger.severe('Error validating backup file: $e');
      return false;
    }
  }

  /// Get backup file metadata
  Future<Map<String, dynamic>> getBackupMetadata(String backupFilePath) async {
    try {
      final backupFile = File(backupFilePath);
      if (!await backupFile.exists()) {
        throw Exception('Backup file not found: $backupFilePath');
      }

      final stat = await backupFile.stat();
      final fileName = backupFile.path.split('/').last;

      // Extract timestamp from filename if it follows our naming convention
      DateTime? backupDate;
      final timestampMatch = RegExp(r'cattle_manager_(\d+)\.isar').firstMatch(fileName);
      if (timestampMatch != null) {
        final timestamp = int.tryParse(timestampMatch.group(1) ?? '');
        if (timestamp != null) {
          backupDate = DateTime.fromMillisecondsSinceEpoch(timestamp);
        }
      }

      return {
        'fileName': fileName,
        'filePath': backupFilePath,
        'fileSize': stat.size,
        'fileSizeMB': (stat.size / 1024 / 1024).toStringAsFixed(2),
        'createdDate': stat.modified,
        'backupDate': backupDate ?? stat.modified,
        'isValid': await validateBackupFile(backupFilePath),
      };
    } catch (e) {
      _logger.severe('Error getting backup metadata: $e');
      return {
        'fileName': 'Unknown',
        'filePath': backupFilePath,
        'fileSize': 0,
        'fileSizeMB': '0.00',
        'createdDate': DateTime.now(),
        'backupDate': DateTime.now(),
        'isValid': false,
        'error': e.toString(),
      };
    }
  }

  /// List all backup files in the backup directory
  Future<List<Map<String, dynamic>>> listBackupFiles(String backupDirectory) async {
    try {
      final backupDir = Directory(backupDirectory);
      if (!await backupDir.exists()) {
        _logger.info('Backup directory does not exist: $backupDirectory');
        return [];
      }

      final files = await backupDir
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.isar'))
          .cast<File>()
          .toList();

      final backupList = <Map<String, dynamic>>[];
      for (final file in files) {
        final metadata = await getBackupMetadata(file.path);
        backupList.add(metadata);
      }

      // Sort by backup date (newest first)
      backupList.sort((a, b) => (b['backupDate'] as DateTime).compareTo(a['backupDate'] as DateTime));

      _logger.info('Found ${backupList.length} backup files in $backupDirectory');
      return backupList;
    } catch (e) {
      _logger.severe('Error listing backup files: $e');
      return [];
    }
  }

  /// Delete old backup files based on retention policy
  Future<int> cleanupOldBackups(String backupDirectory, {int maxBackups = 10}) async {
    try {
      final backupList = await listBackupFiles(backupDirectory);
      if (backupList.length <= maxBackups) {
        _logger.info('No cleanup needed. Current backups: ${backupList.length}, Max: $maxBackups');
        return 0;
      }

      // Sort by date and keep only the newest maxBackups
      final backupsToDelete = backupList.skip(maxBackups).toList();
      int deletedCount = 0;

      for (final backup in backupsToDelete) {
        try {
          final file = File(backup['filePath'] as String);
          if (await file.exists()) {
            await file.delete();
            deletedCount++;
            _logger.info('Deleted old backup: ${backup['fileName']}');
          }
        } catch (e) {
          _logger.warning('Failed to delete backup ${backup['fileName']}: $e');
        }
      }

      _logger.info('Cleanup completed. Deleted $deletedCount old backup files');
      return deletedCount;
    } catch (e) {
      _logger.severe('Error during backup cleanup: $e');
      return 0;
    }
  }

  //=== USER ROLES MANAGEMENT ===//

  /// Get all user roles
  Future<List<UserRoleIsar>> getAllUserRoles() async {
    try {
      final roles = await _isar.userRoleIsars.where().sortByName().findAll();

      return roles;
    } catch (e) {
      _logger.severe('Error getting user roles: $e');
      throw DatabaseException('Failed to retrieve user roles', e.toString());
    }
  }

  /// Get a role by its business ID
  Future<UserRoleIsar?> getUserRoleByBusinessId(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Role business ID is required');
      }

      final role = await _isar.userRoleIsars
          .filter()
          .businessIdEqualTo(businessId)
          .findFirst();

      return role;
    } catch (e) {
      _logger.severe('Error getting role by business ID: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve role', e.toString());
    }
  }

  /// Add or update a user role
  Future<void> saveUserRole(UserRoleIsar role) async {
    try {
      await _validateUserRole(role);

      await _isar.writeTxn(() async {
        await _isar.userRoleIsars.put(role);
      });

      _logger.info('Successfully saved user role: ${role.name}');
    } catch (e) {
      _logger.severe('Error saving user role: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to save user role', e.toString());
    }
  }

  /// Delete a user role
  Future<void> deleteUserRole(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Role business ID is required');
      }

      final role = await getUserRoleByBusinessId(businessId);
      if (role == null) {
        throw RecordNotFoundException('Role not found');
      }

      // Check if any users are using this role
      final usersCount = await _isar.farmUserIsars
          .filter()
          .roleBusinessIdEqualTo(businessId)
          .count();

      if (usersCount > 0) {
        throw ValidationException(
            'Cannot delete a role that is assigned to users');
      }

      await _isar.writeTxn(() async {
        await _isar.userRoleIsars.delete(role.id);
      });

      _logger.info('Successfully deleted user role: ${role.name}');
    } catch (e) {
      _logger.severe('Error deleting user role: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete user role', e.toString());
    }
  }

  /// Ensure default roles exist
  Future<void> ensureDefaultUserRoles() async {
    try {
      final existingRoles = await getAllUserRoles();

      if (existingRoles.isEmpty) {
        final defaultRoles = UserRoleIsar.defaultRoles;

        await _isar.writeTxn(() async {
          for (final role in defaultRoles) {
            await _isar.userRoleIsars.put(role);
          }
        });

        _logger.info('Successfully created default user roles');
      }
    } catch (e) {
      _logger.severe('Error ensuring default user roles: $e');
      throw DatabaseException(
          'Failed to create default user roles', e.toString());
    }
  }

  /// Validate user role
  Future<void> _validateUserRole(UserRoleIsar role) async {
    if (role.name == null || role.name!.trim().isEmpty) {
      throw ValidationException('Role name is required');
    }

    if (role.businessId == null || role.businessId!.trim().isEmpty) {
      role.businessId = const Uuid().v4();
    }

    // Check for duplicate name (except for the same role)
    final existingRole = await _isar.userRoleIsars
        .filter()
        .nameEqualTo(role.name!)
        .and()
        .not()
        .businessIdEqualTo(role.businessId!)
        .findFirst();

    if (existingRole != null) {
      throw ValidationException('A role with this name already exists');
    }
  }

  //=== FARM USERS MANAGEMENT ===//

  /// Get all users for the farm (single farm per user)
  Future<List<FarmUserIsar>> getFarmUsers() async {
    try {
      return await _isar.farmUserIsars
          .where()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting farm users: $e');
      throw DatabaseException('Failed to retrieve farm users', e.toString());
    }
  }

  /// Get a user by their business ID
  Future<FarmUserIsar?> getUserByBusinessId(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('User business ID is required');
      }

      final user = await _isar.farmUserIsars
          .filter()
          .businessIdEqualTo(businessId)
          .findFirst();

      // Load the linked role if it exists
      if (user != null && user.roleBusinessId != null) {
        final role = await getUserRoleByBusinessId(user.roleBusinessId!);
        if (role != null) {
          user.role.value = role;
        }
      }

      return user;
    } catch (e) {
      _logger.severe('Error getting user by business ID: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve user', e.toString());
    }
  }

  /// Get a user by their email address
  Future<FarmUserIsar?> getUserByEmail(String email) async {
    try {
      if (email.isEmpty) {
        throw ValidationException('User email is required');
      }

      final user = await _isar.farmUserIsars
          .filter()
          .emailEqualTo(email)
          .findFirst();

      // Load the linked role if it exists
      if (user != null && user.roleBusinessId != null) {
        final role = await getUserRoleByBusinessId(user.roleBusinessId!);
        if (role != null) {
          user.role.value = role;
        }
      }

      return user;
    } catch (e) {
      _logger.severe('Error getting user by email: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve user', e.toString());
    }
  }

  /// Add or update a farm user
  Future<void> saveUser(FarmUserIsar user) async {
    try {
      await _validateUser(user);

      // Ensure the user is associated with a role via link
      if (user.role.value == null && user.roleBusinessId != null) {
        final role = await getUserRoleByBusinessId(user.roleBusinessId!);
        if (role != null) {
          user.role.value = role;
        }
      }

      // Update roleBusinessId from the link if needed
      if (user.role.value != null && user.role.value!.businessId != null) {
        user.roleBusinessId = user.role.value!.businessId;
      }

      await _isar.writeTxn(() async {
        await _isar.farmUserIsars.put(user);
      });

      _logger.info('Successfully saved farm user: ${user.name}');
    } catch (e) {
      _logger.severe('Error saving farm user: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to save farm user', e.toString());
    }
  }

  /// Delete a farm user
  Future<void> deleteUser(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('User business ID is required');
      }

      final user = await getUserByBusinessId(businessId);
      if (user == null) {
        throw RecordNotFoundException('User not found');
      }

      await _isar.writeTxn(() async {
        await _isar.farmUserIsars.delete(user.id);
      });

      _logger.info('Successfully deleted farm user: ${user.name}');
    } catch (e) {
      _logger.severe('Error deleting farm user: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete farm user', e.toString());
    }
  }

  /// Validate farm user
  Future<void> _validateUser(FarmUserIsar user) async {
    if (user.name == null || user.name!.trim().isEmpty) {
      throw ValidationException('User name is required');
    }

    if (user.email == null || user.email!.trim().isEmpty) {
      throw ValidationException('User email is required');
    }

    if (user.farmBusinessId == null || user.farmBusinessId!.trim().isEmpty) {
      final farm = await getActiveFarm();
      if (farm == null || farm.farmBusinessId == null) {
        throw ValidationException('User must be associated with a farm');
      }
      user.farmBusinessId = farm.farmBusinessId;
    }

    if (user.roleBusinessId == null && user.role.value == null) {
      throw ValidationException('User must have a role assigned');
    }

    if (user.businessId == null || user.businessId!.trim().isEmpty) {
      user.businessId = const Uuid().v4();
    }

    // Check for duplicate email (except for the same user)
    final existingUser = await _isar.farmUserIsars
        .filter()
        .emailEqualTo(user.email!)
        .and()
        .not()
        .businessIdEqualTo(user.businessId!)
        .findFirst();

    if (existingUser != null) {
      throw ValidationException('A user with this email already exists');
    }
  }

  /// Migrate users from SharedPreferences to Isar
  Future<void> migrateUsersFromSharedPreferences(
      List<FarmUserIsar> users, List<UserRoleIsar> roles) async {
    try {
      await _isar.writeTxn(() async {
        // First save all roles to establish references
        for (final role in roles) {
          if (role.businessId == null || role.businessId!.isEmpty) {
            role.businessId = const Uuid().v4();
          }
          await _isar.userRoleIsars.put(role);
        }

        // Then save all users
        for (final user in users) {
          if (user.businessId == null || user.businessId!.isEmpty) {
            user.businessId = const Uuid().v4();
          }

          // Set up role link if possible
          if (user.roleBusinessId != null) {
            final role = roles.firstWhere(
              (r) => r.businessId == user.roleBusinessId,
              orElse: () => UserRoleIsar.defaultRoles.first,
            );
            user.role.value = role;
          }

          await _isar.farmUserIsars.put(user);
        }
      });

      _logger.info(
          'Successfully migrated ${users.length} users and ${roles.length} roles from SharedPreferences to Isar');
    } catch (e) {
      _logger.severe('Error migrating users from SharedPreferences: $e');
      throw DatabaseException(
          'Failed to migrate users from SharedPreferences', e.toString());
    }
  }

  //=== ANIMAL STAGES ===//
  /* Temporarily commented out until AnimalStageIsar is properly generated
  /// Get all animal stages
  Future<List<AnimalStageIsar>> getAllAnimalStages() async {
    try {
      final animalStages = await _isar.animalStageIsars
          .where()
          .sortByDisplayOrder()
          .findAll();
      
      return animalStages;
    } catch (e) {
      _logger.severe('Error retrieving animal stages: $e');
      throw DatabaseException('Failed to retrieve animal stages', e.toString());
    }
  }

  /// Get animal stages for a specific animal type
  Future<List<AnimalStageIsar>> getAnimalStagesForType(String animalTypeId, {String? gender}) async {
    try {
      if (animalTypeId.isEmpty) {
        throw ValidationException('Animal type ID is required');
      }

      final query = _isar.animalStageIsars
          .filter()
          .animalTypeIdEqualTo(animalTypeId);
          
      // Apply gender filter if provided
      if (gender != null) {
        query.genderEqualTo(gender);
      }
      
      final stages = await query
          .sortByDisplayOrder()
          .findAll();
      
      return stages;
    } catch (e) {
      _logger.severe('Error retrieving animal stages for type: $animalTypeId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve animal stages for type', e.toString());
    }
  }

  /// Add or update an animal stage
  Future<void> addOrUpdateAnimalStage(AnimalStageIsar stage) async {
    try {
      await _validateAnimalStage(stage);

      await _isar.writeTxn(() async {
        await _isar.animalStageIsars.put(stage);
      });

      _logger.info('Successfully saved animal stage: ${stage.businessId}');
    } catch (e) {
      _logger.severe('Error saving animal stage: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to save animal stage', e.toString());
    }
  }

  /// Delete an animal stage
  Future<void> deleteAnimalStage(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Animal stage ID is required');
      }

      await _isar.writeTxn(() async {
        final stage = await _isar.animalStageIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (stage == null) {
          throw RecordNotFoundException('Animal stage not found: $businessId');
        }

        // Check if stage is in use by cattle
        final cattleCount = await _isar.collection<CattleIsar>()
            .filter()
            .stageEqualTo(stage.name)
            .count();

        if (cattleCount > 0) {
          throw ValidationException('Cannot delete animal stage that is in use by ${cattleCount} cattle');
        }
        
        await _isar.animalStageIsars.delete(stage.id);
      });

      _logger.info('Successfully deleted animal stage: $businessId');
    } catch (e) {
      _logger.severe('Error deleting animal stage: $businessId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete animal stage', e.toString());
    }
  }

  /// Calculate appropriate stage for an animal based on type, gender and age
  Future<String?> calculateAnimalStage(String animalTypeId, String gender, int ageInDays) async {
    try {
      // Get stages for this animal type and gender
      final stages = await getAnimalStagesForType(animalTypeId, gender: gender);
      
      if (stages.isEmpty) {
        _logger.warning('No stages found for animal type: $animalTypeId, gender: $gender');
        return null;
      }
      
      // Find appropriate stage based on age
      String? appropriateStage;
      
      // First look for a stage where the age falls between min and max age
      for (final stage in stages) {
        final minAge = stage.minAgeDays ?? 0;
        final maxAge = stage.maxAgeDays ?? -1;
        
        if ((ageInDays >= minAge) && (maxAge == -1 || ageInDays < maxAge)) {
          appropriateStage = stage.name;
          break;
        }
      }
      
      // If no appropriate stage found, use the first stage
      if (appropriateStage == null && stages.isNotEmpty) {
        appropriateStage = stages.first.name;
      }
      
      return appropriateStage;
    } catch (e) {
      _logger.severe('Error calculating animal stage: $e');
      return null;
    }
  }

  /// Validate animal stage data
  Future<void> _validateAnimalStage(AnimalStageIsar stage) async {
    // Ensure name is provided
    if (stage.name == null || stage.name!.isEmpty) {
      throw ValidationException('Stage name is required');
    }

    // Ensure animal type ID is provided
    if (stage.animalTypeId == null || stage.animalTypeId!.isEmpty) {
      throw ValidationException('Animal type ID is required');
    }

    // Ensure animal type exists
    final animalType = await _isar.animalTypeIsars
        .filter()
        .businessIdEqualTo(stage.animalTypeId!)
        .findFirst();

    if (animalType == null) {
      throw ValidationException('Invalid animal type ID: ${stage.animalTypeId}');
    }

    // Ensure min age is not negative (except -1 for unlimited)
    if (stage.minAgeDays != null && stage.minAgeDays! < -1) {
      throw ValidationException('Minimum age cannot be negative');
    }

    // Ensure max age is greater than min age or -1 for unlimited
    if (stage.maxAgeDays != null && stage.maxAgeDays! != -1 && 
        stage.minAgeDays != null && stage.maxAgeDays! <= stage.minAgeDays!) {
      throw ValidationException('Maximum age must be greater than minimum age');
    }

    // Set default values if missing
    stage.displayOrder ??= 0;
    stage.createdAt ??= DateTime.now();
    stage.updatedAt = DateTime.now();
    
    // Generate businessId if missing
    if (stage.businessId == null || stage.businessId!.isEmpty) {
      stage.businessId = const Uuid().v4();
    }
  }
  */
}
