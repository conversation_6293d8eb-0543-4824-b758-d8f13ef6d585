import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:logging/logging.dart';
import '../models/event_isar.dart';
import '../../../services/database/exceptions/database_exceptions.dart'
    as app_exceptions;

/// Simple Event class for application use (not persisted directly)
class Event {
  final int id;
  final String? businessId;
  final String? cattleId;
  final String title;
  final EventType type;
  final EventPriority priority;
  final DateTime eventDate;
  final DateTime? dueDate;
  final TimeOfDay? time;
  final String? notes;
  final bool isCompleted;
  final bool isMissed;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Event({
    required this.id,
    this.businessId,
    this.cattleId,
    required this.title,
    required this.type,
    required this.priority,
    required this.eventDate,
    this.dueDate,
    this.time,
    this.notes,
    required this.isCompleted,
    required this.isMissed,
    this.createdAt,
    this.updatedAt,
  });
}

/// Service for managing events with a local SQLite database
class EventService {
  final Database? db;
  final Logger _logger = Logger('EventService');

  EventService({this.db});

  /// Get all events
  Future<List<EventIsar>> getAllEvents() async {
    try {
      if (db == null) {
        throw app_exceptions.DatabaseException(
            'Database not initialized', 'Database is null');
      }

      final List<Map<String, dynamic>> maps = await db!.query('events');

      return List.generate(maps.length, (i) {
        final eventDate = maps[i]['event_date'] != null
            ? DateTime.parse(maps[i]['event_date'])
            : null;

        final timeStr = maps[i]['time'];
        TimeOfDayIsar? timeOfDay;

        if (timeStr != null) {
          try {
            final parsedTime = TimeOfDay.fromDateTime(DateTime.parse(timeStr));
            timeOfDay = TimeOfDayIsar()
              ..hour = parsedTime.hour
              ..minute = parsedTime.minute;
          } catch (e) {
            _logger.warning('Error parsing time: $e');
          }
        }

        final typeStr = maps[i]['type'];
        EventTypeEmbedded? eventType;
        if (typeStr != null) {
          eventType = EventTypeEmbedded()..value = typeStr;
        }

        return EventIsar()
          ..id = maps[i]['id'] ?? 0
          ..businessId = maps[i]['business_id']
          ..title = maps[i]['title']
          ..eventDate = eventDate
          ..time = timeOfDay
          ..isCompleted = maps[i]['is_completed'] == 1
          ..cattleId = maps[i]['cattle_id']
          ..notes = maps[i]['notes']
          ..type = eventType
          ..isMissed = maps[i]['is_missed'] == 1
          ..priority = _parsePriority(maps[i]['priority']);
      });
    } catch (e) {
      _logger.severe('Error getting all events: $e');
      return [];
    }
  }

  /// Create a new event in the database
  Future<void> createEvent(EventIsar? event) async {
    try {
      if (event == null) {
        throw app_exceptions.ValidationException('Event cannot be null');
      }

      if (db == null) {
        throw app_exceptions.DatabaseException(
            'Database not initialized', 'Database is null');
      }

      final Map<String, dynamic> eventMap = _convertEventToMap(event);

      await db!.insert(
        'events',
        eventMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      _logger.severe('Error creating event: $e');
      rethrow;
    }
  }

  /// Update an existing event
  Future<void> updateEvent(EventIsar? event) async {
    try {
      if (event == null) {
        throw app_exceptions.ValidationException('Event cannot be null');
      }

      if (event.businessId == null) {
        throw app_exceptions.ValidationException(
            'Event business ID cannot be null');
      }

      if (db == null) {
        throw app_exceptions.DatabaseException(
            'Database not initialized', 'Database is null');
      }

      final Map<String, dynamic> eventMap = _convertEventToMap(event);

      await db!.update(
        'events',
        eventMap,
        where: 'business_id = ?',
        whereArgs: [event.businessId],
      );
    } catch (e) {
      _logger.severe('Error updating event: $e');
      rethrow;
    }
  }

  /// Delete an event
  Future<void> deleteEvent(String? eventId) async {
    try {
      if (eventId == null || eventId.isEmpty) {
        throw app_exceptions.ValidationException(
            'Event ID cannot be null or empty');
      }

      if (db == null) {
        throw app_exceptions.DatabaseException(
            'Database not initialized', 'Database is null');
      }

      await db!.delete(
        'events',
        where: 'business_id = ?',
        whereArgs: [eventId],
      );
    } catch (e) {
      _logger.severe('Error deleting event: $e');
      rethrow;
    }
  }

  /// Get events by type
  Future<List<EventIsar>> getEventsByType(EventType type) async {
    try {
      final events = await getAllEvents();
      return events.where((event) {
        if (event.type == null) return false;
        return event.type?.toEventType() == type;
      }).toList();
    } catch (e) {
      _logger.severe('Error getting events by type: $e');
      return [];
    }
  }

  /// Get events by cattle ID
  Future<List<EventIsar>> getEventsByCattle(String? cattleId) async {
    try {
      if (cattleId == null) {
        return [];
      }

      final events = await getAllEvents();
      return events.where((event) => event.cattleId == cattleId).toList();
    } catch (e) {
      _logger.severe('Error getting events by cattle: $e');
      return [];
    }
  }

  /// Helper method to convert EventIsar to Map for database operations
  Map<String, dynamic> _convertEventToMap(EventIsar event) {
    return {
      'business_id': event.businessId,
      'cattle_id': event.cattleId,
      'title': event.title,
      'type': event.type?.value,
      'priority': _priorityToString(event.priority),
      'event_date': event.eventDate?.toIso8601String(),
      'due_date': event.dueDate?.toIso8601String(),
      'time': event.time != null
          ? DateTime(2022, 1, 1, event.time?.hour ?? 0, event.time?.minute ?? 0)
              .toIso8601String()
          : null,
      'notes': event.notes,
      'is_completed': event.isCompleted ? 1 : 0,
      'is_missed': event.isMissed ? 1 : 0,
      'created_at': event.createdAt?.toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  /// Convert string priority to enum
  EventPriority _parsePriority(String? priority) {
    if (priority == null) return EventPriority.medium;

    switch (priority.toLowerCase()) {
      case 'high':
        return EventPriority.high;
      case 'low':
        return EventPriority.low;
      default:
        return EventPriority.medium;
    }
  }

  /// Convert enum priority to string
  String _priorityToString(EventPriority priority) {
    switch (priority) {
      case EventPriority.high:
        return 'high';
      case EventPriority.low:
        return 'low';
      case EventPriority.medium:
        return 'medium';
      case EventPriority.urgent:
        return 'urgent';
      case EventPriority.critical:
        return 'critical';
    }
  }
}
