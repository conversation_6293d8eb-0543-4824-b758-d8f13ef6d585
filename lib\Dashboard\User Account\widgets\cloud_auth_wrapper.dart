import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../services/cloud_authentication_service.dart';
import '../screens/cloud_login_screen.dart';
import '../../dashboard_screen.dart';
import '../../../constants/app_constants.dart';
import '../../../services/permission_service.dart';

/// Cloud authentication wrapper for the entire app
class Cloud<PERSON>uthWrapper extends StatefulWidget {
  final bool isarInitialized;

  const CloudAuthWrapper({
    Key? key,
    required this.isarInitialized,
  }) : super(key: key);

  @override
  State<CloudAuthWrapper> createState() => _CloudAuthWrapperState();
}

class _CloudAuthWrapperState extends State<CloudAuthWrapper> {
  CloudAuthenticationService? _authService;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    try {
      if (!widget.isarInitialized) {
        setState(() {
          _errorMessage = 'Database not initialized';
          _isLoading = false;
        });
        return;
      }

      // Get cloud authentication service from GetIt
      try {
        _authService = GetIt.instance<CloudAuthenticationService>();
      } catch (e) {
        debugPrint('CloudAuthenticationService not registered: $e');
        setState(() {
          _errorMessage = 'Cloud authentication service not available';
          _isLoading = false;
        });
        return;
      }

      // Initialize authentication service
      if (!_authService!.isInitialized) {
        await _authService!.initialize();
      }

      // Initialize permission service
      try {
        final permissionService = GetIt.instance<PermissionService>();
        if (!permissionService.isInitialized) {
          await permissionService.initialize();
        }
      } catch (e) {
        debugPrint('Permission service not available: $e');
      }

      // Listen to authentication state changes
      _authService!.addListener(_onAuthStateChanged);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error initializing authentication: $e');
      setState(() {
        _errorMessage = 'Failed to initialize authentication: $e';
        _isLoading = false;
      });
    }
  }

  void _onAuthStateChanged() {
    if (mounted) {
      // Refresh permission service when auth state changes
      try {
        final permissionService = GetIt.instance<PermissionService>();
        permissionService.refresh();
      } catch (e) {
        debugPrint('Error refreshing permission service: $e');
      }

      setState(() {});
    }
  }

  @override
  void dispose() {
    _authService?.removeListener(_onAuthStateChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingScreen();
    }

    if (_errorMessage != null) {
      return _buildErrorScreen();
    }

    // Check if database is switching during authentication
    final isDatabaseSwitching = _authService?.isDatabaseSwitching ?? false;
    if (isDatabaseSwitching) {
      return _buildDatabaseSwitchingScreen();
    }

    // Check authentication state
    final isAuthenticated = _authService?.isAuthenticated ?? false;

    if (isAuthenticated) {
      // User is fully authenticated with active session (Remember Me enabled)
      return const DashboardScreen();
    } else {
      // Show normal login screen for all other cases
      return const CloudLoginScreen();
    }
  }

  Widget _buildLoadingScreen() {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppConstants.primaryColor,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.agriculture,
                size: 40,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Cattle Manager',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: AppConstants.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            CircularProgressIndicator(
              color: AppConstants.primaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              'Initializing...',
              style: TextStyle(
                fontSize: 16,
                color: AppConstants.primaryColor.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDatabaseSwitchingScreen() {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppConstants.primaryColor,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.sync,
                size: 40,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Setting up your account...',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppConstants.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            CircularProgressIndicator(
              color: AppConstants.primaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              'Please wait while we prepare your dashboard',
              style: TextStyle(
                fontSize: 16,
                color: AppConstants.primaryColor.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen() {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 80,
                color: AppConstants.errorColor,
              ),
              const SizedBox(height: 24),
              Text(
                'Initialization Error',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppConstants.errorColor,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isLoading = true;
                    _errorMessage = null;
                  });
                  _initializeAuth();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Retry'),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () {
                  // Fallback to local authentication
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => const CloudLoginScreen(),
                    ),
                  );
                },
                child: Text(
                  'Continue with Local Authentication',
                  style: TextStyle(
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
