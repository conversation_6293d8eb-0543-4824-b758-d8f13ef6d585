import 'dart:io';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import '../../../services/logging_service.dart';

/// Cloud backup service for Google Drive integration
class CloudBackupService {
  static final LoggingService _logger = LoggingService.instance;
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  
  // Google Drive scopes
  static const List<String> _scopes = [
    drive.DriveApi.driveFileScope,
  ];
  
  // Singleton instance
  static final CloudBackupService _instance = CloudBackupService._internal();
  static CloudBackupService get instance => _instance;
  
  // Private constructor
  CloudBackupService._internal();
  
  GoogleSignIn? _googleSignIn;
  drive.DriveApi? _driveApi;
  String? _currentUserEmail;
  bool _signInInProgress = false;
  
  /// Initialize Google Sign-In
  void _initializeGoogleSignIn() {
    _googleSignIn = GoogleSignIn(
      scopes: _scopes,
    );
  }
  
  /// Check if user is signed in to Google Drive
  Future<bool> isSignedIn() async {
    // Don't check during sign-in process to prevent conflicts
    if (_signInInProgress) return false;

    try {
      _initializeGoogleSignIn();
      final account = await _googleSignIn?.isSignedIn();
      return account ?? false;
    } catch (e) {
      _logger.logError('Error checking sign-in status: $e');
      return false;
    }
  }
  
  /// Get current signed-in user email
  Future<String?> getCurrentUserEmail() async {
    // Don't check during sign-in process to prevent conflicts
    if (_signInInProgress) return _currentUserEmail;

    try {
      if (_currentUserEmail != null) return _currentUserEmail;

      _initializeGoogleSignIn();
      final account = await _googleSignIn?.signInSilently();
      if (account != null) {
        _currentUserEmail = account.email;
        return _currentUserEmail;
      }
      return null;
    } catch (e) {
      _logger.logError('Error getting current user email: $e');
      return null;
    }
  }
  
  /// Sign in to Google Drive
  Future<CloudBackupResult> signIn() async {
    // Prevent multiple simultaneous sign-in attempts
    if (_signInInProgress) {
      return CloudBackupResult(
        success: false,
        message: 'Sign-in already in progress',
      );
    }

    _signInInProgress = true;

    try {
      _logger.logInfo('Starting Google Drive sign-in');
      _initializeGoogleSignIn();

      final account = await _googleSignIn?.signIn();
      if (account == null) {
        _signInInProgress = false;
        return CloudBackupResult(
          success: false,
          message: 'Sign-in was cancelled by user',
        );
      }

      // Get authentication headers
      final authHeaders = await account.authHeaders;
      final client = GoogleAuthClient(authHeaders);

      // Initialize Drive API
      _driveApi = drive.DriveApi(client);
      _currentUserEmail = account.email;

      // Store credentials securely
      await _storeCredentials(account.email);

      _signInInProgress = false;
      _logger.logInfo('Successfully signed in to Google Drive: ${account.email}');
      return CloudBackupResult(
        success: true,
        message: 'Successfully signed in to Google Drive',
        userEmail: account.email,
      );

    } catch (e) {
      _signInInProgress = false;
      _logger.logError('Error signing in to Google Drive: $e');
      return CloudBackupResult(
        success: false,
        message: 'Failed to sign in: $e',
      );
    }
  }
  
  /// Sign out from Google Drive
  Future<void> signOut() async {
    try {
      await _googleSignIn?.signOut();
      _driveApi = null;
      _currentUserEmail = null;
      await _clearStoredCredentials();
      _logger.logInfo('Successfully signed out from Google Drive');
    } catch (e) {
      _logger.logError('Error signing out from Google Drive: $e');
    }
  }
  
  /// Upload backup file to Google Drive
  Future<CloudBackupResult> uploadBackup(
    String localFilePath, {
    Function(double)? onProgress,
  }) async {
    try {
      _logger.logInfo('Starting backup upload to Google Drive: $localFilePath');
      
      // Ensure we're signed in
      if (_driveApi == null) {
        final signInResult = await _signInSilently();
        if (!signInResult.success) {
          return signInResult;
        }
      }
      
      onProgress?.call(0.1);
      
      // Read the backup file
      final file = File(localFilePath);
      if (!await file.exists()) {
        throw Exception('Backup file not found: $localFilePath');
      }
      
      final fileBytes = await file.readAsBytes();
      final fileName = path.basename(localFilePath);
      
      onProgress?.call(0.3);
      
      // Create folder structure if needed
      final folderId = await _ensureBackupFolder();
      
      onProgress?.call(0.5);
      
      // Create file metadata
      final driveFile = drive.File()
        ..name = fileName
        ..parents = [folderId];
      
      // Upload the file
      final media = drive.Media(
        Stream.fromIterable([fileBytes]),
        fileBytes.length,
        contentType: 'application/octet-stream',
      );
      
      final uploadedFile = await _driveApi!.files.create(
        driveFile,
        uploadMedia: media,
      );
      
      onProgress?.call(0.9);
      
      // Cleanup old backups if needed
      await _cleanupOldCloudBackups();
      
      onProgress?.call(1.0);
      
      _logger.logInfo('Successfully uploaded backup to Google Drive: ${uploadedFile.id}');
      return CloudBackupResult(
        success: true,
        message: 'Backup uploaded successfully',
        cloudFileId: uploadedFile.id,
        cloudFileName: fileName,
      );
      
    } catch (e) {
      _logger.logError('Error uploading backup to Google Drive: $e');
      return CloudBackupResult(
        success: false,
        message: 'Failed to upload backup: $e',
      );
    }
  }
  
  /// Download backup file from Google Drive
  Future<CloudBackupResult> downloadBackup(
    String cloudFileId,
    String localFilePath, {
    Function(double)? onProgress,
  }) async {
    try {
      _logger.logInfo('Starting backup download from Google Drive: $cloudFileId');
      
      // Ensure we're signed in
      if (_driveApi == null) {
        final signInResult = await _signInSilently();
        if (!signInResult.success) {
          return signInResult;
        }
      }
      
      onProgress?.call(0.1);
      
      // Download the file
      final media = await _driveApi!.files.get(
        cloudFileId,
        downloadOptions: drive.DownloadOptions.fullMedia,
      ) as drive.Media;
      
      onProgress?.call(0.5);
      
      // Save to local file
      final file = File(localFilePath);
      final sink = file.openWrite();
      
      await for (final chunk in media.stream) {
        sink.add(chunk);
      }
      await sink.close();
      
      onProgress?.call(1.0);
      
      _logger.logInfo('Successfully downloaded backup from Google Drive to: $localFilePath');
      return CloudBackupResult(
        success: true,
        message: 'Backup downloaded successfully',
        localFilePath: localFilePath,
      );
      
    } catch (e) {
      _logger.logError('Error downloading backup from Google Drive: $e');
      return CloudBackupResult(
        success: false,
        message: 'Failed to download backup: $e',
      );
    }
  }
  
  /// List backup files in Google Drive
  Future<List<CloudBackupFile>> listCloudBackups() async {
    try {
      _logger.logInfo('Listing backup files from Google Drive');
      
      // Ensure we're signed in
      if (_driveApi == null) {
        final signInResult = await _signInSilently();
        if (!signInResult.success) {
          return [];
        }
      }
      
      // Get backup folder
      final folderId = await _ensureBackupFolder();
      
      // List files in backup folder
      final fileList = await _driveApi!.files.list(
        q: "'$folderId' in parents and name contains 'cattle_manager' and trashed=false",
        orderBy: 'createdTime desc',
        spaces: 'drive',
      );
      
      final backupFiles = <CloudBackupFile>[];
      for (final file in fileList.files ?? []) {
        backupFiles.add(CloudBackupFile(
          id: file.id!,
          name: file.name!,
          size: file.size != null ? int.parse(file.size!) : 0,
          createdTime: file.createdTime ?? DateTime.now(),
          modifiedTime: file.modifiedTime ?? DateTime.now(),
        ));
      }
      
      _logger.logInfo('Found ${backupFiles.length} backup files in Google Drive');
      return backupFiles;
      
    } catch (e) {
      _logger.logError('Error listing backup files from Google Drive: $e');
      return [];
    }
  }
  
  /// Delete backup file from Google Drive
  Future<CloudBackupResult> deleteCloudBackup(String cloudFileId) async {
    try {
      _logger.logInfo('Deleting backup file from Google Drive: $cloudFileId');

      // Ensure we're signed in
      if (_driveApi == null) {
        final signInResult = await _signInSilently();
        if (!signInResult.success) {
          return signInResult;
        }
      }

      await _driveApi!.files.delete(cloudFileId);

      _logger.logInfo('Successfully deleted backup file from Google Drive: $cloudFileId');
      return CloudBackupResult(
        success: true,
        message: 'Backup deleted successfully',
      );

    } catch (e) {
      _logger.logError('Error deleting backup file from Google Drive: $e');
      return CloudBackupResult(
        success: false,
        message: 'Failed to delete backup: $e',
      );
    }
  }

  /// Sign in silently (without user interaction)
  Future<CloudBackupResult> _signInSilently() async {
    try {
      _initializeGoogleSignIn();
      final account = await _googleSignIn?.signInSilently();

      if (account == null) {
        return CloudBackupResult(
          success: false,
          message: 'Please sign in to Google Drive first',
        );
      }

      // Get authentication headers
      final authHeaders = await account.authHeaders;
      final client = GoogleAuthClient(authHeaders);

      // Initialize Drive API
      _driveApi = drive.DriveApi(client);
      _currentUserEmail = account.email;

      return CloudBackupResult(
        success: true,
        message: 'Successfully authenticated',
        userEmail: account.email,
      );

    } catch (e) {
      _logger.logError('Error signing in silently: $e');
      return CloudBackupResult(
        success: false,
        message: 'Authentication failed: $e',
      );
    }
  }

  /// Ensure backup folder exists in Google Drive
  Future<String> _ensureBackupFolder() async {
    try {
      // Check if folder already exists
      final folderList = await _driveApi!.files.list(
        q: "name='CattleManager_Backups' and mimeType='application/vnd.google-apps.folder' and trashed=false",
        spaces: 'drive',
      );

      if (folderList.files != null && folderList.files!.isNotEmpty) {
        return folderList.files!.first.id!;
      }

      // Create folder if it doesn't exist
      final folder = drive.File()
        ..name = 'CattleManager_Backups'
        ..mimeType = 'application/vnd.google-apps.folder';

      final createdFolder = await _driveApi!.files.create(folder);
      _logger.logInfo('Created backup folder in Google Drive: ${createdFolder.id}');

      return createdFolder.id!;

    } catch (e) {
      _logger.logError('Error ensuring backup folder: $e');
      throw Exception('Failed to create backup folder: $e');
    }
  }

  /// Cleanup old cloud backups based on settings
  Future<void> _cleanupOldCloudBackups() async {
    try {
      final backupFiles = await listCloudBackups();

      // Default to keeping 10 backups if no settings available
      const maxBackups = 10;

      if (backupFiles.length > maxBackups) {
        // Sort by creation time (newest first)
        backupFiles.sort((a, b) => b.createdTime.compareTo(a.createdTime));

        // Delete old backups
        final filesToDelete = backupFiles.skip(maxBackups);
        for (final file in filesToDelete) {
          await deleteCloudBackup(file.id);
          _logger.logInfo('Deleted old backup from cloud: ${file.name}');
        }
      }

    } catch (e) {
      _logger.logError('Error cleaning up old cloud backups: $e');
    }
  }

  /// Store credentials securely
  Future<void> _storeCredentials(String email) async {
    try {
      await _secureStorage.write(key: 'google_drive_email', value: email);
    } catch (e) {
      _logger.logError('Error storing credentials: $e');
    }
  }

  /// Clear stored credentials
  Future<void> _clearStoredCredentials() async {
    try {
      await _secureStorage.delete(key: 'google_drive_email');
    } catch (e) {
      _logger.logError('Error clearing credentials: $e');
    }
  }
}

/// HTTP client for Google API authentication
class GoogleAuthClient extends http.BaseClient {
  final Map<String, String> _headers;
  final http.Client _client = http.Client();

  GoogleAuthClient(this._headers);

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) {
    request.headers.addAll(_headers);
    return _client.send(request);
  }

  @override
  void close() {
    _client.close();
  }
}

/// Result class for cloud backup operations
class CloudBackupResult {
  final bool success;
  final String message;
  final String? userEmail;
  final String? cloudFileId;
  final String? cloudFileName;
  final String? localFilePath;

  CloudBackupResult({
    required this.success,
    required this.message,
    this.userEmail,
    this.cloudFileId,
    this.cloudFileName,
    this.localFilePath,
  });
}

/// Cloud backup file information
class CloudBackupFile {
  final String id;
  final String name;
  final int size;
  final DateTime createdTime;
  final DateTime modifiedTime;

  CloudBackupFile({
    required this.id,
    required this.name,
    required this.size,
    required this.createdTime,
    required this.modifiedTime,
  });

  String get formattedSize {
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}
