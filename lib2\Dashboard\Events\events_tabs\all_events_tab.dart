import 'package:flutter/material.dart';
import '../models/event.dart';
import '../dialogs/event_form_dialog.dart';
import '../../../services/database_helper.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class AllEventsTab extends StatefulWidget {
  const AllEventsTab({Key? key}) : super(key: key);

  @override
  State<AllEventsTab> createState() => _AllEventsTabState();
}

class _AllEventsTabState extends State<AllEventsTab> {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  String _selectedType = 'all';
  String _searchQuery = '';
  List<FarmEvent> _events = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadEvents();
    _setupFarmChangeListener();
  }

  @override
  void dispose() {
    _dbHelper.removeListener(_onFarmChanged);
    super.dispose();
  }

  void _setupFarmChangeListener() {
    _dbHelper.addListener(_onFarmChanged);
  }

  void _onFarmChanged() {
    _loadEvents();
  }

  Future<void> _loadEvents() async {
    try {
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) {
        setState(() {
          _events = [];
          _isLoading = false;
        });
        return;
      }

      final prefs = await SharedPreferences.getInstance();
      final key = '${selectedFarmId}_events';
      final eventsJson = prefs.getStringList(key) ?? [];
      setState(() {
        _events = eventsJson.map((json) => FarmEvent.fromMap(jsonDecode(json))).toList();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading events: $e');
      setState(() {
        _events = [];
        _isLoading = false;
      });
    }
  }

  Future<void> _saveEvents() async {
    try {
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) return;

      final prefs = await SharedPreferences.getInstance();
      final key = '${selectedFarmId}_events';
      final eventsJson = _events.map((event) => jsonEncode(event.toMap())).toList();
      await prefs.setStringList(key, eventsJson);
    } catch (e) {
      debugPrint('Error saving events: $e');
    }
  }

  List<FarmEvent> _getFilteredEvents() {
    return _events.where((event) {
      bool matchesType = _selectedType == 'all' || event.type.toString().split('.').last == _selectedType;
      bool matchesSearch = _searchQuery.isEmpty ||
          event.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          event.description.toLowerCase().contains(_searchQuery.toLowerCase());
      return matchesType && matchesSearch;
    }).toList();
  }

  Future<void> _addEvent(FarmEvent event) async {
    setState(() => _events.add(event));
    await _saveEvents();
  }

  Future<void> _updateEvent(FarmEvent updatedEvent) async {
    final index = _events.indexWhere((e) => e.id == updatedEvent.id);
    if (index != -1) {
      setState(() => _events[index] = updatedEvent);
      await _saveEvents();
    }
  }

  Future<void> _deleteEvent(String eventId) async {
    setState(() => _events.removeWhere((e) => e.id == eventId));
    await _saveEvents();
  }

  Future<void> _toggleEventCompletion(String eventId) async {
    final index = _events.indexWhere((e) => e.id == eventId);
    if (index != -1) {
      setState(() {
        _events[index] = _events[index].copyWith(
          isCompleted: !_events[index].isCompleted,
          completedAt: !_events[index].isCompleted ? DateTime.now() : null,
        );
      });
      await _saveEvents();
    }
  }

  @override
  Widget build(BuildContext context) {
    final filteredEvents = _getFilteredEvents();

    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // Event filters
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16.0),
          child: Row(
            children: [
              Expanded(
                flex: 2,
                child: DropdownButtonFormField<String>(
                  isExpanded: true,
                  decoration: const InputDecoration(
                    labelText: 'Filter by Type',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedType,
                  items: [
                    const DropdownMenuItem(
                      value: 'all',
                      child: Text('All Events'),
                    ),
                    ...EventType.values.map((type) {
                      final name = type.toString().split('.').last;
                      return DropdownMenuItem(
                        value: name,
                        child: Text(name),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedType = value!);
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 3,
                child: TextField(
                  decoration: const InputDecoration(
                    labelText: 'Search Events',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.search),
                  ),
                  onChanged: (value) {
                    setState(() => _searchQuery = value);
                  },
                ),
              ),
            ],
          ),
        ),
        // Events list
        Expanded(
          child: filteredEvents.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.event_busy,
                        size: 64,
                        color: Theme.of(context).primaryColor.withAlpha(128),
                      ),
                      SizedBox(height: ResponsiveSpacing.getMD(context)),
                      const Text(
                        'No events found',
                        style: TextStyle(fontSize: 18),
                      ),
                      SizedBox(height: ResponsiveSpacing.getSM(context)),
                      ElevatedButton.icon(
                        onPressed: () => _showEventDialog(context),
                        icon: const Icon(Icons.add),
                        label: const Text('Add Event'),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: filteredEvents.length,
                  itemBuilder: (context, index) {
                    final event = filteredEvents[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 8.0,
                        vertical: 4.0,
                      ),
                      child: ListTile(
                        leading: Icon(
                          event.isCompleted ? Icons.check_circle : Icons.event,
                          color: event.isCompleted
                              ? Colors.green
                              : Theme.of(context).primaryColor,
                        ),
                        title: Text(
                          event.title,
                          style: TextStyle(
                            decoration: event.isCompleted
                                ? TextDecoration.lineThrough
                                : null,
                          ),
                        ),
                        subtitle: Text(event.description),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.edit),
                              onPressed: () => _showEventDialog(
                                context,
                                existingEvent: event,
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete),
                              onPressed: () => _showDeleteDialog(
                                context,
                                event,
                              ),
                            ),
                          ],
                        ),
                        onTap: () => _toggleEventCompletion(event.id),
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Future<void> _showEventDialog(BuildContext context, {FarmEvent? existingEvent}) async {
    final result = await showDialog<FarmEvent>(
      context: context,
      builder: (context) => EventFormDialog(event: existingEvent),
    );

    if (result != null) {
      if (existingEvent == null) {
        await _addEvent(result);
      } else {
        await _updateEvent(result);
      }
    }
  }

  Future<void> _showDeleteDialog(BuildContext context, FarmEvent event) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Event'),
        content: Text('Are you sure you want to delete "${event.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _deleteEvent(event.id);
    }
  }
}
