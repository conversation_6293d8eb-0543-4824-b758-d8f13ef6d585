import 'package:flutter/material.dart';
import 'dart:async';
import '../../Cattle/models/cattle_isar.dart';
import '../dialogs/health_record_form_dialog.dart';
import '../widgets/health_record_card.dart';
import '../../../constants/app_colors.dart';
import '../../../utils/message_utils.dart';

import '../models/health_record_isar.dart';
import '../services/health_service.dart';

class CattleHealthRecordsTab extends StatefulWidget {
  final CattleIsar cattle;
  final VoidCallback onCattleUpdated;

  const CattleHealthRecordsTab({
    Key? key,
    required this.cattle,
    required this.onCattleUpdated,
  }) : super(key: key);

  @override
  State<CattleHealthRecordsTab> createState() => _CattleHealthRecordsTabState();
}

class _CattleHealthRecordsTabState extends State<CattleHealthRecordsTab> {
  final HealthService _healthService = HealthService.instance;

  List<HealthRecordIsar> _healthRecords = [];
  bool _isLoading = true;

  // Stream subscriptions
  StreamSubscription<Map<String, dynamic>>? _healthChangeSubscription;

  @override
  void initState() {
    super.initState();
    _setupStreamSubscriptions();
    _loadHealthRecords();
  }

  @override
  void dispose() {
    _healthChangeSubscription?.cancel();
    super.dispose();
  }

  void _setupStreamSubscriptions() {
    // Stream subscriptions can be added here when available
    debugPrint('Stream subscriptions setup for health records');
  }

  Future<void> _loadHealthRecords() async {
    if (!mounted) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      await _healthService.initialize();
      final records = await _healthService.getHealthRecordsForCattle(widget.cattle.businessId ?? '');
      
      if (mounted) {
        setState(() {
          _healthRecords = records;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        MessageUtils.showErrorSnackBar(context, 'Failed to load health records: $e');
      }
    }
  }

  void _addHealthRecord() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HealthRecordFormDialog(
          cattle: [widget.cattle],
          veterinarians: [], // Will be loaded in the dialog
          onRecordAdded: () {
            _loadHealthRecords();
            widget.onCattleUpdated();
          },
        ),
      ),
    );
  }

  void _editHealthRecord(HealthRecordIsar record) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HealthRecordFormDialog(
          cattle: [widget.cattle],
          veterinarians: [], // Will be loaded in the dialog
          existingRecord: record,
          onRecordAdded: () {
            _loadHealthRecords();
            widget.onCattleUpdated();
          },
        ),
      ),
    );
  }

  Future<void> _deleteHealthRecord(HealthRecordIsar record) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Health Record'),
        content: const Text('Are you sure you want to delete this health record?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _healthService.deleteHealthRecord(record.businessId ?? '');
        _loadHealthRecords();
        widget.onCattleUpdated();
        if (mounted) {
          MessageUtils.showSuccessSnackBar(context, 'Health record deleted successfully');
        }
      } catch (e) {
        if (mounted) {
          MessageUtils.showErrorSnackBar(context, 'Failed to delete health record: $e');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Health Status Summary
                _buildHealthStatusSummary(),
                
                // Health Records List
                Expanded(
                  child: _healthRecords.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          padding: const EdgeInsets.all(16.0),
                          itemCount: _healthRecords.length,
                          itemBuilder: (context, index) {
                            final record = _healthRecords[index];
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 8.0),
                              child: HealthRecordCard(
                                record: record,
                                cattle: widget.cattle,
                                onEdit: () => _editHealthRecord(record),
                                onDelete: () => _deleteHealthRecord(record),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addHealthRecord,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildHealthStatusSummary() {
    final totalRecords = _healthRecords.length;
    final recentRecords = _healthRecords.where((record) {
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      return record.date != null && record.date!.isAfter(thirtyDaysAgo);
    }).length;

    return Container(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.health_and_safety, color: Colors.red, size: 24),
              const SizedBox(width: 12),
              const Text(
                'Health Summary',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Total Records',
                  totalRecords.toString(),
                  Icons.medical_information,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  'Recent (30 days)',
                  recentRecords.toString(),
                  Icons.schedule,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  'Health Status',
                  _getHealthStatus(),
                  Icons.trending_up,
                  _getHealthStatusColor(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getHealthStatus() {
    if (_healthRecords.isEmpty) return 'No Data';

    // Get the most recent health record
    _healthRecords.sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
    final mostRecent = _healthRecords.first;

    return mostRecent.healthStatus ?? 'Unknown';
  }

  Color _getHealthStatusColor() {
    final status = _getHealthStatus().toLowerCase();
    switch (status) {
      case 'healthy':
      case 'good':
        return Colors.green;
      case 'sick':
      case 'poor':
        return Colors.red;
      case 'recovering':
      case 'fair':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.medical_information_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Health Records',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add the first health record for ${widget.cattle.name ?? 'this cattle'}',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _addHealthRecord,
            icon: const Icon(Icons.add),
            label: const Text('Add Health Record'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
