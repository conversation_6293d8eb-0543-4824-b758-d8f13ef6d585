#!/usr/bin/env python3
"""
<PERSON><PERSON>t to fix import path issues in the Cattle Manager App.
This script corrects relative import paths for responsive utilities.
"""

import os
import re
import subprocess

def calculate_correct_path(file_path):
    """Calculate the correct relative path to utils and theme directories."""
    # Count the depth from lib directory
    parts = file_path.replace('lib/', '').split('/')
    depth = len(parts) - 1  # Subtract 1 for the file itself
    
    # Generate the correct relative path
    if depth == 0:  # Files directly in lib/
        return ''
    elif depth == 1:  # Files in lib/subfolder/
        return '../'
    elif depth == 2:  # Files in lib/subfolder/subfolder/
        return '../../'
    elif depth == 3:  # Files in lib/subfolder/subfolder/subfolder/
        return '../../../'
    elif depth == 4:  # Files in lib/subfolder/subfolder/subfolder/subfolder/
        return '../../../../'
    else:
        return '../' * depth

def fix_import_paths(file_path):
    """Fix import paths in a single file."""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        original_content = content
        
        # Calculate correct relative path
        correct_path = calculate_correct_path(file_path)
        
        # Fix responsive utility imports
        patterns = [
            (r"import\s+['\"]\.\.\/\.\.\/\.\.\/utils\/responsive_helper\.dart['\"];",
             f"import '{correct_path}utils/responsive_helper.dart';"),
            (r"import\s+['\"]\.\.\/\.\.\/\.\.\/utils\/responsive_layout\.dart['\"];",
             f"import '{correct_path}utils/responsive_layout.dart';"),
            (r"import\s+['\"]\.\.\/\.\.\/\.\.\/theme\/responsive_theme\.dart['\"];",
             f"import '{correct_path}theme/responsive_theme.dart';"),
            
            # Fix other common incorrect paths
            (r"import\s+['\"]\.\.\/\.\.\/\.\.\/\.\.\/utils\/responsive_helper\.dart['\"];",
             f"import '{correct_path}utils/responsive_helper.dart';"),
            (r"import\s+['\"]\.\.\/\.\.\/\.\.\/\.\.\/utils\/responsive_layout\.dart['\"];",
             f"import '{correct_path}utils/responsive_layout.dart';"),
            (r"import\s+['\"]\.\.\/\.\.\/\.\.\/\.\.\/theme\/responsive_theme\.dart['\"];",
             f"import '{correct_path}theme/responsive_theme.dart';"),
            
            # Fix any remaining incorrect paths
            (r"import\s+['\"]\.\.\/\.\.\/utils\/responsive_helper\.dart['\"];",
             f"import '{correct_path}utils/responsive_helper.dart';"),
            (r"import\s+['\"]\.\.\/\.\.\/utils\/responsive_layout\.dart['\"];",
             f"import '{correct_path}utils/responsive_layout.dart';"),
            (r"import\s+['\"]\.\.\/\.\.\/theme\/responsive_theme\.dart['\"];",
             f"import '{correct_path}theme/responsive_theme.dart';"),
        ]
        
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content)
        
        # Remove duplicate imports
        lines = content.split('\n')
        seen_imports = set()
        cleaned_lines = []
        
        for line in lines:
            if line.strip().startswith('import') and 'responsive' in line:
                if line.strip() not in seen_imports:
                    seen_imports.add(line.strip())
                    cleaned_lines.append(line)
            else:
                cleaned_lines.append(line)
        
        content = '\n'.join(cleaned_lines)
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def check_for_syntax_errors(file_path):
    """Check for common Dart syntax errors."""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        errors = []
        
        # Check for missing semicolons
        if re.search(r'\)\s*$', content, re.MULTILINE):
            errors.append("Possible missing semicolon")
        
        # Check for unmatched brackets
        open_brackets = content.count('{')
        close_brackets = content.count('}')
        if open_brackets != close_brackets:
            errors.append(f"Unmatched brackets: {open_brackets} open, {close_brackets} close")