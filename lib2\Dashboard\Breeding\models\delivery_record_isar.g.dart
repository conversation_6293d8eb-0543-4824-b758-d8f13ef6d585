// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivery_record_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetDeliveryRecordIsarCollection on Isar {
  IsarCollection<DeliveryRecordIsar> get deliveryRecordIsars =>
      this.collection();
}

const DeliveryRecordIsarSchema = CollectionSchema(
  name: r'DeliveryRecordIsar',
  id: -574137174295036132,
  properties: {
    r'businessId': PropertySchema(
      id: 0,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'calfCount': PropertySchema(
      id: 1,
      name: r'calfCount',
      type: IsarType.long,
    ),
    r'calfDetailsJson': PropertySchema(
      id: 2,
      name: r'calfDetailsJson',
      type: IsarType.string,
    ),
    r'calfIds': PropertySchema(
      id: 3,
      name: r'calfIds',
      type: IsarType.string,
    ),
    r'cattleId': PropertySchema(
      id: 4,
      name: r'cattleId',
      type: IsarType.string,
    ),
    r'complicationDetails': PropertySchema(
      id: 5,
      name: r'complicationDetails',
      type: IsarType.string,
    ),
    r'createdAt': PropertySchema(
      id: 6,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'deliveryDate': PropertySchema(
      id: 7,
      name: r'deliveryDate',
      type: IsarType.dateTime,
    ),
    r'deliveryType': PropertySchema(
      id: 8,
      name: r'deliveryType',
      type: IsarType.string,
    ),
    r'hadComplications': PropertySchema(
      id: 9,
      name: r'hadComplications',
      type: IsarType.bool,
    ),
    r'notes': PropertySchema(
      id: 10,
      name: r'notes',
      type: IsarType.string,
    ),
    r'pregnancyId': PropertySchema(
      id: 11,
      name: r'pregnancyId',
      type: IsarType.string,
    ),
    r'status': PropertySchema(
      id: 12,
      name: r'status',
      type: IsarType.string,
    ),
    r'updatedAt': PropertySchema(
      id: 13,
      name: r'updatedAt',
      type: IsarType.dateTime,
    ),
    r'veterinarian': PropertySchema(
      id: 14,
      name: r'veterinarian',
      type: IsarType.string,
    )
  },
  estimateSize: _deliveryRecordIsarEstimateSize,
  serialize: _deliveryRecordIsarSerialize,
  deserialize: _deliveryRecordIsarDeserialize,
  deserializeProp: _deliveryRecordIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'cattleId': IndexSchema(
      id: 3179256717057104213,
      name: r'cattleId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'cattleId',
          type: IndexType.value,
          caseSensitive: true,
        )
      ],
    ),
    r'pregnancyId': IndexSchema(
      id: 5106044491664989674,
      name: r'pregnancyId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'pregnancyId',
          type: IndexType.value,
          caseSensitive: true,
        )
      ],
    ),
    r'deliveryDate': IndexSchema(
      id: 3163565673826690650,
      name: r'deliveryDate',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'deliveryDate',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _deliveryRecordIsarGetId,
  getLinks: _deliveryRecordIsarGetLinks,
  attach: _deliveryRecordIsarAttach,
  version: '3.1.0+1',
);

int _deliveryRecordIsarEstimateSize(
  DeliveryRecordIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.calfDetailsJson;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.calfIds;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.cattleId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.complicationDetails;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.deliveryType;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.notes;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.pregnancyId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.status;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.veterinarian;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _deliveryRecordIsarSerialize(
  DeliveryRecordIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.businessId);
  writer.writeLong(offsets[1], object.calfCount);
  writer.writeString(offsets[2], object.calfDetailsJson);
  writer.writeString(offsets[3], object.calfIds);
  writer.writeString(offsets[4], object.cattleId);
  writer.writeString(offsets[5], object.complicationDetails);
  writer.writeDateTime(offsets[6], object.createdAt);
  writer.writeDateTime(offsets[7], object.deliveryDate);
  writer.writeString(offsets[8], object.deliveryType);
  writer.writeBool(offsets[9], object.hadComplications);
  writer.writeString(offsets[10], object.notes);
  writer.writeString(offsets[11], object.pregnancyId);
  writer.writeString(offsets[12], object.status);
  writer.writeDateTime(offsets[13], object.updatedAt);
  writer.writeString(offsets[14], object.veterinarian);
}

DeliveryRecordIsar _deliveryRecordIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = DeliveryRecordIsar();
  object.businessId = reader.readStringOrNull(offsets[0]);
  object.calfCount = reader.readLong(offsets[1]);
  object.calfDetailsJson = reader.readStringOrNull(offsets[2]);
  object.calfIds = reader.readStringOrNull(offsets[3]);
  object.cattleId = reader.readStringOrNull(offsets[4]);
  object.complicationDetails = reader.readStringOrNull(offsets[5]);
  object.createdAt = reader.readDateTimeOrNull(offsets[6]);
  object.deliveryDate = reader.readDateTimeOrNull(offsets[7]);
  object.deliveryType = reader.readStringOrNull(offsets[8]);
  object.hadComplications = reader.readBool(offsets[9]);
  object.id = id;
  object.notes = reader.readStringOrNull(offsets[10]);
  object.pregnancyId = reader.readStringOrNull(offsets[11]);
  object.status = reader.readStringOrNull(offsets[12]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[13]);
  object.veterinarian = reader.readStringOrNull(offsets[14]);
  return object;
}

P _deliveryRecordIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readLong(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 7:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 8:
      return (reader.readStringOrNull(offset)) as P;
    case 9:
      return (reader.readBool(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readStringOrNull(offset)) as P;
    case 12:
      return (reader.readStringOrNull(offset)) as P;
    case 13:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 14:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _deliveryRecordIsarGetId(DeliveryRecordIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _deliveryRecordIsarGetLinks(
    DeliveryRecordIsar object) {
  return [];
}

void _deliveryRecordIsarAttach(
    IsarCollection<dynamic> col, Id id, DeliveryRecordIsar object) {
  object.id = id;
}

extension DeliveryRecordIsarByIndex on IsarCollection<DeliveryRecordIsar> {
  Future<DeliveryRecordIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  DeliveryRecordIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<DeliveryRecordIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<DeliveryRecordIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(DeliveryRecordIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(DeliveryRecordIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<DeliveryRecordIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<DeliveryRecordIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension DeliveryRecordIsarQueryWhereSort
    on QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QWhere> {
  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhere>
      anyCattleId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'cattleId'),
      );
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhere>
      anyPregnancyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'pregnancyId'),
      );
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhere>
      anyDeliveryDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'deliveryDate'),
      );
    });
  }
}

extension DeliveryRecordIsarQueryWhere
    on QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QWhereClause> {
  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      cattleIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleId',
        value: [null],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      cattleIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cattleId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      cattleIdEqualTo(String? cattleId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleId',
        value: [cattleId],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      cattleIdNotEqualTo(String? cattleId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleId',
              lower: [],
              upper: [cattleId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleId',
              lower: [cattleId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleId',
              lower: [cattleId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleId',
              lower: [],
              upper: [cattleId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      cattleIdGreaterThan(
    String? cattleId, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cattleId',
        lower: [cattleId],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      cattleIdLessThan(
    String? cattleId, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cattleId',
        lower: [],
        upper: [cattleId],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      cattleIdBetween(
    String? lowerCattleId,
    String? upperCattleId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cattleId',
        lower: [lowerCattleId],
        includeLower: includeLower,
        upper: [upperCattleId],
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      cattleIdStartsWith(String CattleIdPrefix) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cattleId',
        lower: [CattleIdPrefix],
        upper: ['$CattleIdPrefix\u{FFFFF}'],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      cattleIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleId',
        value: [''],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      cattleIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'cattleId',
              upper: [''],
            ))
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'cattleId',
              lower: [''],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'cattleId',
              lower: [''],
            ))
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'cattleId',
              upper: [''],
            ));
      }
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      pregnancyIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'pregnancyId',
        value: [null],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      pregnancyIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'pregnancyId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      pregnancyIdEqualTo(String? pregnancyId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'pregnancyId',
        value: [pregnancyId],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      pregnancyIdNotEqualTo(String? pregnancyId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'pregnancyId',
              lower: [],
              upper: [pregnancyId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'pregnancyId',
              lower: [pregnancyId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'pregnancyId',
              lower: [pregnancyId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'pregnancyId',
              lower: [],
              upper: [pregnancyId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      pregnancyIdGreaterThan(
    String? pregnancyId, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'pregnancyId',
        lower: [pregnancyId],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      pregnancyIdLessThan(
    String? pregnancyId, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'pregnancyId',
        lower: [],
        upper: [pregnancyId],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      pregnancyIdBetween(
    String? lowerPregnancyId,
    String? upperPregnancyId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'pregnancyId',
        lower: [lowerPregnancyId],
        includeLower: includeLower,
        upper: [upperPregnancyId],
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      pregnancyIdStartsWith(String PregnancyIdPrefix) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'pregnancyId',
        lower: [PregnancyIdPrefix],
        upper: ['$PregnancyIdPrefix\u{FFFFF}'],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      pregnancyIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'pregnancyId',
        value: [''],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      pregnancyIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'pregnancyId',
              upper: [''],
            ))
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'pregnancyId',
              lower: [''],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.greaterThan(
              indexName: r'pregnancyId',
              lower: [''],
            ))
            .addWhereClause(IndexWhereClause.lessThan(
              indexName: r'pregnancyId',
              upper: [''],
            ));
      }
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      deliveryDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'deliveryDate',
        value: [null],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      deliveryDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'deliveryDate',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      deliveryDateEqualTo(DateTime? deliveryDate) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'deliveryDate',
        value: [deliveryDate],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      deliveryDateNotEqualTo(DateTime? deliveryDate) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'deliveryDate',
              lower: [],
              upper: [deliveryDate],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'deliveryDate',
              lower: [deliveryDate],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'deliveryDate',
              lower: [deliveryDate],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'deliveryDate',
              lower: [],
              upper: [deliveryDate],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      deliveryDateGreaterThan(
    DateTime? deliveryDate, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'deliveryDate',
        lower: [deliveryDate],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      deliveryDateLessThan(
    DateTime? deliveryDate, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'deliveryDate',
        lower: [],
        upper: [deliveryDate],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterWhereClause>
      deliveryDateBetween(
    DateTime? lowerDeliveryDate,
    DateTime? upperDeliveryDate, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'deliveryDate',
        lower: [lowerDeliveryDate],
        includeLower: includeLower,
        upper: [upperDeliveryDate],
        includeUpper: includeUpper,
      ));
    });
  }
}

extension DeliveryRecordIsarQueryFilter
    on QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QFilterCondition> {
  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfCountEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'calfCount',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfCountGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'calfCount',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfCountLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'calfCount',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfCountBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'calfCount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfDetailsJsonIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'calfDetailsJson',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfDetailsJsonIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'calfDetailsJson',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfDetailsJsonEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'calfDetailsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfDetailsJsonGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'calfDetailsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfDetailsJsonLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'calfDetailsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfDetailsJsonBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'calfDetailsJson',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfDetailsJsonStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'calfDetailsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfDetailsJsonEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'calfDetailsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfDetailsJsonContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'calfDetailsJson',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfDetailsJsonMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'calfDetailsJson',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfDetailsJsonIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'calfDetailsJson',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfDetailsJsonIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'calfDetailsJson',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfIdsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'calfIds',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfIdsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'calfIds',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfIdsEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'calfIds',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfIdsGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'calfIds',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfIdsLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'calfIds',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfIdsBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'calfIds',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfIdsStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'calfIds',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfIdsEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'calfIds',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfIdsContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'calfIds',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfIdsMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'calfIds',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfIdsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'calfIds',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      calfIdsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'calfIds',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      cattleIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cattleId',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      cattleIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cattleId',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      cattleIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      cattleIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cattleId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      cattleIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cattleId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      cattleIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cattleId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      cattleIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cattleId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      cattleIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cattleId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      cattleIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cattleId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      cattleIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cattleId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      cattleIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleId',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      cattleIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cattleId',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      complicationDetailsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'complicationDetails',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      complicationDetailsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'complicationDetails',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      complicationDetailsEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'complicationDetails',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      complicationDetailsGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'complicationDetails',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      complicationDetailsLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'complicationDetails',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      complicationDetailsBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'complicationDetails',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      complicationDetailsStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'complicationDetails',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      complicationDetailsEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'complicationDetails',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      complicationDetailsContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'complicationDetails',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      complicationDetailsMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'complicationDetails',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      complicationDetailsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'complicationDetails',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      complicationDetailsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'complicationDetails',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'deliveryDate',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'deliveryDate',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'deliveryDate',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'deliveryDate',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'deliveryDate',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'deliveryDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'deliveryType',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'deliveryType',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryTypeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'deliveryType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryTypeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'deliveryType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryTypeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'deliveryType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryTypeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'deliveryType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryTypeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'deliveryType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryTypeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'deliveryType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryTypeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'deliveryType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryTypeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'deliveryType',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryTypeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'deliveryType',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      deliveryTypeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'deliveryType',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      hadComplicationsEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'hadComplications',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      notesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      notesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      notesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      notesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      notesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      notesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'notes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      notesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      notesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      notesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      notesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'notes',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      notesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      notesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      pregnancyIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'pregnancyId',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      pregnancyIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'pregnancyId',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      pregnancyIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'pregnancyId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      pregnancyIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'pregnancyId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      pregnancyIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'pregnancyId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      pregnancyIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'pregnancyId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      pregnancyIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'pregnancyId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      pregnancyIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'pregnancyId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      pregnancyIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'pregnancyId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      pregnancyIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'pregnancyId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      pregnancyIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'pregnancyId',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      pregnancyIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'pregnancyId',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      statusIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'status',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      statusIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'status',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      statusEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      statusGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      statusLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      statusBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'status',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      statusStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      statusEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      statusContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      statusMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'status',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      statusIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      statusIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'status',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      veterinarianIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'veterinarian',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      veterinarianIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'veterinarian',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      veterinarianEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'veterinarian',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      veterinarianGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'veterinarian',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      veterinarianLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'veterinarian',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      veterinarianBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'veterinarian',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      veterinarianStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'veterinarian',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      veterinarianEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'veterinarian',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      veterinarianContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'veterinarian',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      veterinarianMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'veterinarian',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      veterinarianIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'veterinarian',
        value: '',
      ));
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterFilterCondition>
      veterinarianIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'veterinarian',
        value: '',
      ));
    });
  }
}

extension DeliveryRecordIsarQueryObject
    on QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QFilterCondition> {}

extension DeliveryRecordIsarQueryLinks
    on QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QFilterCondition> {}

extension DeliveryRecordIsarQuerySortBy
    on QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QSortBy> {
  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByCalfCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calfCount', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByCalfCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calfCount', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByCalfDetailsJson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calfDetailsJson', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByCalfDetailsJsonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calfDetailsJson', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByCalfIds() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calfIds', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByCalfIdsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calfIds', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByCattleId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleId', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByCattleIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleId', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByComplicationDetails() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'complicationDetails', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByComplicationDetailsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'complicationDetails', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByDeliveryDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deliveryDate', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByDeliveryDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deliveryDate', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByDeliveryType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deliveryType', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByDeliveryTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deliveryType', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByHadComplications() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hadComplications', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByHadComplicationsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hadComplications', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByPregnancyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancyId', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByPregnancyIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancyId', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByVeterinarian() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veterinarian', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      sortByVeterinarianDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veterinarian', Sort.desc);
    });
  }
}

extension DeliveryRecordIsarQuerySortThenBy
    on QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QSortThenBy> {
  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByCalfCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calfCount', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByCalfCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calfCount', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByCalfDetailsJson() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calfDetailsJson', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByCalfDetailsJsonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calfDetailsJson', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByCalfIds() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calfIds', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByCalfIdsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calfIds', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByCattleId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleId', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByCattleIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleId', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByComplicationDetails() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'complicationDetails', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByComplicationDetailsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'complicationDetails', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByDeliveryDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deliveryDate', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByDeliveryDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deliveryDate', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByDeliveryType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deliveryType', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByDeliveryTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deliveryType', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByHadComplications() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hadComplications', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByHadComplicationsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hadComplications', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByPregnancyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancyId', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByPregnancyIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancyId', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByVeterinarian() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veterinarian', Sort.asc);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QAfterSortBy>
      thenByVeterinarianDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veterinarian', Sort.desc);
    });
  }
}

extension DeliveryRecordIsarQueryWhereDistinct
    on QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct> {
  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct>
      distinctByCalfCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'calfCount');
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct>
      distinctByCalfDetailsJson({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'calfDetailsJson',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct>
      distinctByCalfIds({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'calfIds', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct>
      distinctByCattleId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cattleId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct>
      distinctByComplicationDetails({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'complicationDetails',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct>
      distinctByDeliveryDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'deliveryDate');
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct>
      distinctByDeliveryType({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'deliveryType', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct>
      distinctByHadComplications() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'hadComplications');
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct>
      distinctByNotes({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notes', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct>
      distinctByPregnancyId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'pregnancyId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct>
      distinctByStatus({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'status', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }

  QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QDistinct>
      distinctByVeterinarian({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'veterinarian', caseSensitive: caseSensitive);
    });
  }
}

extension DeliveryRecordIsarQueryProperty
    on QueryBuilder<DeliveryRecordIsar, DeliveryRecordIsar, QQueryProperty> {
  QueryBuilder<DeliveryRecordIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<DeliveryRecordIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<DeliveryRecordIsar, int, QQueryOperations> calfCountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'calfCount');
    });
  }

  QueryBuilder<DeliveryRecordIsar, String?, QQueryOperations>
      calfDetailsJsonProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'calfDetailsJson');
    });
  }

  QueryBuilder<DeliveryRecordIsar, String?, QQueryOperations>
      calfIdsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'calfIds');
    });
  }

  QueryBuilder<DeliveryRecordIsar, String?, QQueryOperations>
      cattleIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cattleId');
    });
  }

  QueryBuilder<DeliveryRecordIsar, String?, QQueryOperations>
      complicationDetailsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'complicationDetails');
    });
  }

  QueryBuilder<DeliveryRecordIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<DeliveryRecordIsar, DateTime?, QQueryOperations>
      deliveryDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'deliveryDate');
    });
  }

  QueryBuilder<DeliveryRecordIsar, String?, QQueryOperations>
      deliveryTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'deliveryType');
    });
  }

  QueryBuilder<DeliveryRecordIsar, bool, QQueryOperations>
      hadComplicationsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'hadComplications');
    });
  }

  QueryBuilder<DeliveryRecordIsar, String?, QQueryOperations> notesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notes');
    });
  }

  QueryBuilder<DeliveryRecordIsar, String?, QQueryOperations>
      pregnancyIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'pregnancyId');
    });
  }

  QueryBuilder<DeliveryRecordIsar, String?, QQueryOperations> statusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'status');
    });
  }

  QueryBuilder<DeliveryRecordIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }

  QueryBuilder<DeliveryRecordIsar, String?, QQueryOperations>
      veterinarianProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'veterinarian');
    });
  }
}
