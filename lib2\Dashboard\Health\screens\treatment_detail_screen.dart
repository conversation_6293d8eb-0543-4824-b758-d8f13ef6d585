import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/treatment_record_isar.dart';
import '../models/veterinarian_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../constants/health_constants.dart';
import '../../../constants/app_bar.dart';
import '../dialogs/treatment_form_dialog.dart';

class TreatmentDetailScreen extends StatelessWidget {
  final TreatmentRecordIsar treatmentRecord;
  final CattleIsar? cattle;
  final List<CattleIsar> allCattle;
  final List<VeterinarianIsar> veterinarians;
  final VoidCallback onRecordUpdated;

  const TreatmentDetailScreen({
    super.key,
    required this.treatmentRecord,
    this.cattle,
    required this.allCattle,
    required this.veterinarians,
    required this.onRecordUpdated,
  });

  @override
  Widget build(BuildContext context) {
    final statusColor = HealthConstants.getTreatmentStatusColor(treatmentRecord.status);
    final now = DateTime.now();

    return Scaffold(
      appBar: AppBarConfig.standard(
        title: 'Treatment Details',
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _editRecord(context),
            tooltip: 'Edit Treatment',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card
            _buildHeaderCard(statusColor),
            const SizedBox(height: 16),

            // Medication Information
            _buildMedicationCard(),
            const SizedBox(height: 16),

            // Dosage and Administration
            _buildDosageCard(),
            const SizedBox(height: 16),

            // Treatment Schedule
            _buildScheduleCard(),
            const SizedBox(height: 16),

            // Withdrawal Periods
            if (_hasWithdrawalPeriods()) ...[
              _buildWithdrawalCard(now),
              const SizedBox(height: 16),
            ],

            // Veterinarian Information
            if (treatmentRecord.prescribedBy != null) ...[
              _buildVeterinarianCard(),
              const SizedBox(height: 16),
            ],

            // Cost Information
            if (treatmentRecord.cost != null || treatmentRecord.supplier != null) ...[
              _buildCostCard(),
              const SizedBox(height: 16),
            ],

            // Flags and Status
            _buildFlagsCard(),
            const SizedBox(height: 16),

            // Notes
            if (treatmentRecord.notes != null && treatmentRecord.notes!.isNotEmpty) ...[
              _buildNotesCard(),
              const SizedBox(height: 16),
            ],

            // Record Metadata
            _buildMetadataCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard(Color statusColor) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Cattle Information
            Row(
              children: [
                Icon(Icons.pets, size: 24, color: Colors.blue[600]),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        cattle?.name ?? 'Unknown Cattle',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Tag: ${cattle?.tagId ?? 'Unknown'}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                // Status
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    treatmentRecord.status ?? 'Unknown',
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Medication Name
            Row(
              children: [
                Icon(Icons.medication, size: 20, color: Colors.green[600]),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    treatmentRecord.medicationName ?? 'Unknown Medication',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMedicationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.medication, color: Colors.blue[600]),
                const SizedBox(width: 8),
                Text(
                  'Medication Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildDetailRow('Medication Name', treatmentRecord.medicationName ?? 'Unknown'),
            
            if (treatmentRecord.medicationCategory != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow('Category', treatmentRecord.medicationCategory!),
            ],
            
            if (treatmentRecord.reason != null && treatmentRecord.reason!.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildDetailRow('Reason', treatmentRecord.reason!),
            ],
            
            if (treatmentRecord.batchNumber != null && treatmentRecord.batchNumber!.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildDetailRow('Batch Number', treatmentRecord.batchNumber!),
            ],
            
            if (treatmentRecord.expiryDate != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow(
                'Expiry Date',
                DateFormat('MMM dd, yyyy').format(treatmentRecord.expiryDate!),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDosageCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.local_pharmacy, color: Colors.green[600]),
                const SizedBox(width: 8),
                Text(
                  'Dosage & Administration',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildDosageItem(
                    'Dosage',
                    '${treatmentRecord.dosageAmount ?? 0} ${treatmentRecord.dosageUnit ?? ''}',
                    Icons.medication_liquid,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDosageItem(
                    'Frequency',
                    treatmentRecord.frequency ?? 'Unknown',
                    Icons.schedule,
                    Colors.purple,
                  ),
                ),
              ],
            ),
            
            if (treatmentRecord.route != null) ...[
              const SizedBox(height: 16),
              _buildDosageItem(
                'Route',
                treatmentRecord.route!,
                Icons.route,
                Colors.teal,
              ),
            ],
            
            if (treatmentRecord.instructions != null && treatmentRecord.instructions!.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildDetailRow('Instructions', treatmentRecord.instructions!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDosageItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.date_range, color: Colors.purple[600]),
                const SizedBox(width: 8),
                Text(
                  'Treatment Schedule',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildDetailRow(
              'Start Date',
              treatmentRecord.startDate != null 
                  ? DateFormat('MMM dd, yyyy').format(treatmentRecord.startDate!)
                  : 'Unknown',
            ),
            
            if (treatmentRecord.endDate != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow(
                'End Date',
                DateFormat('MMM dd, yyyy').format(treatmentRecord.endDate!),
              ),
            ],
            
            if (treatmentRecord.durationDays != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow('Duration', '${treatmentRecord.durationDays!} days'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildWithdrawalCard(DateTime now) {
    final milkWithdrawal = treatmentRecord.milkWithdrawalEndDate;
    final meatWithdrawal = treatmentRecord.meatWithdrawalEndDate;
    final inMilkWithdrawal = milkWithdrawal != null && milkWithdrawal.isAfter(now);
    final inMeatWithdrawal = meatWithdrawal != null && meatWithdrawal.isAfter(now);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning, color: Colors.red[600]),
                const SizedBox(width: 8),
                Text(
                  'Withdrawal Periods',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (treatmentRecord.milkWithdrawalDays != null) ...[
              Row(
                children: [
                  Expanded(
                    child: _buildWithdrawalItem(
                      'Milk Withdrawal',
                      '${treatmentRecord.milkWithdrawalDays!} days',
                      milkWithdrawal != null 
                          ? 'Until ${DateFormat('MMM dd, yyyy').format(milkWithdrawal)}'
                          : null,
                      inMilkWithdrawal,
                    ),
                  ),
                ],
              ),
            ],
            
            if (treatmentRecord.meatWithdrawalDays != null) ...[
              if (treatmentRecord.milkWithdrawalDays != null) const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildWithdrawalItem(
                      'Meat Withdrawal',
                      '${treatmentRecord.meatWithdrawalDays!} days',
                      meatWithdrawal != null 
                          ? 'Until ${DateFormat('MMM dd, yyyy').format(meatWithdrawal)}'
                          : null,
                      inMeatWithdrawal,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildWithdrawalItem(String label, String duration, String? endDate, bool isActive) {
    final color = isActive ? Colors.red : Colors.green;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isActive ? Icons.warning : Icons.check_circle,
                color: color,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            duration,
            style: TextStyle(
              fontSize: 14,
              color: color,
            ),
          ),
          if (endDate != null) ...[
            const SizedBox(height: 4),
            Text(
              endDate,
              style: TextStyle(
                fontSize: 12,
                color: color,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildVeterinarianCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Colors.teal[600]),
                const SizedBox(width: 8),
                Text(
                  'Veterinarian Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildDetailRow('Prescribed By', treatmentRecord.prescribedBy!),
          ],
        ),
      ),
    );
  }

  Widget _buildCostCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.attach_money, color: Colors.indigo[600]),
                const SizedBox(width: 8),
                Text(
                  'Cost Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.indigo[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (treatmentRecord.cost != null) ...[
              _buildDetailRow('Cost', '\$${treatmentRecord.cost!.toStringAsFixed(2)}'),
            ],
            
            if (treatmentRecord.supplier != null && treatmentRecord.supplier!.isNotEmpty) ...[
              if (treatmentRecord.cost != null) const SizedBox(height: 12),
              _buildDetailRow('Supplier', treatmentRecord.supplier!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFlagsCard() {
    final flags = <String, Color>{};
    
    if (treatmentRecord.isEmergency) flags['Emergency'] = Colors.red;
    if (treatmentRecord.isPreventive) flags['Preventive'] = Colors.green;
    if (treatmentRecord.requiresFollowUp) flags['Requires Follow-up'] = Colors.blue;
    if (treatmentRecord.reminderSet) flags['Reminder Set'] = Colors.purple;

    if (flags.isEmpty) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flag, color: Colors.deepPurple[600]),
                const SizedBox(width: 8),
                Text(
                  'Treatment Flags',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.deepPurple[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: flags.entries.map((entry) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: entry.value.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: entry.value.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    entry.key,
                    style: TextStyle(
                      color: entry.value,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.notes, color: Colors.blueGrey[600]),
                const SizedBox(width: 8),
                Text(
                  'Notes',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blueGrey[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Text(
              treatmentRecord.notes!,
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  'Record Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (treatmentRecord.createdAt != null) ...[
              _buildDetailRow(
                'Created',
                DateFormat('MMM dd, yyyy HH:mm').format(treatmentRecord.createdAt!),
              ),
              const SizedBox(height: 12),
            ],
            
            if (treatmentRecord.updatedAt != null) ...[
              _buildDetailRow(
                'Last Updated',
                DateFormat('MMM dd, yyyy HH:mm').format(treatmentRecord.updatedAt!),
              ),
              const SizedBox(height: 12),
            ],
            
            if (treatmentRecord.businessId != null) ...[
              _buildDetailRow('Record ID', treatmentRecord.businessId!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 14),
          ),
        ),
      ],
    );
  }

  bool _hasWithdrawalPeriods() {
    return treatmentRecord.milkWithdrawalDays != null ||
           treatmentRecord.meatWithdrawalDays != null;
  }

  void _editRecord(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TreatmentFormDialog(
          cattle: allCattle,
          veterinarians: veterinarians,
          existingRecord: treatmentRecord,
          onRecordAdded: onRecordUpdated,
        ),
      ),
    );
  }
}
