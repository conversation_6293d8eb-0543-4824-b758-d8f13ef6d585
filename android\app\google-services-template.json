#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix ResponsiveTheme class references after moving nested classes to top-level.
"""

import os
import re
import subprocess

def fix_class_references(file_path):
    """Fix class references in a single file."""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        original_content = content
        
        # Fix ResponsiveSpacing references
        content = re.sub(r'ResponsiveTheme\.ResponsiveSpacing\.', 'ResponsiveSpacing.', content)
        
        # Fix ResponsiveIconSizes references
        content = re.sub(r'ResponsiveTheme\.ResponsiveIconSizes\.', 'ResponsiveIconSizes.', content)
        
        # Fix ResponsiveBorderRadius references
        content = re.sub(r'ResponsiveTheme\.ResponsiveBorderRadius\.', 'ResponsiveBorderRadius.', content)
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w') as f:
                f.write(content)
            return Tr