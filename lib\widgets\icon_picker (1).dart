import 'package:flutter/material.dart';
import '../../Breeding/models/pregnancy_record.dart';
import 'report_data.dart';
import 'package:intl/intl.dart';
import 'chart_data.dart';

class PregnancyReportData extends ReportData {
  final List<PregnancyRecord> pregnancies;
  final String? stage;

  PregnancyReportData({
    required this.pregnancies,
    this.stage,
    required DateTime startDate,
    required DateTime endDate,
  }) : super(startDate: startDate, endDate: endDate);

  @override
  String get reportTitle => 'Pregnancy Report';

  @override
  List<DataColumn> get tableColumns => const [
        DataColumn(label: Text('Cattle ID')),
        DataColumn(label: Text('Start Date')),
        DataColumn(label: Text('Due Date')),
        DataColumn(label: Text('Stage')),
        DataColumn(label: Text('Status')),
        DataColumn(label: Text('Notes')),
      ];

  @override
  List<DataRow> get tableRows => filteredPregnancies.map((pregnancy) {
        final dueDate = pregnancy.startDate.add(const Duration(days: 280));
        final stage = _calculateStage(pregnancy.startDate);

        return DataRow(
          cells: [
            DataCell(Text(pregnancy.cattleId)),
            DataCell(
                Text(DateFormat('yyyy-MM-dd').format(pregnancy.startDate))),
            DataCell(Text(DateFormat('yyyy-MM-dd').format(dueDate))),
            DataCell(Text(stage)),
            DataCell(Text(pregnancy.status)),
            DataCell(Text(pregnancy.notes ?? '')),
          ],
        );
      }).toList();

  List<PregnancyRecord> get filteredPregnancies {
    return pregnancies.where((pregnancy) {
      if (startDate != null && pregnancy.startDate.isBefore(startDate!)) {
        return false;
      }
      if (endDate != null && pregnancy.startDate.isAfter(endDate!)) {
        return false;
      }
      if (stage != null && _calculateStage(pregnancy.startDate) != stage) {
        return false;
      }
      return true;
    }).toList()
      ..sort((a, b) => b.startDate.compareTo(a.startDate));
  }

  String _calculateStage(DateTime startDate) {
    final now = DateTime.now();
    final daysPregnant = now.difference(startDate).inDays;

    if (daysPregnant < 90) return 'First Trimester';
    if (daysPregnant < 180) return 'Second Trimester';
    if (daysPregnant < 280) return 'Third Trimester';
    return 'Due';
  }

  @override
  Map<String, dynamic> get summaryData {
    if (filteredPregnancies.isEmpty) {
      return {
        'Total Pregnancies': 0,
        'Active Pregnancies': 0,
        'Due Soon': 0,
        'Success Rate': 0.0,
      };
    }

    final now = DateTime.now();
    int active = 0;
    int dueSoon = 0;
    int successful = 0;
    int completed = 0;

    for (var pregnancy in filteredPregnancies) {
      if (pregnancy.status == 'Active') {
        active++;
        final dueDate = pregnancy.startDate.add(const Duration(days: 280));
        if (dueDate.difference(now).inDays <= 30) {
          dueSoon++;
        }
      } else {
        completed++;
        if (pregnancy.status == 'Successful') {
          successful++;
        }
      }
    }

    final successRate = completed > 0 ? (successful / completed) * 100 : 0.0;

    return {
      'Total Pregnancies': filteredPregnancies.length,
      'Active Pregnancies': active,
      'Due Soon': dueSoon,
      'Success Rate': successRate,
    };
  }

  Map<String, int> get stageDistribution {
    final distribution = <String, int>{};

    for (var pregnancy in filteredPregnancies) {
      final stage = _calculateStage(pregnancy.startDate);
      distribution[stage] = (distribution[stage] ?? 0) + 1;
    }

    return distribution;
  }

  Map<String, int> get statusDistribution {
    final distribution = <String, int>{};

    for (var pregnancy in filteredPregnancies) {
      distribution[pregnancy.status] =
          (distribution[pregnancy.status] ?? 0) + 1;
    }

    return distribution;
  }

  @override
  List<ChartData> get chartData {
    final Map<String, int> statusCounts = {};

    for (var pregnancy in filteredPregnancies) {
      statusCounts[pregnancy.status] =
          (statusCounts[pregnancy.status] ?? 0) + 1;
    }

    return statusCounts.entries.map((entry) {
      Color color;
      switch (entry.key.toLowerCase()) {
        case 'active':
          color = Colors.green;
          break;
        case 'successful':
          color = Colors.blue;
          break;
        case 'failed':
          color = Colors.red;
          break;
        default:
          color = Colors.grey;
      }

      return ChartData(
        label: entry.key,
        value: entry.value.toDouble(),
        color: color,
      );
    }).toList();
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         