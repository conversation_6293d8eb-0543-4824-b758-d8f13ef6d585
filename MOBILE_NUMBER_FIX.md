nter,
                  style: Text<PERSON><PERSON><PERSON>(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
                <PERSON>zed<PERSON><PERSON>(height: ResponsiveTheme.getFormSpacing(context)),
                ElevatedButton.icon(
                  onPressed: () {
                    // TODO: Implement QR code printing functionality
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Printing feature coming soon!'),
                      ),
                    );
                  },
                  icon: const Icon(Icons.print),
                  label: const Text('Print QR Code'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _confirmDelete() async {
    final confirmDelete = await showDialog<bool>(
      context: context,
      builder: (currentContext) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content:
            Text('Are you sure you want to delete ${_currentCattle.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(currentContext).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(currentContext).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmDelete == true) {
      try {
        await DatabaseHelper.instance.deleteCattle(_currentCattle.id);

        // Check if the widget is still mounted before navigating
        if (!mounted) return;

        Navigator.of(context).pop();
      } catch (e) {
        // Check if the widget is still mounted before showing SnackBar
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete cattle: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 7,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: LayoutBuilder(
            builder: (context, constraints) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: Text(
                      _currentCattle.name,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        fontSize: 16,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '(${_currentCattle.tagId})',
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                  ),
                ],
              );
            },
          ),
          actions: [
            Tooltip(
              message: 'Edit Cattle',
              child: IconButton(
                icon: const Icon(Icons.edit),
                onPressed: _showEditDialog,
              ),
            ),
            Tooltip(
              message: 'Delete Cattle',
              child: IconButton(
                icon: const Icon(Icons.delete_outline),
                onPressed: _confirmDelete,
              ),
            ),
            Tooltip(
              message: 'Show QR Code',
              child: IconButton(
                icon: const Icon(Icons.qr_code),
                onPressed: () => _showQRCodeDialog(),
              ),
            ),
            const SizedBox(width: 8),
          ],
          toolbarHeight: 40, // Reduced height
          bottom: TabBar(
            isScrollable: MediaQuery.of(context).size.width < 700,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            indicatorColor: Colors.white,
            labelStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            