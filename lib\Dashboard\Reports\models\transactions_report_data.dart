import 'package:flutter/material.dart';

class BreedCategory {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String animalTypeId;

  BreedCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.createdAt,
    required this.updatedAt,
    required this.animalTypeId,
  });

  factory BreedCategory.fromMap(Map<String, dynamic> map) {
    return BreedCategory(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      icon: IconData(
        map['iconCodePoint'] as int,
        fontFamily: map['iconFontFamily'] as String? ?? 'MaterialIcons',
        fontPackage: map['iconFontPackage'] as String?,
      ),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      animalTypeId: map['animalTypeId'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'iconCodePoint': icon.codePoint,
      'iconFontFamily': icon.fontFamily ?? 'MaterialIcons',
      'iconFontPackage': icon.fontPackage,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'animalTypeId': animalTypeId,
    };
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        