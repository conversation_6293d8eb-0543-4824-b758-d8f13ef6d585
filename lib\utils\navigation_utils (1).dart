import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/cattle_report_data.dart';
import '../models/chart_data.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class CattleSummaryTab extends StatelessWidget {
  final CattleReportData reportData;

  const CattleSummaryTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final summaryData = reportData.summaryData;
    final chartData = reportData.chartData;

    return SingleChildScrollView(
      child: Padding(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCard(summaryData),
            SizedBox(height: ResponsiveSpacing.getLG(context)),
            if (chartData.isNotEmpty) _buildCategoryChart(chartData),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(Map<String, dynamic> summaryData) {
    return ResponsiveCard(
      child: Padding(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Cattle Summary',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            _buildSummaryRow('Total Cattle', summaryData['totalCattle'].toString()),
            _buildSummaryRow('Healthy', summaryData['healthyCount'].toString()),
            _buildSummaryRow('Sick', summaryData['sickCount'].toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisA