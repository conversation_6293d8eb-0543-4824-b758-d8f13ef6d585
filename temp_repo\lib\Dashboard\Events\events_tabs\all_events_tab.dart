import 'package:flutter/material.dart';
import '../utils/responsive_helper.dart';

/// Responsive theme extension that provides screen-size aware styling
class ResponsiveTheme {
  static const Color primaryColor = Color(0xFF2E7D32);
  static const Color secondaryColor = Color(0xFF1B5E20);
  static const Color errorColor = Colors.red;
  static const Color backgroundColor = Colors.white;
  static const Color textColor = Colors.black87;
  static final Color scaffoldBackground = Colors.grey[50]!;

  // Responsive text styles
  static TextStyle getHeadlineStyle(BuildContext context, {
    double baseFontSize = 24.0,
    FontWeight fontWeight = FontWeight.bold,
    Color? color,
  }) {
    return TextStyle(
      fontSize: ResponsiveHelper.getScaledFontSize(context, baseFontSize),
      fontWeight: fontWeight,
      color: color ?? textColor,
    );
  }

  static TextStyle getTitleStyle(BuildContext context, {
    double baseFontSize = 18.0,
    FontWeight fontWeight = FontWeight.w600,
    Color? color,
  }) {
    return TextStyle(
      fontSize: ResponsiveHelper.getScaledFontSize(context, baseFontSize),
      fontWeight: fontWeight,
      color: color ?? textColor,
    );
  }

  static TextStyle getSubtitleStyle(BuildContext context, {
    double baseFontSize = 16.0,
    FontWeight fontWeight = FontWeight.w500,
    Color? color,
  }) {
    return TextStyle(
      fontSize: ResponsiveHelper.getScaledFontSize(context, baseFontSize),
      fontWeight: fontWeight,
      color: color ?? Colors.black54,
    );
  }

  static TextStyle getBodyStyle(BuildContext context, {
    double baseFontSize = 14.0,
    FontWeight fontWeight = FontWeight.normal,
    Color? color,
  }) {
    return TextStyle(
      fontSize: ResponsiveHelper.getScaledFontSize(context, baseFontSize),
      fontWeight: fontWeight,
      color: color ?? textColor,
    );
  }

  static TextStyle getCaptionStyle(BuildContext context, {
    double baseFontSize = 12.0,
    FontWeight fontWeight = FontWeight.normal,
    Color? color,
  }) {
    return TextStyle(
      fontSize: ResponsiveHelper.getScaledFontSize(context, baseFontSize),
      fontWeight: fontWeight,
      color: color ?? Colors.black54,
    );
  }

  // Responsive button styles
  static ButtonStyle getPrimaryButtonStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
      padding: EdgeInsets.symmetric(
        horizontal: ResponsiveHelper.getResponsiveSpacing(context, mobile: 24),
        vertical: ResponsiveHelper.getResponsiveSpacing(context, mobile: 12),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: ResponsiveHelper.getCardBorderRadius(context),
      ),
      minimumSize: Size(
        ResponsiveHelper.getResponsiveValue(context, mobile: 120.0, tablet: 140.0, desktop: 160.0),
        ResponsiveHelper.getFormFieldHeight(context),
      ),
    );
  }

  static ButtonStyle getSecondaryButtonStyle(BuildContext context) {
    return OutlinedButton.styleFrom(
      foregroundColor: primaryColor,
      side: const BorderSide(color: primaryColor),
      padding: EdgeInsets.symmetric(
        horizontal: ResponsiveHelper.getResponsiveSpacing(context, mobile: 24),
        vertical: ResponsiveHelper.getResponsiveSpacing(context, mobile: 12),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: ResponsiveHelper.getCardBorderRadius(context),
      ),
      minimumSize: Size(
        ResponsiveHelper.getResponsiveValue(context, mobile: 120.0, tablet: 140.0, desktop: 160.0),
        ResponsiveHelper.getFormFieldHeight(context),
      ),
    );
  }

  static ButtonStyle getTextButtonStyle(BuildContext context) {
    return TextButton.styleFrom(
      foregroundColor: primaryColor,
      padding: EdgeInsets.symmetric(
        horizontal: ResponsiveHelper.getResponsiveSpacing(context, mobile: 16),
        vertical: ResponsiveHelper.getResponsiveSpacing(context, mobile: 8),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: ResponsiveHelper.getCardBorderRadius(context),
      ),
    );
  }

  // Responsive input decoration
  static InputDecoration getInputDecoration(BuildContext context, {
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    String? errorText,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      errorText: errorText,
      border: OutlineInputBorder(
        borderRadius: ResponsiveHelper.getCardBorderRadius(context),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: ResponsiveHelper.getCardBorderRadius(context),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: ResponsiveHelper.getCardBorderRadius(context),
        borderSide: const BorderSide(color: primaryColor, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: ResponsiveHelper.getCardBorderRadius(context),
        borderSide: const BorderSide(color: errorColor),
      ),
      contentPadding: EdgeInsets.symmetric(
        horizontal: ResponsiveHelper.getResponsiveSpacing(context, mobile: 16),
        vertical: ResponsiveHelper.getResponsiveSpacing(context, mobile: 12),
      ),
      labelStyle: getBodyStyle(context),
      hintStyle: getBodyStyle(context, color: Colors.grey.shade600),
    );
  }

  // Responsive card decoration
  static BoxDecoration getCardDecoration(BuildContext context, {
    Color? color,
    List<BoxShadow>? boxShadow,
  }) {
    return BoxDecoration(
      color: color ?? backgroundColor,
      borderRadius: ResponsiveHelper.getCardBorderRadius(context),
      boxShadow: boxShadow ?? [
        BoxShadow(
          color: Colors.black.withOpacity(0.1),
          blurRadius: ResponsiveHelper.getCardElevation(context),
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  // Responsive app bar theme
  static AppBarTheme getAppBarTheme(BuildContext context) {
    return AppBarTheme(
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
      elevation: ResponsiveHelper.getCardElevation(context),
      toolbarHeight: ResponsiveHelper.getAppBarHeight(context),
      titleTextStyle: getTitleStyle(context, color: Colors.white),
      iconTheme: IconThemeData(
        size: ResponsiveHelper.getIconSize(context),
        color: Colors.white,
      ),
    );
  }

}

// Responsive spacing values
class ResponsiveSpacing {
  static double get xs => 4.0;
  static double get sm => 8.0;
  static double get md => 16.0;
  static double get lg => 24.0;
  static double get xl => 32.0;
  static double get xxl => 48.0;

  static double getXS(BuildContext context) =>
      ResponsiveHelper.getResponsiveSpacing(context, mobile: xs);

  static double getSM(BuildContext context) =>
      ResponsiveHelper.getResponsiveSpacing(context, mobile: sm);

  static double getMD(BuildContext context) =>
      ResponsiveHelper.getResponsiveSpacing(context, mobile: md);

  static double getLG(BuildContext context) =>
      ResponsiveHelper.getResponsiveSpacing(context, mobile: lg);

  static double getXL(BuildContext context) =>
      ResponsiveHelper.getResponsiveSpacing(context, mobile: xl);

  static double getXXL(BuildContext context) =>
      ResponsiveHelper.getResponsiveSpacing(context, mobile: xxl);
}

// Responsive icon sizes
class ResponsiveIconSizes {
  static double getSmall(BuildContext context) =>
      ResponsiveHelper.getIconSize(context, mobile: 16.0);

  static double getMedium(BuildContext context) =>
      ResponsiveHelper.getIconSize(context, mobile: 24.0);

  static double getLarge(BuildContext context) =>
      ResponsiveHelper.getIconSize(context, mobile: 32.0);

  static double getExtraLarge(BuildContext context) =>
      ResponsiveHelper.getIconSize(context, mobile: 48.0);
}

// Responsive border radius
class ResponsiveBorderRadius {
  static BorderRadius getSmall(BuildContext context) =>
      BorderRadius.circular(ResponsiveHelper.isMobile(context) ? 4.0 : 6.0);

  static BorderRadius getMedium(BuildContext context) =>
      ResponsiveHelper.getCardBorderRadius(context);

  static BorderRadius getLarge(BuildContext context) =>
      BorderRadius.circular(ResponsiveHelper.isMobile(context) ? 16.0 : 20.0);
}

  // Responsive shadows
  static List<BoxShadow> getCardShadow(BuildContext context) {
    final elevation = ResponsiveHelper.getCardElevation(context);
    return [
      BoxShadow(
        color: Colors.black.withOpacity(0.1),
        blurRadius: elevation,
        offset: Offset(0, elevation / 2),
      ),
    ];
  }

  // Responsive grid spacing
  static double getGridSpacing(BuildContext context) {
    return ResponsiveHelper.getResponsiveSpacing(context, mobile: 8.0, tablet: 12.0, desktop: 16.0);
  }

  // Responsive list spacing
  static double getListSpacing(BuildContext context) {
    return ResponsiveHelper.getResponsiveSpacing(context, mobile: 4.0, tablet: 6.0, desktop: 8.0);
  }

  // Responsive form spacing
  static double getFormSpacing(BuildContext context) {
    return ResponsiveHelper.getResponsiveSpacing(context, mobile: 16.0, tablet: 20.0, desktop: 24.0);
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        