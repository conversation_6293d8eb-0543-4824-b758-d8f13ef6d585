import 'package:flutter/foundation.dart';
import 'package:logging/logging.dart';

class LoggingService {
  static final LoggingService _instance = LoggingService._internal();
  factory LoggingService() => _instance;
  LoggingService._internal();
  String _getEmojiForLogLevel(Level level) {
    if (level == Level.SEVERE) return '🔴'; // Error
    if (level == Level.WARNING) return '⚠️'; // Warning
    if (level == Level.INFO) return 'ℹ️'; // Info
    if (level == Level.FINE || level == Level.FINER || level == Level.FINEST) return '🔍'; // Debug
    return '📝'; // Default
  }
  void setupLogging() {
    // Configure logging levels based on environment
    Logger.root.level = kDebugMode ? Level.ALL : Level.INFO;

    // Set up a listener to handle log records
    Logger.root.onRecord.listen((record) {
      // Customize log output
      String emoji = _getEmojiForLogLevel(record.level);
      String logMessage = '$emoji ${record.time}: '
          '[${record.loggerName}] '
          '${record.level.name}: '
          '${record.message}';

      // Add stack trace for severe and warning levels
      if (record.level >= Level.WARNING && record.stackTrace != null) {
        logMessage += '\nStack Trace: ${record.stackTrace}';
      }

      // In debug mode, use print. In release mode, you might want to use 
      // a more sophisticated logging mechanism
      if (kDebugMode) {
        print(logMessage);
      } else {
  