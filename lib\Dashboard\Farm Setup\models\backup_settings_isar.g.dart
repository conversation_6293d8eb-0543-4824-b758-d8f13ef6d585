// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'backup_settings_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetBackupSettingsIsarCollection on Isar {
  IsarCollection<BackupSettingsIsar> get backupSettingsIsars =>
      this.collection();
}

const BackupSettingsIsarSchema = CollectionSchema(
  name: r'BackupSettingsIsar',
  id: -8422423144613465867,
  properties: {
    r'autoBackupEnabled': PropertySchema(
      id: 0,
      name: r'autoBackupEnabled',
      type: IsarType.bool,
    ),
    r'autoBackupFrequency': PropertySchema(
      id: 1,
      name: r'autoBackupFrequency',
      type: IsarType.long,
    ),
    r'backupFrequencyDays': PropertySchema(
      id: 2,
      name: r'backupFrequencyDays',
      type: IsarType.long,
    ),
    r'backupLocation': PropertySchema(
      id: 3,
      name: r'backupLocation',
      type: IsarType.string,
    ),
    r'businessId': PropertySchema(
      id: 4,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'cloudAccountEmail': PropertySchema(
      id: 5,
      name: r'cloudAccountEmail',
      type: IsarType.string,
    ),
    r'cloudBackupEnabled': PropertySchema(
      id: 6,
      name: r'cloudBackupEnabled',
      type: IsarType.bool,
    ),
    r'cloudProvider': PropertySchema(
      id: 7,
      name: r'cloudProvider',
      type: IsarType.string,
    ),
    r'createdAt': PropertySchema(
      id: 8,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'farmBusinessId': PropertySchema(
      id: 9,
      name: r'farmBusinessId',
      type: IsarType.string,
    ),
    r'lastBackupDate': PropertySchema(
      id: 10,
      name: r'lastBackupDate',
      type: IsarType.dateTime,
    ),
    r'lastCloudBackupDate': PropertySchema(
      id: 11,
      name: r'lastCloudBackupDate',
      type: IsarType.dateTime,
    ),
    r'maxCloudBackups': PropertySchema(
      id: 12,
      name: r'maxCloudBackups',
      type: IsarType.long,
    ),
    r'syncToCloudEnabled': PropertySchema(
      id: 13,
      name: r'syncToCloudEnabled',
      type: IsarType.bool,
    ),
    r'updatedAt': PropertySchema(
      id: 14,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _backupSettingsIsarEstimateSize,
  serialize: _backupSettingsIsarSerialize,
  deserialize: _backupSettingsIsarDeserialize,
  deserializeProp: _backupSettingsIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'farmBusinessId': IndexSchema(
      id: -7111559275757090744,
      name: r'farmBusinessId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'farmBusinessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _backupSettingsIsarGetId,
  getLinks: _backupSettingsIsarGetLinks,
  attach: _backupSettingsIsarAttach,
  version: '3.1.0+1',
);

int _backupSettingsIsarEstimateSize(
  BackupSettingsIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.backupLocation.length * 3;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.cloudAccountEmail;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.cloudProvider.length * 3;
  {
    final value = object.farmBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _backupSettingsIsarSerialize(
  BackupSettingsIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeBool(offsets[0], object.autoBackupEnabled);
  writer.writeLong(offsets[1], object.autoBackupFrequency);
  writer.writeLong(offsets[2], object.backupFrequencyDays);
  writer.writeString(offsets[3], object.backupLocation);
  writer.writeString(offsets[4], object.businessId);
  writer.writeString(offsets[5], object.cloudAccountEmail);
  writer.writeBool(offsets[6], object.cloudBackupEnabled);
  writer.writeString(offsets[7], object.cloudProvider);
  writer.writeDateTime(offsets[8], object.createdAt);
  writer.writeString(offsets[9], object.farmBusinessId);
  writer.writeDateTime(offsets[10], object.lastBackupDate);
  writer.writeDateTime(offsets[11], object.lastCloudBackupDate);
  writer.writeLong(offsets[12], object.maxCloudBackups);
  writer.writeBool(offsets[13], object.syncToCloudEnabled);
  writer.writeDateTime(offsets[14], object.updatedAt);
}

BackupSettingsIsar _backupSettingsIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = BackupSettingsIsar();
  object.autoBackupEnabled = reader.readBool(offsets[0]);
  object.autoBackupFrequency = reader.readLong(offsets[1]);
  object.backupFrequencyDays = reader.readLongOrNull(offsets[2]);
  object.backupLocation = reader.readString(offsets[3]);
  object.businessId = reader.readStringOrNull(offsets[4]);
  object.cloudAccountEmail = reader.readStringOrNull(offsets[5]);
  object.cloudBackupEnabled = reader.readBool(offsets[6]);
  object.cloudProvider = reader.readString(offsets[7]);
  object.createdAt = reader.readDateTimeOrNull(offsets[8]);
  object.farmBusinessId = reader.readStringOrNull(offsets[9]);
  object.id = id;
  object.lastBackupDate = reader.readDateTimeOrNull(offsets[10]);
  object.lastCloudBackupDate = reader.readDateTimeOrNull(offsets[11]);
  object.maxCloudBackups = reader.readLong(offsets[12]);
  object.syncToCloudEnabled = reader.readBool(offsets[13]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[14]);
  return object;
}

P _backupSettingsIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readBool(offset)) as P;
    case 1:
      return (reader.readLong(offset)) as P;
    case 2:
      return (reader.readLongOrNull(offset)) as P;
    case 3:
      return (reader.readString(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readBool(offset)) as P;
    case 7:
      return (reader.readString(offset)) as P;
    case 8:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 9:
      return (reader.readStringOrNull(offset)) as P;
    case 10:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 11:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 12:
      return (reader.readLong(offset)) as P;
    case 13:
      return (reader.readBool(offset)) as P;
    case 14:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _backupSettingsIsarGetId(BackupSettingsIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _backupSettingsIsarGetLinks(
    BackupSettingsIsar object) {
  return [];
}

void _backupSettingsIsarAttach(
    IsarCollection<dynamic> col, Id id, BackupSettingsIsar object) {
  object.id = id;
}

extension BackupSettingsIsarByIndex on IsarCollection<BackupSettingsIsar> {
  Future<BackupSettingsIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  BackupSettingsIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<BackupSettingsIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<BackupSettingsIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(BackupSettingsIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(BackupSettingsIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<BackupSettingsIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<BackupSettingsIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension BackupSettingsIsarQueryWhereSort
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QWhere> {
  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension BackupSettingsIsarQueryWhere
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QWhereClause> {
  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'farmBusinessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      farmBusinessIdEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [farmBusinessId],
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterWhereClause>
      farmBusinessIdNotEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension BackupSettingsIsarQueryFilter
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QFilterCondition> {
  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      autoBackupEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'autoBackupEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      autoBackupFrequencyEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'autoBackupFrequency',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      autoBackupFrequencyGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'autoBackupFrequency',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      autoBackupFrequencyLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'autoBackupFrequency',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      autoBackupFrequencyBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'autoBackupFrequency',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupFrequencyDaysIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'backupFrequencyDays',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupFrequencyDaysIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'backupFrequencyDays',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupFrequencyDaysEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'backupFrequencyDays',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupFrequencyDaysGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'backupFrequencyDays',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupFrequencyDaysLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'backupFrequencyDays',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupFrequencyDaysBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'backupFrequencyDays',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'backupLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'backupLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'backupLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'backupLocation',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'backupLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'backupLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'backupLocation',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'backupLocation',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'backupLocation',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      backupLocationIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'backupLocation',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudAccountEmailIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cloudAccountEmail',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudAccountEmailIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cloudAccountEmail',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudAccountEmailEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cloudAccountEmail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudAccountEmailGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cloudAccountEmail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudAccountEmailLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cloudAccountEmail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudAccountEmailBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cloudAccountEmail',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudAccountEmailStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cloudAccountEmail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudAccountEmailEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cloudAccountEmail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudAccountEmailContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cloudAccountEmail',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudAccountEmailMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cloudAccountEmail',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudAccountEmailIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cloudAccountEmail',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudAccountEmailIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cloudAccountEmail',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudBackupEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cloudBackupEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudProviderEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cloudProvider',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudProviderGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cloudProvider',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudProviderLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cloudProvider',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudProviderBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cloudProvider',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudProviderStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cloudProvider',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudProviderEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cloudProvider',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudProviderContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cloudProvider',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudProviderMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cloudProvider',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudProviderIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cloudProvider',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      cloudProviderIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cloudProvider',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'farmBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'farmBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      farmBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastBackupDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'lastBackupDate',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastBackupDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'lastBackupDate',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastBackupDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastBackupDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastBackupDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lastBackupDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastBackupDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lastBackupDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastBackupDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lastBackupDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastCloudBackupDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'lastCloudBackupDate',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastCloudBackupDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'lastCloudBackupDate',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastCloudBackupDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastCloudBackupDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastCloudBackupDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lastCloudBackupDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastCloudBackupDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lastCloudBackupDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      lastCloudBackupDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lastCloudBackupDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      maxCloudBackupsEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'maxCloudBackups',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      maxCloudBackupsGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'maxCloudBackups',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      maxCloudBackupsLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'maxCloudBackups',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      maxCloudBackupsBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'maxCloudBackups',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      syncToCloudEnabledEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'syncToCloudEnabled',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension BackupSettingsIsarQueryObject
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QFilterCondition> {}

extension BackupSettingsIsarQueryLinks
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QFilterCondition> {}

extension BackupSettingsIsarQuerySortBy
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QSortBy> {
  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByAutoBackupEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupEnabled', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByAutoBackupEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupEnabled', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByAutoBackupFrequency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupFrequency', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByAutoBackupFrequencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupFrequency', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByBackupFrequencyDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupFrequencyDays', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByBackupFrequencyDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupFrequencyDays', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByBackupLocation() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupLocation', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByBackupLocationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupLocation', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByCloudAccountEmail() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudAccountEmail', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByCloudAccountEmailDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudAccountEmail', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByCloudBackupEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudBackupEnabled', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByCloudBackupEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudBackupEnabled', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByCloudProvider() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudProvider', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByCloudProviderDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudProvider', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByLastBackupDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastBackupDate', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByLastBackupDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastBackupDate', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByLastCloudBackupDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastCloudBackupDate', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByLastCloudBackupDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastCloudBackupDate', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByMaxCloudBackups() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxCloudBackups', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByMaxCloudBackupsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxCloudBackups', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortBySyncToCloudEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'syncToCloudEnabled', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortBySyncToCloudEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'syncToCloudEnabled', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension BackupSettingsIsarQuerySortThenBy
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QSortThenBy> {
  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByAutoBackupEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupEnabled', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByAutoBackupEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupEnabled', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByAutoBackupFrequency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupFrequency', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByAutoBackupFrequencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'autoBackupFrequency', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByBackupFrequencyDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupFrequencyDays', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByBackupFrequencyDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupFrequencyDays', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByBackupLocation() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupLocation', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByBackupLocationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'backupLocation', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByCloudAccountEmail() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudAccountEmail', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByCloudAccountEmailDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudAccountEmail', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByCloudBackupEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudBackupEnabled', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByCloudBackupEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudBackupEnabled', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByCloudProvider() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudProvider', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByCloudProviderDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudProvider', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByLastBackupDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastBackupDate', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByLastBackupDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastBackupDate', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByLastCloudBackupDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastCloudBackupDate', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByLastCloudBackupDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastCloudBackupDate', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByMaxCloudBackups() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxCloudBackups', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByMaxCloudBackupsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxCloudBackups', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenBySyncToCloudEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'syncToCloudEnabled', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenBySyncToCloudEnabledDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'syncToCloudEnabled', Sort.desc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension BackupSettingsIsarQueryWhereDistinct
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct> {
  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByAutoBackupEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'autoBackupEnabled');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByAutoBackupFrequency() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'autoBackupFrequency');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByBackupFrequencyDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'backupFrequencyDays');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByBackupLocation({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'backupLocation',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByCloudAccountEmail({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cloudAccountEmail',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByCloudBackupEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cloudBackupEnabled');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByCloudProvider({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cloudProvider',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByFarmBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'farmBusinessId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByLastBackupDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lastBackupDate');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByLastCloudBackupDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lastCloudBackupDate');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByMaxCloudBackups() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'maxCloudBackups');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctBySyncToCloudEnabled() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'syncToCloudEnabled');
    });
  }

  QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension BackupSettingsIsarQueryProperty
    on QueryBuilder<BackupSettingsIsar, BackupSettingsIsar, QQueryProperty> {
  QueryBuilder<BackupSettingsIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<BackupSettingsIsar, bool, QQueryOperations>
      autoBackupEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'autoBackupEnabled');
    });
  }

  QueryBuilder<BackupSettingsIsar, int, QQueryOperations>
      autoBackupFrequencyProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'autoBackupFrequency');
    });
  }

  QueryBuilder<BackupSettingsIsar, int?, QQueryOperations>
      backupFrequencyDaysProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'backupFrequencyDays');
    });
  }

  QueryBuilder<BackupSettingsIsar, String, QQueryOperations>
      backupLocationProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'backupLocation');
    });
  }

  QueryBuilder<BackupSettingsIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<BackupSettingsIsar, String?, QQueryOperations>
      cloudAccountEmailProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cloudAccountEmail');
    });
  }

  QueryBuilder<BackupSettingsIsar, bool, QQueryOperations>
      cloudBackupEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cloudBackupEnabled');
    });
  }

  QueryBuilder<BackupSettingsIsar, String, QQueryOperations>
      cloudProviderProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cloudProvider');
    });
  }

  QueryBuilder<BackupSettingsIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<BackupSettingsIsar, String?, QQueryOperations>
      farmBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'farmBusinessId');
    });
  }

  QueryBuilder<BackupSettingsIsar, DateTime?, QQueryOperations>
      lastBackupDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lastBackupDate');
    });
  }

  QueryBuilder<BackupSettingsIsar, DateTime?, QQueryOperations>
      lastCloudBackupDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lastCloudBackupDate');
    });
  }

  QueryBuilder<BackupSettingsIsar, int, QQueryOperations>
      maxCloudBackupsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'maxCloudBackups');
    });
  }

  QueryBuilder<BackupSettingsIsar, bool, QQueryOperations>
      syncToCloudEnabledProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'syncToCloudEnabled');
    });
  }

  QueryBuilder<BackupSettingsIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}
