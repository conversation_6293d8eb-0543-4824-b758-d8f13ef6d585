class CattleMilkRecord {
  final String id;
  final String cattleId;
  final DateTime date;
  final double morningYield;
  final double eveningYield;
  final String? notes;
  final DateTime recordedAt;

  CattleMilkRecord({
    required this.id,
    required this.cattleId,
    required this.date,
    required this.morningYield,
    required this.eveningYield,
    this.notes,
    DateTime? recordedAt,
  }) : recordedAt = recordedAt ?? DateTime.now();

  double get totalYield => morningYield + eveningYield;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'cattleId': cattleId,
      'date': date.toIso8601String(),
      'morningYield': morningYield,
      'eveningYield': eveningYield,
      'notes': notes,
      'recordedAt': recordedAt.toIso8601String(),
    };
  }

  factory CattleMilkRecord.fromMap(Map<String, dynamic> map) {
    return CattleMilkRecord(
      id: map['id'],
      cattleId: map['cattleId'],
      date: DateTime.parse(map['date']),
      morningYield: map['morningYield'].toDouble(),
      eveningYield: map['eveningYield'].toDouble(),
      notes: map['notes'],
      recordedAt: DateTime.parse(map['recordedAt']),
    );
  }
}
                                                                                                                                                                                                                                                                                                                                                                                           