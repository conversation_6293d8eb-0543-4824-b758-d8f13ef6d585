// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'veterinarian_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetVeterinarianIsarCollection on Isar {
  IsarCollection<VeterinarianIsar> get veterinarianIsars => this.collection();
}

const VeterinarianIsarSchema = CollectionSchema(
  name: r'VeterinarianIsar',
  id: 5656874922053866306,
  properties: {
    r'address': PropertySchema(
      id: 0,
      name: r'address',
      type: IsarType.string,
    ),
    r'associations': PropertySchema(
      id: 1,
      name: r'associations',
      type: IsarType.stringList,
    ),
    r'businessId': PropertySchema(
      id: 2,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'city': PropertySchema(
      id: 3,
      name: r'city',
      type: IsarType.string,
    ),
    r'clinicName': PropertySchema(
      id: 4,
      name: r'clinicName',
      type: IsarType.string,
    ),
    r'consultationFee': PropertySchema(
      id: 5,
      name: r'consultationFee',
      type: IsarType.double,
    ),
    r'country': PropertySchema(
      id: 6,
      name: r'country',
      type: IsarType.string,
    ),
    r'createdAt': PropertySchema(
      id: 7,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'createdBy': PropertySchema(
      id: 8,
      name: r'createdBy',
      type: IsarType.string,
    ),
    r'education': PropertySchema(
      id: 9,
      name: r'education',
      type: IsarType.string,
    ),
    r'email': PropertySchema(
      id: 10,
      name: r'email',
      type: IsarType.string,
    ),
    r'emergencyAvailable': PropertySchema(
      id: 11,
      name: r'emergencyAvailable',
      type: IsarType.bool,
    ),
    r'emergencyFee': PropertySchema(
      id: 12,
      name: r'emergencyFee',
      type: IsarType.double,
    ),
    r'emergencyHours': PropertySchema(
      id: 13,
      name: r'emergencyHours',
      type: IsarType.string,
    ),
    r'emergencyPhone': PropertySchema(
      id: 14,
      name: r'emergencyPhone',
      type: IsarType.string,
    ),
    r'farmBusinessId': PropertySchema(
      id: 15,
      name: r'farmBusinessId',
      type: IsarType.string,
    ),
    r'fullAddress': PropertySchema(
      id: 16,
      name: r'fullAddress',
      type: IsarType.string,
    ),
    r'fullContactInfo': PropertySchema(
      id: 17,
      name: r'fullContactInfo',
      type: IsarType.string,
    ),
    r'isActive': PropertySchema(
      id: 18,
      name: r'isActive',
      type: IsarType.bool,
    ),
    r'isPrimary': PropertySchema(
      id: 19,
      name: r'isPrimary',
      type: IsarType.bool,
    ),
    r'languages': PropertySchema(
      id: 20,
      name: r'languages',
      type: IsarType.stringList,
    ),
    r'lastContactDate': PropertySchema(
      id: 21,
      name: r'lastContactDate',
      type: IsarType.dateTime,
    ),
    r'licenseNumber': PropertySchema(
      id: 22,
      name: r'licenseNumber',
      type: IsarType.string,
    ),
    r'maxTravelDistance': PropertySchema(
      id: 23,
      name: r'maxTravelDistance',
      type: IsarType.double,
    ),
    r'minimumTravelFee': PropertySchema(
      id: 24,
      name: r'minimumTravelFee',
      type: IsarType.double,
    ),
    r'name': PropertySchema(
      id: 25,
      name: r'name',
      type: IsarType.string,
    ),
    r'notes': PropertySchema(
      id: 26,
      name: r'notes',
      type: IsarType.string,
    ),
    r'operatingHours': PropertySchema(
      id: 27,
      name: r'operatingHours',
      type: IsarType.string,
    ),
    r'paymentMethods': PropertySchema(
      id: 28,
      name: r'paymentMethods',
      type: IsarType.stringList,
    ),
    r'phoneNumber': PropertySchema(
      id: 29,
      name: r'phoneNumber',
      type: IsarType.string,
    ),
    r'postalCode': PropertySchema(
      id: 30,
      name: r'postalCode',
      type: IsarType.string,
    ),
    r'rating': PropertySchema(
      id: 31,
      name: r'rating',
      type: IsarType.double,
    ),
    r'reviewCount': PropertySchema(
      id: 32,
      name: r'reviewCount',
      type: IsarType.long,
    ),
    r'secondaryPhone': PropertySchema(
      id: 33,
      name: r'secondaryPhone',
      type: IsarType.string,
    ),
    r'services': PropertySchema(
      id: 34,
      name: r'services',
      type: IsarType.stringList,
    ),
    r'specialization': PropertySchema(
      id: 35,
      name: r'specialization',
      type: IsarType.string,
    ),
    r'state': PropertySchema(
      id: 36,
      name: r'state',
      type: IsarType.string,
    ),
    r'title': PropertySchema(
      id: 37,
      name: r'title',
      type: IsarType.string,
    ),
    r'travelFeePerKm': PropertySchema(
      id: 38,
      name: r'travelFeePerKm',
      type: IsarType.double,
    ),
    r'updatedAt': PropertySchema(
      id: 39,
      name: r'updatedAt',
      type: IsarType.dateTime,
    ),
    r'updatedBy': PropertySchema(
      id: 40,
      name: r'updatedBy',
      type: IsarType.string,
    ),
    r'website': PropertySchema(
      id: 41,
      name: r'website',
      type: IsarType.string,
    ),
    r'yearsOfExperience': PropertySchema(
      id: 42,
      name: r'yearsOfExperience',
      type: IsarType.long,
    )
  },
  estimateSize: _veterinarianIsarEstimateSize,
  serialize: _veterinarianIsarSerialize,
  deserialize: _veterinarianIsarDeserialize,
  deserializeProp: _veterinarianIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'farmBusinessId': IndexSchema(
      id: -7111559275757090744,
      name: r'farmBusinessId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'farmBusinessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _veterinarianIsarGetId,
  getLinks: _veterinarianIsarGetLinks,
  attach: _veterinarianIsarAttach,
  version: '3.1.0+1',
);

int _veterinarianIsarEstimateSize(
  VeterinarianIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.address;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final list = object.associations;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.city;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.clinicName;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.country;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.createdBy;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.education;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.email;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.emergencyHours;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.emergencyPhone;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.farmBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.fullAddress.length * 3;
  bytesCount += 3 + object.fullContactInfo.length * 3;
  {
    final list = object.languages;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.licenseNumber;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.name;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.notes;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.operatingHours;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final list = object.paymentMethods;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.phoneNumber;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.postalCode;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.secondaryPhone;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final list = object.services;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.specialization;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.state;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.title;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.updatedBy;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.website;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _veterinarianIsarSerialize(
  VeterinarianIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.address);
  writer.writeStringList(offsets[1], object.associations);
  writer.writeString(offsets[2], object.businessId);
  writer.writeString(offsets[3], object.city);
  writer.writeString(offsets[4], object.clinicName);
  writer.writeDouble(offsets[5], object.consultationFee);
  writer.writeString(offsets[6], object.country);
  writer.writeDateTime(offsets[7], object.createdAt);
  writer.writeString(offsets[8], object.createdBy);
  writer.writeString(offsets[9], object.education);
  writer.writeString(offsets[10], object.email);
  writer.writeBool(offsets[11], object.emergencyAvailable);
  writer.writeDouble(offsets[12], object.emergencyFee);
  writer.writeString(offsets[13], object.emergencyHours);
  writer.writeString(offsets[14], object.emergencyPhone);
  writer.writeString(offsets[15], object.farmBusinessId);
  writer.writeString(offsets[16], object.fullAddress);
  writer.writeString(offsets[17], object.fullContactInfo);
  writer.writeBool(offsets[18], object.isActive);
  writer.writeBool(offsets[19], object.isPrimary);
  writer.writeStringList(offsets[20], object.languages);
  writer.writeDateTime(offsets[21], object.lastContactDate);
  writer.writeString(offsets[22], object.licenseNumber);
  writer.writeDouble(offsets[23], object.maxTravelDistance);
  writer.writeDouble(offsets[24], object.minimumTravelFee);
  writer.writeString(offsets[25], object.name);
  writer.writeString(offsets[26], object.notes);
  writer.writeString(offsets[27], object.operatingHours);
  writer.writeStringList(offsets[28], object.paymentMethods);
  writer.writeString(offsets[29], object.phoneNumber);
  writer.writeString(offsets[30], object.postalCode);
  writer.writeDouble(offsets[31], object.rating);
  writer.writeLong(offsets[32], object.reviewCount);
  writer.writeString(offsets[33], object.secondaryPhone);
  writer.writeStringList(offsets[34], object.services);
  writer.writeString(offsets[35], object.specialization);
  writer.writeString(offsets[36], object.state);
  writer.writeString(offsets[37], object.title);
  writer.writeDouble(offsets[38], object.travelFeePerKm);
  writer.writeDateTime(offsets[39], object.updatedAt);
  writer.writeString(offsets[40], object.updatedBy);
  writer.writeString(offsets[41], object.website);
  writer.writeLong(offsets[42], object.yearsOfExperience);
}

VeterinarianIsar _veterinarianIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = VeterinarianIsar();
  object.address = reader.readStringOrNull(offsets[0]);
  object.associations = reader.readStringList(offsets[1]);
  object.businessId = reader.readStringOrNull(offsets[2]);
  object.city = reader.readStringOrNull(offsets[3]);
  object.clinicName = reader.readStringOrNull(offsets[4]);
  object.consultationFee = reader.readDoubleOrNull(offsets[5]);
  object.country = reader.readStringOrNull(offsets[6]);
  object.createdAt = reader.readDateTimeOrNull(offsets[7]);
  object.createdBy = reader.readStringOrNull(offsets[8]);
  object.education = reader.readStringOrNull(offsets[9]);
  object.email = reader.readStringOrNull(offsets[10]);
  object.emergencyAvailable = reader.readBool(offsets[11]);
  object.emergencyFee = reader.readDoubleOrNull(offsets[12]);
  object.emergencyHours = reader.readStringOrNull(offsets[13]);
  object.emergencyPhone = reader.readStringOrNull(offsets[14]);
  object.farmBusinessId = reader.readStringOrNull(offsets[15]);
  object.id = id;
  object.isActive = reader.readBool(offsets[18]);
  object.isPrimary = reader.readBool(offsets[19]);
  object.languages = reader.readStringList(offsets[20]);
  object.lastContactDate = reader.readDateTimeOrNull(offsets[21]);
  object.licenseNumber = reader.readStringOrNull(offsets[22]);
  object.maxTravelDistance = reader.readDoubleOrNull(offsets[23]);
  object.minimumTravelFee = reader.readDoubleOrNull(offsets[24]);
  object.name = reader.readStringOrNull(offsets[25]);
  object.notes = reader.readStringOrNull(offsets[26]);
  object.operatingHours = reader.readStringOrNull(offsets[27]);
  object.paymentMethods = reader.readStringList(offsets[28]);
  object.phoneNumber = reader.readStringOrNull(offsets[29]);
  object.postalCode = reader.readStringOrNull(offsets[30]);
  object.rating = reader.readDoubleOrNull(offsets[31]);
  object.reviewCount = reader.readLongOrNull(offsets[32]);
  object.secondaryPhone = reader.readStringOrNull(offsets[33]);
  object.services = reader.readStringList(offsets[34]);
  object.specialization = reader.readStringOrNull(offsets[35]);
  object.state = reader.readStringOrNull(offsets[36]);
  object.title = reader.readStringOrNull(offsets[37]);
  object.travelFeePerKm = reader.readDoubleOrNull(offsets[38]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[39]);
  object.updatedBy = reader.readStringOrNull(offsets[40]);
  object.website = reader.readStringOrNull(offsets[41]);
  object.yearsOfExperience = reader.readLongOrNull(offsets[42]);
  return object;
}

P _veterinarianIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringList(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readDoubleOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 8:
      return (reader.readStringOrNull(offset)) as P;
    case 9:
      return (reader.readStringOrNull(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readBool(offset)) as P;
    case 12:
      return (reader.readDoubleOrNull(offset)) as P;
    case 13:
      return (reader.readStringOrNull(offset)) as P;
    case 14:
      return (reader.readStringOrNull(offset)) as P;
    case 15:
      return (reader.readStringOrNull(offset)) as P;
    case 16:
      return (reader.readString(offset)) as P;
    case 17:
      return (reader.readString(offset)) as P;
    case 18:
      return (reader.readBool(offset)) as P;
    case 19:
      return (reader.readBool(offset)) as P;
    case 20:
      return (reader.readStringList(offset)) as P;
    case 21:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 22:
      return (reader.readStringOrNull(offset)) as P;
    case 23:
      return (reader.readDoubleOrNull(offset)) as P;
    case 24:
      return (reader.readDoubleOrNull(offset)) as P;
    case 25:
      return (reader.readStringOrNull(offset)) as P;
    case 26:
      return (reader.readStringOrNull(offset)) as P;
    case 27:
      return (reader.readStringOrNull(offset)) as P;
    case 28:
      return (reader.readStringList(offset)) as P;
    case 29:
      return (reader.readStringOrNull(offset)) as P;
    case 30:
      return (reader.readStringOrNull(offset)) as P;
    case 31:
      return (reader.readDoubleOrNull(offset)) as P;
    case 32:
      return (reader.readLongOrNull(offset)) as P;
    case 33:
      return (reader.readStringOrNull(offset)) as P;
    case 34:
      return (reader.readStringList(offset)) as P;
    case 35:
      return (reader.readStringOrNull(offset)) as P;
    case 36:
      return (reader.readStringOrNull(offset)) as P;
    case 37:
      return (reader.readStringOrNull(offset)) as P;
    case 38:
      return (reader.readDoubleOrNull(offset)) as P;
    case 39:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 40:
      return (reader.readStringOrNull(offset)) as P;
    case 41:
      return (reader.readStringOrNull(offset)) as P;
    case 42:
      return (reader.readLongOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _veterinarianIsarGetId(VeterinarianIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _veterinarianIsarGetLinks(VeterinarianIsar object) {
  return [];
}

void _veterinarianIsarAttach(
    IsarCollection<dynamic> col, Id id, VeterinarianIsar object) {
  object.id = id;
}

extension VeterinarianIsarByIndex on IsarCollection<VeterinarianIsar> {
  Future<VeterinarianIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  VeterinarianIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<VeterinarianIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<VeterinarianIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(VeterinarianIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(VeterinarianIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<VeterinarianIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<VeterinarianIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension VeterinarianIsarQueryWhereSort
    on QueryBuilder<VeterinarianIsar, VeterinarianIsar, QWhere> {
  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension VeterinarianIsarQueryWhere
    on QueryBuilder<VeterinarianIsar, VeterinarianIsar, QWhereClause> {
  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterWhereClause>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterWhereClause>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'farmBusinessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterWhereClause>
      farmBusinessIdEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [farmBusinessId],
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterWhereClause>
      farmBusinessIdNotEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension VeterinarianIsarQueryFilter
    on QueryBuilder<VeterinarianIsar, VeterinarianIsar, QFilterCondition> {
  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      addressIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'address',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      addressIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'address',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      addressEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'address',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      addressGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'address',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      addressLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'address',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      addressBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'address',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      addressStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'address',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      addressEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'address',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      addressContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'address',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      addressMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'address',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      addressIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'address',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      addressIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'address',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'associations',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'associations',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'associations',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'associations',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'associations',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'associations',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'associations',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'associations',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'associations',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsElementMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'associations',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'associations',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'associations',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'associations',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'associations',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'associations',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'associations',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'associations',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      associationsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'associations',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      cityIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'city',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      cityIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'city',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      cityEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'city',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      cityGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'city',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      cityLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'city',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      cityBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'city',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      cityStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'city',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      cityEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'city',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      cityContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'city',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      cityMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'city',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      cityIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'city',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      cityIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'city',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      clinicNameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'clinicName',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      clinicNameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'clinicName',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      clinicNameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'clinicName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      clinicNameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'clinicName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      clinicNameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'clinicName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      clinicNameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'clinicName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      clinicNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'clinicName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      clinicNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'clinicName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      clinicNameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'clinicName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      clinicNameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'clinicName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      clinicNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'clinicName',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      clinicNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'clinicName',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      consultationFeeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'consultationFee',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      consultationFeeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'consultationFee',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      consultationFeeEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'consultationFee',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      consultationFeeGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'consultationFee',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      consultationFeeLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'consultationFee',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      consultationFeeBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'consultationFee',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      countryIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'country',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      countryIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'country',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      countryEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'country',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      countryGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'country',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      countryLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'country',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      countryBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'country',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      countryStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'country',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      countryEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'country',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      countryContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'country',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      countryMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'country',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      countryIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'country',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      countryIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'country',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdByIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdBy',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdByIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdBy',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdByEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdByGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdByLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdByBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdBy',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdByStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdByEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdByContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdByMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'createdBy',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdByIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdBy',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      createdByIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'createdBy',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      educationIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'education',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      educationIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'education',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      educationEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'education',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      educationGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'education',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      educationLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'education',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      educationBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'education',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      educationStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'education',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      educationEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'education',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      educationContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'education',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      educationMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'education',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      educationIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'education',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      educationIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'education',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emailIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'email',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emailIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'email',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emailEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'email',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emailGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'email',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emailLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'email',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emailBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'email',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emailStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'email',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emailEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'email',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emailContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'email',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emailMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'email',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emailIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'email',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emailIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'email',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyAvailableEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'emergencyAvailable',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyFeeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'emergencyFee',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyFeeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'emergencyFee',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyFeeEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'emergencyFee',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyFeeGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'emergencyFee',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyFeeLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'emergencyFee',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyFeeBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'emergencyFee',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyHoursIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'emergencyHours',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyHoursIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'emergencyHours',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyHoursEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'emergencyHours',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyHoursGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'emergencyHours',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyHoursLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'emergencyHours',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyHoursBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'emergencyHours',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyHoursStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'emergencyHours',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyHoursEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'emergencyHours',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyHoursContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'emergencyHours',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyHoursMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'emergencyHours',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyHoursIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'emergencyHours',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyHoursIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'emergencyHours',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyPhoneIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'emergencyPhone',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyPhoneIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'emergencyPhone',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyPhoneEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'emergencyPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyPhoneGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'emergencyPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyPhoneLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'emergencyPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyPhoneBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'emergencyPhone',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyPhoneStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'emergencyPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyPhoneEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'emergencyPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyPhoneContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'emergencyPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyPhoneMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'emergencyPhone',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyPhoneIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'emergencyPhone',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      emergencyPhoneIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'emergencyPhone',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      farmBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      farmBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      farmBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      farmBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'farmBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      farmBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      farmBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      farmBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      farmBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'farmBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      farmBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      farmBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullAddressEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'fullAddress',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullAddressGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'fullAddress',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullAddressLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'fullAddress',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullAddressBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'fullAddress',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullAddressStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'fullAddress',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullAddressEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'fullAddress',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullAddressContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'fullAddress',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullAddressMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'fullAddress',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullAddressIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'fullAddress',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullAddressIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'fullAddress',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullContactInfoEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'fullContactInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullContactInfoGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'fullContactInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullContactInfoLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'fullContactInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullContactInfoBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'fullContactInfo',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullContactInfoStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'fullContactInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullContactInfoEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'fullContactInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullContactInfoContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'fullContactInfo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullContactInfoMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'fullContactInfo',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullContactInfoIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'fullContactInfo',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      fullContactInfoIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'fullContactInfo',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      isActiveEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isActive',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      isPrimaryEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isPrimary',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'languages',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'languages',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'languages',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'languages',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'languages',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'languages',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'languages',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'languages',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'languages',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesElementMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'languages',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'languages',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'languages',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'languages',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'languages',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'languages',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'languages',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'languages',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      languagesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'languages',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      lastContactDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'lastContactDate',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      lastContactDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'lastContactDate',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      lastContactDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastContactDate',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      lastContactDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lastContactDate',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      lastContactDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lastContactDate',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      lastContactDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lastContactDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      licenseNumberIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'licenseNumber',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      licenseNumberIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'licenseNumber',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      licenseNumberEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'licenseNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      licenseNumberGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'licenseNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      licenseNumberLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'licenseNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      licenseNumberBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'licenseNumber',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      licenseNumberStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'licenseNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      licenseNumberEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'licenseNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      licenseNumberContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'licenseNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      licenseNumberMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'licenseNumber',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      licenseNumberIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'licenseNumber',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      licenseNumberIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'licenseNumber',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      maxTravelDistanceIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'maxTravelDistance',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      maxTravelDistanceIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'maxTravelDistance',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      maxTravelDistanceEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'maxTravelDistance',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      maxTravelDistanceGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'maxTravelDistance',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      maxTravelDistanceLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'maxTravelDistance',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      maxTravelDistanceBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'maxTravelDistance',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      minimumTravelFeeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'minimumTravelFee',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      minimumTravelFeeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'minimumTravelFee',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      minimumTravelFeeEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'minimumTravelFee',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      minimumTravelFeeGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'minimumTravelFee',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      minimumTravelFeeLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'minimumTravelFee',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      minimumTravelFeeBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'minimumTravelFee',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      nameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      nameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      nameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      nameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'name',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      nameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      nameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      nameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      nameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'name',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      nameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      nameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      notesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      notesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      notesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      notesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      notesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      notesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'notes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      notesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      notesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      notesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      notesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'notes',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      notesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      notesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      operatingHoursIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'operatingHours',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      operatingHoursIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'operatingHours',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      operatingHoursEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'operatingHours',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      operatingHoursGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'operatingHours',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      operatingHoursLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'operatingHours',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      operatingHoursBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'operatingHours',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      operatingHoursStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'operatingHours',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      operatingHoursEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'operatingHours',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      operatingHoursContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'operatingHours',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      operatingHoursMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'operatingHours',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      operatingHoursIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'operatingHours',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      operatingHoursIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'operatingHours',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'paymentMethods',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'paymentMethods',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'paymentMethods',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'paymentMethods',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'paymentMethods',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'paymentMethods',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'paymentMethods',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'paymentMethods',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'paymentMethods',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsElementMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'paymentMethods',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'paymentMethods',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'paymentMethods',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'paymentMethods',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'paymentMethods',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'paymentMethods',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'paymentMethods',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'paymentMethods',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      paymentMethodsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'paymentMethods',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      phoneNumberIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'phoneNumber',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      phoneNumberIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'phoneNumber',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      phoneNumberEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'phoneNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      phoneNumberGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'phoneNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      phoneNumberLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'phoneNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      phoneNumberBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'phoneNumber',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      phoneNumberStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'phoneNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      phoneNumberEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'phoneNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      phoneNumberContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'phoneNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      phoneNumberMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'phoneNumber',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      phoneNumberIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'phoneNumber',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      phoneNumberIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'phoneNumber',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      postalCodeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'postalCode',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      postalCodeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'postalCode',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      postalCodeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'postalCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      postalCodeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'postalCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      postalCodeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'postalCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      postalCodeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'postalCode',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      postalCodeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'postalCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      postalCodeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'postalCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      postalCodeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'postalCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      postalCodeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'postalCode',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      postalCodeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'postalCode',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      postalCodeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'postalCode',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      ratingIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'rating',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      ratingIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'rating',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      ratingEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'rating',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      ratingGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'rating',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      ratingLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'rating',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      ratingBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'rating',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      reviewCountIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reviewCount',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      reviewCountIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reviewCount',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      reviewCountEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reviewCount',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      reviewCountGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'reviewCount',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      reviewCountLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'reviewCount',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      reviewCountBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'reviewCount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      secondaryPhoneIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'secondaryPhone',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      secondaryPhoneIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'secondaryPhone',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      secondaryPhoneEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'secondaryPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      secondaryPhoneGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'secondaryPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      secondaryPhoneLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'secondaryPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      secondaryPhoneBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'secondaryPhone',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      secondaryPhoneStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'secondaryPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      secondaryPhoneEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'secondaryPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      secondaryPhoneContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'secondaryPhone',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      secondaryPhoneMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'secondaryPhone',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      secondaryPhoneIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'secondaryPhone',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      secondaryPhoneIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'secondaryPhone',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'services',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'services',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'services',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'services',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'services',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'services',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'services',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'services',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'services',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesElementMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'services',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'services',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'services',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'services',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'services',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'services',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'services',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'services',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      servicesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'services',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      specializationIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'specialization',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      specializationIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'specialization',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      specializationEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'specialization',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      specializationGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'specialization',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      specializationLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'specialization',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      specializationBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'specialization',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      specializationStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'specialization',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      specializationEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'specialization',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      specializationContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'specialization',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      specializationMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'specialization',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      specializationIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'specialization',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      specializationIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'specialization',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      stateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'state',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      stateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'state',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      stateEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'state',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      stateGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'state',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      stateLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'state',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      stateBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'state',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      stateStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'state',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      stateEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'state',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      stateContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'state',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      stateMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'state',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      stateIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'state',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      stateIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'state',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      titleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      titleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      titleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      titleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      titleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      titleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'title',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      titleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      titleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      titleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      titleMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'title',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      titleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      titleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      travelFeePerKmIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'travelFeePerKm',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      travelFeePerKmIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'travelFeePerKm',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      travelFeePerKmEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'travelFeePerKm',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      travelFeePerKmGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'travelFeePerKm',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      travelFeePerKmLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'travelFeePerKm',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      travelFeePerKmBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'travelFeePerKm',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedByIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedBy',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedByIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedBy',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedByEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedByGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedByLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedByBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedBy',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedByStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedByEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedByContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedByMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'updatedBy',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedByIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedBy',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      updatedByIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'updatedBy',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      websiteIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'website',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      websiteIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'website',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      websiteEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'website',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      websiteGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'website',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      websiteLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'website',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      websiteBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'website',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      websiteStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'website',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      websiteEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'website',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      websiteContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'website',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      websiteMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'website',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      websiteIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'website',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      websiteIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'website',
        value: '',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      yearsOfExperienceIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'yearsOfExperience',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      yearsOfExperienceIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'yearsOfExperience',
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      yearsOfExperienceEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'yearsOfExperience',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      yearsOfExperienceGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'yearsOfExperience',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      yearsOfExperienceLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'yearsOfExperience',
        value: value,
      ));
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterFilterCondition>
      yearsOfExperienceBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'yearsOfExperience',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension VeterinarianIsarQueryObject
    on QueryBuilder<VeterinarianIsar, VeterinarianIsar, QFilterCondition> {}

extension VeterinarianIsarQueryLinks
    on QueryBuilder<VeterinarianIsar, VeterinarianIsar, QFilterCondition> {}

extension VeterinarianIsarQuerySortBy
    on QueryBuilder<VeterinarianIsar, VeterinarianIsar, QSortBy> {
  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByAddress() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'address', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByAddressDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'address', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy> sortByCity() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'city', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByCityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'city', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByClinicName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'clinicName', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByClinicNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'clinicName', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByConsultationFee() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consultationFee', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByConsultationFeeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consultationFee', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByCountry() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'country', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByCountryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'country', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByCreatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByCreatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByEducation() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'education', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByEducationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'education', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy> sortByEmail() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'email', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByEmailDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'email', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByEmergencyAvailable() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyAvailable', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByEmergencyAvailableDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyAvailable', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByEmergencyFee() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyFee', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByEmergencyFeeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyFee', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByEmergencyHours() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyHours', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByEmergencyHoursDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyHours', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByEmergencyPhone() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyPhone', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByEmergencyPhoneDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyPhone', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByFullAddress() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fullAddress', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByFullAddressDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fullAddress', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByFullContactInfo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fullContactInfo', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByFullContactInfoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fullContactInfo', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByIsActive() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isActive', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByIsActiveDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isActive', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByIsPrimary() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPrimary', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByIsPrimaryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPrimary', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByLastContactDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastContactDate', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByLastContactDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastContactDate', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByLicenseNumber() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'licenseNumber', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByLicenseNumberDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'licenseNumber', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByMaxTravelDistance() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxTravelDistance', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByMaxTravelDistanceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxTravelDistance', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByMinimumTravelFee() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'minimumTravelFee', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByMinimumTravelFeeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'minimumTravelFee', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy> sortByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy> sortByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByOperatingHours() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'operatingHours', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByOperatingHoursDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'operatingHours', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByPhoneNumber() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'phoneNumber', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByPhoneNumberDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'phoneNumber', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByPostalCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'postalCode', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByPostalCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'postalCode', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByRating() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rating', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByRatingDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rating', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByReviewCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reviewCount', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByReviewCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reviewCount', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortBySecondaryPhone() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'secondaryPhone', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortBySecondaryPhoneDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'secondaryPhone', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortBySpecialization() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'specialization', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortBySpecializationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'specialization', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy> sortByState() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'state', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByStateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'state', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy> sortByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByTravelFeePerKm() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'travelFeePerKm', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByTravelFeePerKmDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'travelFeePerKm', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByUpdatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByUpdatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByWebsite() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'website', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByWebsiteDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'website', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByYearsOfExperience() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'yearsOfExperience', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      sortByYearsOfExperienceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'yearsOfExperience', Sort.desc);
    });
  }
}

extension VeterinarianIsarQuerySortThenBy
    on QueryBuilder<VeterinarianIsar, VeterinarianIsar, QSortThenBy> {
  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByAddress() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'address', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByAddressDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'address', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy> thenByCity() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'city', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByCityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'city', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByClinicName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'clinicName', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByClinicNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'clinicName', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByConsultationFee() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consultationFee', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByConsultationFeeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consultationFee', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByCountry() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'country', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByCountryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'country', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByCreatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByCreatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByEducation() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'education', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByEducationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'education', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy> thenByEmail() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'email', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByEmailDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'email', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByEmergencyAvailable() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyAvailable', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByEmergencyAvailableDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyAvailable', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByEmergencyFee() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyFee', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByEmergencyFeeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyFee', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByEmergencyHours() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyHours', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByEmergencyHoursDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyHours', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByEmergencyPhone() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyPhone', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByEmergencyPhoneDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'emergencyPhone', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByFullAddress() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fullAddress', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByFullAddressDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fullAddress', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByFullContactInfo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fullContactInfo', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByFullContactInfoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fullContactInfo', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByIsActive() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isActive', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByIsActiveDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isActive', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByIsPrimary() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPrimary', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByIsPrimaryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPrimary', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByLastContactDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastContactDate', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByLastContactDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastContactDate', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByLicenseNumber() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'licenseNumber', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByLicenseNumberDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'licenseNumber', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByMaxTravelDistance() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxTravelDistance', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByMaxTravelDistanceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'maxTravelDistance', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByMinimumTravelFee() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'minimumTravelFee', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByMinimumTravelFeeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'minimumTravelFee', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy> thenByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy> thenByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByOperatingHours() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'operatingHours', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByOperatingHoursDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'operatingHours', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByPhoneNumber() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'phoneNumber', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByPhoneNumberDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'phoneNumber', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByPostalCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'postalCode', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByPostalCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'postalCode', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByRating() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rating', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByRatingDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rating', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByReviewCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reviewCount', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByReviewCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reviewCount', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenBySecondaryPhone() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'secondaryPhone', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenBySecondaryPhoneDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'secondaryPhone', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenBySpecialization() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'specialization', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenBySpecializationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'specialization', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy> thenByState() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'state', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByStateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'state', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy> thenByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByTravelFeePerKm() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'travelFeePerKm', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByTravelFeePerKmDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'travelFeePerKm', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByUpdatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByUpdatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByWebsite() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'website', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByWebsiteDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'website', Sort.desc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByYearsOfExperience() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'yearsOfExperience', Sort.asc);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QAfterSortBy>
      thenByYearsOfExperienceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'yearsOfExperience', Sort.desc);
    });
  }
}

extension VeterinarianIsarQueryWhereDistinct
    on QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct> {
  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct> distinctByAddress(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'address', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByAssociations() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'associations');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct> distinctByCity(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'city', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByClinicName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'clinicName', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByConsultationFee() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'consultationFee');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct> distinctByCountry(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'country', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByCreatedBy({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdBy', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByEducation({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'education', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct> distinctByEmail(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'email', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByEmergencyAvailable() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'emergencyAvailable');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByEmergencyFee() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'emergencyFee');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByEmergencyHours({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'emergencyHours',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByEmergencyPhone({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'emergencyPhone',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByFarmBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'farmBusinessId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByFullAddress({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'fullAddress', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByFullContactInfo({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'fullContactInfo',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByIsActive() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isActive');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByIsPrimary() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isPrimary');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByLanguages() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'languages');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByLastContactDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lastContactDate');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByLicenseNumber({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'licenseNumber',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByMaxTravelDistance() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'maxTravelDistance');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByMinimumTravelFee() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'minimumTravelFee');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct> distinctByName(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'name', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct> distinctByNotes(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notes', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByOperatingHours({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'operatingHours',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByPaymentMethods() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'paymentMethods');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByPhoneNumber({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'phoneNumber', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByPostalCode({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'postalCode', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByRating() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'rating');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByReviewCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'reviewCount');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctBySecondaryPhone({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'secondaryPhone',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByServices() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'services');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctBySpecialization({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'specialization',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct> distinctByState(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'state', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct> distinctByTitle(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'title', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByTravelFeePerKm() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'travelFeePerKm');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByUpdatedBy({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedBy', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct> distinctByWebsite(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'website', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<VeterinarianIsar, VeterinarianIsar, QDistinct>
      distinctByYearsOfExperience() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'yearsOfExperience');
    });
  }
}

extension VeterinarianIsarQueryProperty
    on QueryBuilder<VeterinarianIsar, VeterinarianIsar, QQueryProperty> {
  QueryBuilder<VeterinarianIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations> addressProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'address');
    });
  }

  QueryBuilder<VeterinarianIsar, List<String>?, QQueryOperations>
      associationsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'associations');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations> cityProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'city');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations>
      clinicNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'clinicName');
    });
  }

  QueryBuilder<VeterinarianIsar, double?, QQueryOperations>
      consultationFeeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'consultationFee');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations> countryProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'country');
    });
  }

  QueryBuilder<VeterinarianIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations>
      createdByProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdBy');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations>
      educationProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'education');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations> emailProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'email');
    });
  }

  QueryBuilder<VeterinarianIsar, bool, QQueryOperations>
      emergencyAvailableProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'emergencyAvailable');
    });
  }

  QueryBuilder<VeterinarianIsar, double?, QQueryOperations>
      emergencyFeeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'emergencyFee');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations>
      emergencyHoursProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'emergencyHours');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations>
      emergencyPhoneProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'emergencyPhone');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations>
      farmBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'farmBusinessId');
    });
  }

  QueryBuilder<VeterinarianIsar, String, QQueryOperations>
      fullAddressProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'fullAddress');
    });
  }

  QueryBuilder<VeterinarianIsar, String, QQueryOperations>
      fullContactInfoProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'fullContactInfo');
    });
  }

  QueryBuilder<VeterinarianIsar, bool, QQueryOperations> isActiveProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isActive');
    });
  }

  QueryBuilder<VeterinarianIsar, bool, QQueryOperations> isPrimaryProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isPrimary');
    });
  }

  QueryBuilder<VeterinarianIsar, List<String>?, QQueryOperations>
      languagesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'languages');
    });
  }

  QueryBuilder<VeterinarianIsar, DateTime?, QQueryOperations>
      lastContactDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lastContactDate');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations>
      licenseNumberProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'licenseNumber');
    });
  }

  QueryBuilder<VeterinarianIsar, double?, QQueryOperations>
      maxTravelDistanceProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'maxTravelDistance');
    });
  }

  QueryBuilder<VeterinarianIsar, double?, QQueryOperations>
      minimumTravelFeeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'minimumTravelFee');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations> nameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'name');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations> notesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notes');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations>
      operatingHoursProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'operatingHours');
    });
  }

  QueryBuilder<VeterinarianIsar, List<String>?, QQueryOperations>
      paymentMethodsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'paymentMethods');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations>
      phoneNumberProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'phoneNumber');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations>
      postalCodeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'postalCode');
    });
  }

  QueryBuilder<VeterinarianIsar, double?, QQueryOperations> ratingProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'rating');
    });
  }

  QueryBuilder<VeterinarianIsar, int?, QQueryOperations> reviewCountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reviewCount');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations>
      secondaryPhoneProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'secondaryPhone');
    });
  }

  QueryBuilder<VeterinarianIsar, List<String>?, QQueryOperations>
      servicesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'services');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations>
      specializationProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'specialization');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations> stateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'state');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations> titleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'title');
    });
  }

  QueryBuilder<VeterinarianIsar, double?, QQueryOperations>
      travelFeePerKmProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'travelFeePerKm');
    });
  }

  QueryBuilder<VeterinarianIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations>
      updatedByProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedBy');
    });
  }

  QueryBuilder<VeterinarianIsar, String?, QQueryOperations> websiteProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'website');
    });
  }

  QueryBuilder<VeterinarianIsar, int?, QQueryOperations>
      yearsOfExperienceProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'yearsOfExperience');
    });
  }
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VeterinarianIsar _$VeterinarianIsarFromJson(Map<String, dynamic> json) =>
    VeterinarianIsar()
      ..id = (json['id'] as num).toInt()
      ..businessId = json['businessId'] as String?
      ..farmBusinessId = json['farmBusinessId'] as String?
      ..name = json['name'] as String?
      ..title = json['title'] as String?
      ..licenseNumber = json['licenseNumber'] as String?
      ..specialization = json['specialization'] as String?
      ..clinicName = json['clinicName'] as String?
      ..phoneNumber = json['phoneNumber'] as String?
      ..secondaryPhone = json['secondaryPhone'] as String?
      ..emergencyPhone = json['emergencyPhone'] as String?
      ..email = json['email'] as String?
      ..website = json['website'] as String?
      ..address = json['address'] as String?
      ..city = json['city'] as String?
      ..state = json['state'] as String?
      ..postalCode = json['postalCode'] as String?
      ..country = json['country'] as String?
      ..services =
          (json['services'] as List<dynamic>?)?.map((e) => e as String).toList()
      ..operatingHours = json['operatingHours'] as String?
      ..emergencyAvailable = json['emergencyAvailable'] as bool
      ..emergencyHours = json['emergencyHours'] as String?
      ..consultationFee = (json['consultationFee'] as num?)?.toDouble()
      ..emergencyFee = (json['emergencyFee'] as num?)?.toDouble()
      ..travelFeePerKm = (json['travelFeePerKm'] as num?)?.toDouble()
      ..minimumTravelFee = (json['minimumTravelFee'] as num?)?.toDouble()
      ..maxTravelDistance = (json['maxTravelDistance'] as num?)?.toDouble()
      ..paymentMethods = (json['paymentMethods'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList()
      ..languages = (json['languages'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList()
      ..yearsOfExperience = (json['yearsOfExperience'] as num?)?.toInt()
      ..education = json['education'] as String?
      ..associations = (json['associations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList()
      ..rating = (json['rating'] as num?)?.toDouble()
      ..reviewCount = (json['reviewCount'] as num?)?.toInt()
      ..notes = json['notes'] as String?
      ..isActive = json['isActive'] as bool
      ..isPrimary = json['isPrimary'] as bool
      ..lastContactDate = json['lastContactDate'] == null
          ? null
          : DateTime.parse(json['lastContactDate'] as String)
      ..createdAt = json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String)
      ..updatedAt = json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String)
      ..createdBy = json['createdBy'] as String?
      ..updatedBy = json['updatedBy'] as String?;

Map<String, dynamic> _$VeterinarianIsarToJson(VeterinarianIsar instance) =>
    <String, dynamic>{
      'id': instance.id,
      'businessId': instance.businessId,
      'farmBusinessId': instance.farmBusinessId,
      'name': instance.name,
      'title': instance.title,
      'licenseNumber': instance.licenseNumber,
      'specialization': instance.specialization,
      'clinicName': instance.clinicName,
      'phoneNumber': instance.phoneNumber,
      'secondaryPhone': instance.secondaryPhone,
      'emergencyPhone': instance.emergencyPhone,
      'email': instance.email,
      'website': instance.website,
      'address': instance.address,
      'city': instance.city,
      'state': instance.state,
      'postalCode': instance.postalCode,
      'country': instance.country,
      'services': instance.services,
      'operatingHours': instance.operatingHours,
      'emergencyAvailable': instance.emergencyAvailable,
      'emergencyHours': instance.emergencyHours,
      'consultationFee': instance.consultationFee,
      'emergencyFee': instance.emergencyFee,
      'travelFeePerKm': instance.travelFeePerKm,
      'minimumTravelFee': instance.minimumTravelFee,
      'maxTravelDistance': instance.maxTravelDistance,
      'paymentMethods': instance.paymentMethods,
      'languages': instance.languages,
      'yearsOfExperience': instance.yearsOfExperience,
      'education': instance.education,
      'associations': instance.associations,
      'rating': instance.rating,
      'reviewCount': instance.reviewCount,
      'notes': instance.notes,
      'isActive': instance.isActive,
      'isPrimary': instance.isPrimary,
      'lastContactDate': instance.lastContactDate?.toIso8601String(),
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'createdBy': instance.createdBy,
      'updatedBy': instance.updatedBy,
    };
