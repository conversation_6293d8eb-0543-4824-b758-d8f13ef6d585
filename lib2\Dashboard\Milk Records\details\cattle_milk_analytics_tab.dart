import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/milk_record_isar.dart';
import '../services/milk_service.dart';
import '../../widgets/date_range_filter_widget.dart';
import '../../widgets/filter_status_bar.dart';

class CattleMilkAnalyticsTab extends StatefulWidget {
  final CattleIsar cattle;
  final List<MilkRecordIsar> milkRecords;

  const CattleMilkAnalyticsTab({
    Key? key,
    required this.cattle,
    required this.milkRecords,
  }) : super(key: key);

  @override
  State<CattleMilkAnalyticsTab> createState() => _CattleMilkAnalyticsTabState();
}

class _CattleMilkAnalyticsTabState extends State<CattleMilkAnalyticsTab> {
  final MilkService _milkService = MilkService();
  
  // Date filtering
  DateTime? _startDate;
  DateTime? _endDate;
  
  // Chart toggle
  bool _showTrendChart = true;
  
  // Loading states
  bool _isLoadingAnalysis = false;
  Map<String, dynamic>? _productionAnalysis;
  
  // Colors
  final Color _analyticsColor = Colors.blue;
  final List<Color> _chartColors = [
    Colors.blue,
    Colors.green,
    Colors.orange,
    Colors.purple,
    Colors.red,
    Colors.teal,
  ];

  @override
  void initState() {
    super.initState();
    _loadProductionAnalysis();
  }

  Future<void> _loadProductionAnalysis() async {
    try {
      setState(() => _isLoadingAnalysis = true);

      final analysis = await _milkService.getProductionSummary(
        widget.cattle.businessId!
      );

      setState(() {
        _productionAnalysis = analysis;
        _isLoadingAnalysis = false;
      });
    } catch (e) {
      setState(() => _isLoadingAnalysis = false);
    }
  }

  List<MilkRecordIsar> _getFilteredRecords() {
    if (_startDate == null && _endDate == null) {
      return widget.milkRecords;
    }

    return widget.milkRecords.where((record) {
      final recordDate = record.date;
      if (recordDate == null) return false;

      if (_startDate != null && recordDate.isBefore(_startDate!)) {
        return false;
      }
      if (_endDate != null && recordDate.isAfter(_endDate!)) {
        return false;
      }
      return true;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(_getResponsivePadding()),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date Range Filter
          DateRangeFilterWidget(
            startDate: _startDate,
            endDate: _endDate,
            onStartDateChanged: (date) {
              setState(() {
                _startDate = date;
              });
            },
            onEndDateChanged: (date) {
              setState(() {
                _endDate = date;
              });
            },
            onClearFilter: () {
              setState(() {
                _startDate = null;
                _endDate = null;
              });
            },
            themeColor: _analyticsColor,
          ),
          const SizedBox(height: 4),

          // Filter Status Bar
          FilterStatusBar.analytics(
            filterStates: {
              'startDate': _startDate,
              'endDate': _endDate,
            },
            onClearFilters: _clearFilters,
            totalCount: widget.milkRecords.length,
            filteredCount: _getFilteredRecords().length,
            defaultStartDate: DateTime.now().subtract(const Duration(days: 30)),
            defaultEndDate: DateTime.now(),
          ),
          SizedBox(height: _getResponsiveSpacing()),
          _buildSummaryCards(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildChartsSection(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildProductionAnalysisSection(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildYieldDistributionChart(),
        ],
      ),
    );
  }

  // Filter helper methods
  bool _hasActiveFilters() {
    return _startDate != null || _endDate != null;
  }

  void _clearFilters() {
    setState(() {
      _startDate = null;
      _endDate = null;
    });
  }

  List<String> _getActiveFiltersList() {
    List<String> filters = [];
    if (_startDate != null || _endDate != null) {
      if (_startDate != null && _endDate != null) {
        filters.add('Date: ${DateFormat('MMM dd').format(_startDate!)} - ${DateFormat('MMM dd, yyyy').format(_endDate!)}');
      } else if (_startDate != null) {
        filters.add('From: ${DateFormat('MMM dd, yyyy').format(_startDate!)}');
      } else if (_endDate != null) {
        filters.add('Until: ${DateFormat('MMM dd, yyyy').format(_endDate!)}');
      }
    }
    return filters;
  }

  Widget _buildSummaryCards() {
    final analytics = _calculateAnalytics();
    
    if (MediaQuery.of(context).size.width < 600) {
      // Mobile layout - 2x2 grid
      return Column(
        children: [
          Row(
            children: [
              Expanded(child: _buildSummaryCard('Total Records', '${analytics['totalRecords']}', Icons.list, _chartColors[0])),
              SizedBox(width: _getResponsivePadding() * 0.5),
              Expanded(child: _buildSummaryCard('Total Yield', '${analytics['totalYield']?.toStringAsFixed(1) ?? '0'} L', Icons.water_drop, _chartColors[1])),
            ],
          ),
          SizedBox(width: _getResponsivePadding() * 0.5),
          Row(
            children: [
              Expanded(child: _buildSummaryCard('Avg Daily', '${analytics['avgDaily']?.toStringAsFixed(1) ?? '0'} L', Icons.trending_up, _chartColors[2])),
              SizedBox(width: _getResponsivePadding() * 0.5),
              Expanded(child: _buildSummaryCard('Best Day', '${analytics['bestDay']?.toStringAsFixed(1) ?? '0'} L', Icons.star, _chartColors[3])),
            ],
          ),
        ],
      );
    } else {
      return Row(
        children: [
          Expanded(child: _buildSummaryCard('Total Records', '${analytics['totalRecords']}', Icons.list, _chartColors[0])),
          SizedBox(width: _getResponsivePadding() * 0.5),
          Expanded(child: _buildSummaryCard('Total Yield', '${analytics['totalYield']?.toStringAsFixed(1) ?? '0'} L', Icons.water_drop, _chartColors[1])),
          SizedBox(width: _getResponsivePadding() * 0.5),
          Expanded(child: _buildSummaryCard('Avg Daily', '${analytics['avgDaily']?.toStringAsFixed(1) ?? '0'} L', Icons.trending_up, _chartColors[2])),
          SizedBox(width: _getResponsivePadding() * 0.5),
          Expanded(child: _buildSummaryCard('Best Day', '${analytics['bestDay']?.toStringAsFixed(1) ?? '0'} L', Icons.star, _chartColors[3])),
        ],
      );
    }
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: _getResponsiveIconSize()),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Responsive design helpers
  double _getResponsivePadding() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 12.0;
    if (screenWidth < 1200) return 16.0;
    return 20.0;
  }

  double _getResponsiveSpacing() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 16.0;
    if (screenWidth < 1200) return 20.0;
    return 24.0;
  }

  double _getResponsiveIconSize() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) return 24.0;
    if (screenWidth < 600) return 28.0;
    return 32.0;
  }

  Widget _buildChartsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Production Charts',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _analyticsColor,
              ),
            ),
            const SizedBox(height: 12),
            SegmentedButton<bool>(
              segments: const [
                ButtonSegment<bool>(
                  value: true,
                  label: Text('Trend'),
                  icon: Icon(Icons.show_chart),
                ),
                ButtonSegment<bool>(
                  value: false,
                  label: Text('Distribution'),
                  icon: Icon(Icons.pie_chart),
                ),
              ],
              selected: {_showTrendChart},
              onSelectionChanged: (Set<bool> newSelection) {
                setState(() {
                  _showTrendChart = newSelection.first;
                });
              },
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          height: 400,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: _analyticsColor),
          ),
          child: _showTrendChart ? _buildTrendChart() : _buildDistributionChart(),
        ),
      ],
    );
  }

  Widget _buildTrendChart() {
    final spots = _getYieldTrendSpots();

    if (spots.isEmpty) {
      return const Center(child: Text('No trend data available'));
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          horizontalInterval: 1,
          verticalInterval: 1,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey[300]!,
              strokeWidth: 1,
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.grey[300]!,
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (double value, TitleMeta meta) {
                final records = _getFilteredRecords();
                if (value.toInt() >= 0 && value.toInt() < records.length) {
                  final record = records[value.toInt()];
                  return SideTitleWidget(
                    meta: meta,
                    space: 4,
                    child: Text(
                      DateFormat('MM/dd').format(record.date ?? DateTime.now()),
                      style: const TextStyle(
                        color: Colors.grey,
                        fontWeight: FontWeight.bold,
                        fontSize: 10,
                      ),
                    ),
                  );
                }
                return Container();
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: 1,
              getTitlesWidget: (double value, TitleMeta meta) {
                return Text(
                  '${value.toInt()}L',
                  style: const TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                    fontSize: 10,
                  ),
                );
              },
              reservedSize: 42,
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: const Color(0xff37434d)),
        ),
        minX: 0,
        maxX: spots.length.toDouble() - 1,
        minY: 0,
        maxY: spots.map((spot) => spot.y).reduce((a, b) => a > b ? a : b) * 1.1,
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            gradient: LinearGradient(
              colors: [
                _chartColors[1],
                _chartColors[1].withValues(alpha: 0.3),
              ],
            ),
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: const FlDotData(
              show: false,
            ),
            belowBarData: BarAreaData(
              show: true,
              gradient: LinearGradient(
                colors: [
                  _chartColors[1].withValues(alpha: 0.3),
                  _chartColors[1].withValues(alpha: 0.1),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDistributionChart() {
    final data = _getYieldDistributionData();

    if (data.isEmpty) {
      return const Center(child: Text('No distribution data available'));
    }

    return PieChart(
      PieChartData(
        pieTouchData: PieTouchData(
          touchCallback: (FlTouchEvent event, pieTouchResponse) {
            // Handle touch events if needed
          },
        ),
        borderData: FlBorderData(
          show: false,
        ),
        sectionsSpace: 0,
        centerSpaceRadius: 40,
        sections: data.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final color = _chartColors[index % _chartColors.length];

          return PieChartSectionData(
            color: color,
            value: item['value'],
            title: '${item['percentage'].toStringAsFixed(1)}%',
            radius: 50,
            titleStyle: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          );
        }).toList(),
      ),
    );
  }

  List<FlSpot> _getYieldTrendSpots() {
    final filteredRecords = _getFilteredRecords();
    final sortedRecords = List<MilkRecordIsar>.from(filteredRecords)
      ..sort((a, b) => (a.date ?? DateTime.now())
          .compareTo(b.date ?? DateTime.now()));

    return sortedRecords.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.totalYield);
    }).toList();
  }

  List<Map<String, dynamic>> _getYieldDistributionData() {
    final filteredRecords = _getFilteredRecords();

    if (filteredRecords.isEmpty) return [];

    // Group by yield ranges
    final ranges = {
      '0-5L': 0,
      '5-10L': 0,
      '10-15L': 0,
      '15-20L': 0,
      '20L+': 0,
    };

    for (final record in filteredRecords) {
      final yield = record.totalYield;
      if (yield < 5) {
        ranges['0-5L'] = ranges['0-5L']! + 1;
      } else if (yield < 10) {
        ranges['5-10L'] = ranges['5-10L']! + 1;
      } else if (yield < 15) {
        ranges['10-15L'] = ranges['10-15L']! + 1;
      } else if (yield < 20) {
        ranges['15-20L'] = ranges['15-20L']! + 1;
      } else {
        ranges['20L+'] = ranges['20L+']! + 1;
      }
    }

    final total = filteredRecords.length;
    return ranges.entries.where((entry) => entry.value > 0).map((entry) {
      return {
        'label': entry.key,
        'value': entry.value.toDouble(),
        'percentage': (entry.value / total) * 100,
      };
    }).toList();
  }

  Map<String, dynamic> _calculateAnalytics() {
    final filteredRecords = _getFilteredRecords();

    if (filteredRecords.isEmpty) {
      return {
        'totalRecords': 0,
        'totalYield': 0.0,
        'avgDaily': 0.0,
        'bestDay': 0.0,
      };
    }

    final totalYield = filteredRecords.fold<double>(0.0, (sum, record) => sum + record.totalYield);
    final avgDaily = totalYield / filteredRecords.length;
    final bestDay = filteredRecords.map((r) => r.totalYield).reduce((a, b) => a > b ? a : b);

    return {
      'totalRecords': filteredRecords.length,
      'totalYield': totalYield,
      'avgDaily': avgDaily,
      'bestDay': bestDay,
    };
  }

  Widget _buildProductionAnalysisSection() {
    if (_isLoadingAnalysis) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: _analyticsColor),
        ),
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_productionAnalysis == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: _analyticsColor),
        ),
        child: const Center(child: Text('No production analysis available')),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _analyticsColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: _analyticsColor),
              const SizedBox(width: 8),
              Text(
                'Production Analysis',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _analyticsColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildAnalysisRow('Total Records', _productionAnalysis!['totalRecords'].toString()),
          _buildAnalysisRow('Total Production', '${_productionAnalysis!['totalProduction'].toStringAsFixed(1)} L'),
          _buildAnalysisRow('Average Daily', '${_productionAnalysis!['averageDaily'].toStringAsFixed(1)} L'),
          _buildAnalysisRow('Production Trend', _productionAnalysis!['productionTrend']),
          _buildAnalysisRow('Best Day', '${_productionAnalysis!['bestDay'].toStringAsFixed(1)} L'),
        ],
      ),
    );
  }

  Widget _buildAnalysisRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: _analyticsColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildYieldDistributionChart() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Yield Distribution',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: _analyticsColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          height: 300,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: _analyticsColor),
          ),
          child: _buildDistributionChart(),
        ),
      ],
    );
  }
}
