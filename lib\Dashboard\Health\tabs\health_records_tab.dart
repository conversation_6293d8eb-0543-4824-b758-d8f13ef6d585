import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/health_record_isar.dart';
import '../models/veterinarian_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../../constants/health_constants.dart';
import '../../../widgets/responsive_grid.dart';
import '../dialogs/health_record_form_dialog.dart';
// New component imports for universal filter system
import '../../widgets/filter_widget.dart';
import '../../widgets/sort_widget.dart';
import '../../widgets/search_widget.dart';
import '../../widgets/date_range_filter_widget.dart';
import '../../widgets/filter_status_bar.dart';
import '../../widgets/index.dart';

class HealthRecordsTab extends StatefulWidget {
  final List<HealthRecordIsar> healthRecords;
  final Map<String, CattleIsar> cattleMap;
  final Map<String, AnimalTypeIsar> animalTypeMap;
  final Map<String, BreedCategoryIsar> breedMap;
  final List<VeterinarianIsar> veterinarians;
  final bool isSelectionMode;
  final Set<String> selectedRecords;
  final Function(bool) onSelectionModeChanged;
  final Function(String, bool) onSelectionChanged;
  final VoidCallback onRefresh;

  const HealthRecordsTab({
    super.key,
    required this.healthRecords,
    required this.cattleMap,
    required this.animalTypeMap,
    required this.breedMap,
    required this.veterinarians,
    required this.isSelectionMode,
    required this.selectedRecords,
    required this.onSelectionModeChanged,
    required this.onSelectionChanged,
    required this.onRefresh,
  });

  @override
  State<HealthRecordsTab> createState() => _HealthRecordsTabState();
}

class _HealthRecordsTabState extends State<HealthRecordsTab> {
  // Universal filter system state variables (following Weight/Breeding pattern)
  String _searchQuery = '';
  String? _selectedCattleId;
  String? _selectedAnimalType;
  String? _selectedBreed;
  String? _selectedGender;

  // Module-specific filter state variables
  String? _selectedCondition;
  String? _selectedHealthStatus;
  String? _selectedSeverity;
  String? _selectedRecordType;
  String? _selectedVeterinarian;
  bool _showEmergencyOnly = false;
  bool _showChronicOnly = false;
  bool _showFollowUpOnly = false;
  bool _showUnderTreatmentOnly = false;

  // Date range filter state
  DateTime? _startDate;
  DateTime? _endDate;

  // Sort state
  String _sortBy = 'date';
  bool _sortAscending = false;

  List<HealthRecordIsar> get _filteredRecords {
    var filtered = widget.healthRecords.where((record) {
      final cattle = widget.cattleMap[record.cattleBusinessId];

      // Search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final cattleName = cattle?.name?.toLowerCase() ?? '';
        final cattleTag = cattle?.tagId?.toLowerCase() ?? '';
        final condition = record.condition?.toLowerCase() ?? '';
        final symptoms = record.symptoms?.toLowerCase() ?? '';
        final treatment = record.treatment?.toLowerCase() ?? '';
        final veterinarian = record.veterinarian?.toLowerCase() ?? '';

        if (!cattleName.contains(query) &&
            !cattleTag.contains(query) &&
            !condition.contains(query) &&
            !symptoms.contains(query) &&
            !treatment.contains(query) &&
            !veterinarian.contains(query)) {
          return false;
        }
      }

      // Date range filter
      if (_startDate != null && record.date != null && record.date!.isBefore(_startDate!)) {
        return false;
      }
      if (_endDate != null && record.date != null && record.date!.isAfter(_endDate!)) {
        return false;
      }

      // Core common filters (following Weight/Breeding pattern)
      if (_selectedCattleId != null && record.cattleBusinessId != _selectedCattleId) {
        return false;
      }

      if (_selectedAnimalType != null && cattle?.animalTypeId != _selectedAnimalType) {
        return false;
      }

      if (_selectedBreed != null && cattle?.breedId != _selectedBreed) {
        return false;
      }

      if (_selectedGender != null && cattle?.gender != _selectedGender) {
        return false;
      }

      // Module-specific filters
      if (_selectedCondition != null && record.condition != _selectedCondition) {
        return false;
      }

      if (_selectedHealthStatus != null && record.healthStatus != _selectedHealthStatus) {
        return false;
      }

      if (_selectedSeverity != null && record.severity != _selectedSeverity) {
        return false;
      }

      if (_selectedRecordType != null && record.recordType != _selectedRecordType) {
        return false;
      }

      if (_selectedVeterinarian != null && record.veterinarian != _selectedVeterinarian) {
        return false;
      }

      // Boolean filters
      if (_showEmergencyOnly && !record.isEmergency) {
        return false;
      }

      if (_showChronicOnly && !record.isChronic) {
        return false;
      }

      if (_showFollowUpOnly && !record.requiresFollowUp) {
        return false;
      }

      if (_showUnderTreatmentOnly && !record.isUnderTreatment) {
        return false;
      }

      return true;
    }).toList();

    // Apply sorting
    filtered.sort((a, b) {
      int comparison = 0;

      switch (_sortBy) {
        case 'date':
          comparison = (a.date ?? DateTime.now()).compareTo(b.date ?? DateTime.now());
          break;
        case 'cattle':
          final cattleA = widget.cattleMap[a.cattleBusinessId]?.name ?? '';
          final cattleB = widget.cattleMap[b.cattleBusinessId]?.name ?? '';
          comparison = cattleA.compareTo(cattleB);
          break;
        case 'condition':
          comparison = (a.condition ?? '').compareTo(b.condition ?? '');
          break;
        case 'status':
          comparison = (a.healthStatus ?? '').compareTo(b.healthStatus ?? '');
          break;
        case 'severity':
          final severityOrder = {'Low': 1, 'Medium': 2, 'High': 3, 'Critical': 4};
          final severityA = severityOrder[a.severity] ?? 0;
          final severityB = severityOrder[b.severity] ?? 0;
          comparison = severityA.compareTo(severityB);
          break;
      }

      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _selectedCattleId = null;
      _selectedAnimalType = null;
      _selectedBreed = null;
      _selectedGender = null;
      _selectedCondition = null;
      _selectedHealthStatus = null;
      _selectedSeverity = null;
      _selectedRecordType = null;
      _selectedVeterinarian = null;
      _showEmergencyOnly = false;
      _showChronicOnly = false;
      _showFollowUpOnly = false;
      _showUnderTreatmentOnly = false;
      _startDate = null;
      _endDate = null;
      _sortBy = 'date';
      _sortAscending = false;
    });
  }

  bool get _hasActiveFilters {
    return _searchQuery.isNotEmpty ||
        _selectedCattleId != null ||
        _selectedAnimalType != null ||
        _selectedBreed != null ||
        _selectedGender != null ||
        _selectedCondition != null ||
        _selectedHealthStatus != null ||
        _selectedSeverity != null ||
        _selectedRecordType != null ||
        _selectedVeterinarian != null ||
        _showEmergencyOnly ||
        _showChronicOnly ||
        _showFollowUpOnly ||
        _showUnderTreatmentOnly ||
        _startDate != null ||
        _endDate != null;
  }

  @override
  Widget build(BuildContext context) {
    final filteredRecords = _filteredRecords;

    return Column(
      children: [
        // Row 1: Filter, Date Range, Sort
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              // Filter Widget
              Expanded(
                child: FilterWidget(
                  // 4 Core Common Filters (used in ALL modules) + Module-specific filters
                  filterFields: [
                    // 1. Animal Type (Primary common filter)
                    FilterField(
                      key: 'animalType',
                      label: 'Animal Type',
                      icon: Icons.pets,
                      iconColor: Colors.blue,
                      options: CommonFilterHelper.generateAnimalTypeOptions(widget.animalTypeMap),
                      currentValue: _selectedAnimalType,
                    ),
                    // 2. Breed (Secondary common filter - depends on Animal Type)
                    FilterField(
                      key: 'breed',
                      label: 'Breed',
                      icon: Icons.category,
                      iconColor: Colors.purple,
                      options: CommonFilterHelper.generateBreedOptions(widget.breedMap, filterByAnimalType: _selectedAnimalType),
                      currentValue: _selectedBreed,
                      dependsOn: 'animalType',
                    ),
                    // 3. Gender (Tertiary common filter)
                    FilterField(
                      key: 'gender',
                      label: 'Gender',
                      icon: Icons.wc,
                      iconColor: Colors.pink,
                      options: CommonFilterHelper.generateGenderOptions(),
                      currentValue: _selectedGender,
                    ),
                    // 4. Cattle (Final common filter - depends on all above)
                    FilterField(
                      key: 'cattle',
                      label: 'Cattle',
                      icon: Icons.pets,
                      iconColor: Colors.green,
                      options: CommonFilterHelper.generateCattleOptions(
                        widget.cattleMap,
                        filterByAnimalType: _selectedAnimalType,
                        filterByBreed: _selectedBreed,
                        filterByGender: _selectedGender,
                      ),
                      currentValue: _selectedCattleId,
                      dependsOn: 'animalType,breed,gender',
                    ),

                    // Module-specific filters for Health
                    FilterField(
                      key: 'condition',
                      label: 'Condition',
                      icon: Icons.medical_services,
                      iconColor: Colors.red,
                      options: ModuleSpecificFilterHelper.generateConditionOptions(widget.animalTypeMap, _selectedAnimalType),
                      currentValue: _selectedCondition,
                    ),
                    FilterField(
                      key: 'healthStatus',
                      label: 'Health Status',
                      icon: Icons.health_and_safety,
                      iconColor: Colors.green,
                      options: ModuleSpecificFilterHelper.generateHealthStatusOptions(),
                      currentValue: _selectedHealthStatus,
                    ),
                    FilterField(
                      key: 'severity',
                      label: 'Severity',
                      icon: Icons.warning,
                      iconColor: Colors.orange,
                      options: [
                        const FilterOption(value: 'Low', label: 'Low', icon: Icons.info, iconColor: Colors.blue),
                        const FilterOption(value: 'Medium', label: 'Medium', icon: Icons.warning, iconColor: Colors.orange),
                        const FilterOption(value: 'High', label: 'High', icon: Icons.error, iconColor: Colors.red),
                        const FilterOption(value: 'Critical', label: 'Critical', icon: Icons.dangerous, iconColor: Colors.red),
                      ],
                      currentValue: _selectedSeverity,
                    ),
                    FilterField(
                      key: 'recordType',
                      label: 'Record Type',
                      icon: Icons.assignment,
                      iconColor: Colors.indigo,
                      options: HealthConstants.recordTypes.map((type) => FilterOption(
                        value: type,
                        label: type,
                        icon: Icons.assignment,
                        iconColor: Colors.indigo,
                      )).toList(),
                      currentValue: _selectedRecordType,
                    ),
                    FilterField(
                      key: 'veterinarian',
                      label: 'Veterinarian',
                      icon: Icons.person,
                      iconColor: Colors.teal,
                      options: widget.veterinarians.map((v) => FilterOption(
                        value: v.name ?? '',
                        label: v.name ?? 'Unknown',
                        icon: Icons.person,
                        iconColor: Colors.teal,
                      )).toList(),
                      currentValue: _selectedVeterinarian,
                    ),
                  ],
                  onFilterChanged: (key, value) {
                    setState(() {
                      switch (key) {
                        case 'animalType':
                          _selectedAnimalType = value;
                          break;
                        case 'breed':
                          _selectedBreed = value;
                          break;
                        case 'gender':
                          _selectedGender = value;
                          break;
                        case 'cattle':
                          _selectedCattleId = value;
                          break;
                        case 'condition':
                          _selectedCondition = value;
                          break;
                        case 'healthStatus':
                          _selectedHealthStatus = value;
                          break;
                        case 'severity':
                          _selectedSeverity = value;
                          break;
                        case 'recordType':
                          _selectedRecordType = value;
                          break;
                        case 'veterinarian':
                          _selectedVeterinarian = value;
                          break;
                      }
                    });
                  },
                  onClearFilters: _clearFilters,
                  themeColor: Colors.red,
                  dialogTitle: 'Filter Health Records',
                  cattleMap: widget.cattleMap,
                  animalTypeMap: widget.animalTypeMap,
                  breedMap: widget.breedMap,
                ),
              ),

              const SizedBox(width: 8),

              // Date Range Filter
              Expanded(
                child: DateRangeFilterWidget(
                  startDate: _startDate,  // Pass null when no filter active
                  endDate: _endDate,      // Pass null when no filter active
                  onStartDateChanged: (date) => setState(() => _startDate = date),
                  onEndDateChanged: (date) => setState(() => _endDate = date),
                  themeColor: Colors.red,
                ),
              ),

              const SizedBox(width: 8),

              // Sort Widget
              Expanded(
                child: SortWidget(
                  sortFields: const [
                    SortField(
                      value: 'date',
                      label: 'Date',
                      icon: Icons.calendar_today,
                      iconColor: Colors.blue,
                    ),
                    SortField(
                      value: 'cattle',
                      label: 'Cattle',
                      icon: Icons.pets,
                      iconColor: Colors.green,
                    ),
                    SortField(
                      value: 'condition',
                      label: 'Condition',
                      icon: Icons.medical_services,
                      iconColor: Colors.red,
                    ),
                    SortField(
                      value: 'status',
                      label: 'Health Status',
                      icon: Icons.health_and_safety,
                      iconColor: Colors.orange,
                    ),
                    SortField(
                      value: 'severity',
                      label: 'Severity',
                      icon: Icons.warning,
                      iconColor: Colors.purple,
                    ),
                  ],
                  sortBy: _sortBy,
                  sortAscending: _sortAscending,
                  onSortByChanged: (sortBy) => setState(() => _sortBy = sortBy ?? 'date'),
                  onSortAscendingChanged: (ascending) => setState(() => _sortAscending = ascending),
                  themeColor: Colors.red,
                  dialogTitle: 'Sort Health Records',
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // Filter Status Bar (Clear Filters + Count)
        FilterStatusBar.health(
          filterStates: {
            'searchQuery': _searchQuery,
          },
          totalCount: widget.healthRecords.length,
          filteredCount: filteredRecords.length,
          onClearFilters: _clearFilters,
          defaultStartDate: DateTime.now().subtract(const Duration(days: 365)),
          defaultEndDate: DateTime.now(),
        ),

        // Row 2: Search
        SearchWidget(
          searchQuery: _searchQuery,
          onSearchChanged: (query) => setState(() => _searchQuery = query),
          config: SearchConfig.health,
          themeColor: Colors.red,
        ),

        // Records List
        Expanded(
          child: filteredRecords.isEmpty
              ? _buildEmptyState()
              : RefreshIndicator(
                  onRefresh: () async => widget.onRefresh(),
                  child: ResponsiveGrid(
                    children: filteredRecords.map((record) => _buildHealthRecordCard(record)).toList(),
                  ),
                ),
        ),
      ],
    );
  }





  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 300),
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 32),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red.withAlpha(76)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: EmptyState.health(
        hasData: widget.healthRecords.isNotEmpty,
        action: widget.healthRecords.isEmpty
            ? EmptyState.createActionButton(
                onPressed: () => _showAddHealthRecordDialog(),
                icon: Icons.add,
                label: 'Add First Record',
                backgroundColor: Colors.red,
              )
            : EmptyState.createActionButton(
                onPressed: () => _clearFilters(),
                icon: Icons.clear_all,
                label: 'Clear Filters',
                backgroundColor: Colors.red,
              ),
      ),
    );
  }

  Widget _buildHealthRecordCard(HealthRecordIsar record) {
    final cattle = widget.cattleMap[record.cattleBusinessId];
    final isSelected = widget.selectedRecords.contains(record.businessId);
    final conditionColor = HealthConstants.getConditionColor(record.condition);
    final statusColor = HealthConstants.getHealthStatusColor(record.healthStatus);

    return Card(
      margin: const EdgeInsets.all(8.0),
      elevation: isSelected ? 8 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected 
            ? BorderSide(color: Colors.red, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _onRecordTap(record),
        onLongPress: () => _onRecordLongPress(record),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  // Cattle Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          cattle?.name ?? 'Unknown Cattle',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Tag: ${cattle?.tagId ?? 'Unknown'}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Date
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        DateFormat('MMM dd, yyyy').format(record.date ?? DateTime.now()),
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        record.recordType ?? 'General',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Condition and Status Row
              Row(
                children: [
                  // Condition
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: conditionColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: conditionColor.withValues(alpha: 0.3)),
                      ),
                      child: Text(
                        record.condition ?? 'Unknown',
                        style: TextStyle(
                          fontSize: 12,
                          color: conditionColor,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  
                  // Health Status
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: statusColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                      ),
                      child: Text(
                        record.healthStatus ?? 'Unknown',
                        style: TextStyle(
                          fontSize: 12,
                          color: statusColor,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Symptoms (if available)
              if (record.symptoms != null && record.symptoms!.isNotEmpty) ...[
                Text(
                  'Symptoms:',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  record.symptoms!,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
              ],

              // Flags Row
              Wrap(
                spacing: 6,
                runSpacing: 4,
                children: [
                  if (record.isEmergency)
                    _buildFlag('Emergency', Colors.red),
                  if (record.isChronic)
                    _buildFlag('Chronic', Colors.purple),
                  if (record.requiresFollowUp)
                    _buildFlag('Follow-up', Colors.green),
                  if (record.isUnderTreatment)
                    _buildFlag('Treatment', Colors.blue),
                  if (record.severity != null)
                    _buildFlag(record.severity!, HealthConstants.getSeverityColor(record.severity)),
                ],
              ),

              // Veterinarian (if available)
              if (record.veterinarian != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.person, size: 14, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      record.veterinarian!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],

              // Selection Checkbox
              if (widget.isSelectionMode) ...[
                const SizedBox(height: 8),
                Align(
                  alignment: Alignment.centerRight,
                  child: Checkbox(
                    value: isSelected,
                    onChanged: (selected) => widget.onSelectionChanged(
                      record.businessId ?? '',
                      selected ?? false,
                    ),
                    activeColor: Colors.red,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFlag(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _onRecordTap(HealthRecordIsar record) {
    if (widget.isSelectionMode) {
      final isSelected = widget.selectedRecords.contains(record.businessId);
      widget.onSelectionChanged(record.businessId ?? '', !isSelected);
    } else {
      _editRecord(record);
    }
  }

  void _onRecordLongPress(HealthRecordIsar record) {
    if (!widget.isSelectionMode) {
      widget.onSelectionModeChanged(true);
      widget.onSelectionChanged(record.businessId ?? '', true);
    }
  }

  void _editRecord(HealthRecordIsar record) {
    final cattle = widget.cattleMap.values.toList();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HealthRecordFormDialog(
          cattle: cattle,
          veterinarians: widget.veterinarians,
          existingRecord: record,
          onRecordAdded: widget.onRefresh,
        ),
      ),
    );
  }

  void _showAddHealthRecordDialog() async {
    final cattle = widget.cattleMap.values.toList();

    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => HealthRecordFormDialog(
          cattle: cattle,
          veterinarians: widget.veterinarians,
          onRecordAdded: widget.onRefresh,
        ),
      ),
    );

    if (result == true) {
      widget.onRefresh(); // Reload data if record was added
    }
  }
}
