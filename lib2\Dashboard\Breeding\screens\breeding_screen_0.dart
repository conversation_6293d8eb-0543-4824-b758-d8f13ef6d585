rs.grey,
              type: EventType.miscellaneous,
            ),
            const SizedBox(height: 32),
            const Text(
              'Custom Event Types',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _customTypes.isEmpty
                    ? Center(
                        child: Column(
                          children: [
                            SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                            Icon(
                              Icons.add_circle_outline,
                              size: 48,
                              color: Theme.of(context)
                                  .primaryColor
                                  .withAlpha((0.5 * 255).round()),
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'No custom event types yet',
                              style: TextStyle(fontSize: 16),
                            ),
                            const SizedBox(height: 8),
                            ElevatedButton.icon(
                              onPressed: () => _showCustomTypeDialog(context),
                              icon: const Icon(Icons.add),
                              label: const Text('Add Custom Type'),
                            ),
                          ],
                        ),
                      )
                    : Column(
                        children: _customTypes.map((customType) {
                          return _buildCustomTypeCard(context, customType);
                        }).toList(),
                      ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCustomTypeDialog(context),
        tooltip: 'Add Custom Event Type',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEventTypeCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
    required Color color,
    required EventType type,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withAlpha((255 * 0.2).round()),
          child: Icon(icon, color: color),
        ),
        title: Text(title),
        subtitle: Text(description),
        trailing: const Icon(Icons.info_outline),
        onTap: () {
          // For standard event types, show a snackbar with more information
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  '$title is a standard event type and cannot be modified'),
              duration: const Duration(seconds: 2),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCustomTypeCard(
      BuildContext context, CustomEventType customType) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: customType.color.withAlpha((255 * 0.2).round()),
          child: Icon(customType.icon, color: customType.color),
        ),
        title: Text(customType.name),
        subtitle: Text(customType.description),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _showCustomTypeDialog(context, customType),
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _confirmDeleteCustomType(context, customType),
            ),
          ],
        ),
        onTap: () => _showCustomTypeDialog(context, customType),
      ),
    );
  }

  Future<void> _showCustomTypeDialog(BuildContext context,
      [CustomEventType? existingType]) async {
    final formKey = GlobalKey<FormState>();
    String name = existingType?.name ?? '';
    String description = existingType?.description ?? '';
    Color selectedColor = existingType?.color ?? _availableColors[0];
    IconData selectedIcon = existingType?.icon ?? _availableIcons[0];

    final result = await showDialog<CustomEventType>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(existingType == null
            ? 'Add Custom Event Type'
            : 'Edit Custom Event Type'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  initialValue: name,
                  decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Name'),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a name';
                    }
                    return null;
                  },
                  onSaved: (value) => name = value!,
                ),
                SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                TextFormField(
                  initialValue: description,
                  decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Description'),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a description';
                    }
                    return null;
                  },
                  onSaved: (value) => description = value!,
                ),
                SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                DropdownButtonFormField<Color>(
                  value: selectedColor,
                  decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Color'),
                  items: _availableColors.map((color) {
                    return DropdownMenuItem<Color>(
                      value: color,
                      child: Row(
                        children: [
                          Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: color,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(_getColorName(color)),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (color) {
                    if (color != null) {
                      selectedColor = color;
                    }
                  },
                ),
                SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                DropdownButtonFormField<IconData>(
                  value: selectedIcon,
                  decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Icon'),
                  items: _availableIcons.map((icon) {
                    return DropdownMenuItem<IconData>(
                      value: icon,
                      child: Row(
                        children: [
                          Icon(icon),
                          const SizedBox(width: 8),
                          Text(_getIconName(icon)),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (icon) {
                    if (icon != null) {
                      selectedIcon = icon;
       