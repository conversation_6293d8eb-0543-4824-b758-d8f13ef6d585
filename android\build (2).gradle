dColor: _getTypeColor(notification.type).withAlpha((0.2 * 255).round()),
              child: Icon(
                _getTypeIcon(notification.type),
                color: _getTypeColor(notification.type),
              ),
            ),
            title: Text(
              notification.title,
              style: TextStyle(
                fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(notification.message),
                const SizedBox(height: 4),
                Text(
                  _formatTime(notification.time),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            onTap: () {
              // Handle notification tap
              setState(() {
                notifications[index] = NotificationItem(
                  title: notification.title,
                  message: notification.message,
                  time: notification.time,
                  isRead: true,
                  type: notification.type,
                );
              });
            },
          ),
        );
      },
    );
  }

  String _formatTime(DateTime time) {
    final now 