// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:logging/logging.dart';
import '../models/transaction.dart';
import '../models/category.dart';
import 'package:cattle_manager/Dashboard/Transactions/dialogs/transaction_form_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class TransactionsListTab extends StatefulWidget {
  final bool isSelectionMode;

  const TransactionsListTab({
    super.key,
    this.isSelectionMode = false,
  });

  @override
  State<TransactionsListTab> createState() => _TransactionsListTabState();
}

class _TransactionsListTabState extends State<TransactionsListTab> {
  late Box<Transaction> transactionsBox;
  List<Transaction> transactions = [];
  List<Transaction> filteredTransactions = [];
  late Box<Category> incomeCategoriesBox;
  late Box<Category> expenseCategoriesBox;
  Set<int> _selectedTransactions = {};

  // Filter states
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedType;
  String? _selectedCategory;
  String? _selectedPaymentMethod;
  String? _amountRange;
  String? _sortBy;
  bool _sortAscending = true;
  String _searchQuery = '';

  // Sort states

  final TextEditingController _startDateController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();

  final Logger _logger = Logger('TransactionsListTab');

  @override
  void initState() {
    super.initState();
    transactionsBox = Hive.box<Transaction>('transactions');
    _initializeBoxes(); // Call the method to initialize boxes
  }

  Future<void> _initializeBoxes() async {
    incomeCategoriesBox =
        await Hive.openBox<Category>('incomeCategories'); // Open the box
    _loadCurrencySettings();
    _calculateTotals();
  }

  @override
  void dispose() {
    // Remove the listener when disposing
    if (transactionsBox.isOpen) {
      transactionsBox.listenable().removeListener(_loadTransactions);
    }
    _startDateController.dispose();
    _endDateController.dispose();
    super.dispose();
  }

  String _currencySymbol = '\$';
  bool _symbolBeforeAmount = true;

  Future<void> _loadCurrencySettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _currencySymbol = prefs.getString('currencySymbol') ?? '\$';
      _symbolBeforeAmount = prefs.getBool('symbolBeforeAmount') ?? true;
    });
  }

  String _formatCurrency(double amount) {
    return _symbolBeforeAmount
        ? '$_currencySymbol${amount.toStringAsFixed(2)}'
        : '${amount.toStringAsFixed(2)}$_currencySymbol';
  }

  void _applyFilters() {
    setState(() {
      // Start with all transactions
      filteredTransactions = transactionsBox.values.toList();

      // Apply Date Range Filter
      if (_startDate != null && _endDate != null) {
        filteredTransactions = filteredTransactions.where((transaction) {
          return (transaction.date.isAtSameMomentAs(_startDate!) ||
                  transaction.date.isAfter(_startDate!)) &&
              (transaction.date.isAtSameMomentAs(_endDate!) ||
                  transaction.date.isBefore(_endDate!));
        }).toList();
      }

      // Apply Transaction Type Filter
      if (_selectedType != null && _selectedType != 'All Types') {
        filteredTransactions = filteredTransactions.where((transaction) {
          return transaction.type == _selectedType;
        }).toList();
      }

      // Apply Category Filter
      if (_selectedCategory != null && _selectedCategory != 'All Categories') {
        filteredTransactions = filteredTransactions.where((transaction) {
          return transaction.category == _selectedCategory;
        }).toList();
      }

      // Apply Payment Method Filter
      if (_selectedPaymentMethod != null &&
          _selectedPaymentMethod != 'All Methods') {
        filteredTransactions = filteredTransactions.where((transaction) {
          return transaction.paymentMethod == _selectedPaymentMethod;
        }).toList();
      }

      // Apply Amount Range Filter
      if (_amountRange != null && _amountRange != 'All Amounts') {
        filteredTransactions = filteredTransactions.where((transaction) {
          switch (_amountRange) {
            case 'Under 1000':
              return transaction.amount < 1000;
            case '1000-5000':
              return transaction.amount >= 1000 && transaction.amount <= 5000;
            case '5000-10000':
              return transaction.amount > 5000 && transaction.amount <= 10000;
            case 'Over 10000':
              return transaction.amount > 10000;
            default:
              return true;
          }
        }).toList();
      }

      // Apply Search Query
      if (_searchQuery.isNotEmpty) {
        final lowercaseQuery = _searchQuery.toLowerCase();
        filteredTransactions = filteredTransactions.where((transaction) {
          return transaction.type.toLowerCase().contains(lowercaseQuery) ||
              transaction.category.toLowerCase().contains(lowercaseQuery) ||
              transaction.amount.toString().contains(lowercaseQuery) ||
              (transaction.description
                  .toLowerCase()
                  .contains(lowercaseQuery)) ||
              (transaction.paymentMethod
                      ?.toLowerCase()
                      .contains(lowercaseQuery) ??
                  false);
        }).toList();
      }

      // Apply Sorting
      if (_sortBy != null) {
        switch (_sortBy) {
          case 'Date':
            filteredTransactions.sort((a, b) => a.date.compareTo(b.date));
            break;
          case 'Amount':
            filteredTransactions.sort((a, b) => a.amount.compareTo(b.amount));
            break;
          case 'Category':
            filteredTransactions
                .sort((a, b) => a.category.compareTo(b.category));
            break;
        }

        // Reverse if not ascending
        if (!_sortAscending) {
          filteredTransactions = filteredTransactions.reversed.toList();
        }
      } else {
        // Default sorting by date in descending order if no specific sort is selected
        filteredTransactions.sort((a, b) => b.date.compareTo(a.date));
      }
    });
  }

  Future<void> _loadTransactions() async {
    try {
      if (!mounted) return;
      setState(() {
        transactions = transactionsBox.values.toList();
        filteredTransactions = List.from(transactions);
        _applyFilters();
      });
    } catch (e) {
      _logger.severe('Error loading transactions', e);

      if (mounted) {
        final messenger = ScaffoldMessenger.of(context);
        messenger.showSnackBar(
          const SnackBar(
            content: Text('Error loading transactions'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Updated to use a single selected category

  void _showAddTransactionDialog() {
    final dialogContext = context;
    showDialog<Transaction>(
      context: dialogContext,
      builder: (context) => TransactionFormDialog(
        onSave: (transaction) async {
          try {
            if (transaction.paymentMethod == null ||
                transaction.paymentMethod!.isEmpty) {
              final messenger = ScaffoldMessenger.of(dialogContext);
              messenger.showSnackBar(
                const SnackBar(
                  content: Text('Please select a payment method'),
                  backgroundColor: Colors.red,
                ),
              );
              return;
            }

            await transactionsBox.add(transaction);

            setState(() {
              filteredTransactions = transactionsBox.values.toList()
                ..sort((a, b) => b.date.compareTo(a.date));
            });

            if (mounted) {
              final messenger = ScaffoldMessenger.of(dialogContext);
              messenger.showSnackBar(
                const SnackBar(
                  content: Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.white),
                      SizedBox(width: 8),
                      Text('Transaction saved successfully'),
                    ],
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            _logger.severe('Error saving transaction', e);

            if (mounted) {
              final messenger = ScaffoldMessenger.of(dialogContext);
              messenger.showSnackBar(
                const SnackBar(
                  content: Text('Error saving transaction'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      ),
    );
  }

  void _deleteTransaction(Transaction transaction) async {
    // Show confirmation dialog
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Transaction'),
          content:
              const Text('Are you sure you want to delete this transaction?'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            TextButton(
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );

    if (confirm != true) return;

    try {
      await transaction.delete();
      setState(() {
        filteredTransactions = transactionsBox.values.toList()
          ..sort((a, b) => b.date.compareTo(a.date));
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('Transaction deleted successfully'),
              ],
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _logger.severe('Error deleting transaction', e);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error deleting transaction'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _editTransaction(Transaction transaction) {
    showDialog<Transaction>(
      context: context,
      builder: (context) => TransactionFormDialog(
        existingTransaction: transaction,
        onSave: (updatedTransaction) async {
          try {
            // Update the transaction
            transaction.type = updatedTransaction.type;
            transaction.category = updatedTransaction.category;
            transaction.amount = updatedTransaction.amount;
            transaction.date = updatedTransaction.date;
            transaction.paymentMethod = updatedTransaction.paymentMethod;
            transaction.description = updatedTransaction.description;

            await transaction.save();

            // Update the filtered transactions
            setState(() {
              filteredTransactions = transactionsBox.values.toList()
                ..sort((a, b) => b.date.compareTo(a.date));
            });

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.white),
                      SizedBox(width: 8),
                      Text('Transaction updated successfully'),
                    ],
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            _logger.severe('Error updating transaction', e);

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Error updating transaction'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      ),
    );
  }

  IconData _getPaymentMethodIcon(String? method) {
    switch (method) {
      case 'Cash':
        return Icons.money;
      case 'Card':
        return Icons.credit_card;
      case 'Bank Transfer':
        return Icons.account_balance;
      case 'Other':
        return Icons.payment;
      default:
        return Icons.help_outline;
    }
  }

  void _calculateTotals() {}

  Color _getCategoryColor(String category, String type) {
    final lowerCategory = category.toLowerCase();
    if (type == 'Income') {
      if (lowerCategory.contains('sale')) return Colors.green.shade700;
      if (lowerCategory.contains('rent')) return Colors.teal.shade700;
      if (lowerCategory.contains('investment')) return Colors.blue.shade700;
      if (lowerCategory.contains('other')) return Colors.purple.shade700;
      return Colors.green.shade500;
    } else {
      if (lowerCategory.contains('feed')) return Colors.orange.shade700;
      if (lowerCategory.contains('medicine')) return Colors.red.shade700;
      if (lowerCategory.contains('equipment')) return Colors.brown.shade700;
      if (lowerCategory.contains('labor')) return Colors.blue.shade700;
      if (lowerCategory.contains('transport')) return Colors.indigo.shade700;
      if (lowerCategory.contains('utility')) return Colors.purple.shade700;
      if (lowerCategory.contains('other')) return Colors.grey.shade700;
      return Colors.deepOrange.shade700;
    }
  }

  IconData _getCategoryIcon(String category, String type) {
    final lowerCategory = category.toLowerCase();
    if (type == 'Income') {
      if (lowerCategory.contains('sale')) return Icons.shopping_cart;
      if (lowerCategory.contains('rent')) return Icons.home;
      if (lowerCategory.contains('investment')) return Icons.trending_up;
      if (lowerCategory.contains('other')) return Icons.attach_money;
      return Icons.arrow_circle_up;
    } else {
      if (lowerCategory.contains('feed')) return Icons.grass;
      if (lowerCategory.contains('medicine')) return Icons.medical_services;
      if (lowerCategory.contains('equipment')) return Icons.build;
      if (lowerCategory.contains('labor')) return Icons.engineering;
      if (lowerCategory.contains('transport')) return Icons.local_shipping;
      if (lowerCategory.contains('utility')) return Icons.power;
      if (lowerCategory.contains('other')) return Icons.money_off;
      return Icons.arrow_circle_down;
    }
  }

  Widget _buildTransactionListItem(Transaction transaction, bool isWideScreen) {
    DateFormat('MMM d, y').format(transaction.date);
    final formattedAmount = _formatCurrency(transaction.amount);
    final isIncome = transaction.type == 'Income';
    final statusColor = isIncome ? Colors.green : Colors.red;
    Color categoryColor =
        _getCategoryColor(transaction.category, transaction.type);
    Color alternateColor = Colors.teal; // New color for category icon
    final categoryIcon =
        _getCategoryIcon(transaction.category, transaction.type);

    Color paymentMethodColor;
    switch (transaction.paymentMethod) {
      case 'Cash':
        paymentMethodColor = Colors.orange;
        break;
      case 'Card':
        paymentMethodColor = Colors.blue;
        break;
      case 'Bank Transfer':
        paymentMethodColor = Colors.green;
        break;
      case 'Other':
        paymentMethodColor = Colors.purple;
        break;
      default:
        paymentMethodColor = Colors.grey;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shadowColor: Colors.black26,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: widget.isSelectionMode
            ? () {
                setState(() {
                  if (_selectedTransactions.contains(transaction.key)) {
                    _selectedTransactions.remove(transaction.key as int);
                  } else {
                    _selectedTransactions.add(transaction.key as int);
                  }
                });
              }
            : () => _editTransaction(transaction),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              IntrinsicHeight(
                child: Row(
                  children: [
                    if (widget.isSelectionMode)
                      SizedBox(
                        width: 56, // Fixed width for checkbox
                        child: Checkbox(
                          value:
                              _selectedTransactions.contains(transaction.key),
                          tristate: _selectedTransactions.isNotEmpty &&
                              _selectedTransactions.length !=
                                  transactions.length,
                          onChanged: (bool? value) {
                            setState(() {
                              if (value == true) {
                                _selectedTransactions
                                    .add(transaction.key as int);
                              } else {
                                _selectedTransactions
                                    .remove(transaction.key as int);
                              }
                            });
                          },
                        ),
                      ),
                    // Type indicator
                    Container(
                      width: 4,
                      height: 40,
                      decoration: BoxDecoration(
                        color: statusColor,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Category with icon
                    Expanded(
                      flex: 2,
                      child: Row(
                        children: [
                          SizedBox(
                            width: 40, // Fixed width for icon
                            child: Icon(
                              categoryIcon,
                              color: alternateColor,
                              size: 24,
                            ),
                          ),
                          Expanded(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  transaction.category,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                    color: categoryColor,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                if (transaction.description.isNotEmpty)
                                  Text(
                                    transaction.description,
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                      fontSize: 13,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Amount
                    Container(
                      width: 120, // Fixed width for amount
                      alignment: Alignment.centerRight,
                      child: Text(
                        formattedAmount,
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    if (!widget.isSelectionMode)
                      SizedBox(
                        width: 24, // Fixed width for menu button
                        child: PopupMenuButton<String>(
                          icon: Icon(
                            Icons.more_vert,
                            color: Colors.grey.shade700,
                          ),
                          onSelected: (String choice) {
                            if (choice == 'Edit') {
                              _editTransaction(transaction);
                            } else if (choice == 'Delete') {
                              _deleteTransaction(transaction);
                            }
                          },
                          itemBuilder: (BuildContext context) => [
                            const PopupMenuItem(
                              value: 'Edit',
                              child: Row(
                                children: [
                                  Icon(Icons.edit_outlined),
                                  SizedBox(width: 8),
                                  Text('Edit'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'Delete',
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.delete_outline,
                                    color: Colors.red,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Delete',
                                    style: TextStyle(color: Colors.red),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
              SizedBox(height: ResponsiveSpacing.getSM(context)),
              // Second row with date and payment method
              Row(
                children: [
                  if (widget.isSelectionMode)
                    const SizedBox(width: 56), // Match checkbox width
                  const SizedBox(width: 16), // Match indicator + spacing
                  // Date with icon - aligned with category
                  Expanded(
                    flex: 2,
                    child: Row(
                      children: [
                        SizedBox(
                          width: 40, // Match category icon width
                          child: Icon(
                            Icons.calendar_today_outlined,
                            size: 20,
                            color: Colors.blue.shade700,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            DateFormat('MMM dd, yyyy').format(transaction.date),
                            style: TextStyle(
                              color: Colors.blue.shade700,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Payment method - aligned with amount
                  Container(
                    width: 120, // Match amount width
                    alignment: Alignment.centerRight,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Icon(
                          _getPaymentMethodIcon(transaction.paymentMethod!),
                          size: 16,
                          color: paymentMethodColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          transaction.paymentMethod!,
                          style: TextStyle(
                            color: paymentMethodColor,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (!widget.isSelectionMode)
                    const SizedBox(width: 24), // Match menu button width
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _showDeleteConfirmation(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
            'Delete ${_selectedTransactions.length} Transaction${_selectedTransactions.length > 1 ? 's' : ''}?'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
                'Are you sure you want to delete ${_selectedTransactions.length} selected transaction${_selectedTransactions.length > 1 ? 's' : ''}?'),
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            const Text(
              'Warning: This action cannot be undone!',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text(
              'Delete',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _deleteSelectedTransactions();
    }
  }

  Future<void> _deleteSelectedTransactions() async {
    for (var key in _selectedTransactions) {
      await transactionsBox.delete(key);
    }
    setState(() {
      _selectedTransactions.clear();
    });
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Selected transactions deleted successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _showFilterDialog() {
    // Create local copies of current filter states to allow cancellation
    DateTime? localStartDate = _startDate;
    DateTime? localEndDate = _endDate;
    String? localSelectedType = _selectedType;
    String? localSelectedCategory = _selectedCategory;
    String? localSelectedPaymentMethod = _selectedPaymentMethod;
    String? localAmountRange = _amountRange;
    bool localSortAscending = _sortAscending;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text(
                'Filter & Sort Transactions',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Date Range Filter
                    const Text(
                      'Date Range',
                      style:
                          TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: TextEditingController(
                                text: localStartDate != null
                                    ? DateFormat('yyyy-MM-dd')
                                        .format(localStartDate!)
                                    : ''),
                            decoration: const InputDecoration(
                              labelText: 'Start Date',
                              prefixIcon: Icon(Icons.calendar_today),
                            ),
                            readOnly: true,
                            onTap: () async {
                              final pickedDate = await showDatePicker(
                                context: context,
                                initialDate: localStartDate ?? DateTime.now(),
                                firstDate: DateTime(2000),
                                lastDate: DateTime(2101),
                              );
                              if (pickedDate != null) {
                                setState(() {
                                  localStartDate = pickedDate;
                                });
                              }
                            },
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: TextField(
                            controller: TextEditingController(
                                text: localEndDate != null
                                    ? DateFormat('yyyy-MM-dd')
                                        .format(localEndDate!)
                                    : ''),
                            decoration: const InputDecoration(
                              labelText: 'End Date',
                              prefixIcon: Icon(Icons.calendar_today),
                            ),
                            readOnly: true,
                            onTap: () async {
                              final pickedDate = await showDatePicker(
                                context: context,
                                initialDate: localEndDate ?? DateTime.now(),
                                firstDate: DateTime(2000),
                                lastDate: DateTime(2101),
                              );
                              if (pickedDate != null) {
                                setState(() {
                                  localEndDate = pickedDate;
                                });
                              }
                            },
                          ),
                        ),
                      ],
                    ),

                    // Transaction Type Filter
                    SizedBox(height: ResponsiveSpacing.getMD(context)),
                    const Text(
                      'Transaction Type',
                      style:
                          TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    DropdownButton<String>(
                      isExpanded: true,
                      value: localSelectedType ?? 'All Types',
                      items: ['All Types', 'Income', 'Expense']
                          .map((type) => DropdownMenuItem(
                                value: type,
                                child: Text(type),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          localSelectedType =
                              value == 'All Types' ? null : value;
                        });
                      },
                    ),

                    // Category Filter
                    SizedBox(height: ResponsiveSpacing.getMD(context)),
                    const Text(
                      'Category',
                      style:
                          TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    DropdownButton<String>(
                      isExpanded: true,
                      value: localSelectedCategory ?? 'All Categories',
                      items: [
                            const DropdownMenuItem<String>(
                              value: 'All Categories',
                              child: Text('All Categories'),
                            ),
                          ] +
                          incomeCategoriesBox.values
                              .map((category) => DropdownMenuItem<String>(
                                    value: category.name,
                                    child: Text(category.name),
                                  ))
                              .toList(),
                      onChanged: (value) {
                        setState(() {
                          localSelectedCategory =
                              value == 'All Categories' ? null : value;
                        });
                      },
                    ),

                    // Payment Method Filter
                    SizedBox(height: ResponsiveSpacing.getMD(context)),
                    const Text(
                      'Payment Method',
                      style:
                          TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    DropdownButton<Stri