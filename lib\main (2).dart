import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
// Dashboard screen import removed - using routes
import 'theme/app_theme.dart';
import 'services/logging_service.dart';
import 'services/database/isar_initializer.dart';
import 'services/database/isar_service.dart';
import 'routes/app_routes.dart';
import 'Dashboard/Reports/screens/transactions_report_screen.dart';
import 'Dashboard/Reports/screens/milk_report_screen.dart';
import 'Dashboard/Reports/screens/events_report_screen.dart';
import 'Dashboard/Reports/screens/breeding_report_screen.dart';
import 'Dashboard/Reports/screens/cattle_report_screen.dart';
import 'Dashboard/Cattle/screens/cattle_screen.dart';
import 'Dashboard/Breeding/screens/breeding_screen.dart';

import 'Dashboard/Milk Records/screens/milk_screen.dart';
import 'Dashboard/Transactions/screens/transactions_screen.dart';
import 'Dashboard/Farm Setup/screens/farm_setup_screen.dart';
import 'Dashboard/Reports/screens/weight_report_screen.dart';
import 'Dashboard/Reports/screens/pregnancies_report_screen.dart';
import 'Dashboard/Reports/screens/health_report_screen.dart';
import 'Dashboard/Farm Setup/services/farm_setup_handler.dart';

import 'Dashboard/User Account/widgets/cloud_auth_wrapper.dart';
import 'Dashboard/User Account/screens/user_account_screen.dart';
import 'services/firebase_service.dart';
import 'Dashboard/User Account/services/cloud_authentication_service.dart';
import 'services/permission_service.dart';
import 'services/invitation_service.dart';
// Removed multi-farm services - single farm per user

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Logging Service
  final loggingService = LoggingService();
  loggingService.setupLogging();
  loggingService.logUncaughtExceptions();

  // Initialize Isar database
  bool isarInitialized = false;
  final getIt = GetIt.instance;

  try {
    // Initialize Firebase first to check for saved login
    loggingService.info("Initializing Firebase");
    final firebaseInitialized = await FirebaseService.initialize();

    String? savedUserId;
    bool rememberMe = false;

    if (firebaseInitialized) {
      // Check for saved login preference with 30-day expiration
      try {
        final prefs = await SharedPreferences.getInstance();
        rememberMe = prefs.getBool('remember_me') ?? false;

        if (rememberMe) {
          // Check if Remember Me has expired (30 days)
          final rememberMeTimestamp = prefs.getInt('remember_me_timestamp') ?? 0;
          final rememberMeDate = DateTime.fromMillisecondsSinceEpoch(rememberMeTimestamp);
          final now = DateTime.now();
          final daysSinceRememberMe = now.difference(rememberMeDate).inDays;

          if (daysSinceRememberMe >= 30) {
            loggingService.info("Remember Me expired after $daysSinceRememberMe days, clearing preference");
            await prefs.remove('remember_me');
            await prefs.remove('remember_me_timestamp');
            rememberMe = false;
          } else {
            // Check if user is still logged in
            final currentUser = FirebaseAuth.instance.currentUser;
            if (currentUser != null) {
              await currentUser.reload();
              savedUserId = currentUser.uid;
              loggingService.info("Found saved user session: ${currentUser.email} (${30 - daysSinceRememberMe} days remaining)");
            }
          }
        }
      } catch (e) {
        loggingService.warning("Error checking saved login: $e");
      }
    }

    // Initialize Isar with the correct user context
    if (savedUserId != null && rememberMe) {
      loggingService.info("Initializing Isar database for saved user: ${savedUserId.substring(0, 20)}...");
      await IsarInitializer.initialize(savedUserId);
    } else {
      loggingService.info("Initializing Isar database in guest mode");
      await IsarInitializer.initialize(null); // null = guest mode
    }

    isarInitialized = true;
    loggingService.info("Isar initialization succeeded");

    // Register cloud authentication service (after Isar is ready)
    if (firebaseInitialized) {
      getIt.registerSingleton<CloudAuthenticationService>(CloudAuthenticationService());
      loggingService.info("Cloud authentication service registered");
    } else {
      loggingService.warning("Firebase initialization failed, continuing with local auth only");
    }

    // Register permission service (always register, regardless of Firebase status)
    try {
      getIt.registerSingleton<PermissionService>(PermissionService());
      loggingService.info("✅ Permission service registered successfully");
    } catch (e) {
      loggingService.error("❌ Failed to register Permission service: $e");
    }

    // Register invitation service (always register, regardless of Firebase status)
    try {
      getIt.registerSingleton<InvitationService>(InvitationService());
      loggingService.info("✅ Invitation service registered successfully");
    } catch (e) {
      loggingService.error("❌ Failed to register Invitation service: $e");
    }

    // Multi-farm services removed - single farm per user

    // Verify services are registered
    try {
      final permissionService = getIt<PermissionService>();
      loggingService.info("✅ Permission service verification: ${permissionService.runtimeType}");
    } catch (e) {
      loggingService.error("❌ Permission service verification failed: $e");
    }

    try {
      final invitationService = getIt<InvitationService>();
      loggingService.info("✅ Invitation service verification: ${invitationService.runtimeType}");
    } catch (e) {
      loggingService.error("❌ Invitation service verification failed: $e");
    }

    if (isarInitialized) {
      // Ensure default data exists
      await IsarInitializer.ensureDefaultData();

      // Get handlers from GetIt
      final farmSetupHandler = getIt<FarmSetupHandler>();

      // Log database information for debugging
      final animalTypes = await farmSetupHandler.getAllAnimalTypes();
      loggingService
          .info('Loaded ${animalTypes.length} animal types from Isar');

      final breedCategories = await farmSetupHandler.getAllBreedCategories();
      loggingService
          .info('Loaded ${breedCategories.length} breed categories from Isar');

      // Check if breeds are missing and initialize them if needed
      if (breedCategories.isEmpty && animalTypes.isNotEmpty) {
        loggingService.info('No breeds found but animal types exist. Initializing default breeds...');
        await IsarInitializer.initializeDefaultBreeds();

        // Check again after initialization
        final updatedBreeds = await farmSetupHandler.getAllBreedCategories();
        loggingService.info('After initialization: ${updatedBreeds.length} breed categories loaded');
      }

      final farms = await farmSetupHandler.getAllFarms();
      if (farms.isNotEmpty) {
        loggingService.info('Loaded farm: ${farms.first.name} (ID: ${farms.first.farmBusinessId})');

        // Log current database information for debugging
        final isarService = getIt<IsarService>();
        loggingService.info('Current database info: ${isarService.currentDatabaseInfo}');
      } else {
        loggingService.info('No farm found in database');
      }


    }
  } catch (e) {
    loggingService.error("Error initializing Isar database: $e");
    isarInitialized = false;
  }

  runApp(CattleManagerApp(
    isarInitialized: isarInitialized,
  ));
}

class CattleManagerApp extends StatefulWidget {
  final bool isarInitialized;

  const CattleManagerApp({
    super.key,
    required this.isarInitialized,
  });

  @override
  State<CattleManagerApp> createState() => _CattleManagerAppState();
}

class _CattleManagerAppState extends State<CattleManagerApp> {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Cattle Manager',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.primaryColor),
        useMaterial3: true,
        scaffoldBackgroundColor: AppTheme.scaffoldBackground,
        appBarTheme: AppTheme.appBarTheme,
        cardTheme: CardTheme(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: AppTheme.cardRadius),
        ),
      ),
      home: CloudAuthWrapper(isarInitialized: widget.isarInitialized),
      routes: {
        AppRoutes.cattle: (context) => const CattleScreen(),
        AppRoutes.breeding: (context) => const BreedingScreen(),
        AppRoutes.health: (context) => const _HealthModuleRemovedScreen(),
        AppRoutes.milk: (context) => const MilkScreen(),
        AppRoutes.transactions: (context) => const TransactionsScreen(initialTabIndex: 0), // Open Summary tab by default (now index 0)
        AppRoutes.settings: (context) => const FarmSetupScreen(),
        AppRoutes.userAccount: (context) => const UserAccountScreen(),
        AppRoutes.transactionsReport: (context) =>
            const TransactionsReportScreen(),
        AppRoutes.milkReport: (context) => const MilkReportScreen(),
        AppRoutes.cattleReport: (context) => const CattleReportScreen(),
        AppRoutes.eventsReport: (context) => const EventsReportScreen(),
        AppRoutes.breedingReport: (context) => const BreedingReportScreen(),
        AppRoutes.pregnanciesReport: (context) =>
            const PregnanciesReportScreen(),
        AppRoutes.weightReport: (context) => const WeightReportScreen(),
        AppRoutes.healthReport: (context) => const HealthReportScreen(),
      },
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en', ''),
      ],
    );
  }

  @override
  void dispose() {
    // Clean up services when the app is closed
    IsarInitializer.dispose();
    super.dispose();
  }
}

class _HealthModuleRemovedScreen extends StatelessWidget {
  const _HealthModuleRemovedScreen();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Health'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.health_and_safety_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Health Module Removed',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Health functionality has been removed',
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class DatabaseErrorScreen extends StatelessWidget {
  const DatabaseErrorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 64,
              ),
              const SizedBox(height: 16),
              const Text(
                'Database Error',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'The database failed to initialize. Please restart the app or contact support.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  // You could add a retry mechanism here if needed
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => const CattleManagerApp(
                        isarInitialized: false,
                      ),
                    ),
                  );
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
