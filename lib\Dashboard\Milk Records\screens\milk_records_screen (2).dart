import 'package:flutter/material.dart';
import '../models/milk_record.dart';
import '../dialogs/milk_form_dialog.dart';
import '../milk_tabs/production_summary_tab.dart';
import '../milk_tabs/milk_sales_tab.dart';
import '../milk_tabs/alerts_tab.dart';
import '../../Cattle/models/cattle.dart';
import '../../../services/milk_record_service.dart';
import '../../../services/csv_export_service.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class MilkRecordsScreen extends StatefulWidget {
  const MilkRecordsScreen({Key? key}) : super(key: key);

  @override
  MilkRecordsScreenState createState() => MilkRecordsScreenState();
}

class MilkRecordsScreenState extends State<MilkRecordsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<MilkRecord> _milkRecords = [];
  final List<Cattle> _cattle = []; // Ensure this is a List of Cattle

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadMilkRecords();
  }

  Future<void> _loadMilkRecords() async {
    final records = await MilkRecordService().getMilkRecords();
    if (mounted) {
      setState(() {
        _milkRecords.clear();
        _milkRecords.addAll(records);
      });
    }
  }

  Future<void> _syncData() async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final milkRecordService = MilkRecordService();

    scaffoldMessenger.showSnackBar(
      const SnackBar(content: Text('Syncing data...')),
    );

    final success = await milkRecordService.syncData();

    scaffoldMessenger.clearSnackBars();
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Text(success ? 'Data synced successfully' : 'Failed to sync data'),
        backgroundColor: success ? Colors.green : Colors.red,
      ),
    );

    if (success) {
      await _loadMilkRecords();
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Milk Records'),
        actions: [
          IconButton(
            icon: const Icon(Icons.cloud_upload),
            onPressed: _syncData,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Milk Entry'),
            Tab(text: 'Production Summary'),
            Tab(text: 'Milk Sales'),
            Tab(text: 'Alerts'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMilkEntryTab(),
          const ProductionSummaryTab(),
          const MilkSalesTab(),
          const AlertsTab(),
        ],
      ),
      floatingActionButton: _tabController.index == 0
          ? FloatingActionButton(
              onPressed: _addNewRecord,
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildMilkEntryTab() {
    return _milkRecords.isEmpty
        ? const Center(
            child: Text('No milk records yet. Add your first record!'),
          )
        : ListView.builder(
            itemCount: _milkRecords.length,
            itemBuilder: (context, index) {
              final record = _milkRecords[index];
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: ListTile(
                  title: Text('Cattle ID: ${record.cattleId}'),
                  subtitle: Text(
                    'Date: ${record.date.toString().split(' ')[0]}\n'
                    'Shift: ${record.shift}\n'
                    'Quantity: ${record.quantity} Liters',
                  ),
                  isThreeLine: true,
                  trailing: IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () => _deleteRecord(index),
                  ),
                ),
              );
            },
          );
  }

  Future<void> _addNewRecord() async {
    final result = await showDialog<MilkRecord>(
      context: context,
      builder: (context) => MilkFormDialog(cattleList: _cattle), // Pass the cattle list here
    );

    if (result != null) {
      setState(() {
        _milkRecords.add(result);
      });
    }
  }

  void _deleteRecord(int index) {
    setState(() {
      _milkRecords.removeAt(index);
    });
  }

  Future<void> _exportData() async {
    try {
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      scaffoldMessenger.showSnackBar(
        const SnackBar(content: Text('Exporting data...')),
      );

      final csvService = CsvExportService();
      final filePath = await csvService.exportMilkRecords(_milkRecords);

      scaffoldMessenger.clearSnackBars();
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Data exported successfully to: $filePath'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to export data: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
