// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_report_data_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetEventReportDataIsarCollection on Isar {
  IsarCollection<EventReportDataIsar> get eventReportDataIsars =>
      this.collection();
}

const EventReportDataIsarSchema = CollectionSchema(
  name: r'EventReportDataIsar',
  id: 8705705035643219704,
  properties: {
    r'businessId': PropertySchema(
      id: 0,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'cancelledEvents': PropertySchema(
      id: 1,
      name: r'cancelledEvents',
      type: IsarType.long,
    ),
    r'completedEvents': PropertySchema(
      id: 2,
      name: r'completedEvents',
      type: IsarType.long,
    ),
    r'completionRateColors': PropertySchema(
      id: 3,
      name: r'completionRateColors',
      type: IsarType.longList,
    ),
    r'completionRateLabels': PropertySchema(
      id: 4,
      name: r'completionRateLabels',
      type: IsarType.stringList,
    ),
    r'completionRateValues': PropertySchema(
      id: 5,
      name: r'completionRateValues',
      type: IsarType.doubleList,
    ),
    r'createdAt': PropertySchema(
      id: 6,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'endDate': PropertySchema(
      id: 7,
      name: r'endDate',
      type: IsarType.dateTime,
    ),
    r'eventCounts': PropertySchema(
      id: 8,
      name: r'eventCounts',
      type: IsarType.longList,
    ),
    r'eventDates': PropertySchema(
      id: 9,
      name: r'eventDates',
      type: IsarType.dateTimeList,
    ),
    r'eventTypeColors': PropertySchema(
      id: 10,
      name: r'eventTypeColors',
      type: IsarType.longList,
    ),
    r'eventTypeCounts': PropertySchema(
      id: 11,
      name: r'eventTypeCounts',
      type: IsarType.longList,
    ),
    r'eventTypeNames': PropertySchema(
      id: 12,
      name: r'eventTypeNames',
      type: IsarType.stringList,
    ),
    r'filterCriteria': PropertySchema(
      id: 13,
      name: r'filterCriteria',
      type: IsarType.string,
    ),
    r'generatedAt': PropertySchema(
      id: 14,
      name: r'generatedAt',
      type: IsarType.dateTime,
    ),
    r'overdueEvents': PropertySchema(
      id: 15,
      name: r'overdueEvents',
      type: IsarType.long,
    ),
    r'pendingEvents': PropertySchema(
      id: 16,
      name: r'pendingEvents',
      type: IsarType.long,
    ),
    r'reportType': PropertySchema(
      id: 17,
      name: r'reportType',
      type: IsarType.string,
    ),
    r'startDate': PropertySchema(
      id: 18,
      name: r'startDate',
      type: IsarType.dateTime,
    ),
    r'title': PropertySchema(
      id: 19,
      name: r'title',
      type: IsarType.string,
    ),
    r'totalEvents': PropertySchema(
      id: 20,
      name: r'totalEvents',
      type: IsarType.long,
    ),
    r'updatedAt': PropertySchema(
      id: 21,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _eventReportDataIsarEstimateSize,
  serialize: _eventReportDataIsarSerialize,
  deserialize: _eventReportDataIsarDeserialize,
  deserializeProp: _eventReportDataIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'reportType': IndexSchema(
      id: 3559997651334899995,
      name: r'reportType',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'reportType',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _eventReportDataIsarGetId,
  getLinks: _eventReportDataIsarGetLinks,
  attach: _eventReportDataIsarAttach,
  version: '3.1.0+1',
);

int _eventReportDataIsarEstimateSize(
  EventReportDataIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.completionRateColors;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final list = object.completionRateLabels;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.completionRateValues;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.eventCounts;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.eventDates;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.eventTypeColors;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.eventTypeCounts;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final list = object.eventTypeNames;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.filterCriteria;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.reportType;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.title;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _eventReportDataIsarSerialize(
  EventReportDataIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.businessId);
  writer.writeLong(offsets[1], object.cancelledEvents);
  writer.writeLong(offsets[2], object.completedEvents);
  writer.writeLongList(offsets[3], object.completionRateColors);
  writer.writeStringList(offsets[4], object.completionRateLabels);
  writer.writeDoubleList(offsets[5], object.completionRateValues);
  writer.writeDateTime(offsets[6], object.createdAt);
  writer.writeDateTime(offsets[7], object.endDate);
  writer.writeLongList(offsets[8], object.eventCounts);
  writer.writeDateTimeList(offsets[9], object.eventDates);
  writer.writeLongList(offsets[10], object.eventTypeColors);
  writer.writeLongList(offsets[11], object.eventTypeCounts);
  writer.writeStringList(offsets[12], object.eventTypeNames);
  writer.writeString(offsets[13], object.filterCriteria);
  writer.writeDateTime(offsets[14], object.generatedAt);
  writer.writeLong(offsets[15], object.overdueEvents);
  writer.writeLong(offsets[16], object.pendingEvents);
  writer.writeString(offsets[17], object.reportType);
  writer.writeDateTime(offsets[18], object.startDate);
  writer.writeString(offsets[19], object.title);
  writer.writeLong(offsets[20], object.totalEvents);
  writer.writeDateTime(offsets[21], object.updatedAt);
}

EventReportDataIsar _eventReportDataIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = EventReportDataIsar(
    endDate: reader.readDateTimeOrNull(offsets[7]),
    startDate: reader.readDateTimeOrNull(offsets[18]),
  );
  object.businessId = reader.readStringOrNull(offsets[0]);
  object.cancelledEvents = reader.readLongOrNull(offsets[1]);
  object.completedEvents = reader.readLongOrNull(offsets[2]);
  object.completionRateColors = reader.readLongList(offsets[3]);
  object.completionRateLabels = reader.readStringList(offsets[4]);
  object.completionRateValues = reader.readDoubleList(offsets[5]);
  object.createdAt = reader.readDateTimeOrNull(offsets[6]);
  object.eventCounts = reader.readLongList(offsets[8]);
  object.eventDates = reader.readDateTimeList(offsets[9]);
  object.eventTypeColors = reader.readLongList(offsets[10]);
  object.eventTypeCounts = reader.readLongList(offsets[11]);
  object.eventTypeNames = reader.readStringList(offsets[12]);
  object.filterCriteria = reader.readStringOrNull(offsets[13]);
  object.generatedAt = reader.readDateTimeOrNull(offsets[14]);
  object.id = id;
  object.overdueEvents = reader.readLongOrNull(offsets[15]);
  object.pendingEvents = reader.readLongOrNull(offsets[16]);
  object.reportType = reader.readStringOrNull(offsets[17]);
  object.title = reader.readStringOrNull(offsets[19]);
  object.totalEvents = reader.readLongOrNull(offsets[20]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[21]);
  return object;
}

P _eventReportDataIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readLongOrNull(offset)) as P;
    case 2:
      return (reader.readLongOrNull(offset)) as P;
    case 3:
      return (reader.readLongList(offset)) as P;
    case 4:
      return (reader.readStringList(offset)) as P;
    case 5:
      return (reader.readDoubleList(offset)) as P;
    case 6:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 7:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 8:
      return (reader.readLongList(offset)) as P;
    case 9:
      return (reader.readDateTimeList(offset)) as P;
    case 10:
      return (reader.readLongList(offset)) as P;
    case 11:
      return (reader.readLongList(offset)) as P;
    case 12:
      return (reader.readStringList(offset)) as P;
    case 13:
      return (reader.readStringOrNull(offset)) as P;
    case 14:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 15:
      return (reader.readLongOrNull(offset)) as P;
    case 16:
      return (reader.readLongOrNull(offset)) as P;
    case 17:
      return (reader.readStringOrNull(offset)) as P;
    case 18:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 19:
      return (reader.readStringOrNull(offset)) as P;
    case 20:
      return (reader.readLongOrNull(offset)) as P;
    case 21:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _eventReportDataIsarGetId(EventReportDataIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _eventReportDataIsarGetLinks(
    EventReportDataIsar object) {
  return [];
}

void _eventReportDataIsarAttach(
    IsarCollection<dynamic> col, Id id, EventReportDataIsar object) {
  object.id = id;
}

extension EventReportDataIsarByIndex on IsarCollection<EventReportDataIsar> {
  Future<EventReportDataIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  EventReportDataIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<EventReportDataIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<EventReportDataIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(EventReportDataIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(EventReportDataIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<EventReportDataIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<EventReportDataIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension EventReportDataIsarQueryWhereSort
    on QueryBuilder<EventReportDataIsar, EventReportDataIsar, QWhere> {
  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension EventReportDataIsarQueryWhere
    on QueryBuilder<EventReportDataIsar, EventReportDataIsar, QWhereClause> {
  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterWhereClause>
      reportTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'reportType',
        value: [null],
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterWhereClause>
      reportTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'reportType',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterWhereClause>
      reportTypeEqualTo(String? reportType) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'reportType',
        value: [reportType],
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterWhereClause>
      reportTypeNotEqualTo(String? reportType) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [],
              upper: [reportType],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [reportType],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [reportType],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [],
              upper: [reportType],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension EventReportDataIsarQueryFilter on QueryBuilder<EventReportDataIsar,
    EventReportDataIsar, QFilterCondition> {
  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      cancelledEventsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cancelledEvents',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      cancelledEventsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cancelledEvents',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      cancelledEventsEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cancelledEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      cancelledEventsGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cancelledEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      cancelledEventsLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cancelledEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      cancelledEventsBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cancelledEvents',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completedEventsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'completedEvents',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completedEventsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'completedEvents',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completedEventsEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'completedEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completedEventsGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'completedEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completedEventsLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'completedEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completedEventsBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'completedEvents',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateColorsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'completionRateColors',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateColorsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'completionRateColors',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateColorsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'completionRateColors',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateColorsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'completionRateColors',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateColorsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'completionRateColors',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateColorsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'completionRateColors',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateColorsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateColors',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateColorsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateColors',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateColorsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateColors',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateColorsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateColors',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateColorsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateColors',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateColorsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateColors',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'completionRateLabels',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'completionRateLabels',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'completionRateLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'completionRateLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'completionRateLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'completionRateLabels',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'completionRateLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'completionRateLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsElementContains(String value,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'completionRateLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsElementMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'completionRateLabels',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'completionRateLabels',
        value: '',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'completionRateLabels',
        value: '',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateLabels',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateLabels',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateLabels',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateLabels',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateLabels',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateLabelsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateLabels',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateValuesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'completionRateValues',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateValuesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'completionRateValues',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateValuesElementEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'completionRateValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateValuesElementGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'completionRateValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateValuesElementLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'completionRateValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateValuesElementBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'completionRateValues',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateValuesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateValues',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateValuesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateValues',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateValuesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateValues',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateValuesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateValues',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateValuesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateValues',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      completionRateValuesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'completionRateValues',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      endDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      endDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      endDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      endDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      endDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      endDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'endDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventCountsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'eventCounts',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventCountsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'eventCounts',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventCountsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'eventCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventCountsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'eventCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventCountsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'eventCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventCountsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'eventCounts',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventCountsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventCounts',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventCountsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventCounts',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventCountsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventCounts',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventCountsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventCounts',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventCountsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventCounts',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventCountsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventCounts',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventDatesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'eventDates',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventDatesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'eventDates',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventDatesElementEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'eventDates',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventDatesElementGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'eventDates',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventDatesElementLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'eventDates',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventDatesElementBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'eventDates',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventDatesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventDates',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventDatesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventDates',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventDatesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventDates',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventDatesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventDates',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventDatesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventDates',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventDatesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventDates',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeColorsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'eventTypeColors',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeColorsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'eventTypeColors',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeColorsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'eventTypeColors',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeColorsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'eventTypeColors',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeColorsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'eventTypeColors',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeColorsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'eventTypeColors',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeColorsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeColors',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeColorsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeColors',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeColorsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeColors',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeColorsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeColors',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeColorsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeColors',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeColorsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeColors',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeCountsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'eventTypeCounts',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeCountsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'eventTypeCounts',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeCountsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'eventTypeCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeCountsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'eventTypeCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeCountsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'eventTypeCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeCountsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'eventTypeCounts',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeCountsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeCounts',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeCountsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeCounts',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeCountsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeCounts',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeCountsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeCounts',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeCountsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeCounts',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeCountsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeCounts',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'eventTypeNames',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'eventTypeNames',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'eventTypeNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'eventTypeNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'eventTypeNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'eventTypeNames',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'eventTypeNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'eventTypeNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'eventTypeNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesElementMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'eventTypeNames',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'eventTypeNames',
        value: '',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'eventTypeNames',
        value: '',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeNames',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeNames',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeNames',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeNames',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeNames',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      eventTypeNamesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'eventTypeNames',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      filterCriteriaIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'filterCriteria',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      filterCriteriaIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'filterCriteria',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      filterCriteriaEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      filterCriteriaGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      filterCriteriaLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      filterCriteriaBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'filterCriteria',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      filterCriteriaStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      filterCriteriaEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      filterCriteriaContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      filterCriteriaMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'filterCriteria',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      filterCriteriaIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filterCriteria',
        value: '',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      filterCriteriaIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'filterCriteria',
        value: '',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      generatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'generatedAt',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      generatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'generatedAt',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      generatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      generatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      generatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      generatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'generatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      overdueEventsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'overdueEvents',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      overdueEventsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'overdueEvents',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      overdueEventsEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'overdueEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      overdueEventsGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'overdueEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      overdueEventsLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'overdueEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      overdueEventsBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'overdueEvents',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      pendingEventsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'pendingEvents',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      pendingEventsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'pendingEvents',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      pendingEventsEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'pendingEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      pendingEventsGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'pendingEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      pendingEventsLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'pendingEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      pendingEventsBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'pendingEvents',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      reportTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reportType',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      reportTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reportType',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      reportTypeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      reportTypeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      reportTypeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      reportTypeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'reportType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      reportTypeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      reportTypeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      reportTypeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      reportTypeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'reportType',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      reportTypeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reportType',
        value: '',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      reportTypeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'reportType',
        value: '',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      startDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      startDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      startDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      startDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      startDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      startDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'startDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      titleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      titleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      titleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      titleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      titleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      titleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'title',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      titleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      titleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      titleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      titleMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'title',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      titleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      titleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      totalEventsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'totalEvents',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      totalEventsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'totalEvents',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      totalEventsEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      totalEventsGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      totalEventsLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalEvents',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      totalEventsBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalEvents',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension EventReportDataIsarQueryObject on QueryBuilder<EventReportDataIsar,
    EventReportDataIsar, QFilterCondition> {}

extension EventReportDataIsarQueryLinks on QueryBuilder<EventReportDataIsar,
    EventReportDataIsar, QFilterCondition> {}

extension EventReportDataIsarQuerySortBy
    on QueryBuilder<EventReportDataIsar, EventReportDataIsar, QSortBy> {
  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByCancelledEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cancelledEvents', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByCancelledEventsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cancelledEvents', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByCompletedEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedEvents', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByCompletedEventsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedEvents', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByFilterCriteria() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByFilterCriteriaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByGeneratedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByOverdueEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'overdueEvents', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByOverdueEventsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'overdueEvents', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByPendingEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pendingEvents', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByPendingEventsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pendingEvents', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByReportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByReportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByTotalEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalEvents', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByTotalEventsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalEvents', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension EventReportDataIsarQuerySortThenBy
    on QueryBuilder<EventReportDataIsar, EventReportDataIsar, QSortThenBy> {
  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByCancelledEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cancelledEvents', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByCancelledEventsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cancelledEvents', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByCompletedEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedEvents', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByCompletedEventsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedEvents', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByFilterCriteria() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByFilterCriteriaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByGeneratedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByOverdueEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'overdueEvents', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByOverdueEventsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'overdueEvents', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByPendingEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pendingEvents', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByPendingEventsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pendingEvents', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByReportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByReportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByTotalEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalEvents', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByTotalEventsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalEvents', Sort.desc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension EventReportDataIsarQueryWhereDistinct
    on QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct> {
  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByCancelledEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cancelledEvents');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByCompletedEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'completedEvents');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByCompletionRateColors() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'completionRateColors');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByCompletionRateLabels() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'completionRateLabels');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByCompletionRateValues() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'completionRateValues');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'endDate');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByEventCounts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'eventCounts');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByEventDates() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'eventDates');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByEventTypeColors() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'eventTypeColors');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByEventTypeCounts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'eventTypeCounts');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByEventTypeNames() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'eventTypeNames');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByFilterCriteria({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'filterCriteria',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'generatedAt');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByOverdueEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'overdueEvents');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByPendingEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'pendingEvents');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByReportType({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'reportType', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'startDate');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByTitle({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'title', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByTotalEvents() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalEvents');
    });
  }

  QueryBuilder<EventReportDataIsar, EventReportDataIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension EventReportDataIsarQueryProperty
    on QueryBuilder<EventReportDataIsar, EventReportDataIsar, QQueryProperty> {
  QueryBuilder<EventReportDataIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<EventReportDataIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<EventReportDataIsar, int?, QQueryOperations>
      cancelledEventsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cancelledEvents');
    });
  }

  QueryBuilder<EventReportDataIsar, int?, QQueryOperations>
      completedEventsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'completedEvents');
    });
  }

  QueryBuilder<EventReportDataIsar, List<int>?, QQueryOperations>
      completionRateColorsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'completionRateColors');
    });
  }

  QueryBuilder<EventReportDataIsar, List<String>?, QQueryOperations>
      completionRateLabelsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'completionRateLabels');
    });
  }

  QueryBuilder<EventReportDataIsar, List<double>?, QQueryOperations>
      completionRateValuesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'completionRateValues');
    });
  }

  QueryBuilder<EventReportDataIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<EventReportDataIsar, DateTime?, QQueryOperations>
      endDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'endDate');
    });
  }

  QueryBuilder<EventReportDataIsar, List<int>?, QQueryOperations>
      eventCountsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'eventCounts');
    });
  }

  QueryBuilder<EventReportDataIsar, List<DateTime>?, QQueryOperations>
      eventDatesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'eventDates');
    });
  }

  QueryBuilder<EventReportDataIsar, List<int>?, QQueryOperations>
      eventTypeColorsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'eventTypeColors');
    });
  }

  QueryBuilder<EventReportDataIsar, List<int>?, QQueryOperations>
      eventTypeCountsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'eventTypeCounts');
    });
  }

  QueryBuilder<EventReportDataIsar, List<String>?, QQueryOperations>
      eventTypeNamesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'eventTypeNames');
    });
  }

  QueryBuilder<EventReportDataIsar, String?, QQueryOperations>
      filterCriteriaProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'filterCriteria');
    });
  }

  QueryBuilder<EventReportDataIsar, DateTime?, QQueryOperations>
      generatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'generatedAt');
    });
  }

  QueryBuilder<EventReportDataIsar, int?, QQueryOperations>
      overdueEventsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'overdueEvents');
    });
  }

  QueryBuilder<EventReportDataIsar, int?, QQueryOperations>
      pendingEventsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'pendingEvents');
    });
  }

  QueryBuilder<EventReportDataIsar, String?, QQueryOperations>
      reportTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reportType');
    });
  }

  QueryBuilder<EventReportDataIsar, DateTime?, QQueryOperations>
      startDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'startDate');
    });
  }

  QueryBuilder<EventReportDataIsar, String?, QQueryOperations> titleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'title');
    });
  }

  QueryBuilder<EventReportDataIsar, int?, QQueryOperations>
      totalEventsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalEvents');
    });
  }

  QueryBuilder<EventReportDataIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}
