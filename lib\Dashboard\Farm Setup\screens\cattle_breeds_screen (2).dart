// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../Cattle/models/breed_category.dart';
import '../../Cattle/models/animal_type.dart';
import '../../../services/database_helper.dart';
import '../../../widgets/icon_picker.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class CattleBreedsScreen extends StatefulWidget {
  const CattleBreedsScreen({Key? key}) : super(key: key);

  @override
  State<CattleBreedsScreen> createState() => _CattleBreedsScreenState();
}

class _CattleBreedsScreenState extends State<CattleBreedsScreen> {
  final List<BreedCategory> _breeds = [];
  bool _isLoading = true;
  List<AnimalType> _animalTypes = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    final breeds = await DatabaseHelper.instance.getCattleBreeds();
    final animalTypes = await DatabaseHelper.instance.getAnimalTypes();
    setState(() {
      _breeds.clear();
      _breeds.addAll(breeds);
      _animalTypes = animalTypes;
      _isLoading = false;
    });
  }

  Future<void> _showAddEditBreedDialog(BuildContext context,
      [BreedCategory? breed]) async {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController(text: breed?.name ?? '');
    String? selectedAnimalTypeId = breed?.animalTypeId;
    IconData selectedIcon = breed?.icon ?? Icons.pets_outlined;

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (dialogContext, setState) => AlertDialog(
          title: Text(
            breed == null ? 'Add Breed' : 'Edit Breed',
            style: const TextStyle(
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                DropdownButtonFormField<String>(
                  value: selectedAnimalTypeId,
                  decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Animal Type'),
                  items: _animalTypes.map((type) {
                    return DropdownMenuItem(
                      value: type.id,
                      child: Text(type.name),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedAnimalTypeId = value;
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select an animal type';
                    }
                    return null;
                  },
                ),
                SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                TextFormField(
                  controller: nameController,
                  decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Breed Name'),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a breed name';
                    }
                    return null;
                  },
                ),
                SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                Row(
                  children: [
                    Icon(selectedIcon),
                    const SizedBox(width: 8),
                    TextButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) => IconPicker(
                            selectedIcon: selectedIcon,
                            onIconSelected: (icon) {
                              setState(() {
                                selectedIcon = icon;
                              });
                              Navigator.pop(context);
                            },
                          ),
                        );
                      },
                      child: const Text('Select Icon'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.grey),
              ),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7D32),
                foregroundColor: Colors.white,
              ),
              onPressed: () async {
                try {
                  if (formKey.currentState!.validate()) {
                    final name = nameController.text.trim();

                    // Check for duplicate names within the same animal type
                    if (_breeds.any((b) =>
                        b.animalTypeId == selectedAnimalTypeId &&
                        b.name.toLowerCase() == name.toLowerCase() &&
                        (breed == null || b.id != breed.id))) {
                      ScaffoldMessenger.of(dialogContext).showSnackBar(
                        const SnackBar(
                          content: Text(
                              'A breed with this name already exists for this animal type'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }

                    // Store context-dependent objects before any async operations
                    final navigator = Navigator.of(dialogContext);
                    final messenger = ScaffoldMessenger.of(dialogContext);

                    try {
                      if (breed == null) {
                        // Add new breed
                        final newBreed = BreedCategory(
                          id: const Uuid().v4(),
                          name: name,
                          animalTypeId: selectedAnimalTypeId!,
                          description: '',
                          icon: selectedIcon,
                          createdAt: DateTime.now(),
                          updatedAt: DateTime.now(),
                        );
                        await DatabaseHelper.instance
                            .createCattleBreed(newBreed);
                      } else {
                        // Update existing breed
                        final updatedBreed = BreedCategory(
                          id: breed.id,
                          name: name,
                          animalTypeId: selectedAnimalTypeId!,
                          description: breed.description,
                          icon: selectedIcon,
                          createdAt: breed.createdAt,
                          updatedAt: DateTime.now(),
                        );
                        await DatabaseHelper.instance
                            .updateCattleBreed(updatedBreed);
                      }

                      if (mounted) {
                        navigator.pop();
                      }

                      await _loadData();
                    } on Exception catch (e) {
                      if (mounted) {
                        messenger.showSnackBar(
                          SnackBar(
                            content:
                                Text('Error saving breed: ${e.toString()}'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(dialogContext).showSnackBar(
                      const SnackBar(
                        content: Text('Error saving breed'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              child: Text(breed == null ? 'Add' : 'Save'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _deleteBreed(BreedCategory breed) async {
    try {
      final confirm = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Delete Breed'),
          content: Text('Delete ${breed.name}?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7D32),
                foregroundColor: Colors.white,
              ),
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Delete'),
            ),
          ],
        ),
      );

      if (confirm == true) {
        try {
          await DatabaseHelper.instance.deleteCattleBreed(breed.id);
          _loadData();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Breed deleted successfully')),
            );
          }
        } on Exception catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error deleting breed: ${e.toString()}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting breed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    const mainColor = Color(0xFF2E7D32);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Cattle Breeds',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: mainColor,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView.builder(
              padding: const EdgeInsets.all(8.0),
              itemCount: _breeds.length,
              itemBuilder: (context, index) {
                final breed = _breeds[index];
                final animalType = _animalTypes.firstWhere(
                  (type) => type.id == breed.animalTypeId,
                  orElse: () => AnimalType(
                    id: '',
                    name: 'Unknown',
                    icon: Icons.help_outline,
                    defaultGestationDays: 0,
                    defaultHeatCycleDays: 0,
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now(),
                  ),
                );
                return Card(
                  elevation: 2,
                  margin: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    leading: const Icon(
                      Icons.pets_outlined,
                      size: 32,
                      color: mainColor,
                    ),
                    title: Text(
                      breed.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                    ),
                    subtitle: Text(
                      'Animal Type: ${animalType.name}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(
                            Icons.edit_outlined,
                            size: 24,
                          ),
                          color: mainColor,
                          onPressed: () =>
                              _showAddEditBreedDialog(context, breed),
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete_outline),
                          color: Colors.red,
                          onPressed: () => _deleteBreed(breed),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddEditBreedDialog(context),
        backgroundColor: mainColor,
        child: const Icon(Icons.add),
      ),
    );
  }
}
