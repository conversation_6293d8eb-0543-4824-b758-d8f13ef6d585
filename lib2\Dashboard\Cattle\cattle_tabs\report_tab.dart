class HealthRecord {
  final String id;
  final String cattleId;
  final DateTime date;
  final String condition;
  final String treatment;
  final String notes;
  final String veterinarian;
  final double cost;

  HealthRecord({
    required this.id,
    required this.cattleId,
    required this.date,
    required this.condition,
    required this.treatment,
    this.notes = '',
    this.veterinarian = '',
    this.cost = 0.0,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'cattleId': 