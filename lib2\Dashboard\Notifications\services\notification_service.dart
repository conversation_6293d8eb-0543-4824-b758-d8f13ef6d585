import 'dart:async';
import 'package:logging/logging.dart';
import '../../../services/streams/record_keys.dart';

/// Callback signature for notification listeners
typedef NotificationCallback = void Function(String recordType, String action,
    String cattleId, Map<String, dynamic> data);

/// Service for managing notifications about database changes
class NotificationService {
  // Singleton implementation
  static NotificationService? _instance;
  static NotificationService get instance =>
      _instance ??= NotificationService._internal();

  // Private constructor
  NotificationService._internal();

  final Logger _logger = Logger('NotificationService');
  final Map<String, StreamController<Map<String, dynamic>>> _controllers = {};
  final List<NotificationCallback> _listeners = [];

  NotificationService() {
    final recordTypes = [
      RecordKeys.cattleRecords.toString(),
      RecordKeys.breedingRecords.toString(),
      RecordKeys.pregnancyRecords.toString(),
      RecordKeys.healthRecords.toString(),
      RecordKeys.milkRecords.toString(),
      RecordKeys.eventRecords.toString(),
    ];

    for (final type in recordTypes) {
      _controllers[type] = StreamController<Map<String, dynamic>>.broadcast();
    }
  }

  // Register a listener
  void addListener(NotificationCallback listener) {
    _listeners.add(listener);
    _logger.fine(
        'Added notification listener, total listeners: ${_listeners.length}');
  }

  // Remove a listener
  void removeListener(NotificationCallback listener) {
    _listeners.remove(listener);
    _logger.fine(
        'Removed notification listener, total listeners: ${_listeners.length}');
  }

  // Notify all listeners about a database change
  void notify(String recordType, String action, String cattleId,
      Map<String, dynamic> data) {
    _logger.fine('Notifying about $action on $recordType for cattle $cattleId');

    try {
      for (final listener in _listeners) {
        try {
          listener(recordType, action, cattleId, data);
        } catch (e) {
          _logger.warning('Error in notification listener', e);
        }
      }
    } catch (e) {
      _logger.severe('Error sending notifications', e);
    }
  }

  // Get a stream for a specific record type
  Stream<Map<String, dynamic>> getStream(String recordType) {
    if (!_controllers.containsKey(recordType)) {
      throw ArgumentError('No stream available for record type: $recordType');
    }
    return _controllers[recordType]!.stream;
  }

  // Dispose of all controllers
  void dispose() {
    for (final controller in _controllers.values) {
      controller.close();
    }
    _controllers.clear();
  }
}
