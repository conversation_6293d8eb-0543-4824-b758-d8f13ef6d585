import 'package:flutter/material.dart';
import '../../../Dashboard/Events/models/event_type_isar.dart';
import '../services/farm_setup_handler.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../../../utils/message_utils.dart';
import '../../../widgets/dialogs/standard_form_dialog.dart';
import '../../../widgets/dialogs/standard_form_builder.dart';

class EventTypeDialog extends StatefulWidget {
  final EventTypeIsar? eventType;

  const EventTypeDialog({Key? key, this.eventType}) : super(key: key);

  @override
  State<EventTypeDialog> createState() => _EventTypeDialogState();
}

class _EventTypeDialogState extends State<EventTypeDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _farmSetupHandler = FarmSetupHandler.instance;
  bool _isProcessing = false;
  late Color _selectedColor;

  @override
  void initState() {
    super.initState();
    if (widget.eventType != null) {
      _nameController.text = widget.eventType!.name;
      _descriptionController.text = widget.eventType?.description ?? '';
      _selectedColor = widget.eventType!.color;
    } else {
      _selectedColor = Colors.blue;
    }
  }

  Future<void> _handleSubmit() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final eventType = widget.eventType ?? EventTypeIsar();
      eventType.name = _nameController.text;
      eventType.description = _descriptionController.text;
      eventType.color = _selectedColor;

      if (widget.eventType == null) {
        eventType.id = 0; // Let Isar auto-increment
        eventType.businessId = const Uuid().v4();
        eventType.createdAt = DateTime.now();
      }

      eventType.updatedAt = DateTime.now();

      await _farmSetupHandler.saveEventType(eventType);

      if (mounted) {
        Navigator.of(context).pop(eventType);
      }
    } catch (e) {
      if (mounted) {
        FarmSetupMessageUtils.showError(context, 'Error saving event type: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  List<FormFieldConfig> _buildFormFields() {
    return [
      // Name
      TextFieldConfig(
        labelText: 'Name',
        controller: _nameController,
        prefixIcon: Icons.label,
        iconColor: DialogThemes.farmSetup,
        hintText: 'Enter event type name',
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Please enter a name';
          }
          return null;
        },
      ),

      // Description
      MultilineFieldConfig(
        labelText: 'Description',
        controller: _descriptionController,
        prefixIcon: Icons.description,
        iconColor: DialogThemes.farmSetup,
        hintText: 'Enter event type description',
        maxLines: 3,
      ),

      // Color picker
      CustomFieldConfig(
        builder: () => Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: _selectedColor,
                shape: BoxShape.circle,
                border: Border.all(color: const Color(0xFFE0E0E0)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: const Text('Pick a color'),
                        content: SingleChildScrollView(
                          child: BlockPicker(
                            pickerColor: _selectedColor,
                            onColorChanged: (Color color) {
                              setState(() {
                                _selectedColor = color;
                              });
                              Navigator.of(context).pop();
                            },
                          ),
                        ),
                      );
                    },
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: DialogThemes.farmSetup,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                icon: const Icon(Icons.color_lens),
                label: const Text('Choose Color'),
              ),
            ),
          ],
        ),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return StandardFormDialog(
      title: widget.eventType == null ? 'Create Event Type' : 'Edit Event Type',
      headerColor: DialogThemes.farmSetup,
      headerIcon: Icons.event_note,
      isSaving: _isProcessing,
      onSave: _handleSubmit,
      saveText: widget.eventType == null ? 'Create' : 'Update',
      content: StandardFormBuilder(
        formKey: _formKey,
        fields: _buildFormFields(),
      ),
    );
  }
}
