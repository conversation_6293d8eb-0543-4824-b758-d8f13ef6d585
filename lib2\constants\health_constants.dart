import 'package:flutter/material.dart';

/// Health-related constants and predefined data for the cattle management app
class HealthConstants {
  
  /// Common health conditions by animal type
  static const Map<String, List<String>> commonConditions = {
    'cow': [
      'Mastitis',
      'Lameness',
      'Respiratory Infection',
      'Digestive Issues',
      'Reproductive Problems',
      'Milk Fever',
      'Ketosis',
      'Bloat',
      'Foot Rot',
      'Pink Eye',
      'Pneumonia',
      'Diarrhea',
      'Fever',
      'Loss of Appetite',
      'Injury',
      'Skin Condition',
      'Parasites',
      'Vitamin Deficiency',
      'Mineral Deficiency',
      'Other',
    ],
    'buffalo': [
      'Mastitis',
      'Lameness',
      'Respiratory Infection',
      'Digestive Issues',
      'Reproductive Problems',
      'Foot and Mouth Disease',
      'Hemorrhagic Septicemia',
      'Black Quarter',
      'Anthrax',
      'Parasites',
      'Skin Condition',
      'Fever',
      'Diarrhea',
      'Loss of Appetite',
      'Injury',
      'Other',
    ],
    'goat': [
      'Pneumonia',
      'Parasites',
      'Foot Rot',
      'Caprine Arthritis Encephalitis',
      'Caseous Lymphadenitis',
      'Enterotoxemia',
      'Pregnancy Toxemia',
      'Mastitis',
      'Skin Condition',
      'Digestive Issues',
      'Respiratory Infection',
      'Fever',
      'Diarrhea',
      'Loss of Appetite',
      'Injury',
      'Other',
    ],
    'sheep': [
      'Pneumonia',
      'Parasites',
      'Foot Rot',
      'Scrapie',
      'Enterotoxemia',
      'Pregnancy Toxemia',
      'Mastitis',
      'Skin Condition',
      'Digestive Issues',
      'Respiratory Infection',
      'Fever',
      'Diarrhea',
      'Loss of Appetite',
      'Injury',
      'Other',
    ],
    'horse': [
      'Colic',
      'Lameness',
      'Respiratory Infection',
      'Skin Condition',
      'Eye Problems',
      'Dental Issues',
      'Parasites',
      'Laminitis',
      'Thrush',
      'Rain Rot',
      'Fever',
      'Loss of Appetite',
      'Injury',
      'Behavioral Issues',
      'Other',
    ],
    'general': [
      'Fever',
      'Loss of Appetite',
      'Diarrhea',
      'Respiratory Issues',
      'Skin Condition',
      'Injury',
      'Parasites',
      'Digestive Issues',
      'Lameness',
      'Eye Problems',
      'Behavioral Changes',
      'Weight Loss',
      'Dehydration',
      'Other',
    ],
  };

  /// Common treatments and medicines
  static const List<String> commonTreatments = [
    'Antibiotics',
    'Anti-inflammatory',
    'Pain Relief',
    'Deworming',
    'Vitamin Injection',
    'Mineral Supplement',
    'Fluid Therapy',
    'Wound Care',
    'Topical Treatment',
    'Oral Medication',
    'Injectable Medicine',
    'Supportive Care',
    'Isolation',
    'Rest and Monitoring',
    'Dietary Changes',
    'Environmental Management',
    'Surgical Intervention',
    'Physical Therapy',
    'Alternative Treatment',
    'Other',
  ];

  /// Common medicines by category
  static const Map<String, List<String>> commonMedicines = {
    'Antibiotics': [
      'Penicillin',
      'Oxytetracycline',
      'Amoxicillin',
      'Enrofloxacin',
      'Tylosin',
      'Ceftiofur',
      'Florfenicol',
      'Trimethoprim-Sulfa',
    ],
    'Anti-inflammatory': [
      'Flunixin Meglumine',
      'Phenylbutazone',
      'Meloxicam',
      'Ketoprofen',
      'Aspirin',
      'Dexamethasone',
    ],
    'Dewormers': [
      'Ivermectin',
      'Fenbendazole',
      'Albendazole',
      'Levamisole',
      'Doramectin',
      'Moxidectin',
    ],
    'Vitamins': [
      'Vitamin A',
      'Vitamin D',
      'Vitamin E',
      'Vitamin B Complex',
      'Vitamin C',
      'Multi-vitamin',
    ],
    'Minerals': [
      'Calcium',
      'Phosphorus',
      'Magnesium',
      'Iron',
      'Zinc',
      'Selenium',
      'Copper',
    ],
  };

  /// Common vaccines by animal type
  static const Map<String, List<String>> commonVaccines = {
    'cow': [
      'IBR/BVD/PI3/BRSV',
      'Clostridial (7-way)',
      'Leptospirosis',
      'Vibriosis',
      'Anthrax',
      'Rabies',
      'Foot and Mouth Disease',
      'Brucellosis',
      'Blackleg',
      'Pasteurella',
    ],
    'buffalo': [
      'Foot and Mouth Disease',
      'Hemorrhagic Septicemia',
      'Black Quarter',
      'Anthrax',
      'Brucellosis',
      'Rabies',
      'Pasteurella',
    ],
    'goat': [
      'CDT (Clostridium)',
      'Pneumonia',
      'Caseous Lymphadenitis',
      'Rabies',
      'Foot and Mouth Disease',
    ],
    'sheep': [
      'CDT (Clostridium)',
      'Pneumonia',
      'Rabies',
      'Foot and Mouth Disease',
    ],
    'horse': [
      'Tetanus',
      'Eastern/Western Encephalitis',
      'West Nile Virus',
      'Rabies',
      'Influenza',
      'Rhinopneumonitis',
      'Strangles',
    ],
  };

  /// Dosage units
  static const List<String> dosageUnits = [
    'mg',
    'g',
    'kg',
    'ml',
    'L',
    'cc',
    'IU',
    'tablets',
    'capsules',
    'drops',
    'pumps',
    'doses',
  ];

  /// Treatment status options
  static const List<String> treatmentStatus = [
    'Active',
    'Completed',
    'Discontinued',
    'On Hold',
  ];

  /// Health record status options (different from treatment status)
  static const List<String> healthStatus = [
    'Healthy',
    'Sick',
    'Recovering',
    'Cured',
    'Chronic',
    'Dead',
    'Under Observation',
  ];

  /// Health record types
  static const List<String> recordTypes = [
    'General Health',
    'Treatment',
    'Vaccination',
    'Checkup',
    'Emergency',
    'Follow-up',
    'Preventive',
  ];

  /// Severity levels
  static const List<String> severityLevels = [
    'Low',
    'Medium',
    'High',
    'Critical',
  ];

  /// Vaccine types
  static const List<String> vaccineTypes = [
    'Core',
    'Non-Core',
    'Combination',
    'Live',
    'Killed',
    'Modified Live',
    'Recombinant',
  ];

  /// Vaccination frequencies
  static const List<String> vaccinationFrequencies = [
    'Annual',
    'Semi-annual',
    'One-time',
    'As needed',
    'Every 6 months',
    'Every 2 years',
    'Every 3 years',
    'Monthly',
    'Quarterly',
  ];

  /// Medication categories
  static const List<String> medicationCategories = [
    'Antibiotics',
    'Anti-inflammatory',
    'Pain Relief',
    'Deworming',
    'Vitamin Injection',
    'Mineral Supplement',
    'Fluid Therapy',
    'Wound Care',
    'Topical Treatment',
    'Oral Medication',
    'Injectable Medicine',
    'Supportive Care',
    'Other',
  ];

  /// Get conditions for specific animal type
  static List<String> getConditionsForAnimalType(String? animalType) {
    if (animalType == null) return commonConditions['general'] ?? [];
    
    final type = animalType.toLowerCase();
    return commonConditions[type] ?? commonConditions['general'] ?? [];
  }

  /// Get vaccines for specific animal type
  static List<String> getVaccinesForAnimalType(String? animalType) {
    if (animalType == null) return [];
    
    final type = animalType.toLowerCase();
    return commonVaccines[type] ?? [];
  }

  /// Get medicines by category
  static List<String> getMedicinesByCategory(String category) {
    return commonMedicines[category] ?? [];
  }

  /// Get all medicines (flattened list)
  static List<String> getAllMedicines() {
    final allMedicines = <String>[];
    for (final medicines in commonMedicines.values) {
      allMedicines.addAll(medicines);
    }
    return allMedicines..sort();
  }

  /// Health condition colors for UI (avoiding orange, yellow, grey, amber, brown)
  static const Map<String, Color> conditionColors = {
    'Mastitis': Colors.red,
    'Lameness': Colors.deepPurple,
    'Respiratory Infection': Colors.blue,
    'Digestive Issues': Colors.indigo,
    'Reproductive Problems': Colors.pink,
    'Fever': Colors.red,
    'Injury': Colors.purple,
    'Parasites': Colors.green,
    'Skin Condition': Colors.teal,
    'Other': Colors.blueGrey,
  };

  /// Treatment status colors for UI
  static const Map<String, Color> treatmentStatusColors = {
    'Active': Colors.green,
    'Completed': Colors.blue,
    'Discontinued': Colors.red,
    'On Hold': Colors.deepPurple,
  };

  /// Health status colors for UI
  static const Map<String, Color> healthStatusColors = {
    'Healthy': Colors.green,
    'Sick': Colors.red,
    'Recovering': Colors.blue,
    'Cured': Colors.teal,
    'Chronic': Colors.deepPurple,
    'Dead': Colors.black,
    'Under Observation': Colors.indigo,
  };

  /// Severity level colors for UI
  static const Map<String, Color> severityColors = {
    'Low': Colors.green,
    'Medium': Colors.blue,
    'High': Colors.deepPurple,
    'Critical': Colors.red,
  };

  /// Get color for health condition
  static Color getConditionColor(String? condition) {
    if (condition == null) return Colors.blueGrey;
    return conditionColors[condition] ?? Colors.blueGrey;
  }

  /// Get color for treatment status
  static Color getTreatmentStatusColor(String? status) {
    if (status == null) return Colors.blueGrey;
    return treatmentStatusColors[status] ?? Colors.blueGrey;
  }

  /// Get color for health status
  static Color getHealthStatusColor(String? status) {
    if (status == null) return Colors.blueGrey;
    return healthStatusColors[status] ?? Colors.blueGrey;
  }

  /// Get color for severity level
  static Color getSeverityColor(String? severity) {
    if (severity == null) return Colors.blueGrey;
    return severityColors[severity] ?? Colors.blueGrey;
  }
}
