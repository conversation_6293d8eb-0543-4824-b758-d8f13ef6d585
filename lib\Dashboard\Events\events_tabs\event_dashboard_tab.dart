import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:logging/logging.dart';

import '../services/event_stream_service.dart';
import '../models/event_isar.dart';
import '../../../services/database/database_helper.dart';

class EventDashboardTab extends StatefulWidget {
  const EventDashboardTab({Key? key}) : super(key: key);

  @override
  State<EventDashboardTab> createState() => _EventDashboardTabState();
}

class _EventDashboardTabState extends State<EventDashboardTab> {
  static final Logger _logger = Logger('EventDashboardTab');
  final EventStreamService _eventStreamService = EventStreamService.instance;

  Map<String, dynamic> _analytics = {};
  List<EventIsar> _todayEvents = [];
  List<EventIsar> _upcomingEvents = [];
  List<EventIsar> _overdueEvents = [];
  bool _isLoading = true;

  // Responsive helper methods
  double _getResponsiveFontSize() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 11.0; // Small screens
    } else if (screenWidth < 600) {
      return 12.0; // Medium screens
    } else {
      return 13.0; // Large screens
    }
  }

  double _getResponsiveIconSize() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 20.0; // Small screens
    } else if (screenWidth < 600) {
      return 22.0; // Medium screens
    } else {
      return 24.0; // Large screens
    }
  }

  double _getResponsivePadding() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 12.0; // Small screens
    } else if (screenWidth < 600) {
      return 16.0; // Medium screens
    } else {
      return 20.0; // Large screens
    }
  }

  double _getResponsiveSpacing() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 16.0; // Small screens
    } else if (screenWidth < 600) {
      return 20.0; // Medium screens
    } else {
      return 24.0; // Large screens
    }
  }

  @override
  void initState() {
    super.initState();
    _logger.info('⚡ EventDashboardTab: Initializing with instant data loading...');
    _loadDataInstantly();
  }

  Future<void> _loadDataInstantly() async {
    try {
      _logger.info('⚡ EventDashboardTab: Loading data directly from database...');

      // Initialize service first (if not already initialized)
      await _eventStreamService.initialize();

      // Load data directly from database - NO WAITING FOR STREAMS
      final dbHelper = DatabaseHelper.instance;
      final allEvents = await dbHelper.eventsHandler.getAllEvents();

      _logger.info('⚡ EventDashboardTab: Loaded ${allEvents.length} events from database');

      // Calculate analytics immediately
      final analytics = _calculateAnalytics(allEvents);
      final todayEvents = _getTodayEvents(allEvents);
      final upcomingEvents = _getUpcomingEvents(allEvents);
      final overdueEvents = _getOverdueEvents(allEvents);

      // Update UI immediately - NO DELAYS
      if (mounted) {
        setState(() {
          _analytics = analytics;
          _todayEvents = todayEvents;
          _upcomingEvents = upcomingEvents.take(5).toList();
          _overdueEvents = overdueEvents.take(5).toList();
          _isLoading = false;
        });
        _logger.info('⚡ EventDashboardTab: UI updated instantly - Total: ${analytics['totalEvents']}, Today: ${todayEvents.length}, Upcoming: ${upcomingEvents.length}, Overdue: ${overdueEvents.length}');
      }

      // Set up streams for real-time updates AFTER initial load
      _setupRealTimeStreams();

    } catch (e) {
      _logger.severe('❌ EventDashboardTab: Error loading data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _setupRealTimeStreams() {
    _logger.info('🔄 EventDashboardTab: Setting up real-time streams for updates...');

    // Listen to analytics stream for real-time updates only
    _eventStreamService.eventAnalyticsStream.listen((analytics) {
      if (mounted) {
        setState(() {
          _analytics = analytics;
        });
      }
    });

    // Listen to today's events for real-time updates only
    _eventStreamService.todayEventsStream.listen((events) {
      if (mounted) {
        setState(() {
          _todayEvents = events;
        });
      }
    });

    // Listen to upcoming events for real-time updates only
    _eventStreamService.upcomingEventsStream.listen((events) {
      if (mounted) {
        setState(() {
          _upcomingEvents = events.take(5).toList();
        });
      }
    });

    // Listen to overdue events for real-time updates only
    _eventStreamService.overdueEventsStream.listen((events) {
      if (mounted) {
        setState(() {
          _overdueEvents = events.take(5).toList();
        });
      }
    });
  }

  Map<String, dynamic> _calculateAnalytics(List<EventIsar> events) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    final totalEvents = events.length;
    final completedEvents = events.where((e) => e.isCompleted).length;
    final pendingEvents = events.where((e) => !e.isCompleted && !e.isMissed).length;
    final overdueEvents = events.where((e) =>
      e.eventDate != null &&
      e.eventDate!.isBefore(today) &&
      !e.isCompleted
    ).length;

    final todayEvents = events.where((e) {
      if (e.eventDate == null) return false;
      final eventDate = DateTime(e.eventDate!.year, e.eventDate!.month, e.eventDate!.day);
      return eventDate.isAtSameMomentAs(today);
    }).length;

    final upcomingEvents = events.where((e) =>
      e.eventDate != null &&
      e.eventDate!.isAfter(today) &&
      e.eventDate!.isBefore(now.add(const Duration(days: 7)))
    ).length;

    // Event type distribution
    final typeDistribution = <String, int>{};
    for (final event in events) {
      final typeName = event.type?.getDisplayName() ?? 'Unknown';
      typeDistribution[typeName] = (typeDistribution[typeName] ?? 0) + 1;
    }

    // Priority distribution
    final priorityDistribution = <String, int>{};
    for (final event in events) {
      final priority = event.priority.name;
      priorityDistribution[priority] = (priorityDistribution[priority] ?? 0) + 1;
    }

    return {
      'totalEvents': totalEvents,
      'completedEvents': completedEvents,
      'pendingEvents': pendingEvents,
      'overdueEvents': overdueEvents,
      'todayEvents': todayEvents,
      'upcomingEvents': upcomingEvents,
      'completionRate': totalEvents > 0 ? (completedEvents / totalEvents * 100).round() : 0,
      'typeDistribution': typeDistribution,
      'priorityDistribution': priorityDistribution,
    };
  }

  List<EventIsar> _getTodayEvents(List<EventIsar> events) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    return events.where((event) {
      if (event.eventDate == null) return false;
      final eventDate = DateTime(
        event.eventDate!.year,
        event.eventDate!.month,
        event.eventDate!.day,
      );
      return eventDate.isAtSameMomentAs(today);
    }).toList();
  }

  List<EventIsar> _getUpcomingEvents(List<EventIsar> events) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));

    return events.where((event) {
      if (event.eventDate == null) return false;
      return event.eventDate!.isAfter(tomorrow) &&
             event.eventDate!.isBefore(now.add(const Duration(days: 7))) &&
             !event.isCompleted;
    }).toList();
  }

  List<EventIsar> _getOverdueEvents(List<EventIsar> events) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    return events.where((event) {
      if (event.eventDate == null) return false;
      return event.eventDate!.isBefore(today) && !event.isCompleted;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    _logger.fine('🎨 EventDashboardTab: Building UI - Loading: $_isLoading');

    if (_isLoading) {
      _logger.info('⏳ EventDashboardTab: Showing loading indicator');
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading Event Dashboard...'),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await _eventStreamService.initialize();
      },
      child: SingleChildScrollView(
        padding: EdgeInsets.all(_getResponsivePadding()),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Summary Cards
            _buildSummaryCards(),
            SizedBox(height: _getResponsiveSpacing()),

            // Charts Section
            _buildChartsSection(),
            SizedBox(height: _getResponsiveSpacing()),

            // Today's Events
            _buildTodayEventsSection(),
            SizedBox(height: _getResponsiveSpacing()),

            // Upcoming Events
            _buildUpcomingEventsSection(),
            SizedBox(height: _getResponsiveSpacing()),

            // Overdue Events
            _buildOverdueEventsSection(),

            // Add padding at the bottom
            SizedBox(height: _getResponsiveSpacing() * 2),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    final screenWidth = MediaQuery.of(context).size.width;

    // Use responsive grid layout
    if (screenWidth < 600) {
      // Mobile: 2x2 grid
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'Total Events',
                  _analytics['totalEvents']?.toString() ?? '0',
                  Icons.event,
                  const Color(0xFF3F51B5), // Indigo
                ),
              ),
              SizedBox(width: _getResponsivePadding() * 0.5),
              Expanded(
                child: _buildSummaryCard(
                  'Completed',
                  _analytics['completedEvents']?.toString() ?? '0',
                  Icons.check_circle,
                  const Color(0xFF3498DB), // Blue
                ),
              ),
            ],
          ),
          SizedBox(height: _getResponsivePadding() * 0.5),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'Pending',
                  _analytics['pendingEvents']?.toString() ?? '0',
                  Icons.pending,
                  const Color(0xFF8E44AD), // Purple
                ),
              ),
              SizedBox(width: _getResponsivePadding() * 0.5),
              Expanded(
                child: _buildSummaryCard(
                  'Overdue',
                  _analytics['overdueEvents']?.toString() ?? '0',
                  Icons.warning,
                  const Color(0xFFE74C3C), // Red
                ),
              ),
            ],
          ),
        ],
      );
    } else {
      // Desktop/Tablet: Single row
      return Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'Total Events',
              _analytics['totalEvents']?.toString() ?? '0',
              Icons.event,
              const Color(0xFF3F51B5), // Indigo
            ),
          ),
          SizedBox(width: _getResponsivePadding() * 0.5),
          Expanded(
            child: _buildSummaryCard(
              'Completed',
              _analytics['completedEvents']?.toString() ?? '0',
              Icons.check_circle,
              const Color(0xFF3498DB), // Blue
            ),
          ),
          SizedBox(width: _getResponsivePadding() * 0.5),
          Expanded(
            child: _buildSummaryCard(
              'Pending',
              _analytics['pendingEvents']?.toString() ?? '0',
              Icons.pending,
              const Color(0xFF8E44AD), // Purple
            ),
          ),
          SizedBox(width: _getResponsivePadding() * 0.5),
          Expanded(
            child: _buildSummaryCard(
              'Overdue',
              _analytics['overdueEvents']?.toString() ?? '0',
              Icons.warning,
              const Color(0xFFE74C3C), // Red
            ),
          ),
        ],
      );
    }
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: _getResponsiveIconSize()),
              const Spacer(),
              Text(
                value,
                style: TextStyle(
                  fontSize: _getResponsiveFontSize() + 10,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          SizedBox(height: _getResponsiveSpacing() * 0.5),
          Text(
            title,
            style: TextStyle(
              fontSize: _getResponsiveFontSize() + 1,
              color: color, // Use the same color as the icon
              fontWeight: FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildChartsSection() {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      // Mobile: Stack charts vertically
      return Column(
        children: [
          _buildEventTypeChart(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildPriorityChart(),
        ],
      );
    } else {
      // Desktop/Tablet: Side by side
      return Row(
        children: [
          Expanded(
            child: _buildEventTypeChart(),
          ),
          SizedBox(width: _getResponsiveSpacing()),
          Expanded(
            child: _buildPriorityChart(),
          ),
        ],
      );
    }
  }

  Widget _buildEventTypeChart() {
    final typeDistribution = _analytics['typeDistribution'] as Map<String, int>? ?? {};

    if (typeDistribution.isEmpty) {
      return _buildEmptyChart('Event Types', Icons.pie_chart, const Color(0xFF3F51B5)); // Use same color as when it has data
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFE0E0E0), // Solid light grey instead of alpha
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with colored background
          Container(
            padding: EdgeInsets.all(_getResponsivePadding()),
            decoration: const BoxDecoration(
              color: Color(0xFF3F51B5), // Indigo
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.pie_chart,
                  color: Colors.white,
                  size: _getResponsiveIconSize(),
                ),
                SizedBox(width: _getResponsivePadding() * 0.5),
                Text(
                  'Event Types',
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize() + 4,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          // Chart content
          Padding(
            padding: EdgeInsets.all(_getResponsivePadding()),
            child: SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: typeDistribution.entries.take(5).map((entry) {
                    final index = typeDistribution.keys.toList().indexOf(entry.key);
                    final colors = [
                      const Color(0xFF3F51B5), // Indigo
                      const Color(0xFF3498DB), // Blue
                      const Color(0xFF8E44AD), // Purple
                      const Color(0xFF1976D2), // Dark Blue
                      const Color(0xFFE74C3C), // Red
                    ];
                    return PieChartSectionData(
                      value: entry.value.toDouble(),
                      title: '${entry.value}',
                      color: colors[index % colors.length],
                      radius: 60,
                      titleStyle: TextStyle(
                        fontSize: _getResponsiveFontSize(),
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    );
                  }).toList(),
                  centerSpaceRadius: 40,
                  sectionsSpace: 2,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityChart() {
    final priorityDistribution = _analytics['priorityDistribution'] as Map<String, int>? ?? {};

    if (priorityDistribution.isEmpty) {
      return _buildEmptyChart('Priority Distribution', Icons.bar_chart, const Color(0xFF9B59B6)); // Use same color as when it has data
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFE0E0E0), // Solid light grey instead of alpha
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with colored background
          Container(
            padding: EdgeInsets.all(_getResponsivePadding()),
            decoration: const BoxDecoration(
              color: Color(0xFF9B59B6), // Violet Purple
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.bar_chart,
                  color: Colors.white,
                  size: _getResponsiveIconSize(),
                ),
                SizedBox(width: _getResponsivePadding() * 0.5),
                Text(
                  'Priority Distribution',
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize() + 4,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          // Chart content
          Padding(
            padding: EdgeInsets.all(_getResponsivePadding()),
            child: SizedBox(
              height: 200,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: priorityDistribution.values.isNotEmpty
                      ? priorityDistribution.values.reduce((a, b) => a > b ? a : b).toDouble() + 1
                      : 10,
                  barGroups: priorityDistribution.entries.map((entry) {
                    final index = priorityDistribution.keys.toList().indexOf(entry.key);
                    final colors = [
                      const Color(0xFF3F51B5), // Indigo
                      const Color(0xFF3498DB), // Blue
                      const Color(0xFF8E44AD), // Purple
                      const Color(0xFF1976D2), // Dark Blue
                      const Color(0xFFE74C3C), // Red
                    ];
                    return BarChartGroupData(
                      x: index,
                      barRods: [
                        BarChartRodData(
                          toY: entry.value.toDouble(),
                          color: colors[index % colors.length],
                          width: 20,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ],
                    );
                  }).toList(),
                  titlesData: FlTitlesData(
                    leftTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final priorities = priorityDistribution.keys.toList();
                          if (value.toInt() < priorities.length) {
                            return Text(
                              priorities[value.toInt()].toUpperCase(),
                              style: TextStyle(fontSize: _getResponsiveFontSize() - 2),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                  ),
                  borderData: FlBorderData(show: false),
                  gridData: const FlGridData(show: false),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyChart(String title, IconData icon, [Color? headerColor]) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFE0E0E0), // Solid light grey instead of alpha
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with colored background
          Container(
            padding: EdgeInsets.all(_getResponsivePadding()),
            decoration: BoxDecoration(
              color: headerColor ?? const Color(0xFF8E44AD), // Use provided color or default Purple
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: _getResponsiveIconSize(),
                ),
                SizedBox(width: _getResponsivePadding() * 0.5),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize() + 4,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          // Empty state content
          Padding(
            padding: EdgeInsets.all(_getResponsivePadding()),
            child: SizedBox(
              height: 200,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.analytics_outlined,
                      size: _getResponsiveIconSize() * 2,
                      color: headerColor ?? const Color(0xFF8E44AD), // Use provided color or default Purple
                    ),
                    SizedBox(height: _getResponsiveSpacing()),
                    Text(
                      'No data available',
                      style: TextStyle(
                        color: headerColor ?? const Color(0xFF8E44AD), // Use provided color or default Purple
                        fontSize: _getResponsiveFontSize() + 2,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: _getResponsiveSpacing() * 0.5),
                    Text(
                      'Add some events to see analytics',
                      style: TextStyle(
                        color: headerColor ?? const Color(0xFF8E44AD), // Use provided color or default Purple
                        fontSize: _getResponsiveFontSize(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTodayEventsSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFE0E0E0), // Solid light grey instead of alpha
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with colored background
          Container(
            padding: EdgeInsets.all(_getResponsivePadding()),
            decoration: const BoxDecoration(
              color: Color(0xFF16A085), // Teal
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.today,
                  color: Colors.white,
                  size: _getResponsiveIconSize(),
                ),
                SizedBox(width: _getResponsivePadding() * 0.5),
                Text(
                  'Today\'s Events',
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize() + 4,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: _getResponsivePadding() * 0.5,
                    vertical: _getResponsivePadding() * 0.25,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${_todayEvents.length}',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: _getResponsiveFontSize() + 2,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Events content
          Padding(
            padding: EdgeInsets.all(_getResponsivePadding()),
            child: Column(
              children: [
                if (_todayEvents.isEmpty)
                  Center(
                    child: Padding(
                      padding: EdgeInsets.all(_getResponsivePadding() * 2),
                      child: Column(
                        children: [
                          Icon(
                            Icons.event_busy,
                            size: _getResponsiveIconSize() * 2,
                            color: const Color(0xFF16A085), // Teal
                          ),
                          SizedBox(height: _getResponsiveSpacing()),
                          Text(
                            'No events scheduled for today',
                            style: TextStyle(
                              color: const Color(0xFF16A085), // Teal
                              fontSize: _getResponsiveFontSize() + 2,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  )
                else ...[
                  ...(_todayEvents.take(3).map((event) => _buildEventListItem(event))),
                  if (_todayEvents.length > 3)
                    Padding(
                      padding: EdgeInsets.only(top: _getResponsiveSpacing() * 0.5),
                      child: TextButton(
                        onPressed: () {
                          // Navigate to all events tab
                        },
                        child: Text(
                          'View all ${_todayEvents.length} events',
                          style: TextStyle(fontSize: _getResponsiveFontSize() + 1),
                        ),
                      ),
                    ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildUpcomingEventsSection() {
    return _buildEventSection(
      'Upcoming Events',
      Icons.schedule,
      const Color(0xFF1976D2), // Dark Blue
      _upcomingEvents,
    );
  }

  Widget _buildOverdueEventsSection() {
    return _buildEventSection(
      'Overdue Events',
      Icons.warning,
      const Color(0xFFE74C3C), // Red
      _overdueEvents,
    );
  }

  Widget _buildEventSection(String title, IconData icon, Color color, List<EventIsar> events) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFE0E0E0), // Solid light grey instead of alpha
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with colored background
          Container(
            padding: EdgeInsets.all(_getResponsivePadding()),
            decoration: BoxDecoration(
              color: color,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: Colors.white, size: _getResponsiveIconSize()),
                SizedBox(width: _getResponsivePadding() * 0.5),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: _getResponsiveFontSize() + 4,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: _getResponsivePadding() * 0.5,
                    vertical: _getResponsivePadding() * 0.25,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${events.length}',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: _getResponsiveFontSize() + 2,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Events content
          Padding(
            padding: EdgeInsets.all(_getResponsivePadding()),
            child: Column(
              children: [
                if (events.isEmpty)
                  Center(
                    child: Padding(
                      padding: EdgeInsets.all(_getResponsivePadding() * 2),
                      child: Column(
                        children: [
                          Icon(
                            Icons.event_busy,
                            size: _getResponsiveIconSize() * 2,
                            color: color, // Use the section's color
                          ),
                          SizedBox(height: _getResponsiveSpacing()),
                          Text(
                            'No ${title.toLowerCase()}',
                            style: TextStyle(
                              color: color, // Use the section's color
                              fontSize: _getResponsiveFontSize() + 2,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  ...(events.map((event) => _buildEventListItem(event))),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventListItem(EventIsar event) {
    final eventColor = event.type?.getColor() ?? const Color(0xFF16A085); // Teal default

    return Container(
      margin: EdgeInsets.only(bottom: _getResponsiveSpacing() * 0.5),
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: eventColor),
      ),
      child: Row(
        children: [
          Icon(
            event.type?.getIcon() ?? Icons.event,
            color: eventColor,
            size: _getResponsiveIconSize(),
          ),
          SizedBox(width: _getResponsivePadding()),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  event.title ?? 'Untitled Event',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: _getResponsiveFontSize() + 2,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (event.eventDate != null) ...[
                  SizedBox(height: _getResponsiveSpacing() * 0.25),
                  Text(
                    '${event.eventDate!.day}/${event.eventDate!.month}/${event.eventDate!.year}',
                    style: TextStyle(
                      color: eventColor, // Use the event's color
                      fontSize: _getResponsiveFontSize(),
                    ),
                  ),
                ],
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: _getResponsivePadding() * 0.5,
              vertical: _getResponsivePadding() * 0.25,
            ),
            decoration: BoxDecoration(
              color: _getPriorityColor(event.priority),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              event.priority.name.toUpperCase(),
              style: TextStyle(
                color: Colors.white, // Changed to white for visibility on colored background
                fontSize: _getResponsiveFontSize() - 2,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getPriorityColor(EventPriority priority) {
    switch (priority) {
      case EventPriority.low:
        return const Color(0xFF3F51B5); // Indigo
      case EventPriority.medium:
        return const Color(0xFF3498DB); // Blue
      case EventPriority.high:
        return const Color(0xFF8E44AD); // Purple
      case EventPriority.urgent:
        return const Color(0xFF1976D2); // Dark Blue
      case EventPriority.critical:
        return const Color(0xFFE74C3C); // Red
    }
  }
}
