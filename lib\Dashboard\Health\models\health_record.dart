import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

part 'animal_stage_isar.g.dart';

/// Represents a growth stage for animals in the system
@collection
class AnimalStageIsar {
  /// Isar database ID (auto-incremented)
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) for the stage - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// Name of the stage (e.g., "Heifer Calf", "Yearling Bull")
  @Index(caseSensitive: false)
  String? name;

  /// Description of this stage
  String? description;

  /// Icon code point to represent this stage