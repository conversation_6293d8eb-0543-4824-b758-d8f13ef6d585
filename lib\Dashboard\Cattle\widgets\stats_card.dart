import 'package:flutter/material.dart';
import 'stat_item.dart';

enum StatsCardLayout { grid, row, wrap }

enum StatsCardStyle { standard, flat, outlined, compact }

class StatsCard extends StatelessWidget {
  final String title;
  final Color titleColor;
  final IconData titleIcon;
  final List<Map<String, dynamic>> statItems;
  final double? successRate;
  final String? successRateLabel;

  // Enhanced features
  final StatsCardLayout layout;
  final StatsCardStyle style;
  final Widget? trailing;
  final Widget? subtitle;
  final Widget? footer;
  final List<Widget>? actions;
  final EdgeInsetsGeometry? padding;
  final bool showDividers;
  final Color successRateColor;
  final VoidCallback? onTap;
  final String? tooltip;
  final String? comparePeriod;
  final String? compareValue;
  final bool isLoading;
  final int maxItemsPerRow;

  const StatsCard({
    super.key,
    required this.title,
    this.titleColor = Colors.purple,
    this.titleIcon = Icons.analytics,
    required this.statItems,
    this.successRate,
    this.successRateLabel,
    this.layout = StatsCardLayout.row,
    this.style = StatsCardStyle.standard,
    this.trailing,
    this.subtitle,
    this.footer,
    this.actions,
    this.padding,
    this.showDividers = false,
    this.successRateColor = Colors.green,
    this.onTap,
    this.tooltip,
    this.comparePeriod,
    this.compareValue,
    this.isLoading = false,
    this.maxItemsPerRow = 3,
  });

  @override
  Widget build(BuildContext context) {
    final effectivePadding = padding ?? const EdgeInsets.all(16);

    final Widget cardContent = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        Padding(
          padding: effectivePadding,
          child: Column(
            children: [
              // Subtitle if provided
              if (subtitle != null) ...[
                subtitle!,
                const SizedBox(height: 16),
              ],

              // Stat items based on selected layout
              _buildStatItemsLayout(),

              // Progress bar for success rate if provided
              if (successRate != null) ...[
                const SizedBox(height: 16),
                _buildSuccessRate(context),
              ],

              // Footer if provided
              if (footer != null) ...[
                if (showDividers) const Divider(height: 32),
                footer!,
              ],
            ],
          ),
        ),
      ],
    );

    // Apply card style based on selection
    final card = _buildStyledCard(cardContent);

    // Wrap with loading indicator if loading
    if (isLoading) {
      return Stack(
        children: [
          card,
          Positioned.fill(
            child: Container(
              color: Colors.white.withAlpha(179), // 0.7 * 255 = 179
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
          ),
        ],
      );
    }

    // Apply tooltip if provided
    final wrappedCard =
        tooltip != null ? Tooltip(message: tooltip!, child: card) : card;

    // Apply tap handler if provided
    return onTap != null
        ? InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(12),
            child: wrappedCard,
          )
        : wrappedCard;
  }

  Widget _buildStyledCard(Widget content) {
    switch (style) {
      case StatsCardStyle.standard:
        return Card(
          elevation: 2,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          margin: const EdgeInsets.only(bottom: 16),
          child: content,
        );
      case StatsCardStyle.flat:
        return Card(
          elevation: 0,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          margin: const EdgeInsets.only(bottom: 16),
          color: titleColor.withAlpha(13), // 0.05 * 255 = 13
          child: content,
        );
      case StatsCardStyle.outlined:
        return Card(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(color: titleColor.withAlpha(77), width: 1), // 0.3 * 255 = 77
          ),
          margin: const EdgeInsets.only(bottom: 16),
          child: content,
        );
      case StatsCardStyle.compact:
        return Card(
          elevation: 1,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          margin: const EdgeInsets.only(bottom: 8),
          child: content,
        );
    }
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: titleColor.withAlpha(26), // 0.1 * 255 = 26
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 18,
            backgroundColor: titleColor.withAlpha(51), // 0.2 * 255 = 51
            child: Icon(
              titleIcon,
              color: titleColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: titleColor,
                  ),
                ),
                if (comparePeriod != null) ...[
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      Text(
                        comparePeriod!,
                        style: TextStyle(
                          fontSize: 12,
                          color: titleColor.withAlpha(179), // 0.7 * 255 = 179
                        ),
                      ),
                      if (compareValue != null) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: titleColor.withAlpha(26), // 0.1 * 255 = 26
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            compareValue!,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: titleColor,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) trailing!,
          if (actions != null && actions!.isNotEmpty) ...[
            const SizedBox(width: 8),
            ...actions!,
          ],
        ],
      ),
    );
  }

  Widget _buildStatItemsLayout() {
    switch (layout) {
      case StatsCardLayout.row:
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children:
              _buildStatItems().map((item) => Expanded(child: item)).toList(),
        );
      case StatsCardLayout.grid:
        return _buildGridLayout();
      case StatsCardLayout.wrap:
        return Wrap(
          spacing: 8,
          runSpacing: 16,
          alignment: WrapAlignment.spaceAround,
          children: _buildStatItems().map((item) {
            // For wrap layout, we need to explicitly set width
            return SizedBox(
              width: 100, // Fixed width for wrapped items
              child: item,
            );
          }).toList(),
        );
    }
  }

  Widget _buildGridLayout() {
    final items = _buildStatItems();
    final rows = <Widget>[];

    for (var i = 0; i < items.length; i += maxItemsPerRow) {
      final rowItems = items.skip(i).take(maxItemsPerRow).toList();
      rows.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: rowItems.map((item) => Expanded(child: item)).toList(),
          ),
        ),
      );
    }

    return Column(children: rows);
  }

  List<Widget> _buildStatItems() {
    return statItems.map((item) {
      // Convert badge string to widget if needed
      Widget? badgeWidget;
      if (item['badge'] != null) {
        if (item['badge'] is String) {
          // Convert the string badge to a widget
          badgeWidget = Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: (item['color'] as Color?) ?? Colors.red,
              shape: BoxShape.circle,
            ),
            child: Text(
              item['badge'] as String,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          );
        } else if (item['badge'] is Widget) {
          badgeWidget = item['badge'] as Widget;
        }
      }

      return StatItem(
        label: item['label'],
        value: item['value'],
        icon: item['icon'],
        color: item['color'],
        size: item['size'] ?? StatItemSize.medium,
        tooltip: item['tooltip'],
        onTap: item['onTap'],
        badge: badgeWidget,
        description: item['description'],
      );
    }).toList();
  }

  Widget _buildSuccessRate(BuildContext context) {
    // Calculate success rate as a percentage
    final successRateValue = (successRate! * 100).round();
    final successRateFormatted = successRateValue.toString();

    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                successRateLabel ?? 'Success Rate',
                style: const TextStyle(
                  color: Colors.black87, // Changed from Colors.grey[600] to avoid light colors
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              LinearProgressIndicator(
                value: successRate!,
                backgroundColor: const Color(0xFFEEEEEE), // Light grey shade instead of Colors.grey[200]
                color: successRateColor,
                minHeight: 8,
                borderRadius: BorderRadius.circular(4),
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),
        CircleAvatar(
          radius: 24,
          backgroundColor: successRateColor.withAlpha(51), // 0.2 * 255 = 51
          child: Text(
            '$successRateFormatted%',
            style: TextStyle(
              color: successRateColor,
              fontWeight: FontWeight.bold,
              fontSize: 14.0,
            ),
          ),
        ),
      ],
    );
  }

  // EXAMPLE FACTORY METHODS FOR SPECIFIC STATS CARDS

  /// Creates a breeding statistics card with breeding-specific metrics
  static StatsCard breedingStats({
    required int totalBreedings,
    required int pendingBreedings,
    required int completedBreedings,
    required int failedBreedings,
    int? confirmedBreedings,
    String? compareYear,
    double? previousYearRate,
    VoidCallback? onViewAllTap,
    VoidCallback? onInfoTap,
  }) {
    // Calculate success rate
    final double successRate =
        totalBreedings > 0 ? completedBreedings / totalBreedings : 0.0;

    // Create list of items including confirmed count if provided
    final List<Map<String, dynamic>> items = [
      {
        'label': 'Total',
        'value': totalBreedings.toString(),
        'icon': Icons.analytics,
        'color': Colors.blue,
        'tooltip': 'Total breeding attempts',
      },
      {
        'label': 'Pending',
        'value': pendingBreedings.toString(),
        'icon': Icons.hourglass_top,
        'color': Colors.purple,
        'badge': pendingBreedings > 0 ? pendingBreedings.toString() : null,
        'tooltip': 'Pending breeding records',
      },
    ];

    // Add confirmed count if provided
    if (confirmedBreedings != null) {
      items.add({
        'label': 'Confirmed',
        'value': confirmedBreedings.toString(),
        'icon': Icons.verified,
        'color': Colors.indigo,
        'badge': confirmedBreedings > 0 ? confirmedBreedings.toString() : null,
        'tooltip': 'Confirmed pregnancies',
      });
    }

    // Add completed and failed stats
    items.addAll([
      {
        'label': 'Completed',
        'value': completedBreedings.toString(),
        'icon': Icons.check_circle,
        'color': Colors.green,
        'badge': completedBreedings > 0 ? completedBreedings.toString() : null,
        'tooltip': 'Successfully completed breedings',
      },
      {
        'label': 'Failed',
        'value': failedBreedings.toString(),
        'icon': Icons.cancel,
        'color': Colors.red,
        'badge': failedBreedings > 0 ? failedBreedings.toString() : null,
        'tooltip': 'Failed breeding attempts',
      },
    ]);

    return StatsCard(
      title: 'Breeding Statistics',
      titleColor: Colors.green,
      titleIcon: Icons.favorite,
      layout: StatsCardLayout.wrap,
      comparePeriod: compareYear != null ? 'vs $compareYear' : null,
      compareValue: previousYearRate != null
          ? '${(previousYearRate * 100).round()}%'
          : null,
      onTap: onViewAllTap,
      tooltip: 'View all breeding records',
      successRate: successRate,
      successRateLabel: 'Breeding Success Rate',
      trailing: onInfoTap != null
          ? IconButton(
              icon: const Icon(Icons.info_outline, color: Colors.green),
              onPressed: onInfoTap,
              tooltip: 'Breeding statistics information',
            )
          : null,
      statItems: items,
    );
  }

  /// Creates a pregnancy statistics card with pregnancy-specific metrics
  static StatsCard pregnancyStats({
    required int totalPregnancies,
    required int completedPregnancies,
    required int abortions,
    required int confirmedPregnancies,
    VoidCallback? onInfoTap,
    VoidCallback? onCardTap,
  }) {
    // Calculate delivery success rate
    final double successRate =
        totalPregnancies > 0 ? completedPregnancies / totalPregnancies : 0.0;

    return StatsCard(
      title: 'Pregnancy Statistics',
      titleColor: Colors.purple,
      titleIcon: Icons.pregnant_woman,
      layout: StatsCardLayout.row,
      onTap: onCardTap,
      successRate: successRate,
      successRateLabel: 'Delivery Success Rate',
      trailing: IconButton(
        icon: const Icon(Icons.info_outline, color: Colors.purple),
        onPressed: onInfoTap,
        tooltip: 'Pregnancy status information',
      ),
      statItems: [
        {
          'label': 'Total',
          'value': totalPregnancies.toString(),
          'icon': Icons.analytics,
          'color': Colors.purple,
          'badge': totalPregnancies > 0 ? totalPregnancies.toString() : null,
          'tooltip': 'Total pregnancies',
        },
        {
          'label': 'Confirmed',
          'value': confirmedPregnancies.toString(),
          'icon': Icons.verified,
          'color': Colors.indigo,
          'badge':
              confirmedPregnancies > 0 ? confirmedPregnancies.toString() : null,
          'tooltip': 'Currently active pregnancies',
        },
        {
          'label': 'Completed',
          'value': completedPregnancies.toString(),
          'icon': Icons.check_circle,
          'color': Colors.green,
          'badge':
              completedPregnancies > 0 ? completedPregnancies.toString() : null,
          'tooltip': 'Successfully completed pregnancies',
        },
        {
          'label': 'Abortions',
          'value': abortions.toString(),
          'icon': Icons.do_not_disturb,
          'color': Colors.red,
          'badge': abortions > 0 ? abortions.toString() : null,
          'tooltip': 'Pregnancy abortions',
        },
      ],
    );
  }

  /// Creates a delivery statistics card with calf-specific metrics
  static StatsCard deliveryStats({
    required int totalCalves,
    required int maleCalves,
    required int femaleCalves,
    required int totalDeliveries,
    required int successfulDeliveries,
    VoidCallback? onInfoTap,
    VoidCallback? onCardTap,
  }) {
    // Calculate delivery success rate
    final double successRate =
        totalDeliveries > 0 ? successfulDeliveries / totalDeliveries : 0.0;

    return StatsCard(
      title: 'Calf Statistics',
      titleColor: Colors.purple,
      titleIcon: Icons.child_care,
      layout: StatsCardLayout.row,
      onTap: onCardTap,
      successRate: successRate,
      successRateLabel: 'Success Rate',
      trailing: onInfoTap != null
          ? IconButton(
              icon: const Icon(Icons.info_outline, color: Colors.purple),
              onPressed: onInfoTap,
              tooltip: 'Calf statistics information',
            )
          : null,
      statItems: [
        {
          'label': 'Total Calves',
          'value': totalCalves.toString(),
          'icon': Icons.child_care,
          'color': Colors.blue,
          'badge': totalCalves > 0 ? totalCalves.toString() : null,
          'tooltip': 'Total calves born',
        },
        {
          'label': 'Male',
          'value': maleCalves.toString(),
          'icon': Icons.male,
          'color': Colors.indigo,
          'badge': maleCalves > 0 ? maleCalves.toString() : null,
          'tooltip': 'Male calves born',
        },
        {
          'label': 'Female',
          'value': femaleCalves.toString(),
          'icon': Icons.female,
          'color': Colors.purple,
          'badge': femaleCalves > 0 ? femaleCalves.toString() : null,
          'tooltip': 'Female calves born',
        },
      ],
    );
  }
}
