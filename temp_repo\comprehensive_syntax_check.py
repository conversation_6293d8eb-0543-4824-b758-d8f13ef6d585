import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../models/breeding_report_data.dart';
import '../../Breeding/models/breeding_record.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class BreedingSummaryTab extends StatelessWidget {
  final BreedingReportData reportData;
  final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');

  BreedingSummaryTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final summaryData = reportData.summaryData;
    final records = reportData.filteredRecords;
    final upcomingDeliveries = reportData.upcomingDeliveries;
    final chartData = reportData.chartData;

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: ResponsiveHelper.getResponsivePadding(context),
            child: Wrap(
              spacing: 16.0,
              runSpacing: 16.0,
              children: [
                _buildSummaryCard(
                  'Total Breedings',
                  summaryData['Total Breedings']!.toInt().toString(),
                  Icons.event,
                ),
                _buildSummaryCard(
                  'Success Rate',
                  '${summaryData['Success Rate']!.toStringAsFixed(1)}%',
                  Icons.check_circle,
                ),
                _buildSummaryCard(
                  'Total Cost',
                  currencyFormat.format(summaryData['Total Cost']),
                  Icons.attach_money,
                ),
              ],
            ),
          ),
          if (records.isNotEmpty) ...[
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Breeding Status Distribution',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(
              height: 300,
              child: Padding(
                padding: ResponsiveHelper.getResponsivePadding(context),
                child: PieChart(
                  PieChartData(
                    sections: _buildPieSections(),
                    sectionsSpace: 2,
                    centerSpaceRadius: 40,
                  ),
                ),
              ),
            ),
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Breeding Trend',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            if (chartData.isNotEmpty)
              _buildBreedingChart(chartData
                  .map((data) => FlSpot(
                      data.date!.millisecondsSinceEpoch.toDouble(), data.value))
                  .toList()),
            if (upcomingDeliveries.values.any((list) => list.isNotEmpty)) ...[
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  'Upcoming Deliveries',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              _buildUpcomingDeliveriesSection(upcomingDeliveries),
            ],
          ] else
            const Center(
              child: Padding(
                padding: EdgeInsets.all(32.0),
                child: Text(
                  'No breeding records available for the selected period',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, size: 24, color: const Color(0xFF2E7D32)),
            SizedBox(height: ResponsiveSpacing.getSM(context)),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<PieChartSectionData> _buildPieSections() {
    final data = reportData.summaryData;
    final colors = [
      const Color(0xFF2E7D32), // Successful
      const Color(0xFFFFA000), // Pending
      const Color(0xFFD32F2F), // Failed
    ];

    final sections = <PieChartSectionData>[];

    void addSection(String key, Color color) {
      final value = data[key]!;
      if (value > 0) {
        sections.add(
          PieChartSectionData(
            value: value,
            title: '$key\n${value.toInt()}',
            color: color,
            radius: 100,
            titleStyle: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        );
      }
    }

    addSection('Successful', colors[0]);
    addSection('Pending', colors[1]);
    addSection('Failed', colors[2]);

    return sections;
  }

  Widget _buildUpcomingDeliveriesSection(
      Map<String, List<BreedingRecord>> deliveries) {
    return Padding(
      padding: ResponsiveHelper.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (deliveries['Due This Week']!.isNotEmpty)
            _buildDeliveryList(
              'Due This Week',
              deliveries['Due This Week']!,
              Colors.red,
            ),
          if (deliveries['Due This Month']!.isNotEmpty)
            _buildDeliveryList(
              'Due This Month',
              deliveries['Due This Month']!,
              Colors.orange,
            ),
          if (deliveries['Due Later']!.isNotEmpty)
            _buildDeliveryList(
              'Due Later',
              deliveries['Due Later']!,
              Colors.green,
            ),
        ],
      ),
    );
  }

  Widget _buildDeliveryList(
      String title, List<BreedingRecord> records, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: records.length,
          itemBuilder: (context, index) {
            final record = records[index];
            return ResponsiveCard(
      child: ListTile(
                title: Text('Cattle ID: ${record.cattleId}'),
                subtitle: Text(
                  'Expected Date: ${DateFormat('yyyy-MM-dd').format(record.expectedDate!)}',
                ),
                leading: const Icon(Icons.calendar_today),
                trailing: Text(
                  '${record.expectedDate!.difference(DateTime.now()).inDays} days',
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildB