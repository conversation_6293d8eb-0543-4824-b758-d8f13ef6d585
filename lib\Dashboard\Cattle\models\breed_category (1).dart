record.quantity} Liters',
                  ),
                  isThreeLine: true,
                  trailing: <PERSON>con<PERSON><PERSON><PERSON>(
                    icon: const Icon(Icons.delete),
                    onPressed: () => _deleteRecord(index),
                  ),
                ),
              );
            },
          );
  }

  Future<void> _addNewRecord() async {
    final result = await showDialog<MilkRecord>(
      context: context,
      builder: (context) => MilkFormDialog(cattleList: _cattle), // Pass the cattle list here
    );

    if (result != null) {
      setState(() {
        _milkRecords.add(result);
      });
    }
  }

  void _deleteRecord(int index) {
    setState(() {
      _milkRecords.removeAt(index);
    });
  }

  Future<void> _exportData() async {
    try {
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      scaffoldMessenger.showSnackBar(
        const SnackBar(content: Text('Exporting data...')),
      );

      final csvService = CsvExportService();
      final filePath = await csvService.exportMilkRecords(_milkRecords);

      scaffoldMessenger.clearSnackBars();
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Data exported successfully to: $filePath'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(con