import 'package:flutter/material.dart';
import '../models/cattle.dart';
import '../models/health_record.dart';
import '../models/medication.dart';
import '../models/vaccination.dart';
import '../services/health_service.dart';
import 'package:uuid/uuid.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class HealthRecordsView extends StatefulWidget {
  final Cattle cattle;

  const HealthRecordsView({Key? key, required this.cattle}) : super(key: key);

  @override
  State<HealthRecordsView> createState() => _HealthRecordsViewState();
}

class _HealthRecordsViewState extends State<HealthRecordsView> {
  final HealthService _healthService = HealthService();
  List<HealthRecord> healthRecords = [];
  List<Medication> medications = [];
  List<Vaccination> vaccinations = [];

  @override
  void initState() {
    super.initState();
    _loadHealthData();
  }

  Future<void> _showAddHealthRecordDialog() async {
    String condition = '';
    String treatment = '';
    String veterinarian = '';
    double cost = 0.0;
    String notes = '';

    if (!mounted) return;
    final BuildContext dialogContext = context;

    await showDialog(
      context: dialogContext,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Add Health Record'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(labelText: 'Condition'),
                onChanged: (value) => condition = value,
   