import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'chart_data_isar.dart';
import 'report_data_isar.dart';

part 'breeding_report_data_isar.g.dart';

@collection
class BreedingReportDataIsar with ReportDataIsarMixin {
  Id id = Isar.autoIncrement;

  @override
  @Index()
  String? get reportType => super.reportType;
  @override
  set reportType(String? value) => super.reportType = value;

  @override
  @Index(unique: true)
  String? get businessId => super.businessId;
  @override
  set businessId(String? value) => super.businessId = value;

  int? totalBreedings;
  int? successfulBreedings;
  int? failedBreedings;
  int? pendingBreedings;
  double? successRate;
  double? averageBreedingPerCow;
  double? averageDaysToPregConfirm;

  // Monthly breeding data for time series chart
  List<DateTime>? breedingDates;
  List<int>? breedingCounts;

  // Breeding method distribution
  List<String>? methodNames;
  List<int>? methodCounts;
  List<int>? methodColors;

  // Bull/Semen usage distribution
  List<String>? bullNames;
  List<int>? bullCounts;
  List<int>? bullColors;

  /// Default constructor with name
  BreedingReportDataIsar.empty();

  /// Factory constructor to create a new breeding report
  factory BreedingReportDataIsar.create({
    required String title,
    DateTime? startDate,
    DateTime? endDate,
    String? filterCriteria,
    int? totalBreedings,
    int? successfulBreedings,
    int? failedBreedings,
    int? pendingBreedings,
    double? successRate,
    double? averageBreedingPerCow,
    double? averageDaysToPregConfirm,
    List<DateTime>? breedingDates,
    List<int>? breedingCounts,
    List<String>? methodNames,
    List<int>? methodCounts,
    List<int>? methodColors,
    List<String>? bullNames,
    List<int>? bullCounts,
    List<int>? bullColors,
  }) {
    final report = BreedingReportDataIsar();

    // Initialize the base report properties
    report.initializeReport(
      reportType: 'breeding',
      title: title,
      startDate: startDate,
      endDate: endDate,
      filterCriteria: filterCriteria,
    );

    // Set the breeding-specific properties
    report.totalBreedings = totalBreedings;
    report.successfulBreedings = successfulBreedings;
    report.failedBreedings = failedBreedings;
    report.pendingBreedings = pendingBreedings;
    report.successRate = successRate;
    report.averageBreedingPerCow = averageBreedingPerCow;
    report.averageDaysToPregConfirm = averageDaysToPregConfirm;
    report.breedingDates = breedingDates;
    report.breedingCounts = breedingCounts;
    report.methodNames = methodNames;
    report.methodCounts = methodCounts;
    report.methodColors = methodColors;
    report.bullNames = bullNames;
    report.bullCounts = bullCounts;
    report.bullColors = bullColors;

    return report;
  }

  /// Constructor used in breeding_report_screen.dart
  BreedingReportDataIsar({
    List<dynamic>? breedingRecords,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
    String? selectedStatus,
    String? selectedMethod,
  }) {
    // Initialize the base report properties
    initializeReport(
      reportType: 'breeding',
      title: 'Breeding Report',
      startDate: startDate,
      endDate: endDate,
      filterCriteria: searchQuery,
    );

    // Process breeding records to populate the report
    if (breedingRecords != null && breedingRecords.isNotEmpty) {
      // Filter records based on criteria
      final filteredRecords = breedingRecords.where((record) {
        bool matchesStatus =
            selectedStatus == null || record.status == selectedStatus;
        bool matchesMethod =
            selectedMethod == null || record.method == selectedMethod;
        bool matchesSearch = searchQuery == null ||
            searchQuery.isEmpty ||
            record.cattleId.toLowerCase().contains(searchQuery.toLowerCase());
        bool withinDateRange = true;

        if (startDate != null && record.date != null) {
          withinDateRange = record.date.isAfter(startDate) ||
              record.date.isAtSameMomentAs(startDate);
        }

        if (endDate != null && record.date != null && withinDateRange) {
          withinDateRange = record.date.isBefore(endDate) ||
              record.date.isAtSameMomentAs(endDate);
        }

        return matchesStatus &&
            matchesMethod &&
            matchesSearch &&
            withinDateRange;
      }).toList();

      // Populate summary statistics
      totalBreedings = filteredRecords.length;
      successfulBreedings =
          filteredRecords.where((r) => r.status == 'Successful').length;
      failedBreedings =
          filteredRecords.where((r) => r.status == 'Failed').length;
      pendingBreedings =
          filteredRecords.where((r) => r.status == 'Pending').length;

      if (totalBreedings! > 0) {
        successRate = (successfulBreedings! / totalBreedings!) * 100;
      }

      // Create method distribution data
      final methodDistribution = <String, int>{};
      for (final record in filteredRecords) {
        final method = record.method ?? 'Unknown';
        methodDistribution[method] = (methodDistribution[method] ?? 0) + 1;
      }

      methodNames = methodDistribution.keys.toList();
      methodCounts = methodDistribution.values.toList();
      methodColors = List.generate(
          methodNames!.length,
          (i) => [Colors.blue, Colors.green, Colors.orange, Colors.purple][
                  i % 4]
              .value); // ignore: deprecated_member_use (value is correct for storing ARGB int)

      // Create time series data
      if (filteredRecords.isNotEmpty) {
        final dateMap = <DateTime, int>{};
        for (final record in filteredRecords) {
          if (record.date != null) {
            final date =
                DateTime(record.date.year, record.date.month, record.date.day);
            dateMap[date] = (dateMap[date] ?? 0) + 1;
          }
        }

        final sortedDates = dateMap.keys.toList()..sort();
        breedingDates = sortedDates;
        breedingCounts = sortedDates.map((date) => dateMap[date]!).toList();
      }
    } else {
      totalBreedings = 0;
      successfulBreedings = 0;
      failedBreedings = 0;
      pendingBreedings = 0;
      successRate = 0;
    }
  }

  @override
  List<DataColumn> getTableColumns() {
    return [
      const DataColumn(label: Text('Metric')),
      const DataColumn(label: Text('Value'), numeric: true),
    ];
  }

  @override
  List<DataRow> getTableRows() {
    return [
      DataRow(cells: [
        const DataCell(Text('Total Breedings')),
        DataCell(Text('${totalBreedings ?? 0}')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Successful Breedings')),
        DataCell(Text('${successfulBreedings ?? 0}')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Failed Breedings')),
        DataCell(Text('${failedBreedings ?? 0}')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Pending Breedings')),
        DataCell(Text('${pendingBreedings ?? 0}')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Success Rate')),
        DataCell(Text('${successRate?.toStringAsFixed(1) ?? '0.0'}%')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Average Breeding Per Cow')),
        DataCell(Text(averageBreedingPerCow?.toStringAsFixed(1) ?? '0.0')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Average Days to Pregnancy Confirmation')),
        DataCell(Text(averageDaysToPregConfirm?.toStringAsFixed(1) ?? '0.0')),
      ]),
    ];
  }

  @override
  Map<String, dynamic> getSummaryData() {
    return {
      'Total Breedings': totalBreedings ?? 0,
      'Successful': successfulBreedings ?? 0,
      'Success Rate': '${successRate?.toStringAsFixed(1) ?? '0.0'}%',
      'Average Days to Confirm':
          averageDaysToPregConfirm?.toStringAsFixed(1) ?? '0.0',
    };
  }

  @override
  List<ChartDataIsar> getChartData() {
    final result = <ChartDataIsar>[];

    // Add breeding method distribution chart data
    if (methodNames != null && methodCounts != null && methodColors != null) {
      for (int i = 0;
          i < methodNames!.length &&
              i < methodCounts!.length &&
              i < methodColors!.length;
          i++) {
        result.add(ChartDataIsar()
          ..label = methodNames![i]
          ..value = methodCounts![i].toDouble()
          ..colorValue = methodColors![i]);
      }
    }

    return result;
  }

  // Helper method to get bull/semen usage chart data
  List<ChartDataIsar> getBullChartData() {
    final result = <ChartDataIsar>[];

    // Add bull/semen usage chart data
    if (bullNames != null && bullCounts != null && bullColors != null) {
      for (int i = 0;
          i < bullNames!.length &&
              i < bullCounts!.length &&
              i < bullColors!.length;
          i++) {
        result.add(ChartDataIsar()
          ..label = bullNames![i]
          ..value = bullCounts![i].toDouble()
          ..colorValue = bullColors![i]);
      }
    }

    return result;
  }

  // Helper method to get time series breeding data
  List<ChartDataIsar> getTimeSeriesChartData() {
    final result = <ChartDataIsar>[];

    // Create time series chart data from breedingDates and breedingCounts
    if (breedingDates != null && breedingCounts != null) {
      for (int i = 0;
          i < breedingDates!.length && i < breedingCounts!.length;
          i++) {
        result.add(ChartDataIsar()
              ..date = breedingDates![i]
              ..value = breedingCounts![i].toDouble()
              ..colorValue = Colors.purple
                  .value // ignore: deprecated_member_use (value is correct for storing ARGB int)
            );
      }
    }

    return result;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'totalBreedings': totalBreedings,
      'successfulBreedings': successfulBreedings,
      'failedBreedings': failedBreedings,
      'pendingBreedings': pendingBreedings,
      'successRate': successRate,
      'averageBreedingPerCow': averageBreedingPerCow,
      'averageDaysToPregConfirm': averageDaysToPregConfirm,
      'breedingDates':
          breedingDates?.map((date) => date.toIso8601String()).toList(),
      'breedingCounts': breedingCounts,
      'methodNames': methodNames,
      'methodCounts': methodCounts,
      'methodColors': methodColors,
      'bullNames': bullNames,
      'bullCounts': bullCounts,
      'bullColors': bullColors,
    });
    return map;
  }

  factory BreedingReportDataIsar.fromMap(Map<String, dynamic> map) {
    final report = BreedingReportDataIsar();

    // Initialize the base properties
    report.initFromMap(map);

    // Set breeding-specific properties
    report.totalBreedings = map['totalBreedings'] as int?;
    report.successfulBreedings = map['successfulBreedings'] as int?;
    report.failedBreedings = map['failedBreedings'] as int?;
    report.pendingBreedings = map['pendingBreedings'] as int?;
    report.successRate = map['successRate'] as double?;
    report.averageBreedingPerCow = map['averageBreedingPerCow'] as double?;
    report.averageDaysToPregConfirm =
        map['averageDaysToPregConfirm'] as double?;

    // Handle lists
    if (map['breedingDates'] != null) {
      report.breedingDates = (map['breedingDates'] as List)
          .map((dateStr) => DateTime.parse(dateStr as String))
          .toList();
    }

    if (map['breedingCounts'] != null) {
      report.breedingCounts = List<int>.from(map['breedingCounts'] as List);
    }

    if (map['methodNames'] != null) {
      report.methodNames = List<String>.from(map['methodNames'] as List);
    }

    if (map['methodCounts'] != null) {
      report.methodCounts = List<int>.from(map['methodCounts'] as List);
    }

    if (map['methodColors'] != null) {
      report.methodColors = List<int>.from(map['methodColors'] as List);
    }

    if (map['bullNames'] != null) {
      report.bullNames = List<String>.from(map['bullNames'] as List);
    }

    if (map['bullCounts'] != null) {
      report.bullCounts = List<int>.from(map['bullCounts'] as List);
    }

    if (map['bullColors'] != null) {
      report.bullColors = List<int>.from(map['bullColors'] as List);
    }

    return report;
  }

  // Get filtered breeding records for the details tab
  @ignore
  List<BreedingRecordIsar> get filteredRecords {
    // This would ideally return the filtered list of breeding records
    // For now we'll return an empty list to fix the getter issue
    return <BreedingRecordIsar>[];
  }

  // Get upcoming deliveries
  @ignore
  List<UpcomingDeliveryIsar> get upcomingDeliveries {
    // This would ideally calculate upcoming deliveries based on successful breedings
    // For now we'll return an empty list to fix the getter issue
    return <UpcomingDeliveryIsar>[];
  }
}

/// Class to represent a breeding record in the breeding report
class BreedingRecordIsar {
  final DateTime date;
  final String cattleId;
  final String bullIdOrType;
  final String method;
  final String status;
  final DateTime? expectedDate;
  final double cost;

  BreedingRecordIsar({
    required this.date,
    required this.cattleId,
    required this.bullIdOrType,
    required this.method,
    required this.status,
    this.expectedDate,
    required this.cost,
  });
}

/// Class to represent an upcoming delivery in the breeding report
class UpcomingDeliveryIsar {
  final String cattleId;
  final DateTime expectedDate;
  final int daysRemaining;
  final String bullIdOrType;

  UpcomingDeliveryIsar({
    required this.cattleId,
    required this.expectedDate,
    required this.daysRemaining,
    required this.bullIdOrType,
  });
}
