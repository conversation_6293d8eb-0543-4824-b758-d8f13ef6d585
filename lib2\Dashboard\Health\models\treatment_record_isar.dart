import 'package:isar/isar.dart';
import 'package:json_annotation/json_annotation.dart';

part 'treatment_record_isar.g.dart';

/// Represents a treatment record in the Isar database
@collection
@JsonSerializable()
class TreatmentRecordIsar {
  factory TreatmentRecordIsar.fromJson(Map<String, dynamic> json) =>
      _$TreatmentRecordIsarFromJson(json);
  Map<String, dynamic> toJson() => _$TreatmentRecordIsarToJson(this);

  /// Isar database ID
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) for the treatment record - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// ID of the farm this record belongs to
  @Index()
  String? farmBusinessId;

  /// ID of the cattle this treatment record is for
  @Index()
  String? cattleBusinessId;

  /// Tag ID of the cattle for quick reference
  @Index()
  String? cattleTagId;

  /// Name of the cattle for quick reference
  String? cattleName;

  /// Related health record ID if this treatment is part of a health record
  @Index()
  String? healthRecordId;

  /// Name of the medication or treatment
  String? medicationName;

  /// Category of medication (Antibiotics, Anti-inflammatory, Dewormers, Vitamins, Minerals)
  @Index()
  String? medicationCategory;

  /// Dosage amount
  double? dosageAmount;

  /// Dosage unit (mg, g, kg, ml, L, cc, IU, tablets, capsules, drops, pumps, doses)
  String? dosageUnit;

  /// Frequency of administration (Once daily, Twice daily, Three times daily, etc.)
  String? frequency;

  /// Route of administration (Oral, Injectable, Topical, Intravenous, Intramuscular, Subcutaneous)
  String? route;

  /// Start date of treatment
  DateTime? startDate;

  /// End date of treatment
  DateTime? endDate;

  /// Duration in days
  int? durationDays;

  /// Current status of treatment (Active, Completed, Discontinued, On Hold)
  @Index()
  String? status;

  /// Reason for treatment
  String? reason;

  /// Instructions for administration
  String? instructions;

  /// Veterinarian who prescribed the treatment
  String? prescribedBy;

  /// Person administering the treatment
  String? administeredBy;

  /// Cost of the medication/treatment
  double? cost;

  /// Supplier or pharmacy where medication was obtained
  String? supplier;

  /// Batch number of the medication
  String? batchNumber;

  /// Expiry date of the medication
  DateTime? expiryDate;

  /// Withdrawal period for milk (in days)
  int? milkWithdrawalDays;

  /// Withdrawal period for meat (in days)
  int? meatWithdrawalDays;

  /// Date when milk withdrawal period ends
  DateTime? milkWithdrawalEndDate;

  /// Date when meat withdrawal period ends
  DateTime? meatWithdrawalEndDate;

  /// Side effects observed
  String? sideEffects;

  /// Effectiveness of treatment (Poor, Fair, Good, Excellent)
  String? effectiveness;

  /// Additional notes
  String? notes;

  /// Whether this is a preventive treatment
  bool isPreventive = false;

  /// Whether this is an emergency treatment
  bool isEmergency = false;

  /// Whether the treatment is completed
  bool isCompleted = false;

  /// Whether follow-up is required
  bool requiresFollowUp = false;

  /// Next administration date for ongoing treatments
  DateTime? nextAdministrationDate;

  /// Reminder set for next dose
  bool reminderSet = false;

  /// Date when the record was created
  DateTime? createdAt;

  /// Date when the record was last updated
  DateTime? updatedAt;

  /// User who created the record
  String? createdBy;

  /// User who last updated the record
  String? updatedBy;

  /// Default constructor
  TreatmentRecordIsar();

  /// Generate a business ID for the treatment record
  static String generateBusinessId(String cattleId, DateTime startDate) {
    final timestamp = startDate.millisecondsSinceEpoch;
    return 'treatment_${cattleId}_$timestamp';
  }

  /// Factory constructor for creating a new treatment record
  factory TreatmentRecordIsar.create({
    required String cattleBusinessId,
    required String cattleTagId,
    required String cattleName,
    required String farmBusinessId,
    String? healthRecordId,
    required String medicationName,
    String? medicationCategory,
    required double dosageAmount,
    required String dosageUnit,
    required String frequency,
    String? route,
    required DateTime startDate,
    DateTime? endDate,
    int? durationDays,
    String? status,
    String? reason,
    String? instructions,
    String? prescribedBy,
    String? administeredBy,
    double? cost,
    String? supplier,
    String? batchNumber,
    DateTime? expiryDate,
    int? milkWithdrawalDays,
    int? meatWithdrawalDays,
    String? sideEffects,
    String? effectiveness,
    String? notes,
    bool isPreventive = false,
    bool isEmergency = false,
    bool requiresFollowUp = false,
    bool reminderSet = false,
    String? createdBy,
  }) {
    // Calculate end date if duration is provided
    DateTime? calculatedEndDate = endDate;
    if (calculatedEndDate == null && durationDays != null) {
      calculatedEndDate = startDate.add(Duration(days: durationDays));
    }

    // Calculate withdrawal end dates
    DateTime? milkWithdrawalEnd;
    DateTime? meatWithdrawalEnd;
    if (milkWithdrawalDays != null) {
      final treatmentEnd = calculatedEndDate ?? startDate;
      milkWithdrawalEnd = treatmentEnd.add(Duration(days: milkWithdrawalDays));
    }
    if (meatWithdrawalDays != null) {
      final treatmentEnd = calculatedEndDate ?? startDate;
      meatWithdrawalEnd = treatmentEnd.add(Duration(days: meatWithdrawalDays));
    }

    final record = TreatmentRecordIsar()
      ..businessId = generateBusinessId(cattleBusinessId, startDate)
      ..farmBusinessId = farmBusinessId
      ..cattleBusinessId = cattleBusinessId
      ..cattleTagId = cattleTagId
      ..cattleName = cattleName
      ..healthRecordId = healthRecordId
      ..medicationName = medicationName
      ..medicationCategory = medicationCategory
      ..dosageAmount = dosageAmount
      ..dosageUnit = dosageUnit
      ..frequency = frequency
      ..route = route
      ..startDate = startDate
      ..endDate = calculatedEndDate
      ..durationDays = durationDays
      ..status = status ?? 'Active'
      ..reason = reason
      ..instructions = instructions
      ..prescribedBy = prescribedBy
      ..administeredBy = administeredBy
      ..cost = cost
      ..supplier = supplier
      ..batchNumber = batchNumber
      ..expiryDate = expiryDate
      ..milkWithdrawalDays = milkWithdrawalDays
      ..meatWithdrawalDays = meatWithdrawalDays
      ..milkWithdrawalEndDate = milkWithdrawalEnd
      ..meatWithdrawalEndDate = meatWithdrawalEnd
      ..sideEffects = sideEffects
      ..effectiveness = effectiveness
      ..notes = notes
      ..isPreventive = isPreventive
      ..isEmergency = isEmergency
      ..isCompleted = status == 'Completed'
      ..requiresFollowUp = requiresFollowUp
      ..reminderSet = reminderSet
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now()
      ..createdBy = createdBy;

    return record;
  }

  /// Create a copy with updated values
  TreatmentRecordIsar copyWith({
    String? businessId,
    String? farmBusinessId,
    String? cattleBusinessId,
    String? cattleTagId,
    String? cattleName,
    String? healthRecordId,
    String? medicationName,
    String? medicationCategory,
    double? dosageAmount,
    String? dosageUnit,
    String? frequency,
    String? route,
    DateTime? startDate,
    DateTime? endDate,
    int? durationDays,
    String? status,
    String? reason,
    String? instructions,
    String? prescribedBy,
    String? administeredBy,
    double? cost,
    String? supplier,
    String? batchNumber,
    DateTime? expiryDate,
    int? milkWithdrawalDays,
    int? meatWithdrawalDays,
    DateTime? milkWithdrawalEndDate,
    DateTime? meatWithdrawalEndDate,
    String? sideEffects,
    String? effectiveness,
    String? notes,
    bool? isPreventive,
    bool? isEmergency,
    bool? isCompleted,
    bool? requiresFollowUp,
    DateTime? nextAdministrationDate,
    bool? reminderSet,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) {
    final record = TreatmentRecordIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..farmBusinessId = farmBusinessId ?? this.farmBusinessId
      ..cattleBusinessId = cattleBusinessId ?? this.cattleBusinessId
      ..cattleTagId = cattleTagId ?? this.cattleTagId
      ..cattleName = cattleName ?? this.cattleName
      ..healthRecordId = healthRecordId ?? this.healthRecordId
      ..medicationName = medicationName ?? this.medicationName
      ..medicationCategory = medicationCategory ?? this.medicationCategory
      ..dosageAmount = dosageAmount ?? this.dosageAmount
      ..dosageUnit = dosageUnit ?? this.dosageUnit
      ..frequency = frequency ?? this.frequency
      ..route = route ?? this.route
      ..startDate = startDate ?? this.startDate
      ..endDate = endDate ?? this.endDate
      ..durationDays = durationDays ?? this.durationDays
      ..status = status ?? this.status
      ..reason = reason ?? this.reason
      ..instructions = instructions ?? this.instructions
      ..prescribedBy = prescribedBy ?? this.prescribedBy
      ..administeredBy = administeredBy ?? this.administeredBy
      ..cost = cost ?? this.cost
      ..supplier = supplier ?? this.supplier
      ..batchNumber = batchNumber ?? this.batchNumber
      ..expiryDate = expiryDate ?? this.expiryDate
      ..milkWithdrawalDays = milkWithdrawalDays ?? this.milkWithdrawalDays
      ..meatWithdrawalDays = meatWithdrawalDays ?? this.meatWithdrawalDays
      ..milkWithdrawalEndDate = milkWithdrawalEndDate ?? this.milkWithdrawalEndDate
      ..meatWithdrawalEndDate = meatWithdrawalEndDate ?? this.meatWithdrawalEndDate
      ..sideEffects = sideEffects ?? this.sideEffects
      ..effectiveness = effectiveness ?? this.effectiveness
      ..notes = notes ?? this.notes
      ..isPreventive = isPreventive ?? this.isPreventive
      ..isEmergency = isEmergency ?? this.isEmergency
      ..isCompleted = isCompleted ?? this.isCompleted
      ..requiresFollowUp = requiresFollowUp ?? this.requiresFollowUp
      ..nextAdministrationDate = nextAdministrationDate ?? this.nextAdministrationDate
      ..reminderSet = reminderSet ?? this.reminderSet
      ..createdAt = createdAt ?? this.createdAt
      ..updatedAt = updatedAt ?? DateTime.now()
      ..createdBy = createdBy ?? this.createdBy
      ..updatedBy = updatedBy ?? this.updatedBy;

    return record;
  }

  @override
  String toString() {
    return 'TreatmentRecordIsar{id: $id, businessId: $businessId, cattleTagId: $cattleTagId, medicationName: $medicationName, status: $status, startDate: $startDate}';
  }
}
