import 'package:flutter/material.dart';
import '../../Cattle/models/animal_type.dart';
import '../../../services/database_helper.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class GestationSettingsScreen extends StatefulWidget {
  const GestationSettingsScreen({super.key});

  @override
  State<GestationSettingsScreen> createState() =>
      _GestationSettingsScreenState();
}

class _GestationSettingsScreenState extends State<GestationSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  List<AnimalType> _types = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAnimalTypes();
  }

  Future<void> _loadAnimalTypes() async {
    try {
      final types = await DatabaseHelper.instance.getAnimalTypes();
      if (mounted) {
        setState(() {
          _types = types;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading animal types: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveSettings(AnimalType type) async {
    if (_formKey.currentState!.validate()) {
      try {
        await DatabaseHelper.instance.updateAnimalType(type);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Settings saved successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
        await _loadAnimalTypes();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error saving settings: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _showEditDialog(AnimalType type) {
    final gestationController = TextEditingController(
      text: type.defaultGestationDays.toString(),
    );
    final heatCycleController = TextEditingController(
      text: type.defaultHeatCycleDays.toString(),
    );

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Edit ${type.name} Settings'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: gestationController,
                decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Gestation Period (days)'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter gestation days';
                  }
                  final days = int.tryParse(value);
                  if (days == null || days <= 0) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
              TextFormField(
                controller: heatCycleController,
                decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Heat Cycle (days)'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter heat cycle days';
                  }
                  final days = int.tryParse(value);
                  if (days == null || days <= 0) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  final updatedType = type.copyWith(
                    defaultGestationDays: int.parse(
                      gestationController.text,
                    ),
                    defaultHeatCycleDays: int.parse(
                      heatCycleController.text,
                    ),
                    updatedAt: DateTime.now(),
                  );
                  _saveSettings(updatedType);
                  Navigator.of(context).pop();
                }
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    const mainColor = Color(0xFF2E7D32);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Gestation Settings',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: mainColor,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _types.isEmpty
              ? const Center(
                  child: Text(
                    'Please add animal types in Farm Setup first',
                    style: TextStyle(fontSize: 16),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(8.0),
                  itemCount: _types.length,
                  itemBuilder: (context, index) {
                    final type = _types[index];
                    return Card(
                      elevation: 2,
                      margin: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      child: ListTile(
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        leading: Icon(
                          type.icon,
                          size: 32, // Consistent size for list items
                          color: mainColor,
                        ),
                        title: Text(
                          type.name,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Colors.black87,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Gestation: ${type.defaultGestationDays} days',
                              style: const TextStyle(
                                color: Colors.black54,
                              ),
                            ),
                            Text(
                              'Heat Cycle: ${type.defaultHeatCycleDays} days',
                              style: const TextStyle(
                                color: Colors.black54,
                              ),
                            ),
                          ],
                        ),
                        trailing: IconButton(
                          icon: const Icon(
                            Icons.edit_outlined,
                            size: 24, // Standard icon button size
                          ),
                          color: mainColor,
                          onPressed: () => _showEditDialog(type),
                        ),
                      ),
                    );
                  },
                ),
    );
  }
}
