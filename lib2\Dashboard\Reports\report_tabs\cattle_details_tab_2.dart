// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cattle.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CattleAdapter extends TypeAdapter<Cattle> {
  @override
  final int typeId = 0;

  @override
  Cattle read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Cattle(
      id: fields[0] as String,
      tagId: fields[1] as String,
      name: fields[2] as String,
      animalTypeId: fields[3] as String,
      breedId: fields[4] as String,
      gender: fields[5] as String,
      source: fields[6] as String,
      motherTagId: fields[7] as String?,
      dateOfBirth: fields[8] as DateTime?,
    