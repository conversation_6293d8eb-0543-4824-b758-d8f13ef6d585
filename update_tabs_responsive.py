import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/event_report_data.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class EventDetailsTab extends StatefulWidget {
  final EventReportData reportData;

  const EventDetailsTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  EventDetailsTabState createState() => EventDetailsTabState();
}

class EventDetailsTabState extends State<EventDetailsTab> {
  String? _sortColumn;
  bool _sortAscending = true;
  final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');

  @override
  Widget build(BuildContext context) {
    final events = List.from(widget.reportData.filteredEvents);

    // Apply sorting
    if (_sortColumn != null) {
      events.sort((a, b) {
        int comparison;
        switch (_sortColumn) {
          case 'date':
            comparison = a.date.compareTo(b.date);
            break;
          case 'cattle':
            comparison = a.cattleId.compareTo(b.cattleId);
            break;
          case 'type':
            comparison = a.type.compareTo(b.type);
            break;
          case 'description':
            comparison = a.description.compareTo(b.description);
            break;
          case 'status':
            comparison = a.status.compareTo(b.status);
            break;
          case 'cost':
            comparison = a.cost.compareTo(b.cost);
            break;
          default:
            comparison = 0;
        }
        return _sortAscending ? comparison : -comparison;
      });
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          sortColumnIndex: _getSortColumnIndex(),
          sortAscending: _sortAscending,
          columns: [
            DataColumn(
              label: const Text('Date'),
              onSort: (_, __) => _onSort('date'),
            ),
            DataColumn(
              label: const Text('Cattle'),
              onSort: (_, __) => _onSort('cattle'),
            ),
            DataColumn(
              label: const Text('Type'),
              onSort: (_, __) => _onSort('type'),
            ),
            DataColumn(
              label: const Text('Description'),
              onSort: (_, __) => _onSort('description'),
            ),
            DataColumn(
              label: const Text('Status'),
              onSort: (_, __) => _onSort('status'),
            ),
            DataColumn(
              label: const Text('Cost'),
              numeric: true,
              onSort: (_, __) => _onSort('cost'),
            ),
          ],
          rows: events.map((event) {
            return DataRow(
              cells: [
                DataCell(Text(DateFormat('yyyy-MM-dd').format(event.date))),
                DataCell(Text(event.cattleId)),
                DataCell(
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getEventTypeColor(event.type),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      event.type,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                DataCell(
                  ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 300),
                    child: Text(
                      event.description,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                DataCell(
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(event.status),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      event.status,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                DataCell(
                  Text(
              