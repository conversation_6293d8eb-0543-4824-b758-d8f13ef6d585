import 'package:flutter/material.dart';
import '../models/event.dart';
import '../../../services/database_helper.dart';
import '../services/notification_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class EventAlertsTab extends StatefulWidget {
  const EventAlertsTab({Key? key}) : super(key: key);

  @override
  State<EventAlertsTab> createState() => _EventAlertsTabState();
}

class _EventAlertsTabState extends State<EventAlertsTab> {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  List<FarmEvent> _events = [];
  bool _isLoading = true;
  bool _showNotificationSettings = false;

  // Notification preferences
  bool _enableUpcomingReminders = true;
  bool _enableMissedAlerts = true;
  bool _enableCustomAlerts = true;
  int _upcomingReminderDays = 7;
  int _missedAlertHours = 24;

  @override
  void initState() {
    super.initState();
    _loadEvents();
    _loadNotificationPreferences();
    _setupFarmChangeListener();
  }

  Future<void> _loadEvents() async {
    try {
      setState(() => _isLoading = true);

      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) {
        setState(() {
          _events = [];
          _isLoading = false;
        });
        return;
      }

      final prefs = await SharedPreferences.getInstance();
      final key = '${selectedFarmId}_events';
      final eventsJson = prefs.getStringList(key) ?? [];
      final allEvents = eventsJson
          .map((json) => FarmEvent.fromMap(jsonDecode(json)))
          .toList();

      // Load events based on notification settings
      final missedEvents = _enableMissedAlerts
          ? await NotificationService.getMissedEvents(allEvents)
          : [];
      final importantEvents = _enableCustomAlerts
          ? await NotificationService.getImportantEvents(allEvents)
          : [];
      final upcomingEvents = _enableUpcomingReminders
          ? await NotificationService.getUpcomingEvents(allEvents)
          : [];

      if (mounted) {
        setState(() {
          _events = [...missedEvents, ...importantEvents, ...upcomingEvents];
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading events: $e');
      if (mounted) {
        setState(() {
          _events = [];
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadNotificationPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _enableUpcomingReminders =
          prefs.getBool('enable_upcoming_reminders') ?? true;
      _enableMissedAlerts = prefs.getBool('enable_missed_alerts') ?? true;
      _enableCustomAlerts = prefs.getBool('enable_custom_alerts') ?? true;
      _upcomingReminderDays = prefs.getInt('upcoming_reminder_days') ?? 7;
      _missedAlertHours = prefs.getInt('missed_alert_hours') ?? 24;
    });
  }

  Future<void> _saveNotificationPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('enable_upcoming_reminders', _enableUpcomingReminders);
    await prefs.setBool('enable_missed_alerts', _enableMissedAlerts);
    await prefs.setBool('enable_custom_alerts', _enableCustomAlerts);
    await prefs.setInt('upcoming_reminder_days', _upcomingReminderDays);
    await prefs.setInt('missed_alert_hours', _missedAlertHours);
  }

  void _setupFarmChangeListener() {
    _dbHelper.addListener(_onFarmChanged);
  }

  void _onFarmChanged() {
    _loadEvents();
  }

  Future<void> _markAsComplete(FarmEvent event) async {
    try {
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) return;

      final prefs = await SharedPreferences.getInstance();
      final key = '${selectedFarmId}_events';
      final eventsJson = prefs.getStringList(key) ?? [];
      final allEvents = eventsJson
          .map((json) => FarmEvent.fromMap(jsonDecode(json)))
          .toList();

      final index = allEvents.indexWhere((e) => e.id == event.id);
      if (index != -1) {
        allEvents[index] = event.copyWith(
          isCompleted: true,
          completedAt: DateTime.now(),
        );

        final updatedEventsJson =
            allEvents.map((e) => jsonEncode(e.toMap())).toList();
        await prefs.setStringList(key, updatedEventsJson);
        _loadEvents();
      }
    } catch (e) {
      debugPrint('Error marking event as complete: $e');
    }
  }

  @override
  void dispose() {
    _dbHelper.removeListener(_onFarmChanged);
    super.dispose();
  }

  Widget _buildNotificationSettings() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Notification Settings',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () =>
                      setState(() => _showNotificationSettings = false),
                ),
              ],
            ),
            const Divider(),
            SwitchListTile(
              title: const Text('Upcoming Event Reminders'),
              subtitle: const Text('Get notified about upcoming events'),
              value: _enableUpcomingReminders,
              onChanged: (value) => setState(() {
                _enableUpcomingReminders = value;
                _saveNotificationPreferences();
              }),
            ),
            if (_enableUpcomingReminders)
              ListTile(
                title: const Text('Reminder Days Before'),
                trailing: DropdownButton<int>(
                  value: _upcomingReminderDays,
                  items: [1, 3, 5, 7, 14].map((days) {
                    return DropdownMenuItem(
                      value: days,
                      child: Text('$days days'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _upcomingReminderDays = value;
                        _saveNotificationPreferences();
                      });
                    }
                  },
                ),
              ),
            SwitchListTile(
              title: const Text('Missed Event Alerts'),
              subtitle: const Text('Get notified about missed events'),
              value: _enableMissedAlerts,
              onChanged: (value) => setState(() {
                _enableMissedAlerts = value;
                _saveNotificationPreferences();
              }),
            ),
            if (_enableMissedAlerts)
              ListTile(
                title: const Text('Alert After Hours'),
                trailing: DropdownButton<int>(
                  value: _missedAlertHours,
                  items: [6, 12, 24, 48].map((hours) {
                    return DropdownMenuItem(
                      value: hours,
                      child: Text('$hours hours'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _missedAlertHours = value;
                        _saveNotificationPreferences();
                      });
                    }
                  },
                ),
              ),
            SwitchListTile(
              title: const Text('Important Event Alerts'),
              subtitle: const Text(
                  'Get notified about critical events (Pregnancy, Vaccination, etc.)'),
              value: _enableCustomAlerts,
              onChanged: (value) => setState(() {
                _enableCustomAlerts = value;
                _saveNotificationPreferences();
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: color.withAlpha(128),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, color: color),
          const SizedBox(width: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventsList(List<FarmEvent> events, bool isMissed) {
    return Column(
      children:
          events.map((event) => _buildEventCard(event, isMissed)).toList(),
    );
  }

  Widget _buildEventCard(FarmEvent event, bool isMissed) {
    final color = isMissed
        ? Colors.red.shade700
        : NotificationService.getPriorityColor(event.priority);
    final priorityLabel =
        NotificationService.getEventPriorityLabel(event.priority);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: color.withAlpha(128),
          child: Icon(
            NotificationService.getEventTypeIcon(event.type),
            color: color,
          ),
        ),
        title: Text(
          event.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: color),
                const SizedBox(width: 4),
                Text(
                  '${event.date.year}-${event.date.month.toString().padLeft(2, '0')}-${event.date.day.toString().padLeft(2, '0')} ${event.time.format(context)}',
                  style: TextStyle(color: color),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: color.withAlpha(128),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                priorityLabel,
                style: TextStyle(color: color, fontSize: 12),
              ),
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (event.description.isNotEmpty) ...[
                  const Text(
                    'Description:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  Text(event.description),
                  const SizedBox(height: 16),
                ],
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton.icon(
                      icon: const Icon(Icons.check_circle_outline),
                      label: const Text('Mark as Complete'),
                      onPressed: () => _markAsComplete(event),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_showNotificationSettings) {
      return _buildNotificationSettings();
    }

    if (_events.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.check_circle_outline,
                size: 48,
                color: Theme.of(context).primaryColor.withAlpha(128),
              ),
              const SizedBox(height: 16),
              const Text(
                'All caught up!',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                'No pending notifications at this time',
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
              const SizedBox(height: 24),
              OutlinedButton.icon(
                icon: const Icon(Icons.settings),
                label: const Text('Notification Settings'),
                onPressed: () =>
                    setState(() => _showNotificationSettings = true),
              ),
            ],
          ),
        ),
      );
    }

    // Group events by type
    final missedEvents = _events
        .where(
            (e) => _enableMissedAlerts && NotificationService.isMissedEvent(e))
        .toList();
    final importantEvents = _events
        .where((e) =>
            _enableCustomAlerts && NotificationService.isImportantEvent(e))
        .toList();
    final upcomingEvents = _events
        .where((e) =>
            _enableUpcomingReminders && NotificationService.isUpcomingEvent(e))
        .toList();

    return Stack(
      children: [
        ListView(
          padding: const EdgeInsets.all(16),
          children: [
            if (missedEvents.isNotEmpty) ...[
              _buildSectionHeader(
                  'Missed Events', Icons.warning, Colors.red.shade700),
              _buildEventsList(missedEvents, true),
              const SizedBox(height: 16),
            ],
            if (importantEvents.isNotEmpty) ...[
              _buildSectionHeader('Critical Events', Icons.priority_high,
                  Colors.orange.shade700),
              _buildEventsList(importantEvents, false),
              const SizedBox(height: 16),
            ],
            if (upcomingEvents.isNotEmpty) ...[
              _buildSectionHeader(
                  'Upcoming Events', Icons.upcoming, Colors.blue.shade600),
              _buildEventsList(upcomingEvents, false),
            ],
            const SizedBox(height: 80), // Space for FAB
          ],
        ),
        Positioned(
          right: 16,
          bottom: 16,
          child: FloatingActionButton(
            onPressed: () => setState(() => _showNotificationSettings = true),
            tooltip: 'Notification Settings',
            child: const Icon(Icons.notifications_active),
          ),
        ),
      ],
    );
  }
}
