class MilkSettings {
  final String unit; // 'liters' or 'gallons'
  final double regularRate; // Rate per unit for regular milk
  final double premiumRate; // Rate per unit for premium quality milk
  final double bulkRate; // Rate per unit for bulk purchases

  MilkSettings({
    required this.unit,
    required this.regularRate,
    required this.premiumRate,
    required this.bulkRate,
  });

  Map<String, dynamic> toMap() {
    return {
      'unit': unit,
      'regularRate': regularRate,
      'premiumRate': premiumRate,
      'bulkRate': bulkRate,
    };
  }

  factory MilkSettings.fromMap(Map<String, dynamic> map) {
    return MilkSettings(
      unit: map['unit'] ?? 'liters',
      regularRate: (map['regularRate'] ?? 0.0).toDouble(),
      premiumRate: (map['premiumRate'] ?? 0.0).toDouble(),
      bulkRate: (map['bulkRate'] ?? 0.0).toDouble(),
    );
  }

  MilkSettings copyWith({
    String? unit,
    double? regularRate,
    double? premiumRate,
    double? bulkRate,
  }) {
    return MilkSettings(
      unit: unit ?? this.unit,
      regularRate: regularRate ?? this.regularRate,
      premiumRate: premiumRate ?? this.premiumRate,
      bulkRate: bulkRate ?? this.bulkRate,
    );
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     