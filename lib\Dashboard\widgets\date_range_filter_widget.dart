import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Configuration class for date range display - same as FilterStatusBar approach
class DateRangeDisplayConfig {
  final String label;
  final bool showValue;
  final bool showLabel;

  const DateRangeDisplayConfig({
    required this.label,
    required this.showValue,
    this.showLabel = false,  // Default to showing only values
  });
}

/// Universal date range filter widget with consistent layout across all modules
///
/// This widget follows the same pattern as HistoryRecordCard, FilterStatusBar, and FilterWidget:
/// - One universal widget class
/// - Factory constructors for each module (.weight(), .breeding(), .cattle(), etc.)
/// - Complex logic handled internally
/// - Simple usage in modules
/// - Consistent responsive design
/// - Module-specific theme colors
///
/// Usage Examples:
/// ```dart
/// // Weight Module
/// DateRangeFilterWidget.weight(
///   startDate: startDate,
///   endDate: endDate,
///   onStartDateChanged: _onStartDateChanged,
///   onEndDateChanged: _onEndDateChanged,
///   onFiltersChanged: _onFiltersChanged,
/// )
///
/// // Breeding Module
/// DateRangeFilterWidget.breeding(
///   startDate: startDate,
///   endDate: endDate,
///   onStartDateChanged: _onStartDateChanged,
///   onEndDateChanged: _onEndDateChanged,
///   onFiltersChanged: _onFiltersChanged,
/// )
/// ```
class DateRangeFilterWidget extends StatelessWidget {
  // Global constants for consistent layout
  static const double _buttonHeight = 48.0;
  static const double _compactButtonHeight = 40.0;

  // Date range display configuration - same as FilterStatusBar approach
  static const Map<String, DateRangeDisplayConfig> _dateRangeConfigs = {
    'dateRange': DateRangeDisplayConfig(label: 'Date range', showValue: false, showLabel: true),
    'today': DateRangeDisplayConfig(label: 'Today', showValue: false, showLabel: true),
    'yesterday': DateRangeDisplayConfig(label: 'Yesterday', showValue: false, showLabel: true),
    'last7Days': DateRangeDisplayConfig(label: 'Last 7 Days', showValue: false, showLabel: true),
    'last30Days': DateRangeDisplayConfig(label: 'Last 30 Days', showValue: false, showLabel: true),
    'last90Days': DateRangeDisplayConfig(label: 'Last 90 Days', showValue: false, showLabel: true),
    'last6Months': DateRangeDisplayConfig(label: 'Last 6 Months', showValue: false, showLabel: true),
    'custom': DateRangeDisplayConfig(label: 'Custom Range', showValue: false, showLabel: true),
  };
  final DateTime? startDate;  // Made nullable
  final DateTime? endDate;    // Made nullable
  final Color themeColor;
  final Function(DateTime) onStartDateChanged;
  final Function(DateTime) onEndDateChanged;
  final VoidCallback? onFiltersChanged; // Optional callback for additional actions
  final VoidCallback? onClearFilter; // New: callback to clear the filter

  // UI customization for flexible layouts
  final bool compact;
  final bool iconOnly;
  final EdgeInsets? padding;
  final double? buttonHeight;
  final double? activeBackgroundOpacity; // New: control active background opacity

  const DateRangeFilterWidget({
    Key? key,
    this.startDate,  // Made optional
    this.endDate,    // Made optional
    required this.themeColor,
    required this.onStartDateChanged,
    required this.onEndDateChanged,
    this.onFiltersChanged,
    this.onClearFilter,
    this.compact = false,
    this.iconOnly = false,
    this.padding,
    this.buttonHeight,
    this.activeBackgroundOpacity, // New parameter for background opacity
  }) : super(key: key);

  /// Factory constructor for Weight Module
  factory DateRangeFilterWidget.weight({
    DateTime? startDate,
    DateTime? endDate,
    required Function(DateTime) onStartDateChanged,
    required Function(DateTime) onEndDateChanged,
    VoidCallback? onFiltersChanged,
    VoidCallback? onClearFilter,
    bool compact = false,
    bool iconOnly = false,
    EdgeInsets? padding,
    double? buttonHeight,
    double? activeBackgroundOpacity,
  }) {
    return DateRangeFilterWidget(
      startDate: startDate,
      endDate: endDate,
      themeColor: Colors.purple,
      onStartDateChanged: onStartDateChanged,
      onEndDateChanged: onEndDateChanged,
      onFiltersChanged: onFiltersChanged,
      onClearFilter: onClearFilter,
      compact: compact,
      iconOnly: iconOnly,
      padding: padding,
      buttonHeight: buttonHeight ?? (compact ? _compactButtonHeight : _buttonHeight),
      activeBackgroundOpacity: activeBackgroundOpacity,
    );
  }

  /// Factory constructor for Breeding Module
  factory DateRangeFilterWidget.breeding({
    DateTime? startDate,
    DateTime? endDate,
    required Function(DateTime) onStartDateChanged,
    required Function(DateTime) onEndDateChanged,
    VoidCallback? onFiltersChanged,
    VoidCallback? onClearFilter,
    bool compact = false,
    bool iconOnly = false,
    EdgeInsets? padding,
    double? buttonHeight,
    double? activeBackgroundOpacity,
  }) {
    return DateRangeFilterWidget(
      startDate: startDate,
      endDate: endDate,
      themeColor: Colors.purple,
      onStartDateChanged: onStartDateChanged,
      onEndDateChanged: onEndDateChanged,
      onFiltersChanged: onFiltersChanged,
      onClearFilter: onClearFilter,
      compact: compact,
      iconOnly: iconOnly,
      padding: padding,
      buttonHeight: buttonHeight ?? (compact ? _compactButtonHeight : _buttonHeight),
      activeBackgroundOpacity: activeBackgroundOpacity,
    );
  }

  /// Factory constructor for Health Module
  factory DateRangeFilterWidget.health({
    DateTime? startDate,
    DateTime? endDate,
    required Function(DateTime) onStartDateChanged,
    required Function(DateTime) onEndDateChanged,
    VoidCallback? onFiltersChanged,
    VoidCallback? onClearFilter,
    bool compact = false,
    bool iconOnly = false,
    EdgeInsets? padding,
    double? buttonHeight,
    double? activeBackgroundOpacity,
  }) {
    return DateRangeFilterWidget(
      startDate: startDate,
      endDate: endDate,
      themeColor: Colors.red,
      onStartDateChanged: onStartDateChanged,
      onEndDateChanged: onEndDateChanged,
      onFiltersChanged: onFiltersChanged,
      onClearFilter: onClearFilter,
      compact: compact,
      iconOnly: iconOnly,
      padding: padding,
      buttonHeight: buttonHeight ?? (compact ? _compactButtonHeight : _buttonHeight),
      activeBackgroundOpacity: activeBackgroundOpacity,
    );
  }

  /// Factory constructor for Cattle Module
  factory DateRangeFilterWidget.cattle({
    DateTime? startDate,
    DateTime? endDate,
    required Function(DateTime) onStartDateChanged,
    required Function(DateTime) onEndDateChanged,
    VoidCallback? onFiltersChanged,
    VoidCallback? onClearFilter,
    bool compact = false,
    bool iconOnly = false,
    EdgeInsets? padding,
    double? buttonHeight,
    double? activeBackgroundOpacity,
  }) {
    return DateRangeFilterWidget(
      startDate: startDate,
      endDate: endDate,
      themeColor: Colors.purple,
      onStartDateChanged: onStartDateChanged,
      onEndDateChanged: onEndDateChanged,
      onFiltersChanged: onFiltersChanged,
      onClearFilter: onClearFilter,
      compact: compact,
      iconOnly: iconOnly,
      padding: padding,
      buttonHeight: buttonHeight ?? (compact ? _compactButtonHeight : _buttonHeight),
      activeBackgroundOpacity: activeBackgroundOpacity,
    );
  }

  /// Factory constructor for Transaction Module
  factory DateRangeFilterWidget.transaction({
    DateTime? startDate,
    DateTime? endDate,
    required Function(DateTime) onStartDateChanged,
    required Function(DateTime) onEndDateChanged,
    VoidCallback? onFiltersChanged,
    VoidCallback? onClearFilter,
    bool compact = false,
    bool iconOnly = false,
    EdgeInsets? padding,
    double? buttonHeight,
    double? activeBackgroundOpacity,
  }) {
    return DateRangeFilterWidget(
      startDate: startDate,
      endDate: endDate,
      themeColor: Colors.green,
      onStartDateChanged: onStartDateChanged,
      onEndDateChanged: onEndDateChanged,
      onFiltersChanged: onFiltersChanged,
      onClearFilter: onClearFilter,
      compact: compact,
      iconOnly: iconOnly,
      padding: padding,
      buttonHeight: buttonHeight ?? (compact ? _compactButtonHeight : _buttonHeight),
      activeBackgroundOpacity: activeBackgroundOpacity,
    );
  }

  /// Factory constructor for Milk Module
  factory DateRangeFilterWidget.milk({
    DateTime? startDate,
    DateTime? endDate,
    required Function(DateTime) onStartDateChanged,
    required Function(DateTime) onEndDateChanged,
    VoidCallback? onFiltersChanged,
    VoidCallback? onClearFilter,
    bool compact = false,
    bool iconOnly = false,
    EdgeInsets? padding,
    double? buttonHeight,
    double? activeBackgroundOpacity,
  }) {
    return DateRangeFilterWidget(
      startDate: startDate,
      endDate: endDate,
      themeColor: Colors.blue,
      onStartDateChanged: onStartDateChanged,
      onEndDateChanged: onEndDateChanged,
      onFiltersChanged: onFiltersChanged,
      onClearFilter: onClearFilter,
      compact: compact,
      iconOnly: iconOnly,
      padding: padding,
      buttonHeight: buttonHeight ?? (compact ? _compactButtonHeight : _buttonHeight),
      activeBackgroundOpacity: activeBackgroundOpacity,
    );
  }

  /// Factory constructor for Notifications Module
  factory DateRangeFilterWidget.notifications({
    DateTime? startDate,
    DateTime? endDate,
    required Function(DateTime) onStartDateChanged,
    required Function(DateTime) onEndDateChanged,
    VoidCallback? onFiltersChanged,
    VoidCallback? onClearFilter,
    bool compact = false,
    bool iconOnly = false,
    EdgeInsets? padding,
    double? buttonHeight,
    double? activeBackgroundOpacity,
  }) {
    return DateRangeFilterWidget(
      startDate: startDate,
      endDate: endDate,
      themeColor: Colors.orange,
      onStartDateChanged: onStartDateChanged,
      onEndDateChanged: onEndDateChanged,
      onFiltersChanged: onFiltersChanged,
      onClearFilter: onClearFilter,
      compact: compact,
      iconOnly: iconOnly,
      padding: padding,
      buttonHeight: buttonHeight ?? (compact ? _compactButtonHeight : _buttonHeight),
      activeBackgroundOpacity: activeBackgroundOpacity,
    );
  }

  /// Factory constructor for Analytics Module
  factory DateRangeFilterWidget.analytics({
    DateTime? startDate,
    DateTime? endDate,
    required Function(DateTime) onStartDateChanged,
    required Function(DateTime) onEndDateChanged,
    VoidCallback? onFiltersChanged,
    VoidCallback? onClearFilter,
    bool compact = false,
    bool iconOnly = false,
    EdgeInsets? padding,
    double? buttonHeight,
    double? activeBackgroundOpacity,
  }) {
    return DateRangeFilterWidget(
      startDate: startDate,
      endDate: endDate,
      themeColor: Colors.indigo,
      onStartDateChanged: onStartDateChanged,
      onEndDateChanged: onEndDateChanged,
      onFiltersChanged: onFiltersChanged,
      onClearFilter: onClearFilter,
      compact: compact,
      iconOnly: iconOnly,
      padding: padding,
      buttonHeight: buttonHeight ?? (compact ? _compactButtonHeight : _buttonHeight),
      activeBackgroundOpacity: activeBackgroundOpacity,
    );
  }

  @override
  Widget build(BuildContext context) {
    final hasActiveFilter = _hasActiveFilter();

    if (iconOnly) {
      return _buildIconOnlyButton(context, hasActiveFilter);
    }

    return Container(
      color: Colors.white,
      padding: padding ?? EdgeInsets.all(compact ? 4.0 : 8.0),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            height: buttonHeight ?? (compact ? 40 : 48),
            child: ElevatedButton.icon(
              onPressed: () => _showDateFilterDialog(context),
              icon: Icon(
                Icons.date_range,
                size: compact ? 18 : 24,
                color: hasActiveFilter ? Colors.white : themeColor,  // Purple when inactive, white when active
              ),
              label: Text(
                'Date Filter',
                style: TextStyle(
                  fontSize: compact ? 12 : 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: hasActiveFilter
                    ? themeColor.withValues(alpha: activeBackgroundOpacity ?? 1.0)  // Use custom opacity or default to solid
                    : themeColor.withValues(alpha: 0.1),
                foregroundColor: hasActiveFilter
                    ? Colors.white
                    : themeColor,
                elevation: hasActiveFilter ? 1 : 0,
                padding: EdgeInsets.symmetric(
                  horizontal: compact ? 8 : 16,
                  vertical: compact ? 8 : 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: hasActiveFilter
                      ? BorderSide(color: themeColor, width: 1.5)
                      : BorderSide.none,
                ),
              ),
            ),
          ),
          // Remove duplicate filter indicator - FilterStatusBar handles this
        ],
      ),
    );
  }

  Widget _buildIconOnlyButton(BuildContext context, bool hasActiveFilter) {
    return IconButton(
      onPressed: () => _showDateFilterDialog(context),
      icon: Icon(
        Icons.date_range,
        color: hasActiveFilter ? themeColor : themeColor,  // Always use theme color
      ),
      tooltip: 'Date Filter',
      style: IconButton.styleFrom(
        backgroundColor: hasActiveFilter
            ? themeColor.withValues(alpha: 0.1)
            : null,
      ),
    );
  }

  bool _hasActiveFilter() {
    // Filter is active only if both dates are set (not null)
    return startDate != null && endDate != null;
  }





  void _showDateFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        DateTime localStartDate = startDate ?? DateTime.now().subtract(const Duration(days: 30));
        DateTime localEndDate = endDate ?? DateTime.now();
        bool showCustomRange = false;

        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: IntrinsicHeight(
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.9,
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width < 600
                        ? MediaQuery.of(context).size.width * 0.95
                        : 450,
                    maxHeight: MediaQuery.of(context).size.height * 0.8,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [themeColor, themeColor],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(16),
                            topRight: Radius.circular(16),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              showCustomRange ? Icons.date_range : Icons.filter_list,
                              color: Colors.white,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                showCustomRange ? 'Custom Date Range' : 'Date Filter',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                  fontSize: 18,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (showCustomRange)
                              IconButton(
                                onPressed: () {
                                  setState(() {
                                    showCustomRange = false;
                                  });
                                },
                                icon: const Icon(Icons.arrow_back, color: Colors.white),
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                          ],
                        ),
                      ),
                      // Content
                      AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        child: showCustomRange
                            ? _buildCustomDateRangeContent(
                                context,
                                setState,
                                localStartDate,
                                localEndDate,
                                (newStartDate) => localStartDate = newStartDate,
                                (newEndDate) => localEndDate = newEndDate,
                              )
                            : _buildQuickFilterContent(context, setState, (value) {
                                setState(() {
                                  showCustomRange = value;
                                });
                              }),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildQuickFilterContent(BuildContext context, StateSetter setState, Function(bool) setShowCustomRange) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildQuickFilterOption(context, 'Today', 0),
          _buildQuickFilterOption(context, 'Yesterday', 1),
          _buildQuickFilterOption(context, 'Last 7 Days', 7),
          _buildQuickFilterOption(context, 'Last 30 Days', 30),
          _buildQuickFilterOption(context, 'Last 90 Days', 90),
          _buildQuickFilterOption(context, 'Last 6 Months', 180),
          const SizedBox(height: 20),
          // Custom Range Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                setShowCustomRange(true);
              },
              icon: Icon(Icons.date_range, color: themeColor),
              label: Text(
                'Custom Date Range',
                style: TextStyle(color: themeColor),
              ),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: themeColor),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFilterOption(BuildContext context, String label, int days) {
    bool isSelected = _isDateRangeSelected(days);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: InkWell(
        onTap: () {
          Navigator.of(context).pop();
          _applyQuickFilter(days);
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? themeColor.withValues(alpha: 0.1) : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? themeColor : themeColor.withValues(alpha: 0.3),  // Always use theme color
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                color: isSelected ? themeColor : themeColor,  // Always use theme color
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                label,
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? themeColor : Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomDateRangeContent(BuildContext context, StateSetter setState, DateTime localStartDate, DateTime localEndDate, Function(DateTime) updateStartDate, Function(DateTime) updateEndDate) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Start Date
          InkWell(
            onTap: () async {
              final picked = await showDatePicker(
                context: context,
                initialDate: localStartDate,
                firstDate: DateTime(2020),
                lastDate: localEndDate,
              );
              if (picked != null) {
                setState(() {
                  updateStartDate(picked);
                });
              }
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: themeColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.calendar_today, color: themeColor),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Start Date', style: TextStyle(fontSize: 12, color: themeColor.withValues(alpha: 0.7))),
                      Text(
                        DateFormat('MMM dd, yyyy').format(localStartDate),
                        style: TextStyle(fontSize: 16, color: themeColor, fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          // End Date
          InkWell(
            onTap: () async {
              final picked = await showDatePicker(
                context: context,
                initialDate: localEndDate,
                firstDate: localStartDate,
                lastDate: DateTime.now(),
              );
              if (picked != null) {
                setState(() {
                  updateEndDate(picked);
                });
              }
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: themeColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.calendar_today, color: themeColor),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('End Date', style: TextStyle(fontSize: 12, color: themeColor.withValues(alpha: 0.7))),
                      Text(
                        DateFormat('MMM dd, yyyy').format(localEndDate),
                        style: TextStyle(fontSize: 16, color: themeColor, fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          // Action Buttons
          Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    onStartDateChanged(localStartDate);
                    onEndDateChanged(localEndDate);
                    onFiltersChanged?.call();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: themeColor,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Apply'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  bool _isDateRangeSelected(int days) {
    if (startDate == null || endDate == null) return false;

    final now = DateTime.now();
    DateTime expectedStart;
    DateTime expectedEnd = DateTime(now.year, now.month, now.day, 23, 59, 59);

    if (days == 0) {
      expectedStart = DateTime(now.year, now.month, now.day);
    } else if (days == 1) {
      final yesterday = now.subtract(const Duration(days: 1));
      expectedStart = DateTime(yesterday.year, yesterday.month, yesterday.day);
      expectedEnd = DateTime(yesterday.year, yesterday.month, yesterday.day, 23, 59, 59);
    } else {
      expectedStart = now.subtract(Duration(days: days));
    }

    return startDate!.year == expectedStart.year &&
           startDate!.month == expectedStart.month &&
           startDate!.day == expectedStart.day &&
           endDate!.year == expectedEnd.year &&
           endDate!.month == expectedEnd.month &&
           endDate!.day == expectedEnd.day;
  }

  void _applyQuickFilter(int days) {
    final now = DateTime.now();
    
    if (days == 0) {
      onStartDateChanged(DateTime(now.year, now.month, now.day));
      onEndDateChanged(DateTime(now.year, now.month, now.day, 23, 59, 59));
    } else if (days == 1) {
      final yesterday = now.subtract(const Duration(days: 1));
      onStartDateChanged(DateTime(yesterday.year, yesterday.month, yesterday.day));
      onEndDateChanged(DateTime(yesterday.year, yesterday.month, yesterday.day, 23, 59, 59));
    } else {
      onStartDateChanged(now.subtract(Duration(days: days)));
      onEndDateChanged(now);
    }
    
    onFiltersChanged?.call();
  }

  /// Get the appropriate display text for the current date range selection
  String _getDateRangeDisplayText() {
    if (startDate == null || endDate == null) {
      return 'Date Filter';  // Default text when no filter is active
    }

    // Check for specific date range patterns
    if (_isDateRangeSelected(0)) {
      return _dateRangeConfigs['today']!.label;  // "Today"
    } else if (_isDateRangeSelected(1)) {
      return _dateRangeConfigs['yesterday']!.label;  // "Yesterday"
    } else if (_isDateRangeSelected(7)) {
      return _dateRangeConfigs['last7Days']!.label;  // "Last 7 Days"
    } else if (_isDateRangeSelected(30)) {
      return _dateRangeConfigs['last30Days']!.label;  // "Last 30 Days"
    } else if (_isDateRangeSelected(90)) {
      return _dateRangeConfigs['last90Days']!.label;  // "Last 90 Days"
    } else if (_isDateRangeSelected(180)) {
      return _dateRangeConfigs['last6Months']!.label;  // "Last 6 Months"
    } else {
      // Custom date range - show the actual dates
      final startFormatted = DateFormat('MMM dd').format(startDate!);
      final endFormatted = DateFormat('MMM dd').format(endDate!);

      // If same year, don't repeat it
      if (startDate!.year == endDate!.year && startDate!.year == DateTime.now().year) {
        return '$startFormatted - $endFormatted';
      } else {
        final startWithYear = DateFormat('MMM dd, yyyy').format(startDate!);
        final endWithYear = DateFormat('MMM dd, yyyy').format(endDate!);
        return '$startWithYear - $endWithYear';
      }
    }
  }

}
