import 'package:flutter/material.dart';
import '../../../routes/app_routes.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class MilkScreen extends StatelessWidget {
  const MilkScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Milk Production'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor