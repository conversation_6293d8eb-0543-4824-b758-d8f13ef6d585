#!/usr/bin/env python3
"""
Validation script to check responsive design implementation across the Cattle Manager App.
This script verifies that all screens, dialogs, and widgets have been properly updated.
"""

import os
import re
from pathlib import Path

def check_responsive_imports(file_path):
    """Check if file has responsive imports."""
    with open(file_path, 'r') as f:
        content = f.read()
        has_responsive_helper = 'responsive_helper.dart' in content
        has_responsive_layout = 'responsive_layout.dart' in content
        has_responsive_theme = 'responsive_theme.dart' in content
        return has_responsive_helper, has_responsive_layout, has_responsive_theme

def check_responsive_patterns(file_path):
    """Check for responsive design patterns in file."""
    with open(file_path, 'r') as f:
        content = f.read()
        
        patterns = {
            'ResponsiveHelper': 'ResponsiveHelper.' in content,
            'ResponsiveLayout': any(widget in content for widget in [
                'ResponsiveGridView', 'ResponsiveContainer', 'ResponsiveDialog',
                'ResponsiveCard', 'ResponsiveListTile', 'ResponsiveText', 'ResponsiveButton'
            ]),
            'ResponsiveTheme': 'ResponsiveTheme.' in content,
            'MediaQuery_usage': 'MediaQuery.of(context)' in content,
            'LayoutBuilder_usage': 'LayoutBuilder(' in content,
        }
        
        return patterns

def analyze_file(file_path):
    """Analyze a single file for responsive implementation."""
    try:
        helper, layout, theme = check_responsive_imports(file_path)
        patterns = check_responsive_patterns(file_path)
        
        return {
            'file': file_path,
            'imports': {
                'responsive_helper': helper,
                'responsive_layout': layout,
                'responsive_theme': theme
            },
            'patterns': patterns,
            'responsive_score': sum([helper, layout, theme]) + sum(patterns.values())
        }
    except Exception as e:
        return {
            'file': file_path,
            'error': str(e),
            'responsive_score': 0
        }

def main():
    """Main validation function."""
    print("🔍 Validating Responsive Design Implementation")
    print("=" * 60)
    
    # Change to workspace directory
    os.chdir('/mnt/persist/workspace')
    
    # Find all relevant Dart files
    dart_files = []
    for root, dirs, files in os.walk('lib'):
        for file in files:
            if file.endswith('.dart'):
                file_path = os.path.join(root, file)
                # Focus on screens, dialogs, widgets, and tabs
                if any(keyword in file for keyword in ['screen', 'dialog', 'widget', 'tab', 'view']):
                    dart_files.append(file_path)
    
    print(f"📁 Found {len(dart_files)} files to analyze\n")
    
    # Analyze each file
    results = []
    for file_path in sorted(dart_files):
        result = analyze_file(file_path)
        results.append(result)
    
    # Generate report
    print("📊 RESPONSIVE DESIGN ANALYSIS REPORT")
    print("=" * 60)
    
    # Summary statistics
    total_files = len(results)
    files_with_responsive_imports = sum(1 for r in results if r.get('imports', {}).get('responsive_helper', False))
    files_with_responsive_patterns = sum(1 for r in results if r.get('patterns', {}).get('ResponsiveLayout', False))
    high_score_files = sum(1 for r in results if r.get('responsive_score', 0) >= 5)
    
    print(f"📈 Summary Statistics:")
    print(f"   Total files analyzed: {total_files}")
    print(f"   Files with responsive imports: {files_with_responsive_imports} ({files_with_responsive_imports/total_files*100:.1f}%)")
    print(f"   Files with responsive patterns: {files_with_responsive_patterns} ({files_with_responsive_patterns/total_files*100:.1f}%)")
    print(f"   High responsive score files: {high_score_files} ({high_score_files/total_files*100:.1f}%)")
    print()
    
    # Detailed breakdown by category
    categories = {
        'Screens': [r for r in results if 'screen' in r['file']],
        'Dialogs': [r for r in results if 'dialog' in r['file']],
        'Widgets': [r for r in results if 'widget' in r['file']],
        'Tabs': [r for r in results if 'tab' in r['file'] or 'view' in r['file']],
    }
    
    for category, files in categories.items():
        if files:
            responsive_count = sum(1 for f in files if f.get('responsive_score', 0) >= 3)
            print(f"📱 {category}: {responsive_count}/{len(files)} responsive ({responsive_count/len(files)*100:.1f}%)")
    
    print()
    
    # Top responsive files
    print("🏆 TOP RESPONSIVE FILES:")
    top_files = sorted(results, key=lambda x: x.get('responsive_score', 0), reverse=True)[:10]
    for i, file_result in enumerate(top_files, 1):
        score = file_result.get('responsive_score', 0)
        file_name = file_result['file'].replace('lib/', '')
        print(f"   {i:2d}. {file_name:<50} (Score: {score})")
    
    print()
    
    # Files needing attention
    print("⚠️  FILES NEEDING ATTENTION:")
    low_score_files = [r for r in results if r.get('responsive_score', 0) < 3 and 'error' not in r]
    if low_score_files:
        for file_result in low_score_files[:10]:
            score = file_result.get('responsive_score', 0)
            file_name = file_result['file'].replace('lib/', '')
            print(f"   • {file_name:<50} (Score: {score})")
    else:
        print("   ✅ All files have good responsive implementation!")
    
    print()
    
    # Responsive utilities usage
    print("🛠️  RESPONSIVE UTILITIES USAGE:")
    helper_usage = sum(1 for r in results if r.get('patterns', {}).get('ResponsiveHelper', False))
    layout_usage = sum(1 for r in results if r.get('patt