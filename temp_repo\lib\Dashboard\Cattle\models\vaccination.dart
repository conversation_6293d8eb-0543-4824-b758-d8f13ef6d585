import 'package:flutter/material.dart';
import '../utils/responsive_helper.dart';
import '../utils/responsive_layout.dart';
import '../theme/responsive_theme.dart';

class SetupMenuItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final double? size;

  const SetupMenuItem({
    Key? key,
    required this.icon,
    required this.title,
    required this.onTap,
    this.size,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final iconSize = size ?? ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 40.0,
      tablet: 48.0,
      desktop: 56.0,
    );

    return ResponsiveCard(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: iconSize,
            color: _getIconColor(title),
          ),
          SizedBox(height: ResponsiveSpacing.getSM(context)),
          ResponsiveText(
            title,
            style: ResponsiveTheme.getSubtitleStyle(c