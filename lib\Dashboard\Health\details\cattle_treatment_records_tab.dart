import 'package:flutter/material.dart';
import 'dart:async';
import '../../Cattle/models/cattle_isar.dart';
import '../dialogs/treatment_form_dialog.dart';
import '../widgets/treatment_record_card.dart';
import '../../../utils/message_utils.dart';
import 'package:get_it/get_it.dart';
import '../../../services/streams/stream_service.dart';
import '../models/treatment_record_isar.dart';
import '../services/health_service.dart';

class CattleTreatmentRecordsTab extends StatefulWidget {
  final CattleIsar cattle;
  final VoidCallback onCattleUpdated;

  const CattleTreatmentRecordsTab({
    Key? key,
    required this.cattle,
    required this.onCattleUpdated,
  }) : super(key: key);

  @override
  State<CattleTreatmentRecordsTab> createState() => _CattleTreatmentRecordsTabState();
}

class _CattleTreatmentRecordsTabState extends State<CattleTreatmentRecordsTab> {
  final HealthService _healthService = HealthService.instance;

  List<TreatmentRecordIsar> _treatmentRecords = [];
  bool _isLoading = true;

  // Stream subscriptions
  StreamSubscription<Map<String, dynamic>>? _treatmentChangeSubscription;

  @override
  void initState() {
    super.initState();
    _setupStreamSubscriptions();
    _loadTreatmentRecords();
  }

  @override
  void dispose() {
    _treatmentChangeSubscription?.cancel();
    super.dispose();
  }

  void _setupStreamSubscriptions() {
    try {
      final streamService = GetIt.instance<StreamService>();
      _treatmentChangeSubscription = streamService.treatmentStream.listen((_) {
        _loadTreatmentRecords();
      });
    } catch (e) {
      debugPrint('Warning: Could not set up stream subscriptions: $e');
    }
  }

  Future<void> _loadTreatmentRecords() async {
    if (!mounted) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      await _healthService.initialize();
      final records = await _healthService.getTreatmentRecordsForCattle(widget.cattle.businessId ?? '');
      
      if (mounted) {
        setState(() {
          _treatmentRecords = records;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        MessageUtils.showErrorSnackBar(context, 'Failed to load treatment records: $e');
      }
    }
  }

  void _addTreatmentRecord() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TreatmentFormDialog(
          cattle: [widget.cattle],
          preSelectedCattle: widget.cattle,
          onRecordAdded: () {
            _loadTreatmentRecords();
            widget.onCattleUpdated();
          },
        ),
      ),
    );
  }

  void _editTreatmentRecord(TreatmentRecordIsar record) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TreatmentFormDialog(
          cattle: [widget.cattle],
          existingRecord: record,
          onRecordAdded: () {
            _loadTreatmentRecords();
            widget.onCattleUpdated();
          },
        ),
      ),
    );
  }

  Future<void> _deleteTreatmentRecord(TreatmentRecordIsar record) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Treatment Record'),
        content: const Text('Are you sure you want to delete this treatment record?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _healthService.deleteTreatmentRecord(record.businessId ?? '');
        _loadTreatmentRecords();
        widget.onCattleUpdated();
        if (mounted) {
          MessageUtils.showSuccessSnackBar(context, 'Treatment record deleted successfully');
        }
      } catch (e) {
        if (mounted) {
          MessageUtils.showErrorSnackBar(context, 'Failed to delete treatment record: $e');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Treatment Status Summary
                _buildTreatmentStatusSummary(),
                
                // Treatment Records List
                Expanded(
                  child: _treatmentRecords.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          padding: const EdgeInsets.all(16.0),
                          itemCount: _treatmentRecords.length,
                          itemBuilder: (context, index) {
                            final record = _treatmentRecords[index];
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 8.0),
                              child: TreatmentRecordCard(
                                record: record,
                                cattle: widget.cattle,
                                onEdit: () => _editTreatmentRecord(record),
                                onDelete: () => _deleteTreatmentRecord(record),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addTreatmentRecord,
        backgroundColor: Colors.blue,
        child: const Icon(Icons.medication, color: Colors.white),
      ),
    );
  }

  Widget _buildTreatmentStatusSummary() {
    final totalRecords = _treatmentRecords.length;
    final activeRecords = _treatmentRecords.where((record) {
      return record.status?.toLowerCase() == 'ongoing' ||
             record.status?.toLowerCase() == 'active';
    }).length;
    final completedRecords = _treatmentRecords.where((record) {
      return record.status?.toLowerCase() == 'completed';
    }).length;

    return Container(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.medication, color: Colors.blue, size: 24),
              const SizedBox(width: 12),
              const Text(
                'Treatment Summary',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Total Treatments',
                  totalRecords.toString(),
                  Icons.medical_services,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  'Active',
                  activeRecords.toString(),
                  Icons.schedule,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  'Completed',
                  completedRecords.toString(),
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.medication_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Treatment Records',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add the first treatment record for ${widget.cattle.name ?? 'this cattle'}',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _addTreatmentRecord,
            icon: const Icon(Icons.add),
            label: const Text('Add Treatment Record'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
