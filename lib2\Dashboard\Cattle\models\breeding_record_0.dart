class BreedingRecord {
  final DateTime date;
  final String type;
  final String? partner;
  final String? notes;

  BreedingRecord({
    required this.date,
    required this.type,
    this.partner,
    this.notes,
  });

  Map<String, dynamic> toMap() {
    return {
      'date': date.toIso8601String(),
      'type': type,
      'partner': partner,
      'notes': notes,
    };
  }

  factory BreedingRecord.fromMap(Map<String, dynamic> map) {
    return BreedingRecord(
      date: DateTime.parse(map['date']),
      type: map['type'],
      partner: map['partner'],
      notes: map['notes'],
    );
  }
}