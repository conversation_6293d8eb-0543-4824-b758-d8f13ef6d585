import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import 'Dashboard/Transactions/models/transaction.dart';
import 'Dashboard/Transactions/models/category.dart';
import 'Dashboard/dashboard_screen.dart';
import 'theme/app_theme.dart';
import 'services/logging_service.dart';
import 'services/database_helper.dart';
import 'routes/app_routes.dart';
import 'Dashboard/Reports/screens/transactions_report_screen.dart';
import 'Dashboard/Reports/screens/milk_report_screen.dart';
import 'Dashboard/Reports/screens/events_report_screen.dart';
import 'Dashboard/Reports/screens/breeding_report_screen.dart';
import 'Dashboard/Reports/screens/cattle_report_screen.dart';
import 'Dashboard/Cattle/screens/cattle_screen.dart';
import 'Dashboard/Breeding/screens/breeding_screen.dart';
import 'Dashboard/Health/screens/health_screen.dart';
import 'Dashboard/Milk Records/screens/milk_screen.dart';
import 'Dashboard/Transactions/screens/transactions_screen.dart';
import 'Dashboard/Farm Setup/screens/farm_setup_screen.dart';
import 'Dashboard/Reports/screens/weight_report_screen.dart';
import 'Dashboard/Reports/screens/pregnancies_report_screen.dart';
import 'Dashboard/Reports/screens/health_report_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Logging Service
  final loggingService = LoggingService();
  loggingService.setupLogging();
  loggingService.logUncaughtExceptions();

  // Initialize Hive
  await Hive.initFlutter();

  // Register Hive Adapters
  Hive.registerAdapter(TransactionAdapter());
  Hive.registerAdapter(CategoryAdapter());

  // Open Hive Boxes
  await Hive.openBox<Transaction>('transactions');
  await Hive.openBox<Category>('income_categories');
  await Hive.openBox<Category>('expense_categories');

  // Initialize DatabaseHelper
  final dbHelper = DatabaseHelper.instance;

  runApp(CattleManagerApp(dbHelper: dbHelper));
}

class CattleManagerApp extends StatelessWidget {
  final DatabaseHelper dbHelper;

  const CattleManagerApp({super.key, required this.dbHelper});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: dbHelper,
      child: MaterialApp(
        title: 'Cattle Manager',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.primaryColor),
          useMaterial3: true,
          scaffoldBackgroundColor: AppTheme.scaffoldBackground,
          appBarTheme: AppTheme.appBarTheme,
          cardTheme: CardTheme(
            elevation: 2,
            shape: RoundedRectangleBorder(borderRadius: AppTheme.cardRadius),
          ),
        ),
        home: const DashboardScreen(),
        routes: {
          AppRoutes.cattle: (context) => const CattleScreen(),
          AppRoutes.breeding: (context) => const BreedingScreen(),
          AppRoutes.health: (context) => const HealthScreen(),
          AppRoutes.milk: (context) => const MilkScreen(),
          AppRoutes.transactions: (context) => const TransactionsScreen(),
          AppRoutes.settings: (context) => const FarmSetupScreen(),
          AppRoutes.transactionsReport: (context) => const TransactionsReportScreen(),
          AppRoutes.milkReport: (context) => const MilkReportScreen(),
          AppRoutes.cattleReport: (context) => const CattleReportScreen(),
          AppRoutes.eventsReport: (context) => const EventsReportScreen(),
          AppRoutes.breedingReport: (context) => const BreedingReportScreen(),
          AppRoutes.pregnanciesReport: (context) => const PregnanciesReportScreen(),
          AppRoutes.weightReport: (context) => const WeightReportScreen(),
          AppRoutes.healthReport: (context) => const HealthReportScreen(),
        },
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('en', ''),
        ],
      ),
    );
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         