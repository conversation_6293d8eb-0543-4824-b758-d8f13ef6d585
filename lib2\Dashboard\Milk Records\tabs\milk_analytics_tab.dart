import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../models/milk_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../widgets/date_range_filter_widget.dart';
import '../../widgets/filter_status_bar.dart';

class MilkAnalyticsTab extends StatefulWidget {
  final List<MilkRecordIsar> milkRecords;
  final Map<String, CattleIsar> cattleMap;
  final VoidCallback? onClearFilters;

  const MilkAnalyticsTab({
    Key? key,
    required this.milkRecords,
    required this.cattleMap,
    this.onClearFilters,
  }) : super(key: key);

  @override
  State<MilkAnalyticsTab> createState() => _MilkAnalyticsTabState();
}

class _MilkAnalyticsTabState extends State<MilkAnalyticsTab> {
  // Multi-colors for milk analytics following the pattern
  static const _analyticsColor = Color(0xFF8E44AD); // Purple for analytics
  static const _productionColor = Color(0xFF1976D2); // Blue for production
  // static const _sessionColor = Color(0xFF2E7D32); // Green for sessions - Unused
  // static const _cattleColor = Color(0xFFE74C3C); // Red for cattle - Unused

  // Chart colors
  static const List<Color> _chartColors = [
    Color(0xFF1976D2), // Blue
    Color(0xFF2E7D32), // Green
    Color(0xFF8E44AD), // Purple
    Color(0xFFE74C3C), // Red
    Color(0xFFFF9800), // Orange
    Color(0xFF607D8B), // Blue Grey
  ];

  // Filter states
  DateTime? _startDate;
  DateTime? _endDate;
  // bool _showTrendChart = true; // Unused field



  // Chart type state
  int _selectedChartType = 0; // 0: Trend, 1: Distribution, 2: Bar, 3: Scatter, 4: Multi-Series

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        // Refresh data if needed
      },
      child: ListView(
        padding: EdgeInsets.all(_getResponsivePadding()),
        children: [
          // Date Range Filter
          DateRangeFilterWidget(
            startDate: _startDate,
            endDate: _endDate,
            onStartDateChanged: (date) => setState(() => _startDate = date),
            onEndDateChanged: (date) => setState(() => _endDate = date),
            onClearFilter: () {
              setState(() {
                _startDate = null;
                _endDate = null;
              });
            },
            themeColor: _analyticsColor,
            compact: false,
          ),
          const SizedBox(height: 4),

          // Filter Status Bar
          FilterStatusBar.analytics(
            filterStates: {
              'startDate': _startDate,
              'endDate': _endDate,
            },
            totalCount: widget.milkRecords.length,
            filteredCount: _getFilteredRecords().length,
            onClearFilters: _clearFilters,
            defaultStartDate: DateTime.now().subtract(const Duration(days: 30)),
            defaultEndDate: DateTime.now(),
          ),
          SizedBox(height: _getResponsiveSpacing()),
          _buildAnalyticsCards(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildChartsSection(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildProductionAnalysis(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildCattlePerformanceRankings(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildExportSection(),
        ],
      ),
    );
  }



  Widget _buildAnalyticsCards() {
    final analytics = _calculateAnalytics();
    
    if (MediaQuery.of(context).size.width < 600) {
      // Mobile layout - 2x2 grid
      return Column(
        children: [
          Row(
            children: [
              Expanded(child: _buildAnalyticsCard('Total Records', '${analytics['totalRecords']}', Icons.list, _chartColors[0])),
              SizedBox(width: _getResponsivePadding() * 0.5),
              Expanded(child: _buildAnalyticsCard('Avg Daily', '${analytics['avgDaily']?.toStringAsFixed(1) ?? '0'} L', Icons.water_drop, _chartColors[1])),
            ],
          ),
          SizedBox(height: _getResponsivePadding() * 0.5),
          Row(
            children: [
              Expanded(child: _buildAnalyticsCard('Total Production', '${analytics['totalProduction']?.toStringAsFixed(1) ?? '0'} L', Icons.local_drink, _chartColors[2])),
              SizedBox(width: _getResponsivePadding() * 0.5),
              Expanded(child: _buildAnalyticsCard('Active Cattle', '${analytics['activeCattle']}', Icons.pets, _chartColors[3])),
            ],
          ),
          SizedBox(height: _getResponsivePadding() * 0.5),
          Row(
            children: [
              Expanded(child: _buildAnalyticsCard('Best Day', '${analytics['bestDay']?.toStringAsFixed(1) ?? '0'} L', Icons.star, _chartColors[4])),
              SizedBox(width: _getResponsivePadding() * 0.5),
              Expanded(child: _buildAnalyticsCard('Efficiency', '${analytics['efficiency']?.toStringAsFixed(1) ?? '0'}%', Icons.speed, _chartColors[5])),
            ],
          ),
        ],
      );
    } else {
      // Desktop layout - single row
      return Row(
        children: [
          Expanded(child: _buildAnalyticsCard('Total Records', '${analytics['totalRecords']}', Icons.list, _chartColors[0])),
          SizedBox(width: _getResponsivePadding() * 0.5),
          Expanded(child: _buildAnalyticsCard('Avg Daily', '${analytics['avgDaily']?.toStringAsFixed(1) ?? '0'} L', Icons.water_drop, _chartColors[1])),
          SizedBox(width: _getResponsivePadding() * 0.5),
          Expanded(child: _buildAnalyticsCard('Total Production', '${analytics['totalProduction']?.toStringAsFixed(1) ?? '0'} L', Icons.local_drink, _chartColors[2])),
          SizedBox(width: _getResponsivePadding() * 0.5),
          Expanded(child: _buildAnalyticsCard('Active Cattle', '${analytics['activeCattle']}', Icons.pets, _chartColors[3])),
          SizedBox(width: _getResponsivePadding() * 0.5),
          Expanded(child: _buildAnalyticsCard('Best Day', '${analytics['bestDay']?.toStringAsFixed(1) ?? '0'} L', Icons.star, _chartColors[4])),
          SizedBox(width: _getResponsivePadding() * 0.5),
          Expanded(child: _buildAnalyticsCard('Efficiency', '${analytics['efficiency']?.toStringAsFixed(1) ?? '0'}%', Icons.speed, _chartColors[5])),
        ],
      );
    }
  }

  Widget _buildAnalyticsCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: _getResponsiveIconSize()),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize(),
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: _getResponsiveSpacing()),
          Text(
            value,
            style: TextStyle(
              fontSize: _getResponsiveFontSize() * 1.5,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Production Charts',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _analyticsColor,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildChartToggleButton('Trend', 0),
                _buildChartToggleButton('Distribution', 1),
                _buildChartToggleButton('Bar Chart', 2),
                _buildChartToggleButton('Scatter Plot', 3),
                _buildChartToggleButton('Multi-Series', 4),
              ],
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: _analyticsColor),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: _buildSelectedChart(),
          ),
        ),
      ],
    );
  }

  Widget _buildChartToggleButton(String label, int index) {
    final isSelected = _selectedChartType == index;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedChartType = index;
          // _showTrendChart = index == 0; // For backward compatibility - removed unused field
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? _analyticsColor : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: _analyticsColor),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : _analyticsColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedChart() {
    switch (_selectedChartType) {
      case 0:
        return _buildTrendChart();
      case 1:
        return _buildDistributionChart();
      case 2:
        return _buildBarChart();
      case 3:
        return _buildScatterPlot();
      case 4:
        return _buildMultiSeriesChart();
      default:
        return _buildTrendChart();
    }
  }

  Widget _buildTrendChart() {
    final spots = _getProductionTrendSpots();

    if (spots.isEmpty) {
      return const SizedBox(
        height: 350,
        child: Center(child: Text('No trend data available')),
      );
    }

    return SizedBox(
      height: 350,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: LineChart(
          LineChartData(
            gridData: FlGridData(show: true),
            titlesData: FlTitlesData(
              leftTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize: 40,
                  getTitlesWidget: (value, meta) => Text(
                    '${value.toInt()}L',
                    style: TextStyle(fontSize: 10, color: _analyticsColor),
                  ),
                ),
              ),
              bottomTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize: 30,
                  getTitlesWidget: (value, meta) {
                    final date = DateTime.fromMillisecondsSinceEpoch(value.toInt());
                    return Text(
                      DateFormat('MMM').format(date),
                      style: TextStyle(fontSize: 10, color: _analyticsColor),
                    );
                  },
                ),
              ),
              rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
              topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            ),
            borderData: FlBorderData(show: true, border: Border.all(color: _analyticsColor)),
            lineBarsData: [
              LineChartBarData(
                spots: spots,
                isCurved: true,
                color: _productionColor,
                barWidth: 3,
                dotData: FlDotData(show: true),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDistributionChart() {
    final distribution = _getSessionDistribution();

    if (distribution.isEmpty) {
      return const SizedBox(
        height: 350,
        child: Center(child: Text('No distribution data available')),
      );
    }

    return SizedBox(
      height: 350,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Pie Chart
            SizedBox(
              height: 250,
              child: Center(
                child: PieChart(
                  PieChartData(
                    sections: distribution.entries.map((entry) {
                      final index = distribution.keys.toList().indexOf(entry.key);
                      return PieChartSectionData(
                        value: entry.value.toDouble(),
                        title: '${entry.value.toStringAsFixed(1)}L',
                        color: _chartColors[index % _chartColors.length],
                        radius: 80,
                        titleStyle: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      );
                    }).toList(),
                    sectionsSpace: 2,
                    centerSpaceRadius: 40,
                  ),
                ),
              ),
            ),
            // Legends
            const SizedBox(height: 16),
            SizedBox(
              height: 50,
              child: _buildPieChartLegends(distribution),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPieChartLegends(Map<String, double> distribution) {
    return Wrap(
      alignment: WrapAlignment.center,
      children: distribution.entries.map((entry) {
        final index = distribution.keys.toList().indexOf(entry.key);
        final color = _chartColors[index % _chartColors.length];
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(width: 4),
              Text(
                entry.key,
                style: TextStyle(
                  fontSize: 12,
                  color: _analyticsColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildBarChart() {
    final filteredRecords = _getFilteredRecords();

    if (filteredRecords.isEmpty) {
      return const Center(
        child: Text(
          'No data available for bar chart',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    // Group data by daily view
    final Map<String, double> groupedData = {};

    for (final record in filteredRecords) {
      if (record.date == null) continue;

      final key = DateFormat('MMM dd').format(record.date!);
      groupedData[key] = (groupedData[key] ?? 0.0) + record.totalYield;
    }

    final sortedEntries = groupedData.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));

    return SizedBox(
      height: 350,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'Daily Production Bar Chart',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _analyticsColor,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 280, // Fixed height for the chart area
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: sortedEntries.map((entry) {
                  final maxValue = sortedEntries.map((e) => e.value).reduce((a, b) => a > b ? a : b);
                  final height = maxValue > 0 ? (entry.value / maxValue) * 220 : 0.0;

                  return Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Container(
                            height: height.clamp(0.0, 220.0),
                            decoration: BoxDecoration(
                              color: _chartColors[sortedEntries.indexOf(entry) % _chartColors.length],
                              borderRadius: const BorderRadius.vertical(top: Radius.circular(4)),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            entry.key,
                            style: const TextStyle(fontSize: 10),
                            textAlign: TextAlign.center,
                          ),
                          Text(
                            '${entry.value.toStringAsFixed(1)}L',
                            style: const TextStyle(fontSize: 8, fontWeight: FontWeight.bold),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScatterPlot() {
    final filteredRecords = _getFilteredRecords();

    if (filteredRecords.isEmpty) {
      return const Center(
        child: Text(
          'No data available for scatter plot',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    // Create scatter plot data (Morning vs Evening production)
    return SizedBox(
      height: 350,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'Morning vs Evening Production Scatter Plot',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _analyticsColor,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 250, // Fixed height for the chart area
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CustomPaint(
                  painter: ScatterPlotPainter(filteredRecords, _chartColors[0]),
                  child: Container(),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Text('Morning Production (L)', style: TextStyle(color: Colors.grey[600])),
                Text('Evening Production (L)', style: TextStyle(color: Colors.grey[600])),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMultiSeriesChart() {
    final filteredRecords = _getFilteredRecords();

    if (filteredRecords.isEmpty) {
      return const Center(
        child: Text(
          'No data available for multi-series chart',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    // Group data by cattle for multi-series
    final Map<String, List<double>> cattleData = {};
    final List<String> dates = [];

    // Get unique dates
    final uniqueDates = filteredRecords
        .where((r) => r.date != null)
        .map((r) => DateFormat('MMM dd').format(r.date!))
        .toSet()
        .toList()
      ..sort();

    dates.addAll(uniqueDates.take(7)); // Show last 7 periods

    // Group by cattle
    for (final record in filteredRecords) {
      if (record.date == null || record.cattleTagId == null) continue;

      // Validate totalYield to prevent NaN values
      final totalYield = record.totalYield;
      if (!totalYield.isFinite || totalYield.isNaN || totalYield < 0) continue;

      final dateKey = DateFormat('MMM dd').format(record.date!);
      if (!dates.contains(dateKey)) continue;

      final cattleKey = record.cattleTagId!;
      cattleData[cattleKey] = cattleData[cattleKey] ?? List.filled(dates.length, 0.0);

      final dateIndex = dates.indexOf(dateKey);
      if (dateIndex >= 0) {
        cattleData[cattleKey]![dateIndex] += totalYield;
      }
    }

    // Remove any cattle with all zero or invalid data
    cattleData.removeWhere((key, values) =>
        values.every((value) => !value.isFinite || value.isNaN || value <= 0));

    // If no valid data remains, show empty state
    if (cattleData.isEmpty) {
      return const Center(
        child: Text(
          'No valid data available for multi-series chart',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return SizedBox(
      height: 350,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'Multi-Cattle Production Comparison',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _analyticsColor,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 250, // Fixed height for the chart area
              child: CustomPaint(
                painter: MultiSeriesChartPainter(cattleData, dates, _chartColors),
                child: Container(),
              ),
            ),
            const SizedBox(height: 8),
            // Legend
            SizedBox(
              height: 40,
              child: Wrap(
                children: cattleData.keys.take(5).map((cattle) {
                  final index = cattleData.keys.toList().indexOf(cattle);
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          color: _chartColors[index % _chartColors.length],
                        ),
                        const SizedBox(width: 4),
                        Text(cattle, style: const TextStyle(fontSize: 12)),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductionAnalysis() {
    final analysisData = _getProductionAnalysisData();

    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _analyticsColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Production Analysis',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _analyticsColor,
            ),
          ),
          const SizedBox(height: 16),
          ...analysisData.entries.map((entry) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(entry.key, style: TextStyle(fontWeight: FontWeight.bold, color: _analyticsColor)),
                Text(entry.value, style: TextStyle(color: _analyticsColor, fontWeight: FontWeight.bold)),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildCattlePerformanceRankings() {
    final rankings = _getCattlePerformanceRankings();

    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _analyticsColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Cattle Performance Rankings',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _analyticsColor,
                ),
              ),
              if (rankings.isNotEmpty)
                Text(
                  'Top ${rankings.length} Performers',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          if (rankings.isEmpty)
            const Center(
              child: Text(
                'No performance data available',
                style: TextStyle(color: Colors.grey),
              ),
            )
          else
            ...rankings.asMap().entries.map((entry) {
              final index = entry.key;
              final ranking = entry.value;
              final isTopPerformer = index < 3;

              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isTopPerformer ? _chartColors[index].withValues(alpha: 0.1) : Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isTopPerformer ? _chartColors[index] : Colors.grey[300]!,
                  ),
                ),
                child: Row(
                  children: [
                    // Rank badge
                    Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        color: isTopPerformer ? _chartColors[index] : Colors.grey[400],
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Center(
                        child: Text(
                          '${index + 1}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Cattle info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            ranking['cattleTag'] ?? 'Unknown',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _analyticsColor,
                            ),
                          ),
                          Text(
                            '${ranking['totalRecords']} records',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Performance metrics
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${ranking['totalProduction']?.toStringAsFixed(1) ?? '0'} L',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _analyticsColor,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          'Avg: ${ranking['avgDaily']?.toStringAsFixed(1) ?? '0'} L/day',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }).toList(),
        ],
      ),
    );
  }

  Widget _buildExportSection() {
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _analyticsColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Export Analytics',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _analyticsColor,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _exportToPDF,
                  icon: const Icon(Icons.picture_as_pdf),
                  label: const Text('Export PDF'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red[600],
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _exportToExcel,
                  icon: const Icon(Icons.table_chart),
                  label: const Text('Export Excel'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[600],
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Export current analytics data and charts to PDF or Excel format',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  // Export Methods
  Future<void> _exportToPDF() async {
    if (!mounted) return;

    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      final analytics = _calculateAnalytics();
      final filteredRecords = _getFilteredRecords();
      final rankings = _getCattlePerformanceRankings();

      // Create PDF content
      await _generatePDFContent(analytics, filteredRecords, rankings);

      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('PDF export completed successfully!'),
          backgroundColor: Colors.green,
          action: SnackBarAction(
            label: 'View',
            textColor: Colors.white,
            onPressed: () {
              // TODO: Open PDF viewer
            },
          ),
        ),
      );
      }
    } catch (e) {
      // Close loading dialog if still open
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('PDF export failed: $e'),
          backgroundColor: Colors.red,
        ),
      );
      }
    }
  }

  Future<void> _exportToExcel() async {
    if (!mounted) return;

    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      final analytics = _calculateAnalytics();
      final filteredRecords = _getFilteredRecords();
      final rankings = _getCattlePerformanceRankings();

      // Create Excel content
      await _generateExcelContent(analytics, filteredRecords, rankings);

      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Excel export completed successfully!'),
          backgroundColor: Colors.green,
          action: SnackBarAction(
            label: 'Open',
            textColor: Colors.white,
            onPressed: () {
              // TODO: Open Excel file
            },
          ),
        ),
      );
      }
    } catch (e) {
      // Close loading dialog if still open
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Excel export failed: $e'),
          backgroundColor: Colors.red,
        ),
      );
      }
    }
  }

  Future<String> _generatePDFContent(
    Map<String, dynamic> analytics,
    List<MilkRecordIsar> records,
    List<Map<String, dynamic>> rankings,
  ) async {
    // Simulate PDF generation
    await Future.delayed(const Duration(seconds: 2));

    final dateRange = _startDate != null && _endDate != null
        ? '${DateFormat('MMM dd, yyyy').format(_startDate!)} - ${DateFormat('MMM dd, yyyy').format(_endDate!)}'
        : 'All Time';

    return '''
    Milk Production Analytics Report
    Generated: ${DateFormat('MMM dd, yyyy HH:mm').format(DateTime.now())}
    Date Range: $dateRange

    SUMMARY METRICS:
    - Total Records: ${analytics['totalRecords']}
    - Average Daily Production: ${analytics['avgDaily']?.toStringAsFixed(1) ?? '0'} L
    - Total Production: ${analytics['totalProduction']?.toStringAsFixed(1) ?? '0'} L
    - Active Cattle: ${analytics['activeCattle']}
    - Best Day Production: ${analytics['bestDay']?.toStringAsFixed(1) ?? '0'} L
    - Efficiency: ${analytics['efficiency']?.toStringAsFixed(1) ?? '0'}%

    TOP PERFORMING CATTLE:
    ${rankings.take(5).map((r) => '${r['cattleTag']}: ${r['totalProduction']?.toStringAsFixed(1)}L (${r['totalRecords']} records)').join('\n')}

    DETAILED RECORDS: ${records.length} entries
    ${records.take(10).map((r) => '${DateFormat('MMM dd').format(r.date ?? DateTime.now())}: ${r.totalYield.toStringAsFixed(1)}L (${r.cattleTagId})').join('\n')}
    ''';
  }

  Future<String> _generateExcelContent(
    Map<String, dynamic> analytics,
    List<MilkRecordIsar> records,
    List<Map<String, dynamic>> rankings,
  ) async {
    // Simulate Excel generation
    await Future.delayed(const Duration(seconds: 2));

    // In a real implementation, this would generate an actual Excel file
    // using packages like excel or syncfusion_flutter_xlsio

    return 'Excel file generated with ${records.length} records and ${rankings.length} cattle rankings';
  }

  // Helper methods
  bool _hasActiveFilters() {
    return _startDate != null || _endDate != null;
  }

  void _clearFilters() {
    setState(() {
      _startDate = null;
      _endDate = null;
    });
    widget.onClearFilters?.call();
  }

  List<String> _getActiveFiltersList() {
    final filters = <String>[];
    if (_startDate != null) {
      filters.add('Start: ${DateFormat('MMM dd, yyyy').format(_startDate!)}');
    }
    if (_endDate != null) {
      filters.add('End: ${DateFormat('MMM dd, yyyy').format(_endDate!)}');
    }
    return filters;
  }

  List<MilkRecordIsar> _getFilteredRecords() {
    var filtered = widget.milkRecords;

    if (_startDate != null) {
      filtered = filtered.where((record) =>
          record.date != null && record.date!.isAfter(_startDate!.subtract(const Duration(days: 1)))).toList();
    }

    if (_endDate != null) {
      filtered = filtered.where((record) =>
          record.date != null && record.date!.isBefore(_endDate!.add(const Duration(days: 1)))).toList();
    }

    return filtered;
  }

  Map<String, dynamic> _calculateAnalytics() {
    final filteredRecords = _getFilteredRecords();

    if (filteredRecords.isEmpty) {
      return {
        'totalRecords': 0,
        'avgDaily': 0.0,
        'totalProduction': 0.0,
        'activeCattle': 0,
        'bestDay': 0.0,
        'efficiency': 0.0,
      };
    }

    final totalProduction = filteredRecords.fold<double>(0.0, (sum, record) => sum + record.totalYield);
    final avgDaily = totalProduction / filteredRecords.length;
    final activeCattle = filteredRecords.map((r) => r.cattleBusinessId).toSet().length;

    // Calculate best day
    final bestDay = filteredRecords.isEmpty ? 0.0 :
        filteredRecords.map((r) => r.totalYield).reduce((a, b) => a > b ? a : b);

    // Calculate efficiency (daily efficiency: actual vs expected)
    double efficiency = 0.0;
    // Daily efficiency: actual vs expected (assuming 20L per day as baseline)
    efficiency = avgDaily > 0 ? (avgDaily / 20.0) * 100 : 0.0;

    return {
      'totalRecords': filteredRecords.length,
      'avgDaily': avgDaily,
      'totalProduction': totalProduction,
      'activeCattle': activeCattle,
      'bestDay': bestDay,
      'efficiency': efficiency.clamp(0.0, 100.0),
    };
  }

  List<FlSpot> _getProductionTrendSpots() {
    final filteredRecords = _getFilteredRecords();

    if (filteredRecords.isEmpty) return [];

    // Group by date and sum production
    final Map<DateTime, double> dailyProduction = {};

    for (final record in filteredRecords) {
      if (record.date != null) {
        final date = DateTime(record.date!.year, record.date!.month, record.date!.day);
        dailyProduction[date] = (dailyProduction[date] ?? 0.0) + record.totalYield;
      }
    }

    final sortedDates = dailyProduction.keys.toList()..sort();

    return sortedDates.map((date) {
      return FlSpot(
        date.millisecondsSinceEpoch.toDouble(),
        dailyProduction[date]!,
      );
    }).toList();
  }

  Map<String, double> _getSessionDistribution() {
    final filteredRecords = _getFilteredRecords();

    double morningTotal = 0.0;
    double eveningTotal = 0.0;

    for (final record in filteredRecords) {
      morningTotal += record.morningAmount ?? 0.0;
      eveningTotal += record.eveningAmount ?? 0.0;
    }

    return {
      'Morning': morningTotal,
      'Evening': eveningTotal,
    };
  }

  Map<String, String> _getProductionAnalysisData() {
    final filteredRecords = _getFilteredRecords();

    if (filteredRecords.isEmpty) {
      return {
        'Total Production': '0.0 L',
        'Average per Record': '0.0 L',
        'Best Day': 'N/A',
        'Trend': 'No Data',
      };
    }

    // Sort records by date
    filteredRecords.sort((a, b) => (a.date ?? DateTime.now()).compareTo(b.date ?? DateTime.now()));

    final totalProduction = filteredRecords.fold<double>(0.0, (sum, record) => sum + record.totalYield);
    final avgPerRecord = totalProduction / filteredRecords.length;

    // Find best day
    final bestRecord = filteredRecords.reduce((a, b) => a.totalYield > b.totalYield ? a : b);
    final bestDay = bestRecord.date != null
        ? DateFormat('MMM dd, yyyy').format(bestRecord.date!)
        : 'Unknown';

    // Calculate trend
    final firstHalf = filteredRecords.take(filteredRecords.length ~/ 2).toList();
    final secondHalf = filteredRecords.skip(filteredRecords.length ~/ 2).toList();

    final firstHalfAvg = firstHalf.isEmpty ? 0.0 :
        firstHalf.fold<double>(0.0, (sum, record) => sum + record.totalYield) / firstHalf.length;
    final secondHalfAvg = secondHalf.isEmpty ? 0.0 :
        secondHalf.fold<double>(0.0, (sum, record) => sum + record.totalYield) / secondHalf.length;

    final trend = secondHalfAvg > firstHalfAvg ? 'Increasing'
                : secondHalfAvg < firstHalfAvg ? 'Decreasing'
                : 'Stable';

    return {
      'Total Production': '${totalProduction.toStringAsFixed(1)} L',
      'Average per Record': '${avgPerRecord.toStringAsFixed(1)} L',
      'Best Day': '$bestDay (${bestRecord.totalYield.toStringAsFixed(1)}L)',
      'Trend': trend,
    };
  }

  List<Map<String, dynamic>> _getCattlePerformanceRankings() {
    final filteredRecords = _getFilteredRecords();

    if (filteredRecords.isEmpty) return [];

    // Group records by cattle
    final Map<String, List<MilkRecordIsar>> cattleRecords = {};
    for (final record in filteredRecords) {
      final cattleId = record.cattleBusinessId ?? 'Unknown';
      cattleRecords[cattleId] = (cattleRecords[cattleId] ?? [])..add(record);
    }

    // Calculate performance metrics for each cattle
    final List<Map<String, dynamic>> rankings = [];

    for (final entry in cattleRecords.entries) {
      final cattleId = entry.key;
      final records = entry.value;

      final totalProduction = records.fold<double>(0.0, (sum, record) => sum + record.totalYield);
      final avgDaily = totalProduction / records.length;
      final cattleTag = records.first.cattleTagId ?? 'Unknown';

      rankings.add({
        'cattleId': cattleId,
        'cattleTag': cattleTag,
        'totalRecords': records.length,
        'totalProduction': totalProduction,
        'avgDaily': avgDaily,
      });
    }

    // Sort by total production (descending) and take top 10
    rankings.sort((a, b) => (b['totalProduction'] as double).compareTo(a['totalProduction'] as double));

    return rankings.take(10).toList();
  }

  // Responsive design helpers
  double _getResponsivePadding() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 12.0;
    if (screenWidth < 1200) return 16.0;
    return 20.0;
  }

  double _getResponsiveSpacing() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 8.0;
    if (screenWidth < 1200) return 12.0;
    return 16.0;
  }

  double _getResponsiveFontSize() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 12.0;
    if (screenWidth < 1200) return 14.0;
    return 16.0;
  }

  double _getResponsiveIconSize() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) return 24.0;
    if (screenWidth < 600) return 28.0;
    return 32.0;
  }
}

// Custom Painters for Advanced Charts
class ScatterPlotPainter extends CustomPainter {
  final List<MilkRecordIsar> records;
  final Color color;

  ScatterPlotPainter(this.records, this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Validate input data and canvas size
    if (records.isEmpty || size.width <= 0 || size.height <= 0) return;

    // Get valid morning and evening amounts
    final morningAmounts = records
        .map((r) => r.morningAmount ?? 0.0)
        .where((amount) => amount.isFinite && !amount.isNaN && amount >= 0)
        .toList();

    final eveningAmounts = records
        .map((r) => r.eveningAmount ?? 0.0)
        .where((amount) => amount.isFinite && !amount.isNaN && amount >= 0)
        .toList();

    if (morningAmounts.isEmpty || eveningAmounts.isEmpty) return;

    // Find max values for scaling
    final maxMorning = morningAmounts.reduce((a, b) => a > b ? a : b);
    final maxEvening = eveningAmounts.reduce((a, b) => a > b ? a : b);

    if (maxMorning <= 0 || maxEvening <= 0 || !maxMorning.isFinite || !maxEvening.isFinite) return;

    // Draw points
    for (final record in records) {
      final morning = record.morningAmount ?? 0.0;
      final evening = record.eveningAmount ?? 0.0;

      // Validate individual data points
      if (!morning.isFinite || morning.isNaN || morning < 0 ||
          !evening.isFinite || evening.isNaN || evening < 0) {
        continue;
      }

      final x = (morning / maxMorning) * size.width;
      final y = size.height - (evening / maxEvening) * size.height;

      // Validate calculated coordinates
      if (!x.isFinite || !y.isFinite || x.isNaN || y.isNaN) continue;

      // Ensure coordinates are within bounds
      final clampedX = x.clamp(0.0, size.width);
      final clampedY = y.clamp(0.0, size.height);

      canvas.drawCircle(Offset(clampedX, clampedY), 4, paint);
    }

    // Draw axes
    final axisPaint = Paint()
      ..color = Colors.grey[400]!
      ..strokeWidth = 1;

    // X-axis
    canvas.drawLine(Offset(0, size.height), Offset(size.width, size.height), axisPaint);
    // Y-axis
    canvas.drawLine(const Offset(0, 0), Offset(0, size.height), axisPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class MultiSeriesChartPainter extends CustomPainter {
  final Map<String, List<double>> cattleData;
  final List<String> dates;
  final List<Color> colors;

  MultiSeriesChartPainter(this.cattleData, this.dates, this.colors);

  @override
  void paint(Canvas canvas, Size size) {
    // Validate input data and canvas size
    if (cattleData.isEmpty || dates.isEmpty || size.width <= 0 || size.height <= 0) return;

    // Get all values and filter out invalid ones
    final allValues = cattleData.values
        .expand((list) => list)
        .where((value) => value.isFinite && !value.isNaN)
        .toList();

    if (allValues.isEmpty) {
      return;
    }

    final maxValue = allValues.reduce((a, b) => a > b ? a : b);
    if (maxValue <= 0 || !maxValue.isFinite) return;

    // Ensure we have at least 2 dates for proper step calculation
    final stepX = dates.length > 1 ? size.width / (dates.length - 1) : 0.0;
    if (!stepX.isFinite || stepX <= 0) return;

    int colorIndex = 0;
    for (final entry in cattleData.entries.take(5)) {
      final paint = Paint()
        ..color = colors[colorIndex % colors.length]
        ..strokeWidth = 2
        ..style = PaintingStyle.stroke;

      final path = Path();
      bool isFirst = true;

      for (int i = 0; i < entry.value.length; i++) {
        // Validate individual data point
        final value = entry.value[i];
        if (!value.isFinite || value.isNaN) continue;

        final x = i * stepX;
        final y = size.height - (value / maxValue) * size.height;

        // Validate calculated coordinates
        if (!x.isFinite || !y.isFinite || x.isNaN || y.isNaN) continue;

        // Ensure coordinates are within bounds
        final clampedX = x.clamp(0.0, size.width);
        final clampedY = y.clamp(0.0, size.height);

        if (isFirst) {
          path.moveTo(clampedX, clampedY);
          isFirst = false;
        } else {
          path.lineTo(clampedX, clampedY);
        }

        // Draw point with validated coordinates
        canvas.drawCircle(
          Offset(clampedX, clampedY),
          3,
          Paint()..color = colors[colorIndex % colors.length]
        );
      }

      // Only draw path if it has valid points
      if (!isFirst) {
        canvas.drawPath(path, paint);
      }
      colorIndex++;
    }

    // Draw grid lines with validation
    final gridPaint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 0.5;

    // Horizontal grid lines
    for (int i = 0; i <= 4; i++) {
      final y = (size.height / 4) * i;
      if (y.isFinite && !y.isNaN) {
        canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
      }
    }

    // Vertical grid lines
    for (int i = 0; i < dates.length; i++) {
      final x = i * stepX;
      if (x.isFinite && !x.isNaN) {
        canvas.drawLine(Offset(x, 0), Offset(x, size.height), gridPaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
