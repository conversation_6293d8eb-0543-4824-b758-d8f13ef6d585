import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:share_plus/share_plus.dart';
import '../models/milk_report_data.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class ExportMilkReportDialog extends StatefulWidget {
  final MilkReportData reportData;

  const ExportMilkReportDialog({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  ExportMilkReportDialogState createState() => ExportMilkReportDialogState();
}

class ExportMilkReportDialogState extends State<ExportMilkReportDialog> {
  bool includeSummary = true;
  bool includeChart = true;
  bool includeDetails = true;
  String exportFormat = 'PDF';

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Export Report'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CheckboxListTile(
            title: const Text('Include Summary'),
            value: includeSummary,
            onChanged: (value) {
              setState(() => includeSummary = value ?? true);
            },
          ),
          CheckboxListTile(
            title: const Text('Include Chart'),
            value: includeChart,
            onChanged: (value) {
              setState(() => includeChart = value ?? true);
            },
          ),
          CheckboxListTile(
            title: const Text('Include Details'),
            value: includeDetails,
            onChanged: (value) {
              setState(() {
                includeDetails = value ?? false;
              });
            },
          ),
          SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'Export Format',
            ),
            value: exportFormat,
            items: const [
              DropdownMenuItem(value: 'PDF', child: Text('PDF')),
              DropdownMenuItem(value: 'Excel', child: Text('Excel')),
            ],
            onChanged: (value) {
              setState(() => exportFormat = value ?? 'PDF');
            },
          ),
          SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => _handleExport(context),
                child: const Text('Export'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _handleExport(BuildContext context) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);
    
    try {
      await _exportReport(context);
      if (!mounted) return;
      navigator.pop();
    } catch (e) {
      if (!mounted) return;
      scaffoldMessenger.showSnackBar(
        SnackBar(content: Text('Failed to export report: $e')),
      );
    }
  }

  Future<void> _exportReport(BuildContext context) async {
    final dateFormat = DateFormat('yyyy-MM-dd');
    final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');
    final quantityFormat = NumberFormat('##0.00');

    if (exportFormat == 'PDF') {
      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          build: (context) {
            final children = <pw.Widget>[];

            // Title
            children.add(
              pw.Header(
                level: 0,
                child: pw.Text('Milk Production Report',
                    style: pw.TextStyle(
                        fontSize: 24, fontWeight: pw.FontWeight.bold)),
              ),
            );

            // Date Range
            if (widget.reportData.startDate != null ||
                widget.reportData.endDate != null) {
              children.add(
                pw.Paragraph(
                  text:
                      'Period: ${widget.reportData.startDate != null ? dateFormat.format(widget.reportData.startDate!) : 'Start'} to ${widget.reportData.endDate != null ? dateFormat.format(widget.reportData.endDate!) : 'End'}',
                ),
              );
            }

            // Summary Section
            if (includeSummary) {
              children.add(pw.SizedBox(height: 20));
              children.add(
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(),
                    borderRadius:
                        const pw.BorderRadius.all(pw.Radius.circular(10)),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text('Summary',
                          style: pw.TextStyle(
                              fontSize: 18, fontWeight: pw.FontWeight.bold)),
                      pw.SizedBox(height: 10),
                      pw.Text(
                          'Total Production: ${quantityFormat.format(widget.reportData.summaryData['Total Production'])} L'),
  