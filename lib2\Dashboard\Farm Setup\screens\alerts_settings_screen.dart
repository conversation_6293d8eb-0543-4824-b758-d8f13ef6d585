import 'package:flutter/material.dart';
import 'dart:convert';
import '../models/alert_settings.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../services/database_helper.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class AlertsSettingsScreen extends StatefulWidget {
  const AlertsSettingsScreen({Key? key}) : super(key: key);

  @override
  State<AlertsSettingsScreen> createState() => _AlertsSettingsScreenState();
}

class _AlertsSettingsScreenState extends State<AlertsSettingsScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  List<AlertChannel> _channels = [];
  List<AlertType> _alertTypes = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) {
        setState(() {
          _channels = AlertChannel.defaultChannels;
          _alertTypes = AlertType.defaultAlertTypes;
          _isLoading = false;
        });
        return;
      }

      final prefs = await SharedPreferences.getInstance();
      
      // Load channels
      final channelsKey = '${selectedFarmId}_alert_channels';
      final channelsJson = prefs.getStringList(channelsKey) ?? [];
      _channels = channelsJson.isEmpty
          ? AlertChannel.defaultChannels
          : channelsJson.map((json) => AlertChannel.fromMap(jsonDecode(json))).toList();

      // Load alert types
      final typesKey = '${selectedFarmId}_alert_types';
      final typesJson = prefs.getStringList(typesKey) ?? [];
      _alertTypes = typesJson.isEmpty
          ? AlertType.defaultAlertTypes
          : typesJson.map((json) => AlertType.fromMap(jsonDecode(json))).toList();

      setState(() => _isLoading = false);
    } catch (e) {
      debugPrint('Error loading alert settings: $e');
      setState(() {
        _channels = AlertChannel.defaultChannels;
        _alertTypes = AlertType.defaultAlertTypes;
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    try {
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) return;

      final prefs = await SharedPreferences.getInstance();
      
      // Save channels
      final channelsKey = '${selectedFarmId}_alert_channels';
      final channelsJson = _channels.map((channel) => jsonEncode(channel.toMap())).toList();
      await prefs.setStringList(channelsKey, channelsJson);

      // Save alert types
      final typesKey = '${selectedFarmId}_alert_types';
      final typesJson = _alertTypes.map((type) => jsonEncode(type.toMap())).toList();
      await prefs.setStringList(typesKey, typesJson);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Settings saved successfully')),
        );
      }
    } catch (e) {
      debugPrint('Error saving alert settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error saving settings')),
        );
      }
    }
  }

  Future<void> _configureChannel(AlertChannel channel) async {
    final nameController = TextEditingController(text: channel.name);
    Map<String, dynamic> config = Map<String, dynamic>.from(channel.config);

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Configure ${channel.name}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(labelText: 'Channel Name'),
              ),
              SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
              if (channel.id == 'email')
                TextField(
                  decoration: const InputDecoration(labelText: 'Email Address'),
                  keyboardType: TextInputType.emailAddress,
                  onChanged: (value) => config['email'] = value,
                  controller: TextEditingController(text: config['email']),
                )
              else if (channel.id == 'sms')
                TextField(
                  decoration: const InputDecoration(labelText: 'Phone Number'),
                  keyboardType: TextInputType.phone,
                  onChanged: (value) => config['phone'] = value,
                  controller: TextEditingController(text: config['phone']),
                ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                final index = _channels.indexWhere((c) => c.id == channel.id);
                _channels[index] = channel.copyWith(
                  name: nameController.text,
                  config: config,
                );
              });
              _saveSettings();
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  Future<void> _configureAlertType(AlertType alertType) async {
    Map<String, dynamic> config = Map<String, dynamic>.from(alertType.config);
    List<String> enabledChannels = List<String>.from(alertType.enabledChannels);

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Configure ${alertType.name}'),
        content: StatefulBuilder(
          builder: (context, setState) => SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(alertType.description),
                SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                const Text(
                  'Notification Channels',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                ...(_channels.map((channel) => CheckboxListTile(
                  title: Text(channel.name),
                  value: enabledChannels.contains(channel.id),
                  onChanged: channel.enabled ? (value) {
                    setState(() {
                      if (value == true) {
                        enabledChannels.add(channel.id);
                      } else {
                        enabledChannels.remove(channel.id);
                      }
                    });
                  } : null,
                ))),
                const Divider(),
                const Text(
                  'Alert Settings',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                if (alertType.id == 'health_check' || alertType.id == 'vaccination')
                  ...[
                    ListTile(
                      title: const Text('Days Before'),
                      trailing: DropdownButton<int>(
                        value: config['days_before'] as int,
                        items: [1, 3, 5, 7, 14].map((days) {
                          return DropdownMenuItem(
                            value: days,
                            child: Text('$days days'),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() => config['days_before'] = value);
                        },
                      ),
                    ),
                    ListTile(
                      title: const Text('Repeat Interval'),
                      trailing: DropdownButton<int>(
                        value: config['repeat_interval'] as int,
                        items: [12, 24, 48, 72].map((hours) {
                          return DropdownMenuItem(
                            value: hours,
                            child: Text('$hours hours'),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() => config['repeat_interval'] = value);
                        },
                      ),
                    ),
                  ]
                else if (alertType.id == 'milk_collection')
                  ...[
                    ListTile(
                      title: const Text('Morning Collection Time'),
                      trailing: TextButton(
                        child: Text(config['morning_time'] as String),
                        onPressed: () async {
                          final time = await showTimePicker(
                            context: context,
                            initialTime: TimeOfDay.fromDateTime(
                              DateTime.parse('2024-01-01 ${config['morning_time']}:00'),
                            ),
                          );
                          if (time != null) {
                            setState(() {
                              config['morning_time'] = '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
                            });
                          }
                        },
                      ),
                    ),
                    ListTile(
                      title: const Text('Evening Collection Time'),
                      trailing: TextButton(
                        child: Text(config['evening_time'] as String),
                        onPressed: () async {
                          final time = await showTimePicker(
                            context: context,
                            initialTime: TimeOfDay.fromDateTime(
                              DateTime.parse('2024-01-01 ${config['evening_time']}:00'),
                            ),
                          );
                          if (time != null) {
                            setState(() {
                              config['evening_time'] = '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
                            });
                          }
                        },
                      ),
                    ),
                    ListTile(
                      title: const Text('Remind Before'),
                      trailing: DropdownButton<int>(
                        value: config['remind_before'] as int,
                        items: [15, 30, 45, 60].map((minutes) {
                          return DropdownMenuItem(
                            value: minutes,
                            child: Text('$minutes min'),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() => config['remind_before'] = value);
                        },
                      ),
                    ),
                  ]
                else if (alertType.id == 'breeding')
                  ...[
                    SwitchListTile(
                      title: const Text('Heat Detection'),
                      value: config['heat_detection'] as bool,
                      onChanged: (value) {
                        setState(() => config['heat_detection'] = value);
                      },
                    ),
                    SwitchListTile(
                      title: const Text('Pregnancy Check'),
                      value: config['pregnancy_check'] as bool,
                      onChanged: (value) {
                        setState(() => config['pregnancy_check'] = value);
                      },
                    ),
                    SwitchListTile(
                      title: const Text('Calving Due'),
                      value: config['calving_due'] as bool,
                      onChanged: (value) {
                        setState(() => config['calving_due'] = value);
                      },
                    ),
                  ]
                else if (alertType.id == 'low_inventory' || alertType.id == 'abnormal_milk')
                  ListTile(
                    title: const Text('Threshold Percentage'),
                    trailing: DropdownButton<int>(
                      value: config['threshold_percentage'] as int,
                      items: [10, 15, 20, 25, 30].map((percent) {
                        return DropdownMenuItem(
                          value: percent,
                          child: Text('$percent%'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() => config['threshold_percentage'] = value);
                      },
                    ),
                  ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                final index = _alertTypes.indexWhere((t) => t.id == alertType.id);
                _alertTypes[index] = alertType.copyWith(
                  enabledChannels: enabledChannels,
                  config: config,
                );
              });
              _saveSettings();
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Alert Settings'),
      ),
      body: ListView(
        padding: ResponsiveHelper.getResponsivePadding(context),
        children: [
          Card(
            child: Padding(
              padding: ResponsiveHelper.getResponsivePadding(context),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Notification Channels',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                  ..._channels.map((channel) => SwitchListTile(
                    title: Text(channel.name),
                    subtitle: Text(
                      channel.id == 'email' 
                          ? channel.config['email'] ?? 'Not configured'
                          : channel.id == 'sms'
                              ? channel.config['phone'] ?? 'Not configured'
                              : 'Push notifications',
                    ),
                    value: channel.enabled,
                    onChanged: (value) {
                      setState(() {
                        final index = _channels.indexWhere((c) => c.id == channel.id);
                        _channels[index] = channel.copyWith(enabled: value);
                      });
                      _saveSettings();
                    },
                    secondary: IconButton(
                      icon: const Icon(Icons.settings),
                      onPressed: () => _configureChannel(channel),
                    ),
                  )),
                ],
              ),
            ),
          ),
          SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
          Card(
            child: Padding(
              padding: ResponsiveHelper.getResponsivePadding(context),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Alert Types',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                  ..._alertTypes.map((type) => ListTile(
                    title: Text(type.name),
                    subtitle: Text(type.description),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Switch(
                          value: type.enabled,
                          onChanged: (value) {
                            setState(() {
                              final index = _alertTypes.indexWhere((t) => t.id == type.id);
                              _alertTypes[index] = type.copyWith(enabled: value);
                            });
                            _saveSettings();
                          },
                        ),
                        IconButton(
                          icon: const Icon(Icons.settings),
                          onPressed: type.enabled ? () => _configureAlertType(type) : null,
                        ),
                      ],
                    ),
                  )),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
