r new records
    if (isNew) {
      notification.createdAt = DateTime.now();
      notification.isRead = false;
      notification.readAt = null;
    }
  }

  /// Validate notification settings
  void _validateNotificationSettings(NotificationSettingsIsar settings) {
    // All these properties are non-nullable in our model, so we only need to check
    // that the settings object itself is not null, which is handled by the parameter type.
    // Instead o