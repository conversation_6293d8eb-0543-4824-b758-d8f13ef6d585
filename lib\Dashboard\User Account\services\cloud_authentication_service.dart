import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';
// GetIt import removed - not needed for Firebase Auth
import '../../../services/firebase_service.dart';
// Local user models removed - using Firebase Auth only
// Local database service removed - using Firebase Auth only
import '../../Farm Setup/services/cloud_backup_service.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
// Removed farm import - single farm per user
// Removed multi-farm services - single farm per user
import '../../../services/database/isar_service.dart';
import '../../../services/database/isar_initializer.dart';

import '../../../services/cloud_data_sync_service.dart';

/// Cloud authentication service using Firebase Auth
class CloudAuthenticationService extends ChangeNotifier {
  static final Logger _logger = Logger();
  final CloudBackupService _cloudBackupService = CloudBackupService.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  User? _currentUser;
  bool _isInitialized = false;
  bool _isLoading = false;
  bool _isDatabaseSwitching = false;
  bool _hasValidSession = false;
  bool _rememberMe = false;
  String? _errorMessage;
  StreamSubscription<User?>? _authStateSubscription;
  Timer? _retryTimer;
  int _retryAttempts = 0;
  static const int _maxRetryAttempts = 3;
  static const Duration _retryDelay = Duration(seconds: 2);
  
  /// Current Firebase user
  User? get currentUser => _currentUser;
  
  /// Whether user is authenticated and database is ready
  bool get isAuthenticated {
    final authenticated = _currentUser != null && _isInitialized && !_isDatabaseSwitching && _hasValidSession;
    _logger.i('🔍 isAuthenticated check: $authenticated (user: ${_currentUser?.uid}, initialized: $_isInitialized, switching: $_isDatabaseSwitching, validSession: $_hasValidSession)');
    return authenticated;
  }

  /// Activate session for explicit login (not auto-login)
  void activateSession() {
    _hasValidSession = true;
    _logger.i('✅ Session activated for explicit login');
    notifyListeners();
  }
  
  /// Whether service is initialized
  bool get isInitialized => _isInitialized;
  
  /// Whether service is loading
  bool get isLoading => _isLoading;

  /// Whether database is currently switching (prevents premature access)
  bool get isDatabaseSwitching => _isDatabaseSwitching;

  /// Current error message
  String? get errorMessage => _errorMessage;
  
  /// User ID
  String? get userId => _currentUser?.uid;
  
  /// User email
  String? get userEmail => _currentUser?.email;

  /// Whether email is verified
  bool get isEmailVerified => _currentUser?.emailVerified ?? false;

  /// Get current user display name
  String? get currentUserDisplayName => _currentUser?.displayName ?? _currentUser?.email;

  /// Load remember me preference from SharedPreferences with 30-day expiration
  Future<void> _loadRememberMePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _rememberMe = prefs.getBool('remember_me') ?? false;

      // Check if Remember Me has expired (30 days)
      if (_rememberMe) {
        final rememberMeTimestamp = prefs.getInt('remember_me_timestamp') ?? 0;
        final rememberMeDate = DateTime.fromMillisecondsSinceEpoch(rememberMeTimestamp);
        final now = DateTime.now();
        final daysSinceRememberMe = now.difference(rememberMeDate).inDays;

        if (daysSinceRememberMe >= 30) {
          _logger.i('⏰ Remember Me expired after $daysSinceRememberMe days, clearing preference');
          await _clearRememberMePreference();
          _rememberMe = false;
        } else {
          _logger.i('📱 Remember Me preference loaded: $_rememberMe (${30 - daysSinceRememberMe} days remaining)');
        }
      } else {
        _logger.i('📱 Remember Me preference loaded: $_rememberMe');
      }
    } catch (e) {
      _logger.w('⚠️ Failed to load Remember Me preference: $e');
      _rememberMe = false;
    }
  }

  /// Save remember me preference to SharedPreferences with timestamp
  Future<void> _saveRememberMePreference(bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('remember_me', value);

      if (value) {
        // Save timestamp when Remember Me is enabled
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        await prefs.setInt('remember_me_timestamp', timestamp);
        _logger.i('💾 Remember Me preference saved: $value (expires in 30 days)');
      } else {
        // Clear timestamp when Remember Me is disabled
        await prefs.remove('remember_me_timestamp');
        _logger.i('💾 Remember Me preference saved: $value');
      }

      _rememberMe = value;
    } catch (e) {
      _logger.w('⚠️ Failed to save Remember Me preference: $e');
    }
  }

  /// Clear remember me preference and timestamp
  Future<void> _clearRememberMePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('remember_me');
      await prefs.remove('remember_me_timestamp');
      _rememberMe = false;
      _logger.i('🗑️ Remember Me preference and timestamp cleared');
    } catch (e) {
      _logger.w('⚠️ Failed to clear Remember Me preference: $e');
    }
  }

  /// Automatically activate the last active farm after cloud data restoration (UPDATED FOR UNIFIED APPROACH)
  Future<void> _autoActivateLastActiveFarm() async {
    try {
      _logger.i('🎯 Auto-activating last active farm using unified approach...');

      // Get services
      // Multi-farm services removed - single farm per user

      // Single farm per user - no farm activation needed
      _logger.i('✅ Single farm per user - no farm activation needed');

      // Single farm per user - no farm activation needed
    } catch (e) {
      _logger.w('⚠️ Error during auto-activation of last active farm: $e');
      // Don't throw - this is a convenience feature
    }
  }
  
  /// Initialize the service with smart session management
  Future<void> initialize() async {
    try {
      _logger.i('🚀 Initializing CloudAuthenticationService with smart session management');
      _setLoading(true);
      _clearError();

      // Initialize Firebase if not already done
      if (!FirebaseService.isInitialized) {
        final success = await FirebaseService.initialize();
        if (!success) {
          throw Exception('Failed to initialize Firebase');
        }
      }

      // Load remember me preference
      await _loadRememberMePreference();
      _logger.i('📱 Remember Me preference loaded: $_rememberMe');

      // Get current Firebase user
      _currentUser = FirebaseService.currentUser;

      // Verify user is still valid if exists
      if (_currentUser != null) {
        try {
          await _currentUser!.reload();
          _currentUser = FirebaseAuth.instance.currentUser;
        } catch (e) {
          _logger.w('User session invalid, clearing: $e');
          _currentUser = null;
          await _clearRememberMePreference();
        }
      }

      // Determine session state based on remember me preference
      if (_currentUser != null && _rememberMe) {
        _logger.i('✅ Auto-login: User has valid session with Remember Me enabled');
        _hasValidSession = true;
        // No database switching needed - we'll start with correct database
      } else if (_currentUser != null && !_rememberMe) {
        _logger.i('🔐 Manual login required: User exists but Remember Me disabled');
        _hasValidSession = false;
        // User will see login screen with option to continue with saved account
      } else {
        _logger.i('👤 First time user: No saved session found');
        _hasValidSession = false;
      }

      _logger.i('🔍 Session state: user=${_currentUser?.email}, rememberMe=$_rememberMe, validSession=$_hasValidSession');

      // Cancel any existing subscription
      await _authStateSubscription?.cancel();

      // Listen to auth state changes (but only for new logins, not auto-login)
      _authStateSubscription = FirebaseService.authStateChanges.listen(
        _onAuthStateChanged,
        onError: (error) {
          _logger.e('Auth state stream error: $error');
          _handleAuthError(error);
        },
      );

      _isInitialized = true;
      _retryAttempts = 0;
      _setLoading(false);
      _logger.i('✅ CloudAuthenticationService initialized successfully');
      notifyListeners();
    } catch (e) {
      _logger.e('❌ Error initializing CloudAuthenticationService: $e');
      await _handleInitializationError(e);
    }
  }
  
  /// Handle authentication state changes (only for new logins)
  void _onAuthStateChanged(User? user) {
    _logger.i('🔄 Auth state changed: ${user?.uid ?? 'null'} (email: ${user?.email ?? 'none'})');

    final previousUser = _currentUser;
    _currentUser = user;

    // Clear any previous errors on successful auth state change
    if (user != null) {
      _clearError();
      _retryAttempts = 0;
    }

    // Single farm per user - no database switching needed
    _logger.i('✅ User authentication state updated - single farm per user');
    _logAuthState(user, previousUser);
    notifyListeners();
  }

  /// Log authentication state for debugging
  void _logAuthState(User? user, User? previousUser) {
    if (user != null) {
      _logger.i('✅ User authenticated: ${user.email}, verified: ${user.emailVerified}');
      _logger.i('🔍 isAuthenticated now: $isAuthenticated');
    } else if (previousUser != null) {
      _logger.i('🚪 User signed out');
    }
  }

  /// Switch database based on user authentication state
  Future<void> _switchDatabaseForUser(User? user) async {
    final userId = user?.uid;
    _logger.i('🔄 Switching database for user: ${userId ?? "guest"}');

    // Set database switching flag to prevent premature access
    _isDatabaseSwitching = true;
    notifyListeners();

    // Retry logic for database switching
    int retryCount = 0;
    const maxRetries = 3;
    const retryDelay = Duration(milliseconds: 500);

    while (retryCount < maxRetries) {
      try {
        final isarService = await IsarService.instance;

        // Add delay to ensure any pending operations complete
        await Future.delayed(const Duration(milliseconds: 500));

        // Close current database cleanly before switching
        if (isarService.isInitialized) {
          _logger.i('🔄 Closing current database before user switch');
          await isarService.close();
          await Future.delayed(const Duration(milliseconds: 300));
        }

        // Switch database
        await isarService.switchUser(userId);
        _logger.i('✅ Database switched successfully for user: ${userId ?? "guest"}');
        _logger.i('📊 Current database info: ${isarService.currentDatabaseInfo}');

        // Wait for database to be fully ready and accessible
        await _waitForDatabaseReady(isarService);
        _logger.i('✅ Database is fully ready and accessible');

        // Single farm per user - no farm context to clear

        // Ensure default data exists in the new database (only for authenticated users)
        if (user != null) {
          try {
            _logger.i('🔧 Ensuring default data for user: ${userId?.substring(0, 20)}...');
            await IsarInitializer.ensureDefaultData().timeout(
              const Duration(seconds: 10),
              onTimeout: () {
                _logger.w('⚠️ Default data initialization timed out');
                throw TimeoutException('Default data initialization timed out', const Duration(seconds: 10));
              },
            );
            _logger.i('✅ Default data ensured for user: ${userId?.substring(0, 20)}...');
          } catch (e) {
            _logger.w('⚠️ Error ensuring default data for user ${userId?.substring(0, 20)}...: $e');
            // Continue even if default data fails
          }

          // For authenticated users, check for existing cloud data and restore it (UPDATED FOR UNIFIED APPROACH)
          try {
            _logger.i('🔍 Checking for existing cloud data for user: ${user.uid.substring(0, 20)}...');
            final cloudSyncService = CloudDataSyncService.instance;
            cloudSyncService.initialize(user.uid);

            final hasCloudData = await FirebaseService.hasExistingUserData(user.uid).timeout(
              const Duration(seconds: 10),
              onTimeout: () {
                _logger.w('⚠️ Cloud data check timed out');
                return false;
              },
            );

            if (hasCloudData) {
              _logger.i('📥 Found existing cloud data, starting unified restoration...');
              final restored = await cloudSyncService.restoreUserDataFromCloud(user.uid).timeout(
                const Duration(seconds: 20), // Increased timeout for multi-farm restoration
                onTimeout: () {
                  _logger.w('⚠️ Cloud data restoration timed out');
                  return false;
                },
              );
              if (restored) {
                _logger.i('🎉 Successfully restored multi-farm user data from cloud');

                // Single farm per user - no ownership updates needed

                // Automatically activate the last active farm after restoration
                await _autoActivateLastActiveFarm();
              } else {
                _logger.w('⚠️ Failed to restore user data from cloud');
                // Still try to activate farms in case there's local data
                await _autoActivateLastActiveFarm();
              }
            } else {
              _logger.i('ℹ️ No existing cloud data found, user will start fresh');

              // Single farm per user - no ownership updates needed

              // For fresh users, ensure default data and auto-activate last active farm
              await _autoActivateLastActiveFarm();
            }
          } catch (e) {
            _logger.e('❌ Error checking/restoring cloud data: $e');
            // Continue with normal flow even if cloud restore fails
          }
        } else {
          _logger.i('⏭️ Skipping default data creation for guest user during login');
        }

        // Success - clear database switching flag and break out of retry loop
        _isDatabaseSwitching = false;
        _logger.i('✅ Database switching completed, notifying listeners');
        notifyListeners();
        return;

      } catch (e) {
        retryCount++;
        _logger.w('⚠️ Database switch attempt $retryCount failed: $e');

        if (retryCount >= maxRetries) {
          _logger.e('❌ Error switching database for user after $maxRetries attempts: $e');
          // Clear database switching flag even on failure
          _isDatabaseSwitching = false;
          _logger.i('⚠️ Database switching failed, notifying listeners anyway');
          notifyListeners();
          // Don't throw here as this shouldn't block authentication
          return;
        }

        // Wait before retrying
        _logger.i('🔄 Retrying database switch in ${retryDelay.inMilliseconds}ms...');
        await Future.delayed(retryDelay);
      }
    }
  }

  /// Wait for database to be fully ready and accessible
  Future<void> _waitForDatabaseReady(IsarService isarService) async {
    const maxWaitTime = Duration(seconds: 10);
    const checkInterval = Duration(milliseconds: 100);
    final startTime = DateTime.now();

    while (DateTime.now().difference(startTime) < maxWaitTime) {
      try {
        // Test database accessibility by trying to get farms
        if (isarService.isInitialized) {
          final farmHandler = GetIt.instance<FarmSetupHandler>();
          await farmHandler.getAllFarms();
          // If we get here without exception, database is ready
          return;
        }
      } catch (e) {
        // Database not ready yet, continue waiting
        _logger.d('Database not ready yet, waiting... ($e)');
      }

      await Future.delayed(checkInterval);
    }

    _logger.w('⚠️ Database readiness check timed out after ${maxWaitTime.inSeconds} seconds');
  }

  /// Handle authentication errors with retry logic
  Future<void> _handleAuthError(dynamic error) async {
    _logger.e('Authentication error: $error');

    if (_retryAttempts < _maxRetryAttempts) {
      _retryAttempts++;
      _logger.i('Retrying authentication initialization in ${_retryDelay.inSeconds} seconds (attempt $_retryAttempts/$_maxRetryAttempts)');

      _retryTimer?.cancel();
      _retryTimer = Timer(_retryDelay, () {
        if (!_isInitialized) {
          initialize();
        }
      });
    } else {
      _setError('Authentication service unavailable. Please check your internet connection and try again.');
    }
  }

  /// Handle initialization errors
  Future<void> _handleInitializationError(dynamic error) async {
    if (_retryAttempts < _maxRetryAttempts) {
      _retryAttempts++;
      _logger.i('Retrying initialization in ${_retryDelay.inSeconds} seconds (attempt $_retryAttempts/$_maxRetryAttempts)');

      _retryTimer?.cancel();
      _retryTimer = Timer(_retryDelay, () {
        initialize();
      });

      _setError('Connecting to authentication service... (attempt $_retryAttempts/$_maxRetryAttempts)');
    } else {
      _setError('Failed to initialize authentication: $error');
      _setLoading(false);
    }
  }
  
  /// Register with email and password
  Future<bool> registerWithEmail({
    required String email,
    required String password,
    required String fullName,
    required String phoneNumber,
    String role = 'Admin',
  }) async {
    try {
      _setLoading(true);
      _clearError();
      
      _logger.i('Registering user with email: $email');
      
      // Create user with Firebase Auth
      final credential = await FirebaseAuth.instance.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      final user = credential.user;
      if (user == null) {
        throw Exception('Failed to create user account');
      }
      
      // Update user profile
      await user.updateDisplayName(fullName);
      
      // Send email verification
      await user.sendEmailVerification();
      
      // Create user document in Firestore
      await FirebaseService.createUserDocument(user.uid, {
        'email': email,
        'fullName': fullName,
        'phoneNumber': phoneNumber,
        'role': role,
        'isActive': true,
        'emailVerified': false,
      });
      
      // Local user record creation removed - using Firebase Auth only
      
      _setLoading(false);
      _logger.i('User registered successfully: $email');
      return true;
    } on FirebaseAuthException catch (e) {
      _setError(_getAuthErrorMessage(e));
      _logger.e('Firebase Auth error during registration: ${e.code} - ${e.message}');
      return false;
    } catch (e) {
      _setError('Registration failed: $e');
      _logger.e('Error during registration: $e');
      return false;
    }
  }
  
  /// Sign in with email and password
  Future<bool> signInWithEmail({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      _setLoading(true);
      _clearError();
      
      _logger.i('Signing in user with email: $email');
      
      final credential = await FirebaseAuth.instance.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      final user = credential.user;
      if (user == null) {
        throw Exception('Failed to sign in');
      }
      
      _setLoading(false);
      activateSession(); // Activate session for explicit login
      await _saveRememberMePreference(rememberMe); // Save remember me preference
      _logger.i('User signed in successfully: $email (Remember Me: $rememberMe)');
      return true;
    } on FirebaseAuthException catch (e) {
      _setError(_getAuthErrorMessage(e));
      _logger.e('Firebase Auth error during sign in: ${e.code} - ${e.message}');
      return false;
    } catch (e) {
      _setError('Sign in failed: $e');
      _logger.e('Error during sign in: $e');
      return false;
    }
  }
  
  /// Sign in with Google using Firebase Auth
  Future<bool> signInWithGoogle({bool rememberMe = true}) async {
    try {
      _setLoading(true);
      _clearError();

      _logger.i('Signing in with Google using Firebase Auth');

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // User canceled the sign-in
        _setLoading(false);
        return false;
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential
      final UserCredential userCredential = await FirebaseAuth.instance.signInWithCredential(credential);

      final user = userCredential.user;
      if (user == null) {
        throw Exception('Failed to sign in with Google');
      }

      // Create or update user document, preserving existing data like phoneNumber
      await FirebaseService.createOrUpdateUserDocument(
        user.uid,
        {
          'email': user.email ?? '',
          'fullName': user.displayName ?? '',
          'role': 'Admin',
          'isActive': true,
          'emailVerified': user.emailVerified,
          'provider': 'google',
          'lastLoginAt': FieldValue.serverTimestamp(),
          // Note: phoneNumber is intentionally omitted to preserve existing value
        },
        defaultsForNewUser: {
          'phoneNumber': '', // Only set for new users, existing users keep their phoneNumber
        },
      );
      _logger.i('Created/updated user document for Google sign-in, preserving existing phoneNumber');

      _setLoading(false);
      activateSession(); // Activate session for explicit login
      await _saveRememberMePreference(rememberMe); // Save remember me preference
      _logger.i('Successfully signed in with Google: ${user.email} (Remember Me: $rememberMe)');

      // The auth state change listener will handle updating _currentUser
      return true;
    } catch (e) {
      _setError('Google sign in failed: $e');
      _logger.e('Error during Google sign in: $e');
      return false;
    }
  }
  
  /// Sign out
  Future<void> signOut() async {
    try {
      _setLoading(true);
      _logger.i('🔄 Starting sign out process');

      // Clear local state first to prevent further operations
      _currentUser = null;
      _hasValidSession = false;
      _retryAttempts = 0;
      _clearError();
      await _clearRememberMePreference(); // Clear remember me preference

      // Sign out from external services (non-blocking)
      await Future.wait([
        _signOutFromGoogleSignIn(),
        _signOutFromFirebaseAuth(),
        _signOutFromCloudBackup(),
      ], eagerError: false);

      // Add delay to ensure all sign-out operations complete
      await Future.delayed(const Duration(milliseconds: 300));

      // Switch to guest database with improved error handling
      await _switchToGuestDatabase();

      _setLoading(false);
      _logger.i('✅ User signed out successfully');
    } catch (e) {
      _setError('Sign out failed: $e');
      _logger.e('❌ Error during sign out: $e');
      _setLoading(false);

      // Force clear state even if sign out partially failed
      _currentUser = null;
      _retryAttempts = 0;
      _clearError();
    }
  }

  Future<void> _signOutFromGoogleSignIn() async {
    try {
      await _googleSignIn.signOut();
      _logger.i('✅ Google Sign-In sign out completed');
    } catch (e) {
      _logger.w('⚠️ Google Sign-In sign out failed: $e');
    }
  }

  Future<void> _signOutFromFirebaseAuth() async {
    try {
      await FirebaseAuth.instance.signOut();
      _logger.i('✅ Firebase Auth sign out completed');
    } catch (e) {
      _logger.w('⚠️ Firebase Auth sign out failed: $e');
    }
  }

  Future<void> _signOutFromCloudBackup() async {
    try {
      await _cloudBackupService.signOut();
      _logger.i('✅ Cloud backup sign out completed');
    } catch (e) {
      _logger.w('⚠️ Cloud backup sign out failed: $e');
    }
  }

  Future<void> _switchToGuestDatabase() async {
    _logger.i('🔄 Switching to guest database after sign out');

    try {
      final isarService = await IsarService.instance;

      // Close current database cleanly before switching
      if (isarService.isInitialized) {
        _logger.i('🔄 Closing current database before guest switch');
        await isarService.close();
        await Future.delayed(const Duration(milliseconds: 200));
      }

      // Switch to guest mode
      await isarService.switchUser(null); // null = guest mode
      _logger.i('✅ Switched to guest database after sign out');
      _logger.i('📊 Current database info: ${isarService.currentDatabaseInfo}');

      // Single farm per user - no farm context to clear

      // Skip default data initialization to prevent database corruption
      _logger.i('⏭️ Skipping default data initialization to prevent corruption');

    } catch (e) {
      _logger.e('❌ Failed to switch to guest database: $e');
      // Don't throw - allow sign out to complete even if database switch fails
    }
  }

  /// Dispose of resources
  @override
  void dispose() {
    _authStateSubscription?.cancel();
    _retryTimer?.cancel();
    super.dispose();
  }
  
  /// Send email verification
  Future<bool> sendEmailVerification() async {
    try {
      final user = _currentUser;
      if (user == null) return false;
      
      await user.sendEmailVerification();
      _logger.i('Email verification sent');
      return true;
    } catch (e) {
      _setError('Failed to send email verification: $e');
      _logger.e('Error sending email verification: $e');
      return false;
    }
  }
  
  /// Send password reset email
  Future<bool> sendPasswordResetEmail(String email) async {
    try {
      await FirebaseAuth.instance.sendPasswordResetEmail(email: email);
      _logger.i('Password reset email sent to: $email');
      return true;
    } on FirebaseAuthException catch (e) {
      _setError(_getAuthErrorMessage(e));
      _logger.e('Firebase Auth error sending password reset: ${e.code} - ${e.message}');
      return false;
    } catch (e) {
      _setError('Failed to send password reset email: $e');
      _logger.e('Error sending password reset email: $e');
      return false;
    }
  }
  
  /// Reload current user to get updated info
  Future<void> reloadUser() async {
    try {
      await _currentUser?.reload();
      _currentUser = FirebaseAuth.instance.currentUser;
      notifyListeners();
    } catch (e) {
      _logger.e('Error reloading user: $e');
    }
  }
  
  // Local user database methods removed - using Firebase Auth only
  
  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  /// Set error message
  void _setError(String error) {
    _errorMessage = error;
    _isLoading = false;
    notifyListeners();
  }
  
  /// Clear error message
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  // Google local user creation methods removed - using Firebase Auth only

  /// Get user-friendly error message from FirebaseAuthException
  String _getAuthErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      case 'weak-password':
        return 'The password provided is too weak. Please use at least 6 characters.';
      case 'email-already-in-use':
        return 'An account already exists for this email. Try signing in instead.';
      case 'invalid-email':
        return 'The email address is not valid. Please check and try again.';
      case 'user-disabled':
        return 'This user account has been disabled. Please contact support.';
      case 'user-not-found':
        return 'No account found for this email. Please check the email or sign up.';
      case 'wrong-password':
        return 'Incorrect password. Please try again or reset your password.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please wait a few minutes and try again.';
      case 'operation-not-allowed':
        return 'This sign-in method is not enabled. Please contact support.';
      case 'network-request-failed':
        return 'Network error. Please check your internet connection and try again.';
      case 'invalid-credential':
        return 'Invalid credentials. Please check your email and password.';
      case 'user-token-expired':
        return 'Your session has expired. Please sign in again.';
      case 'requires-recent-login':
        return 'This operation requires recent authentication. Please sign in again.';
      case 'account-exists-with-different-credential':
        return 'An account already exists with a different sign-in method.';
      default:
        return e.message ?? 'An authentication error occurred. Please try again.';
    }
  }
}
