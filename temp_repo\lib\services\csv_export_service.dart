// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pregnancy_record.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PregnancyRecordAdapter extends TypeAdapter<PregnancyRecord> {
  @override
  final int typeId = 6;

  @override
  PregnancyRecord read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PregnancyRecord(
      id: fields[0] as String,
      cattleId: fields[1] as String,
      startDate: fields[2] as DateTime,
      status: fields[3] as String,
      notes: fields[4] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, PregnancyRecord obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.cattleId)
      ..writeByte(2)
      ..write(obj.startDate)
      ..writeByte(3)
      ..write(obj.status)
      ..writeByte(4)
      ..write(obj.notes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @o