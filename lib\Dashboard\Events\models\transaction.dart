class BackupSettings {
  final bool autoBackupEnabled;
  final String backupLocation; // 'local' or 'cloud'
  final int backupFrequencyDays;
  final DateTime? lastBackupDate;

  BackupSettings({
    this.autoBackupEnabled = false,
    this.backupLocation = 'local',
    this.backupFrequencyDays = 7,
    this.lastBackupDate,
  });

  Map<String, dynamic> toMap() {
    return {
      'autoBackupEnabled': autoBackupEnabled,
      'backupLocation': backupLocation,
      'backupFrequencyDays': backupFrequencyDays,
      'lastBackupDate': lastBackupDate?.toIso8601String(),
    };
  }

  factory BackupSettings.fromMap(Map<String, dynamic> map) {
    return BackupSettings(
      autoBackupEnabled: map['autoBackupEnabled'] ?? false,
      backupLocation: map['backupLocation'] ?? 'local',
      backupFrequencyDays: map['backupFrequencyDays'] ?? 7,
      lastBackupDate: map['lastBackupDate'] != null 
          ? DateTime.parse(map['lastBackupDate']) 
          : null,
    );
  }

  BackupSettings copyWith({
    bool? autoBackupEnabled,
    String? backupLocation,
    int? backupFrequencyDays,
    DateTime? lastBackupDate,
  }) {
    return BackupSettings(
      autoBackupEnabled: autoBackupEnabled ?? this.autoBackupEnabled,
      backupLocation: backupLocation ?? this.backupLocation,
      backupFrequencyDays: backupFrequencyDays ?? this.backupFrequencyDays,
      lastBackupDate: lastBackupDate ?? this.lastBackupDate,
    );
  }
}
                                                                                                                                                                                                      