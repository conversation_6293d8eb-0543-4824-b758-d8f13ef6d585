import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/health_record_isar.dart';
import '../models/veterinarian_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../constants/health_constants.dart';
import '../../../constants/app_bar.dart';
import '../dialogs/health_record_form_dialog.dart';

class HealthRecordDetailScreen extends StatelessWidget {
  final HealthRecordIsar healthRecord;
  final CattleIsar? cattle;
  final List<CattleIsar> allCattle;
  final List<VeterinarianIsar> veterinarians;
  final VoidCallback onRecordUpdated;

  const HealthRecordDetailScreen({
    super.key,
    required this.healthRecord,
    this.cattle,
    required this.allCattle,
    required this.veterinarians,
    required this.onRecordUpdated,
  });

  @override
  Widget build(BuildContext context) {
    final conditionColor = HealthConstants.getConditionColor(healthRecord.condition);
    final statusColor = HealthConstants.getHealthStatusColor(healthRecord.healthStatus);

    return Scaffold(
      appBar: AppBarConfig.standard(
        title: 'Health Record Details',
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _editRecord(context),
            tooltip: 'Edit Record',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card
            _buildHeaderCard(conditionColor, statusColor),
            const SizedBox(height: 16),

            // Basic Information
            _buildBasicInfoCard(),
            const SizedBox(height: 16),

            // Health Details
            _buildHealthDetailsCard(conditionColor, statusColor),
            const SizedBox(height: 16),

            // Treatment Information
            if (_hasTreatmentInfo()) ...[
              _buildTreatmentCard(),
              const SizedBox(height: 16),
            ],

            // Vital Signs
            if (_hasVitalSigns()) ...[
              _buildVitalSignsCard(),
              const SizedBox(height: 16),
            ],

            // Veterinarian Information
            if (healthRecord.veterinarian != null) ...[
              _buildVeterinarianCard(),
              const SizedBox(height: 16),
            ],

            // Important Dates
            _buildDatesCard(),
            const SizedBox(height: 16),

            // Flags and Status
            _buildFlagsCard(),
            const SizedBox(height: 16),

            // Notes
            if (healthRecord.notes != null && healthRecord.notes!.isNotEmpty) ...[
              _buildNotesCard(),
              const SizedBox(height: 16),
            ],

            // Record Metadata
            _buildMetadataCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard(Color conditionColor, Color statusColor) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Cattle Information
            Row(
              children: [
                Icon(Icons.pets, size: 24, color: Colors.blue[600]),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        cattle?.name ?? 'Unknown Cattle',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Tag: ${cattle?.tagId ?? 'Unknown'}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                // Date
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      healthRecord.date != null 
                          ? DateFormat('MMM dd, yyyy').format(healthRecord.date!)
                          : 'Unknown Date',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      healthRecord.recordType ?? 'General',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Condition and Status
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: conditionColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: conditionColor.withValues(alpha: 0.3)),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Condition',
                          style: TextStyle(
                            fontSize: 12,
                            color: conditionColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          healthRecord.condition ?? 'Unknown',
                          style: TextStyle(
                            fontSize: 16,
                            color: conditionColor,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Health Status',
                          style: TextStyle(
                            fontSize: 12,
                            color: statusColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          healthRecord.healthStatus ?? 'Unknown',
                          style: TextStyle(
                            fontSize: 16,
                            color: statusColor,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue[600]),
                const SizedBox(width: 8),
                Text(
                  'Basic Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (healthRecord.symptoms != null && healthRecord.symptoms!.isNotEmpty) ...[
              _buildDetailRow('Symptoms', healthRecord.symptoms!),
              const SizedBox(height: 12),
            ],
            
            if (healthRecord.diagnosis != null && healthRecord.diagnosis!.isNotEmpty) ...[
              _buildDetailRow('Diagnosis', healthRecord.diagnosis!),
              const SizedBox(height: 12),
            ],
            
            if (healthRecord.severity != null) ...[
              _buildDetailRow('Severity', healthRecord.severity!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHealthDetailsCard(Color conditionColor, Color statusColor) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.medical_information, color: Colors.red[600]),
                const SizedBox(width: 8),
                Text(
                  'Health Details',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildDetailRow('Record Type', healthRecord.recordType ?? 'General'),
            const SizedBox(height: 12),
            _buildDetailRow('Condition', healthRecord.condition ?? 'Unknown'),
            const SizedBox(height: 12),
            _buildDetailRow('Health Status', healthRecord.healthStatus ?? 'Unknown'),
            
            if (healthRecord.severity != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow('Severity', healthRecord.severity!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTreatmentCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.medication, color: Colors.green[600]),
                const SizedBox(width: 8),
                Text(
                  'Treatment Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (healthRecord.treatment != null && healthRecord.treatment!.isNotEmpty) ...[
              _buildDetailRow('Treatment', healthRecord.treatment!),
              const SizedBox(height: 12),
            ],
            
            if (healthRecord.medication != null && healthRecord.medication!.isNotEmpty) ...[
              _buildDetailRow('Medication', healthRecord.medication!),
              const SizedBox(height: 12),
            ],
            
            if (healthRecord.dosage != null && healthRecord.dosage!.isNotEmpty) ...[
              _buildDetailRow('Dosage', healthRecord.dosage!),
              const SizedBox(height: 12),
            ],
            
            if (healthRecord.frequency != null && healthRecord.frequency!.isNotEmpty) ...[
              _buildDetailRow('Frequency', healthRecord.frequency!),
              const SizedBox(height: 12),
            ],
            
            if (healthRecord.duration != null && healthRecord.duration!.isNotEmpty) ...[
              _buildDetailRow('Duration', healthRecord.duration!),
              const SizedBox(height: 12),
            ],
            
            if (healthRecord.treatmentCost != null) ...[
              _buildDetailRow('Cost', '\$${healthRecord.treatmentCost!.toStringAsFixed(2)}'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildVitalSignsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.monitor_heart, color: Colors.purple[600]),
                const SizedBox(width: 8),
                Text(
                  'Vital Signs',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                if (healthRecord.temperature != null) ...[
                  Expanded(
                    child: _buildVitalSignItem(
                      'Temperature',
                      '${healthRecord.temperature!.toStringAsFixed(1)}°C',
                      Icons.thermostat,
                      Colors.red,
                    ),
                  ),
                ],
                if (healthRecord.heartRate != null) ...[
                  if (healthRecord.temperature != null) const SizedBox(width: 16),
                  Expanded(
                    child: _buildVitalSignItem(
                      'Heart Rate',
                      '${healthRecord.heartRate!} BPM',
                      Icons.favorite,
                      Colors.pink,
                    ),
                  ),
                ],
              ],
            ),
            
            if (healthRecord.respiratoryRate != null || healthRecord.weight != null) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  if (healthRecord.respiratoryRate != null) ...[
                    Expanded(
                      child: _buildVitalSignItem(
                        'Respiratory Rate',
                        '${healthRecord.respiratoryRate!} /min',
                        Icons.air,
                        Colors.blue,
                      ),
                    ),
                  ],
                  if (healthRecord.weight != null) ...[
                    if (healthRecord.respiratoryRate != null) const SizedBox(width: 16),
                    Expanded(
                      child: _buildVitalSignItem(
                        'Weight',
                        '${healthRecord.weight!.toStringAsFixed(1)} kg',
                        Icons.scale,
                        Colors.green,
                      ),
                    ),
                  ],
                ],
              ),
            ],
            
            if (healthRecord.bodyConditionScore != null) ...[
              const SizedBox(height: 16),
              _buildVitalSignItem(
                'Body Condition Score',
                '${healthRecord.bodyConditionScore!.toStringAsFixed(1)}/9',
                Icons.fitness_center,
                Colors.indigo,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildVitalSignItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildVeterinarianCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Colors.teal[600]),
                const SizedBox(width: 8),
                Text(
                  'Veterinarian Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildDetailRow('Veterinarian', healthRecord.veterinarian!),
            
            if (healthRecord.veterinarianContact != null && healthRecord.veterinarianContact!.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildDetailRow('Contact', healthRecord.veterinarianContact!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDatesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.date_range, color: Colors.indigo[600]),
                const SizedBox(width: 8),
                Text(
                  'Important Dates',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.indigo[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildDetailRow(
              'Record Date',
              healthRecord.date != null 
                  ? DateFormat('MMM dd, yyyy').format(healthRecord.date!)
                  : 'Unknown',
            ),
            
            if (healthRecord.followUpDate != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow(
                'Follow-up Date',
                DateFormat('MMM dd, yyyy').format(healthRecord.followUpDate!),
              ),
            ],
            
            if (healthRecord.recoveryDate != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow(
                'Recovery Date',
                DateFormat('MMM dd, yyyy').format(healthRecord.recoveryDate!),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFlagsCard() {
    final flags = <String, Color>{};
    
    if (healthRecord.isEmergency) flags['Emergency'] = Colors.red;
    if (healthRecord.isChronic) flags['Chronic'] = Colors.purple;
    if (healthRecord.isUnderTreatment) flags['Under Treatment'] = Colors.blue;
    if (healthRecord.requiresFollowUp) flags['Requires Follow-up'] = Colors.green;

    if (flags.isEmpty) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flag, color: Colors.deepPurple[600]),
                const SizedBox(width: 8),
                Text(
                  'Record Flags',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.deepPurple[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: flags.entries.map((entry) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: entry.value.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: entry.value.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    entry.key,
                    style: TextStyle(
                      color: entry.value,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.notes, color: Colors.blueGrey[600]),
                const SizedBox(width: 8),
                Text(
                  'Notes',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blueGrey[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Text(
              healthRecord.notes!,
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  'Record Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (healthRecord.createdAt != null) ...[
              _buildDetailRow(
                'Created',
                DateFormat('MMM dd, yyyy HH:mm').format(healthRecord.createdAt!),
              ),
              const SizedBox(height: 12),
            ],
            
            if (healthRecord.updatedAt != null) ...[
              _buildDetailRow(
                'Last Updated',
                DateFormat('MMM dd, yyyy HH:mm').format(healthRecord.updatedAt!),
              ),
              const SizedBox(height: 12),
            ],
            
            if (healthRecord.businessId != null) ...[
              _buildDetailRow('Record ID', healthRecord.businessId!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 14),
          ),
        ),
      ],
    );
  }

  bool _hasTreatmentInfo() {
    return (healthRecord.treatment != null && healthRecord.treatment!.isNotEmpty) ||
           (healthRecord.medication != null && healthRecord.medication!.isNotEmpty) ||
           (healthRecord.dosage != null && healthRecord.dosage!.isNotEmpty) ||
           (healthRecord.frequency != null && healthRecord.frequency!.isNotEmpty) ||
           (healthRecord.duration != null && healthRecord.duration!.isNotEmpty) ||
           healthRecord.treatmentCost != null;
  }

  bool _hasVitalSigns() {
    return healthRecord.temperature != null ||
           healthRecord.heartRate != null ||
           healthRecord.respiratoryRate != null ||
           healthRecord.weight != null ||
           healthRecord.bodyConditionScore != null;
  }

  void _editRecord(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HealthRecordFormDialog(
          cattle: allCattle,
          veterinarians: veterinarians,
          existingRecord: healthRecord,
          onRecordAdded: onRecordUpdated,
        ),
      ),
    );
  }
}
