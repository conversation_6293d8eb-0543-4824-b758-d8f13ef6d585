import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/transaction_isar.dart';
import '../models/category_isar.dart';
import '../services/transactions_handler.dart';
import '../widgets/transaction_list_item.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_colors.dart';
import '../../../services/streams/stream_service.dart';
import '../screens/transactions_screen.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import '../../widgets/date_range_filter_widget.dart';
import '../../widgets/filter_status_bar.dart';


class TransactionSummaryTab extends StatefulWidget {
  const TransactionSummaryTab({Key? key}) : super(key: key);

  @override
  State<TransactionSummaryTab> createState() => _TransactionSummaryTabState();
}

class _TransactionSummaryTabState extends State<TransactionSummaryTab> {
  final TransactionsHandler _transactionsHandler = GetIt.instance<TransactionsHandler>();
  final FarmSetupHandler _farmSetupHandler = GetIt.instance<FarmSetupHandler>();
  bool _isLoading = true;
  List<TransactionIsar> _transactions = [];
  List<CategoryIsar> _categories = [];
  double _totalIncome = 0.0;
  double _totalExpenses = 0.0;
  double _netBalance = 0.0;
  DateTime? _startDate;  // Start with null (no filter active)
  DateTime? _endDate;    // Start with null (no filter active)

  // Currency settings
  String _currencySymbol = '\$';
  bool _symbolBeforeAmount = true;

  // Enhanced analytics data
  final Map<String, double> _categoryExpenses = {};
  final Map<String, double> _categoryIncome = {};
  final Map<String, int> _paymentMethodCounts = {};
  final List<FlSpot> _incomeSpots = [];
  final List<FlSpot> _expenseSpots = [];
  double _averageTransactionAmount = 0.0;
  double _largestTransaction = 0.0;
  int _transactionCount = 0;

  // Chart toggle state
  bool _showExpenseChart = true; // true for expenses, false for income

  // Stream subscription for real-time updates
  StreamSubscription<Map<String, dynamic>>? _transactionStreamSubscription;

  // Cache for performance optimization
  DateTime? _lastDataLoadTime;
  static const Duration _cacheValidDuration = Duration(minutes: 5);

  @override
  void initState() {
    super.initState();
    _subscribeToTransactionUpdates();
    // Load data asynchronously without blocking UI
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeScreen();
    });
  }

  Future<void> _initializeScreen() async {
    // Load currency settings and data in parallel for faster loading
    await Future.wait([
      _loadCurrencySettings(),
      _loadData(),
    ]);
  }

  @override
  void dispose() {
    _transactionStreamSubscription?.cancel();
    super.dispose();
  }

  /// Subscribe to transaction updates for real-time UI updates
  void _subscribeToTransactionUpdates() {
    final streamService = GetIt.instance<StreamService>();

    // Subscribe to transaction updates
    _transactionStreamSubscription = streamService.transactionStream.listen((event) {
      debugPrint('Transaction Summary stream event: ${event['action']}');
      if (mounted) {
        _loadData(); // Refresh data when transactions change
      }
    }, onError: (error) {
      debugPrint('Error in transaction summary stream: $error');
    });
  }

  Future<void> _loadCurrencySettings() async {
    try {
      final currencySettings = await _farmSetupHandler.getCurrencySettings();
      if (mounted) {
        setState(() {
          _currencySymbol = currencySettings.currencySymbol;
          _symbolBeforeAmount = currencySettings.symbolBeforeAmount;
        });
      }
    } catch (e) {
      debugPrint('Error loading currency settings: $e');
      // Use default values if loading fails
      if (mounted) {
        setState(() {
          _currencySymbol = '\$';
          _symbolBeforeAmount = true;
        });
      }
    }
  }

  String _formatCurrency(double amount) {
    final formattedAmount = amount.toStringAsFixed(2);
    return _symbolBeforeAmount
        ? _currencySymbol + formattedAmount
        : formattedAmount + _currencySymbol;
  }

  bool _hasActiveDateFilter() {
    // Only consider filter active if both dates are set (not null)
    return _startDate != null && _endDate != null;
  }

  void _clearDateFilter() {
    setState(() {
      _startDate = null;
      _endDate = null;
    });
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      // Check if we have recent cached data
      if (_lastDataLoadTime != null &&
          DateTime.now().difference(_lastDataLoadTime!) < _cacheValidDuration &&
          _transactions.isNotEmpty) {
        return; // Use cached data
      }

      if (mounted) setState(() => _isLoading = true);

      // Load transactions and categories in parallel for faster loading
      final futures = await Future.wait([
        _startDate != null && _endDate != null
          ? _transactionsHandler.getTransactionsForDateRange(_startDate!, _endDate!)
          : _transactionsHandler.getAllTransactions(), // Get all transactions if no date filter
        _transactionsHandler.getAllCategories(),
      ]);

      final transactions = futures[0] as List<TransactionIsar>;
      final categories = futures[1] as List<CategoryIsar>;

      if (!mounted) return;

      // Calculate totals efficiently
      double income = 0.0;
      double expenses = 0.0;

      for (final transaction in transactions) {
        if (transaction.categoryType == 'Income') {
          income += transaction.amount;
        } else {
          expenses += transaction.amount;
        }
      }

      // Calculate analytics in background to avoid blocking UI
      Future.microtask(() => _calculateAnalytics(transactions));

      setState(() {
        _transactions = transactions;
        _categories = categories;
        _totalIncome = income;
        _totalExpenses = expenses;
        _netBalance = income - expenses;
        _isLoading = false;
      });

      _lastDataLoadTime = DateTime.now();
    } catch (e) {
      debugPrint('Error loading summary data: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        FinancialMessageUtils.showError(context, 'Error loading summary data');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading financial data...'),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DateRangeFilterWidget(
            startDate: _startDate,  // Pass null when no filter active
            endDate: _endDate,      // Pass null when no filter active
            themeColor: Colors.purple,
            onStartDateChanged: (date) {
              setState(() => _startDate = date);
              _loadData();
            },
            onEndDateChanged: (date) {
              setState(() => _endDate = date);
              _loadData();
            },
            onClearFilter: () {
              setState(() {
                _startDate = null;
                _endDate = null;
              });
              _loadData();
            },
            onFiltersChanged: _loadData,
          ),
          const SizedBox(height: 8),

          // Filter Status Bar (Clear Date Filter + Transaction Count)
          FilterStatusBar.analytics(
            filterStates: {
              'startDate': _startDate,
              'endDate': _endDate,
            },
            totalCount: null, // Don't show count in summary tab
            filteredCount: _transactions.length,
            onClearFilters: _clearDateFilter,
            defaultStartDate: DateTime.now().subtract(const Duration(days: 30)),
            defaultEndDate: DateTime.now(),
          ),

          const SizedBox(height: 16),
          _buildEnhancedSummaryCards(),
          const SizedBox(height: 24),
          // Lazy load charts only when data is available
          if (_transactions.isNotEmpty) ...[
            _buildAnalyticsCharts(),
            const SizedBox(height: 24),
            _buildAdvancedMetrics(),
            const SizedBox(height: 24),
          ],
          _buildRecentTransactions(),
        ],
      ),
    );
  }







  void _calculateAnalytics(List<TransactionIsar> transactions) {
    // Reset analytics data
    _categoryExpenses.clear();
    _categoryIncome.clear();
    _paymentMethodCounts.clear();
    _incomeSpots.clear();
    _expenseSpots.clear();

    if (transactions.isEmpty) {
      _averageTransactionAmount = 0.0;
      _largestTransaction = 0.0;
      _transactionCount = 0;
      return;
    }

    // Calculate basic metrics
    _transactionCount = transactions.length;
    double totalAmount = 0.0;
    _largestTransaction = 0.0;

    // Group transactions by date for trend analysis
    Map<DateTime, double> dailyIncome = {};
    Map<DateTime, double> dailyExpenses = {};

    for (final transaction in transactions) {
      totalAmount += transaction.amount;
      if (transaction.amount > _largestTransaction) {
        _largestTransaction = transaction.amount;
      }

      // Group by category
      if (transaction.categoryType == 'Income') {
        _categoryIncome[transaction.category] =
            (_categoryIncome[transaction.category] ?? 0.0) + transaction.amount;
      } else {
        _categoryExpenses[transaction.category] =
            (_categoryExpenses[transaction.category] ?? 0.0) + transaction.amount;
      }

      // Count payment methods
      _paymentMethodCounts[transaction.paymentMethod] =
          (_paymentMethodCounts[transaction.paymentMethod] ?? 0) + 1;

      // Group by date for trend charts
      final dateKey = DateTime(transaction.date.year, transaction.date.month, transaction.date.day);
      if (transaction.categoryType == 'Income') {
        dailyIncome[dateKey] = (dailyIncome[dateKey] ?? 0.0) + transaction.amount;
      } else {
        dailyExpenses[dateKey] = (dailyExpenses[dateKey] ?? 0.0) + transaction.amount;
      }
    }

    _averageTransactionAmount = totalAmount / _transactionCount;

    // Create chart data points
    final sortedDates = {...dailyIncome.keys, ...dailyExpenses.keys}.toList()..sort();

    for (int i = 0; i < sortedDates.length; i++) {
      final date = sortedDates[i];
      final daysSinceStart = _startDate != null
        ? date.difference(_startDate!).inDays.toDouble()
        : i.toDouble(); // Use index if no start date

      _incomeSpots.add(FlSpot(daysSinceStart, dailyIncome[date] ?? 0.0));
      _expenseSpots.add(FlSpot(daysSinceStart, dailyExpenses[date] ?? 0.0));
    }
  }

  Widget _buildEnhancedSummaryCards() {
    return Column(
      children: [
        // First row - Income and Expenses
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Total Income',
                _totalIncome,
                Icons.trending_up,
                AppColors.primary,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard(
                'Total Expenses',
                _totalExpenses,
                Icons.trending_down,
                Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // Second row - Net Balance and Transaction Count
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Net Balance',
                _netBalance,
                Icons.account_balance_wallet,
                _netBalance >= 0 ? Colors.blue : Colors.red,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildMetricCard(
                'Transactions',
                _transactionCount.toString(),
                Icons.receipt_long,
                Colors.purple,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // Third row - Average and Largest Transaction
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Average Amount',
                _averageTransactionAmount,
                Icons.analytics,
                Colors.teal,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard(
                'Largest Transaction',
                _largestTransaction,
                Icons.star,
                Colors.indigo,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            CircleAvatar(
              backgroundColor: color.withValues(alpha: 0.1),
              radius: 20,
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: color.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    double amount,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            CircleAvatar(
              backgroundColor: color.withValues(alpha: 0.1),
              radius: 20,
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: color.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              _formatCurrency(amount),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticsCharts() {
    if (_transactions.isEmpty) {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: const Padding(
          padding: EdgeInsets.all(24),
          child: Center(
            child: Column(
              children: [
                Icon(Icons.analytics, size: 48, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'No Data Available',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
                ),
                Text(
                  'Add some transactions to see analytics',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Column(
      children: [
        // Income vs Expenses Trend Chart
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Income vs Expenses Trend',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 250,
                  child: LineChart(
                    LineChartData(
                      gridData: FlGridData(
                        show: true,
                        drawVerticalLine: true,
                        horizontalInterval: _getChartInterval(),
                        getDrawingHorizontalLine: (value) {
                          return FlLine(
                            color: Colors.grey.withValues(alpha: 0.3),
                            strokeWidth: 1,
                          );
                        },
                        getDrawingVerticalLine: (value) {
                          return FlLine(
                            color: Colors.grey.withValues(alpha: 0.3),
                            strokeWidth: 1,
                          );
                        },
                      ),
                      titlesData: FlTitlesData(
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            reservedSize: 50,
                            getTitlesWidget: (value, meta) {
                              return Text(
                                _formatCurrency(value),
                                style: const TextStyle(fontSize: 10),
                              );
                            },
                          ),
                        ),
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            reservedSize: 30,
                            getTitlesWidget: (value, meta) {
                              if (_startDate != null) {
                                final date = _startDate!.add(Duration(days: value.toInt()));
                                return Text(
                                  DateFormat('MM/dd').format(date),
                                  style: const TextStyle(fontSize: 10),
                                );
                              }
                              return Text(
                                value.toInt().toString(),
                                style: const TextStyle(fontSize: 10),
                              );
                            },
                          ),
                        ),
                        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                        rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                      ),
                      borderData: FlBorderData(
                        show: true,
                        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                      ),
                      lineBarsData: [
                        LineChartBarData(
                          spots: _incomeSpots,
                          isCurved: true,
                          color: AppColors.primary,
                          barWidth: 3,
                          dotData: FlDotData(
                            show: true,
                            getDotPainter: (spot, percent, barData, index) {
                              return FlDotCirclePainter(
                                radius: 4,
                                color: AppColors.primary,
                                strokeWidth: 2,
                                strokeColor: Colors.white,
                              );
                            },
                          ),
                          belowBarData: BarAreaData(
                            show: true,
                            color: AppColors.primary.withValues(alpha: 0.1),
                          ),
                        ),
                        LineChartBarData(
                          spots: _expenseSpots,
                          isCurved: true,
                          color: Colors.red,
                          barWidth: 3,
                          dotData: FlDotData(
                            show: true,
                            getDotPainter: (spot, percent, barData, index) {
                              return FlDotCirclePainter(
                                radius: 4,
                                color: Colors.red,
                                strokeWidth: 2,
                                strokeColor: Colors.white,
                              );
                            },
                          ),
                          belowBarData: BarAreaData(
                            show: true,
                            color: Colors.red.withValues(alpha: 0.1),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildLegendItem('Income', AppColors.primary),
                    const SizedBox(width: 24),
                    _buildLegendItem('Expenses', Colors.red),
                  ],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        // Category Breakdown Charts
        if (_categoryExpenses.isNotEmpty || _categoryIncome.isNotEmpty)
          _buildCategoryCharts(),
      ],
    );
  }

  double _getChartInterval() {
    final maxValue = [..._incomeSpots, ..._expenseSpots]
        .map((spot) => spot.y)
        .fold(0.0, (max, value) => value > max ? value : max);

    if (maxValue <= 100) return 20;
    if (maxValue <= 500) return 100;
    if (maxValue <= 1000) return 200;
    if (maxValue <= 5000) return 1000;
    return 2000;
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryCharts() {
    if (_categoryExpenses.isEmpty && _categoryIncome.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Category Breakdown',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => setState(() => _showExpenseChart = !_showExpenseChart),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: (_showExpenseChart ? Colors.red : AppColors.primary).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: _showExpenseChart ? Colors.red : AppColors.primary,
                            width: 1.5,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _showExpenseChart ? Icons.trending_up : Icons.trending_down,
                              size: 14,
                              color: _showExpenseChart ? AppColors.primary : Colors.red,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _showExpenseChart ? 'Income' : 'Expenses',
                              style: TextStyle(
                                color: _showExpenseChart ? AppColors.primary : Colors.red,
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: (_showExpenseChart ? Colors.red : AppColors.primary).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Showing ${_showExpenseChart ? 'Expenses' : 'Income'} by Category',
                    style: TextStyle(
                      fontSize: 13,
                      color: _showExpenseChart ? Colors.red : AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _showExpenseChart && _categoryExpenses.isNotEmpty
                ? _buildSingleCategoryChart(
                    _categoryExpenses,
                    _totalExpenses,
                    [Colors.red, Colors.orange, Colors.pink, Colors.deepOrange, Colors.redAccent],
                  )
                : !_showExpenseChart && _categoryIncome.isNotEmpty
                    ? _buildSingleCategoryChart(
                        _categoryIncome,
                        _totalIncome,
                        [AppColors.primary, Colors.teal, Colors.cyan, Colors.lightGreen, Colors.green],
                      )
                    : const Center(
                        child: Padding(
                          padding: EdgeInsets.all(20),
                          child: Text(
                            'No data available for selected category',
                            style: TextStyle(color: Colors.purple, fontSize: 16),  // Use solid purple instead of forbidden grey
                          ),
                        ),
                      ),
          ],
        ),
      ),
    );
  }



  Widget _buildSingleCategoryChart(
    Map<String, double> categoryData,
    double totalAmount,
    List<Color> colors,
  ) {
    final sortedData = categoryData.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return Column(
      children: [
        // Centered Pie Chart
        Container(
          height: 250,
          padding: const EdgeInsets.all(16),
          child: Center(
            child: SizedBox(
              width: 220,
              height: 220,
              child: PieChart(
                PieChartData(
                  sections: sortedData.take(5).map((entry) {
                    final index = sortedData.indexOf(entry);
                    final percentage = (entry.value / totalAmount) * 100;
                    return PieChartSectionData(
                      value: entry.value,
                      title: '${percentage.toStringAsFixed(1)}%',
                      color: colors[index % colors.length],
                      radius: 90,
                      titleStyle: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    );
                  }).toList(),
                  sectionsSpace: 3,
                  centerSpaceRadius: 50,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 32),
        // Legend Below Chart
        Wrap(
          spacing: 16,
          runSpacing: 12,
          alignment: WrapAlignment.center,
          children: sortedData.take(5).map((entry) {
            final index = sortedData.indexOf(entry);
            final percentage = (entry.value / totalAmount) * 100;
            return Container(
              constraints: const BoxConstraints(minWidth: 140, maxWidth: 180),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: colors[index % colors.length],
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          entry.key,
                          style: const TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                        Text(
                          '${_formatCurrency(entry.value)} (${percentage.toStringAsFixed(1)}%)',
                          style: TextStyle(
                            fontSize: 11,
                            color: colors[index % colors.length].withValues(alpha: 0.8),
                            fontWeight: FontWeight.w500,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }



  Widget _buildAdvancedMetrics() {
    if (_transactions.isEmpty) return const SizedBox.shrink();

    final savingsRate = _totalIncome > 0 ? ((_totalIncome - _totalExpenses) / _totalIncome) * 100 : 0.0;
    final avgTransactionsPerDay = (_startDate != null && _endDate != null)
      ? _transactionCount / (_endDate!.difference(_startDate!).inDays + 1)
      : _transactionCount.toDouble(); // If no date filter, show total count

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Advanced Metrics',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem(
                    'Savings Rate',
                    '${savingsRate.toStringAsFixed(1)}%',
                    Icons.savings,
                    savingsRate >= 0 ? AppColors.primary : Colors.red,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildMetricItem(
                    'Daily Avg',
                    avgTransactionsPerDay.toStringAsFixed(1),
                    Icons.today,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_paymentMethodCounts.isNotEmpty) ...[
              Row(
                children: [
                  Icon(Icons.payment, color: Colors.purple, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Payment Methods',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              ..._paymentMethodCounts.entries.map((entry) {
                final percentage = (entry.value / _transactionCount) * 100;
                final methodColor = _getPaymentMethodColor(entry.key);
                final methodIcon = _getPaymentMethodIcon(entry.key);

                return Container(
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: methodColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: methodColor.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: methodColor.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Icon(
                          methodIcon,
                          size: 18,
                          color: methodColor,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          entry.key,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: methodColor,
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: methodColor.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${entry.value} (${percentage.toStringAsFixed(1)}%)',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: methodColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMetricItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.purple,  // Use solid purple instead of forbidden grey
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getPaymentMethodColor(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return Colors.green;
      case 'card':
      case 'credit card':
      case 'debit card':
        return Colors.blue;
      case 'bank transfer':
      case 'transfer':
        return Colors.purple;
      case 'mobile payment':
      case 'mobile':
        return const Color(0xFFE65100); // Deep orange instead of orange
      case 'check':
      case 'cheque':
        return Colors.teal;
      default:
        return const Color(0xFF795548); // Brown instead of grey
    }
  }

  IconData _getPaymentMethodIcon(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return Icons.money;
      case 'card':
      case 'credit card':
      case 'debit card':
        return Icons.credit_card;
      case 'bank transfer':
      case 'transfer':
        return Icons.account_balance;
      case 'mobile payment':
      case 'mobile':
        return Icons.phone_android;
      case 'check':
      case 'cheque':
        return Icons.receipt;
      default:
        return Icons.payment;
    }
  }

  Widget _buildRecentTransactions() {
    final recentTransactions = _transactions.take(5).toList();

    if (recentTransactions.isEmpty) {
      return Card(
        elevation: 6,
        shadowColor: Colors.black12,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: const Padding(
          padding: EdgeInsets.fromLTRB(16, 16, 16, 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Recent Transactions',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: Colors.black87,
                ),
              ),
              SizedBox(height: 16),
              Center(
                child: Text(
                  'No transactions in selected date range',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 6,
      shadowColor: Colors.black12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with padding
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Recent Transactions',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: Colors.black87,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // Navigate to transactions screen with Records tab
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const TransactionsScreen(initialTabIndex: 1),
                      ),
                    );
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
          ),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.only(bottom: 8),
            itemCount: recentTransactions.length,
            itemBuilder: (context, index) {
              final transaction = recentTransactions[index];
              final category = _categories.firstWhere(
                (c) => c.name == transaction.category,
                orElse: () => CategoryIsar()..name = transaction.category,
              );

              return TransactionListItem(
                transaction: transaction,
                category: category,
                compact: true, // Use compact layout for summary screen
                onEdit: () {
                  // Navigate to transactions screen with Records tab for editing
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const TransactionsScreen(initialTabIndex: 1),
                    ),
                  );
                },
                onDelete: () {
                  // Navigate to transactions screen with Records tab for management
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const TransactionsScreen(initialTabIndex: 1),
                    ),
                  );
                },
                isSelectionMode: false, // Always false for summary screen
                isSelected: false, // Always false for summary screen
              );
            },
          ),
        ],
      ),
    );
  }


}
