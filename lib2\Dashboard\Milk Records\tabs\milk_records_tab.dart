import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/milk_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../widgets/milk_record_card.dart';
// New component imports
import '../../widgets/filter_widget.dart';
import '../../widgets/sort_widget.dart';
import '../../widgets/search_widget.dart';
import '../../widgets/date_range_filter_widget.dart';
import '../../widgets/filter_status_bar.dart';
import '../../widgets/index.dart';

class MilkRecordsTab extends StatefulWidget {
  final List<MilkRecordIsar> allMilkRecords;
  final List<MilkRecordIsar> filteredMilkRecords;
  final Map<String, CattleIsar> cattleMap;
  final Map<String, AnimalTypeIsar> animalTypeMap;
  final Map<String, BreedCategoryIsar> breedMap;
  final List<CattleIsar> allCattle;
  final bool isSelectionMode;
  final Set<String> selectedRecords;
  final Function(MilkRecordIsar) onRecordTap;
  final Function(MilkRecordIsar) onRecordLongPress;
  final Function(MilkRecordIsar, bool) onSelectionChanged;
  final VoidCallback onRefresh;
  final Function(MilkRecordIsar) onEditRecord;
  final Function(MilkRecordIsar) onDeleteRecord;

  // Filter states
  final DateTime? startDate;
  final DateTime? endDate;
  final String? selectedAnimalType;
  final String? selectedBreed;
  final String? selectedGender;
  final String? selectedCattleId;
  final String? selectedSession;
  final String? selectedQuality;
  final String? sortBy;
  final bool sortAscending;
  final String searchQuery;

  // Filter callbacks
  final Function(DateTime?) onStartDateChanged;
  final Function(DateTime?) onEndDateChanged;
  final Function(String?) onAnimalTypeChanged;
  final Function(String?) onBreedChanged;
  final Function(String?) onGenderChanged;
  final Function(String?) onCattleChanged;
  final Function(String?) onSessionChanged;
  final Function(String?) onQualityChanged;
  final Function(String?) onSortByChanged;
  final Function(bool) onSortAscendingChanged;
  final Function(String) onSearchChanged;
  final VoidCallback onClearFilters;
  final VoidCallback onApplyFilters;

  const MilkRecordsTab({
    Key? key,
    required this.allMilkRecords,
    required this.filteredMilkRecords,
    required this.cattleMap,
    required this.animalTypeMap,
    required this.breedMap,
    required this.allCattle,
    required this.isSelectionMode,
    required this.selectedRecords,
    required this.onRecordTap,
    required this.onRecordLongPress,
    required this.onSelectionChanged,
    required this.onRefresh,
    required this.onEditRecord,
    required this.onDeleteRecord,
    this.startDate,
    this.endDate,
    this.selectedAnimalType,
    this.selectedBreed,
    this.selectedGender,
    this.selectedCattleId,
    this.selectedSession,
    this.selectedQuality,
    this.sortBy,
    this.sortAscending = false,
    this.searchQuery = '',
    required this.onStartDateChanged,
    required this.onEndDateChanged,
    required this.onAnimalTypeChanged,
    required this.onBreedChanged,
    required this.onGenderChanged,
    required this.onCattleChanged,
    required this.onSessionChanged,
    required this.onQualityChanged,
    required this.onSortByChanged,
    required this.onSortAscendingChanged,
    required this.onSearchChanged,
    required this.onClearFilters,
    required this.onApplyFilters,
  }) : super(key: key);

  @override
  State<MilkRecordsTab> createState() => _MilkRecordsTabState();
}

class _MilkRecordsTabState extends State<MilkRecordsTab> {
  static const _recordsColor = Color(0xFF1976D2); // Blue for records

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async => widget.onRefresh(),
      child: Column(
      children: [
        // Row 1: Filter + Date + Sort
        Row(
          children: [
                // Filter Widget
                Expanded(
                  child: FilterWidget(
                    // 4 Core Common Filters (used in ALL modules) + Module-specific filters
                    filterFields: [
                      // 1. Animal Type (Primary common filter)
                      FilterField(
                        key: 'animalType',
                        label: 'Animal Type',
                        icon: Icons.pets,
                        iconColor: Colors.blue,
                        options: _generateAnimalTypeOptions(),
                        currentValue: widget.selectedAnimalType,
                      ),

                      // 2. Breed (Secondary common filter - dependent on animal type)
                      FilterField(
                        key: 'breed',
                        label: 'Breed',
                        icon: Icons.category,
                        iconColor: Colors.purple,
                        options: _generateBreedOptions(),
                        currentValue: widget.selectedBreed,
                        dependsOn: 'animalType',
                      ),

                      // 3. Gender (Secondary common filter)
                      FilterField(
                        key: 'gender',
                        label: 'Gender',
                        icon: Icons.wc,
                        iconColor: Colors.red,  // Multi-color rule: Red for gender (no repetition)
                        options: _generateGenderOptions(),
                        currentValue: widget.selectedGender,
                      ),

                      // 4. Cattle (Dependent common filter - depends on animalType, breed, gender)
                      FilterField(
                        key: 'cattle',
                        label: 'Cattle',
                        icon: Icons.pets,
                        iconColor: Colors.green,
                        options: _generateCattleOptions(),
                        currentValue: widget.selectedCattleId,
                        dependsOn: 'animalType,breed,gender',
                      ),

                      // Module-specific filters for milk
                      FilterField(
                        key: 'session',
                        label: 'Milking Session',
                        icon: Icons.local_drink,
                        iconColor: Colors.indigo,  // Multi-color rule: Indigo for session (no repetition)
                        options: _generateSessionOptions(),
                        currentValue: widget.selectedSession,
                      ),
                    ],

                    // Data maps for generating dependent filter options
                    cattleMap: widget.cattleMap,
                    animalTypeMap: widget.animalTypeMap,
                    breedMap: widget.breedMap,
                    onFilterChanged: (key, value) {
                      switch (key) {
                        // Core common filters
                        case 'animalType':
                          widget.onAnimalTypeChanged(value);
                          break;
                        case 'breed':
                          widget.onBreedChanged(value);
                          break;
                        case 'gender':
                          widget.onGenderChanged(value);
                          break;
                        case 'cattle':
                          widget.onCattleChanged(value);
                          break;

                        // Module-specific filters
                        case 'session':
                          widget.onSessionChanged(value);
                          break;
                      }
                    },
                    onApplyFilters: widget.onApplyFilters,
                    onClearFilters: widget.onClearFilters,
                    themeColor: Colors.purple,  // Use purple for consistency
                    dialogTitle: 'Filter Milk Records',
                    compact: true,
                    totalCount: widget.allMilkRecords.length,
                    filteredCount: widget.filteredMilkRecords.length,
                    showFilterCount: true,
                    buttonHeight: 44,
                  ),
                ),
                const SizedBox(width: 8),
                // Date Range Filter
                Expanded(
                  child: DateRangeFilterWidget(
                    startDate: widget.startDate,
                    endDate: widget.endDate,
                    onStartDateChanged: widget.onStartDateChanged,
                    onEndDateChanged: widget.onEndDateChanged,
                    onFiltersChanged: widget.onApplyFilters,
                    onClearFilter: widget.onClearFilters, // Use main clear callback
                    themeColor: Colors.purple,  // Use purple for consistency
                    activeBackgroundOpacity: 0.7,
                    compact: true,
                    buttonHeight: 44,
                  ),
                ),
                const SizedBox(width: 8),
                // Sort Widget
                Expanded(
                  child: SortWidget(
                    sortFields: const [
                      SortField(
                        value: 'Date',
                        label: 'Date',
                        icon: Icons.calendar_today,
                        iconColor: Colors.blue,  // Multi-color rule: Blue for date
                      ),
                      SortField(
                        value: 'Cattle',
                        label: 'Cattle',
                        icon: Icons.pets,
                        iconColor: Colors.purple,  // Multi-color rule: Purple for cattle
                      ),
                      SortField(
                        value: 'Amount',
                        label: 'Amount',
                        icon: Icons.water_drop,
                        iconColor: Colors.green,  // Multi-color rule: Green for amount
                      ),
                    ],
                    sortBy: widget.sortBy,
                    sortAscending: widget.sortAscending,
                    onSortByChanged: widget.onSortByChanged,
                    onSortAscendingChanged: widget.onSortAscendingChanged,
                    onApplySort: widget.onApplyFilters,
                    themeColor: Colors.purple,  // Use purple for consistency
                    compact: true,
                    showSortIndicator: true,
                    buttonHeight: 44,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),

          // Filter Status Bar (Clear Filters + Count)
          FilterStatusBar.milk(
            filterStates: {},
            totalCount: widget.allMilkRecords.length,
            filteredCount: widget.filteredMilkRecords.length,
            onClearFilters: widget.onClearFilters,
            defaultStartDate: DateTime.now().subtract(const Duration(days: 30)),
            defaultEndDate: DateTime.now(),
          ),

          // Row 2: Search
          SearchWidget(
            searchQuery: widget.searchQuery,
            onSearchChanged: widget.onSearchChanged,
            config: SearchConfig.milk,
            themeColor: Colors.purple,  // Use purple for consistency
            resultCount: widget.filteredMilkRecords.length,
            showResultCount: true,
            height: 44,
          ),

          // Records list
          Expanded(
            child: _buildRecordsList(),
          ),
        ],
      ),
    );
  }

  bool _hasActiveFilters() {
    // Check if any filters are applied (following weight module pattern exactly)
    bool hasFilters = widget.selectedAnimalType != null ||
                     widget.selectedBreed != null ||
                     widget.selectedGender != null ||
                     widget.selectedCattleId != null ||
                     widget.selectedSession != null;

    // Check if sort is applied (following weight module pattern exactly)
    bool hasSort = widget.sortBy != null;

    // Check if search is applied
    bool hasSearch = widget.searchQuery.isNotEmpty;

    // Check if date range is active (only when both dates are set)
    bool hasDateFilter = widget.startDate != null && widget.endDate != null;

    return hasFilters || hasSort || hasSearch || hasDateFilter;
  }

  List<String> _getActiveFiltersList() {
    final List<String> activeFilters = [];

    // Check core common filters
    if (widget.selectedAnimalType != null && widget.selectedAnimalType != 'All') {
      activeFilters.add('Animal Type: ${widget.selectedAnimalType}');
    }
    if (widget.selectedBreed != null && widget.selectedBreed != 'All') {
      activeFilters.add('Breed: ${widget.selectedBreed}');
    }
    if (widget.selectedGender != null && widget.selectedGender != 'All') {
      activeFilters.add('Gender: ${widget.selectedGender}');
    }
    if (widget.selectedCattleId != null && widget.selectedCattleId != 'All') {
      final cattle = widget.cattleMap[widget.selectedCattleId];
      final cattleName = cattle?.name ?? 'Unknown';
      activeFilters.add('Cattle: $cattleName');
    }

    // Check module-specific filters
    if (widget.selectedSession != null && widget.selectedSession != 'All') {
      activeFilters.add('Session: ${widget.selectedSession}');
    }

    // Check date range (only show if dates are actually set)
    if (widget.startDate != null && widget.endDate != null) {
      final dateFormat = DateFormat('MMM dd');
      activeFilters.add('${dateFormat.format(widget.startDate!)} - ${dateFormat.format(widget.endDate!)}');
    }

    // Check sort
    if (widget.sortBy != null) {
      final direction = widget.sortAscending ? 'Ascending' : 'Descending';
      activeFilters.add('${widget.sortBy} ($direction)');
    }

    // Check search
    if (widget.searchQuery.isNotEmpty) {
      activeFilters.add('Search: "${widget.searchQuery}"');
    }

    return activeFilters;
  }

  Widget _buildRecordsList() {
    if (widget.filteredMilkRecords.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: _getResponsivePadding()),
      itemCount: widget.filteredMilkRecords.length,
      itemBuilder: (context, index) {
        final record = widget.filteredMilkRecords[index];
        final cattle = widget.cattleMap[record.cattleBusinessId];
        final isSelected = widget.selectedRecords.contains(record.businessId);

        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: GestureDetector(
            onLongPress: () => widget.onRecordLongPress(record),
            child: MilkRecordCard(
              record: record,
              cattle: cattle,
              isSelected: isSelected,
              isSelectionMode: widget.isSelectionMode,
              onTap: widget.isSelectionMode
                  ? () => widget.onSelectionChanged(record, !isSelected)
                  : () => widget.onRecordTap(record),
              onEdit: () => widget.onEditRecord(record),
              onDelete: () => widget.onDeleteRecord(record),
              compact: false,
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 300),
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 32),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: _recordsColor.withAlpha(76)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: EmptyState.milk(
        hasData: widget.allMilkRecords.isNotEmpty,
        action: widget.allMilkRecords.isEmpty
            ? null // No add action available in this tab
            : EmptyState.createActionButton(
                onPressed: () => widget.onClearFilters(),
                icon: Icons.clear_all,
                label: 'Clear Filters',
                backgroundColor: _recordsColor,
              ),
      ),
    );
  }

  double _getResponsivePadding() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 12.0; // Mobile
    if (screenWidth < 1200) return 16.0; // Tablet
    return 20.0; // Desktop
  }

  // Filter option generators
  List<FilterOption> _generateAnimalTypeOptions() {
    final options = <FilterOption>[
      const FilterOption(value: '', label: 'All Animal Types'),
    ];

    for (final animalType in widget.animalTypeMap.values) {
      if (animalType.businessId != null && animalType.name != null) {
        options.add(FilterOption(
          value: animalType.businessId!,
          label: animalType.name!,
        ));
      }
    }

    return options;
  }

  List<FilterOption> _generateBreedOptions() {
    final options = <FilterOption>[
      const FilterOption(value: '', label: 'All Breeds'),
    ];

    // Filter breeds based on selected animal type
    final filteredBreeds = widget.selectedAnimalType == null
        ? widget.breedMap.values
        : widget.breedMap.values.where((breed) =>
            breed.animalTypeId == widget.selectedAnimalType);

    for (final breed in filteredBreeds) {
      if (breed.businessId != null && breed.name != null) {
        options.add(FilterOption(
          value: breed.businessId!,
          label: breed.name!,
        ));
      }
    }

    return options;
  }

  List<FilterOption> _generateGenderOptions() {
    return const [
      FilterOption(value: '', label: 'All Genders'),
      FilterOption(value: 'Female', label: 'Female'),
      FilterOption(value: 'Male', label: 'Male'),
    ];
  }

  List<FilterOption> _generateCattleOptions() {
    final options = <FilterOption>[
      const FilterOption(value: '', label: 'All Cattle'),
    ];

    // Filter cattle based on selected filters
    var filteredCattle = widget.cattleMap.values.where((cattle) {
      bool matches = true;

      if (widget.selectedAnimalType != null) {
        matches = matches && cattle.animalTypeId == widget.selectedAnimalType;
      }

      if (widget.selectedBreed != null) {
        matches = matches && cattle.breedId == widget.selectedBreed;
      }

      if (widget.selectedGender != null) {
        matches = matches && cattle.gender?.toLowerCase() == widget.selectedGender?.toLowerCase();
      }

      return matches;
    });

    for (final cattle in filteredCattle) {
      if (cattle.businessId != null && cattle.name != null) {
        final displayName = cattle.tagId != null && cattle.tagId!.isNotEmpty
            ? '${cattle.name} (${cattle.tagId})'
            : cattle.name!;
        options.add(FilterOption(
          value: cattle.businessId!,
          label: displayName,
        ));
      }
    }

    return options;
  }

  List<FilterOption> _generateSessionOptions() {
    return const [
      FilterOption(value: '', label: 'All Sessions'),
      FilterOption(value: 'Morning Only', label: 'Morning Only'),
      FilterOption(value: 'Evening Only', label: 'Evening Only'),
      FilterOption(value: 'Both Sessions', label: 'Both Sessions'),
    ];
  }
}
