// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'milk_report_data_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetMilkReportDataIsarCollection on Isar {
  IsarCollection<MilkReportDataIsar> get milkReportDataIsars =>
      this.collection();
}

const MilkReportDataIsarSchema = CollectionSchema(
  name: r'MilkReportDataIsar',
  id: -15223730739067972,
  properties: {
    r'averageMilkPerCow': PropertySchema(
      id: 0,
      name: r'averageMilkPerCow',
      type: IsarType.double,
    ),
    r'averageMilkPerDay': PropertySchema(
      id: 1,
      name: r'averageMilkPerDay',
      type: IsarType.double,
    ),
    r'businessId': PropertySchema(
      id: 2,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'createdAt': PropertySchema(
      id: 3,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'endDate': PropertySchema(
      id: 4,
      name: r'endDate',
      type: IsarType.dateTime,
    ),
    r'filterCriteria': PropertySchema(
      id: 5,
      name: r'filterCriteria',
      type: IsarType.string,
    ),
    r'generatedAt': PropertySchema(
      id: 6,
      name: r'generatedAt',
      type: IsarType.dateTime,
    ),
    r'milkDates': PropertySchema(
      id: 7,
      name: r'milkDates',
      type: IsarType.dateTimeList,
    ),
    r'milkTimeColors': PropertySchema(
      id: 8,
      name: r'milkTimeColors',
      type: IsarType.longList,
    ),
    r'milkTimeLabels': PropertySchema(
      id: 9,
      name: r'milkTimeLabels',
      type: IsarType.stringList,
    ),
    r'milkTimeValues': PropertySchema(
      id: 10,
      name: r'milkTimeValues',
      type: IsarType.doubleList,
    ),
    r'milkValues': PropertySchema(
      id: 11,
      name: r'milkValues',
      type: IsarType.doubleList,
    ),
    r'reportType': PropertySchema(
      id: 12,
      name: r'reportType',
      type: IsarType.string,
    ),
    r'startDate': PropertySchema(
      id: 13,
      name: r'startDate',
      type: IsarType.dateTime,
    ),
    r'title': PropertySchema(
      id: 14,
      name: r'title',
      type: IsarType.string,
    ),
    r'topProducerAmount': PropertySchema(
      id: 15,
      name: r'topProducerAmount',
      type: IsarType.double,
    ),
    r'topProducerId': PropertySchema(
      id: 16,
      name: r'topProducerId',
      type: IsarType.string,
    ),
    r'topProducerName': PropertySchema(
      id: 17,
      name: r'topProducerName',
      type: IsarType.string,
    ),
    r'totalMilk': PropertySchema(
      id: 18,
      name: r'totalMilk',
      type: IsarType.double,
    ),
    r'totalRecords': PropertySchema(
      id: 19,
      name: r'totalRecords',
      type: IsarType.long,
    ),
    r'updatedAt': PropertySchema(
      id: 20,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _milkReportDataIsarEstimateSize,
  serialize: _milkReportDataIsarSerialize,
  deserialize: _milkReportDataIsarDeserialize,
  deserializeProp: _milkReportDataIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'reportType': IndexSchema(
      id: 3559997651334899995,
      name: r'reportType',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'reportType',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _milkReportDataIsarGetId,
  getLinks: _milkReportDataIsarGetLinks,
  attach: _milkReportDataIsarAttach,
  version: '3.1.0+1',
);

int _milkReportDataIsarEstimateSize(
  MilkReportDataIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.filterCriteria;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.milkDates;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.milkTimeColors;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final list = object.milkTimeLabels;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.milkTimeValues;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.milkValues;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.reportType;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.title;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.topProducerId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.topProducerName;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _milkReportDataIsarSerialize(
  MilkReportDataIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeDouble(offsets[0], object.averageMilkPerCow);
  writer.writeDouble(offsets[1], object.averageMilkPerDay);
  writer.writeString(offsets[2], object.businessId);
  writer.writeDateTime(offsets[3], object.createdAt);
  writer.writeDateTime(offsets[4], object.endDate);
  writer.writeString(offsets[5], object.filterCriteria);
  writer.writeDateTime(offsets[6], object.generatedAt);
  writer.writeDateTimeList(offsets[7], object.milkDates);
  writer.writeLongList(offsets[8], object.milkTimeColors);
  writer.writeStringList(offsets[9], object.milkTimeLabels);
  writer.writeDoubleList(offsets[10], object.milkTimeValues);
  writer.writeDoubleList(offsets[11], object.milkValues);
  writer.writeString(offsets[12], object.reportType);
  writer.writeDateTime(offsets[13], object.startDate);
  writer.writeString(offsets[14], object.title);
  writer.writeDouble(offsets[15], object.topProducerAmount);
  writer.writeString(offsets[16], object.topProducerId);
  writer.writeString(offsets[17], object.topProducerName);
  writer.writeDouble(offsets[18], object.totalMilk);
  writer.writeLong(offsets[19], object.totalRecords);
  writer.writeDateTime(offsets[20], object.updatedAt);
}

MilkReportDataIsar _milkReportDataIsarDeseri