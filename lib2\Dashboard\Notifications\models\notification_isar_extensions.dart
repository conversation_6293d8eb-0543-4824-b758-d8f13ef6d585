import 'package:isar/isar.dart';
import 'notification_isar.dart';
import 'notification_settings_isar.dart';

/// Temporary extension methods to provide access to notification collections
/// These will be replaced by generated code when Isar generator is run
extension NotificationIsarExtension on Isar {
  IsarCollection<NotificationIsar> get notificationIsarCollection => this.collection();
      
  IsarCollection<NotificationSettingsIsar> get notificationSettingsIsars =>
      this.collection();
}

/// Extension methods for NotificationIsar query builders
extension NotificationIsarQueryBuilderExtension on QueryBuilder {
  QueryBuilder sortByCreatedAtDesc() {
    // This is just a stub for now
    return this;
  }
  
  QueryBuilder filter() {
    // This is just a stub for now
    return this;
  }
  
  QueryBuilder isReadEqualTo(bool value) {
    // This is just a stub for now
    return this;
  }
  
  QueryBuilder typeEqualTo(String value) {
    // This is just a stub for now
    return this;
  }
  
  QueryBuilder businessIdEqualTo(String value) {
    // This is just a stub for now
    return this;
  }
  
  Future<List<NotificationIsar>> findAll() async {
    // This is just a stub for now
    return [];
  }
  
  Future<NotificationIsar?> findFirst() async {
    // This is just a stub for now
    return null;
  }
} 