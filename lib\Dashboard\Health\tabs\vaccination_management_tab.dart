import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/vaccination_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../../widgets/responsive_grid.dart';
import '../../../constants/health_constants.dart';

class VaccinationManagementTab extends StatefulWidget {
  final List<VaccinationRecordIsar> vaccinationRecords;
  final Map<String, CattleIsar> cattleMap;
  final Map<String, AnimalTypeIsar> animalTypeMap;
  final Map<String, BreedCategoryIsar> breedMap;
  final VoidCallback onRefresh;
  final bool isSelectionMode;
  final Set<String> selectedRecords;
  final Function(String, bool) onSelectionChanged;
  final Function(bool) onSelectionModeChanged;

  const VaccinationManagementTab({
    super.key,
    required this.vaccinationRecords,
    required this.cattleMap,
    required this.animalTypeMap,
    required this.breedMap,
    required this.onRefresh,
    required this.isSelectionMode,
    required this.selectedRecords,
    required this.onSelectionChanged,
    required this.onSelectionModeChanged,
  });

  @override
  State<VaccinationManagementTab> createState() => _VaccinationManagementTabState();
}

class _VaccinationManagementTabState extends State<VaccinationManagementTab> {
  String _searchQuery = '';
  String? _selectedVaccineType;
  String? _selectedFrequency;
  String? _selectedCattleId;
  bool _showCoreOnly = false;
  bool _showBoostersOnly = false;
  bool _showUpcomingOnly = false;
  bool _showWithdrawalPeriod = false;

  List<VaccinationRecordIsar> get _filteredRecords {
    return widget.vaccinationRecords.where((record) {
      // Search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final cattle = widget.cattleMap[record.cattleBusinessId];
        final cattleName = cattle?.name?.toLowerCase() ?? '';
        final cattleTag = cattle?.tagId?.toLowerCase() ?? '';
        final vaccineName = record.vaccineName?.toLowerCase() ?? '';
        final manufacturer = record.manufacturer?.toLowerCase() ?? '';

        if (!cattleName.contains(query) &&
            !cattleTag.contains(query) &&
            !vaccineName.contains(query) &&
            !manufacturer.contains(query)) {
          return false;
        }
      }

      // Vaccine type filter
      if (_selectedVaccineType != null && record.vaccineType != _selectedVaccineType) {
        return false;
      }

      // Frequency filter
      if (_selectedFrequency != null && record.frequency != _selectedFrequency) {
        return false;
      }

      // Cattle filter
      if (_selectedCattleId != null && record.cattleBusinessId != _selectedCattleId) {
        return false;
      }

      // Core vaccines only filter
      if (_showCoreOnly && !record.isCoreVaccine) {
        return false;
      }

      // Boosters only filter
      if (_showBoostersOnly && !record.isBooster) {
        return false;
      }

      // Upcoming vaccinations filter
      if (_showUpcomingOnly) {
        final nextDue = record.nextDueDate;
        if (nextDue == null || nextDue.isBefore(DateTime.now())) {
          return false;
        }
      }

      // Withdrawal period filter
      if (_showWithdrawalPeriod) {
        final now = DateTime.now();
        final milkWithdrawal = record.milkWithdrawalEndDate;
        final meatWithdrawal = record.meatWithdrawalEndDate;

        if ((milkWithdrawal == null || milkWithdrawal.isBefore(now)) &&
            (meatWithdrawal == null || meatWithdrawal.isBefore(now))) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _selectedVaccineType = null;
      _selectedFrequency = null;
      _selectedCattleId = null;
      _showCoreOnly = false;
      _showBoostersOnly = false;
      _showUpcomingOnly = false;
      _showWithdrawalPeriod = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final filteredRecords = _filteredRecords;

    return Column(
      children: [
        // Filters Section
        _buildFiltersSection(),

        // Records List
        Expanded(
          child: filteredRecords.isEmpty
              ? _buildEmptyState()
              : RefreshIndicator(
                  onRefresh: () async => widget.onRefresh(),
                  child: ResponsiveGrid(
                    children: filteredRecords.map((record) => _buildVaccinationCard(record)).toList(),
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        children: [
          // Search Bar
          TextField(
            decoration: InputDecoration(
              hintText: 'Search by cattle name, vaccine, or manufacturer...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () => setState(() => _searchQuery = ''),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onChanged: (value) => setState(() => _searchQuery = value),
          ),
          const SizedBox(height: 12),

          // Filter Chips Row
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // Vaccine Type Filter
                _buildFilterChip(
                  'Type',
                  _selectedVaccineType,
                  HealthConstants.vaccineTypes,
                  (value) => setState(() => _selectedVaccineType = value),
                  Colors.green,
                ),
                const SizedBox(width: 8),

                // Frequency Filter
                _buildFilterChip(
                  'Frequency',
                  _selectedFrequency,
                  HealthConstants.vaccinationFrequencies,
                  (value) => setState(() => _selectedFrequency = value),
                  Colors.teal,
                ),
                const SizedBox(width: 8),

                // Core Vaccines Filter
                FilterChip(
                  label: const Text('Core Only'),
                  selected: _showCoreOnly,
                  onSelected: (selected) => setState(() => _showCoreOnly = selected),
                  selectedColor: Colors.blue[100],
                  checkmarkColor: Colors.blue,
                ),
                const SizedBox(width: 8),

                // Boosters Filter
                FilterChip(
                  label: const Text('Boosters'),
                  selected: _showBoostersOnly,
                  onSelected: (selected) => setState(() => _showBoostersOnly = selected),
                  selectedColor: Colors.purple[100],
                  checkmarkColor: Colors.purple,
                ),
                const SizedBox(width: 8),

                // Upcoming Filter
                FilterChip(
                  label: const Text('Upcoming'),
                  selected: _showUpcomingOnly,
                  onSelected: (selected) => setState(() => _showUpcomingOnly = selected),
                  selectedColor: Colors.indigo[100],
                  checkmarkColor: Colors.indigo,
                ),
                const SizedBox(width: 8),

                // Withdrawal Period Filter
                FilterChip(
                  label: const Text('Withdrawal Period'),
                  selected: _showWithdrawalPeriod,
                  onSelected: (selected) => setState(() => _showWithdrawalPeriod = selected),
                  selectedColor: Colors.red[100],
                  checkmarkColor: Colors.red,
                ),
                const SizedBox(width: 8),

                // Clear Filters
                if (_searchQuery.isNotEmpty ||
                    _selectedVaccineType != null ||
                    _selectedFrequency != null ||
                    _showCoreOnly ||
                    _showBoostersOnly ||
                    _showUpcomingOnly ||
                    _showWithdrawalPeriod)
                  ActionChip(
                    label: const Text('Clear All'),
                    onPressed: _clearFilters,
                    backgroundColor: Colors.grey[200],
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    String label,
    String? selectedValue,
    List<String> options,
    Function(String?) onChanged,
    Color color,
  ) {
    return PopupMenuButton<String>(
      itemBuilder: (context) => options.map((option) {
        return PopupMenuItem<String>(
          value: option,
          child: Text(option),
        );
      }).toList(),
      onSelected: onChanged,
      child: Chip(
        label: Text(selectedValue ?? label),
        backgroundColor: selectedValue != null ? color.withValues(alpha: 0.1) : null,
        side: selectedValue != null ? BorderSide(color: color) : null,
        deleteIcon: selectedValue != null ? const Icon(Icons.close, size: 18) : null,
        onDeleted: selectedValue != null ? () => onChanged(null) : null,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.vaccines_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No vaccination records found',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add vaccination records to track immunizations and schedules',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildVaccinationCard(VaccinationRecordIsar record) {
    final cattle = widget.cattleMap[record.cattleBusinessId];
    final isSelected = widget.selectedRecords.contains(record.businessId);
    final now = DateTime.now();

    // Check if vaccination is due soon
    final nextDue = record.nextDueDate;
    final isDueSoon = nextDue != null &&
        nextDue.isAfter(now) &&
        nextDue.isBefore(now.add(const Duration(days: 30)));

    // Check withdrawal periods
    final milkWithdrawal = record.milkWithdrawalEndDate;
    final meatWithdrawal = record.meatWithdrawalEndDate;
    final inMilkWithdrawal = milkWithdrawal != null && milkWithdrawal.isAfter(now);
    final inMeatWithdrawal = meatWithdrawal != null && meatWithdrawal.isAfter(now);

    return Card(
      margin: const EdgeInsets.all(8.0),
      elevation: isSelected ? 8 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? BorderSide(color: Colors.green, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _onRecordTap(record),
        onLongPress: () => _onRecordLongPress(record),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  // Cattle Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          cattle?.name ?? 'Unknown Cattle',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Tag: ${cattle?.tagId ?? 'Unknown'}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Vaccination Date
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        record.vaccinationDate != null
                            ? DateFormat('MMM dd, yyyy').format(record.vaccinationDate!)
                            : 'Unknown Date',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (isDueSoon)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.orange[100],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Due Soon',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.orange[700],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Vaccine Info
              Row(
                children: [
                  Icon(Icons.vaccines, size: 16, color: Colors.green[600]),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      record.vaccineName ?? 'Unknown Vaccine',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Manufacturer and Batch
              if (record.manufacturer != null || record.batchNumber != null) ...[
                Row(
                  children: [
                    if (record.manufacturer != null)
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Manufacturer',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              record.manufacturer!,
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                    if (record.batchNumber != null)
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Batch #',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              record.batchNumber!,
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 8),
              ],

              // Dosage and Route
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Dosage',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          '${record.dosageAmount ?? 0} ${record.dosageUnit ?? ''}',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                  if (record.route != null)
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Route',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            record.route!,
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                ],
              ),

              // Next Due Date
              if (nextDue != null) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isDueSoon ? Colors.orange[50] : Colors.blue[50],
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: isDueSoon ? Colors.orange[200]! : Colors.blue[200]!,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        size: 16,
                        color: isDueSoon ? Colors.orange[600] : Colors.blue[600],
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Next Due: ${DateFormat('MMM dd, yyyy').format(nextDue)}',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: isDueSoon ? Colors.orange[700] : Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Withdrawal Periods (if applicable)
              if (inMilkWithdrawal || inMeatWithdrawal) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: Colors.red[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.warning, size: 16, color: Colors.red[600]),
                          const SizedBox(width: 6),
                          Text(
                            'Withdrawal Period Active',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Colors.red[700],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      if (inMilkWithdrawal)
                        Text(
                          'Milk: Until ${DateFormat('MMM dd, yyyy').format(milkWithdrawal)}',
                          style: TextStyle(fontSize: 11, color: Colors.red[600]),
                        ),
                      if (inMeatWithdrawal)
                        Text(
                          'Meat: Until ${DateFormat('MMM dd, yyyy').format(meatWithdrawal)}',
                          style: TextStyle(fontSize: 11, color: Colors.red[600]),
                        ),
                    ],
                  ),
                ),
              ],

              // Flags Row
              const SizedBox(height: 8),
              Wrap(
                spacing: 6,
                runSpacing: 4,
                children: [
                  if (record.isCoreVaccine)
                    _buildFlag('Core', Colors.blue),
                  if (record.isBooster)
                    _buildFlag('Booster', Colors.purple),
                  if (record.vaccineType != null)
                    _buildFlag(record.vaccineType!, Colors.teal),
                  if (record.frequency != null)
                    _buildFlag(record.frequency!, Colors.indigo),
                  if (record.seriesComplete)
                    _buildFlag('Series Complete', Colors.green),
                ],
              ),

              // Selection Checkbox
              if (widget.isSelectionMode) ...[
                const SizedBox(height: 8),
                Align(
                  alignment: Alignment.centerRight,
                  child: Checkbox(
                    value: isSelected,
                    onChanged: (selected) => widget.onSelectionChanged(
                      record.businessId ?? '',
                      selected ?? false,
                    ),
                    activeColor: Colors.green,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFlag(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _onRecordTap(VaccinationRecordIsar record) {
    if (widget.isSelectionMode) {
      final isSelected = widget.selectedRecords.contains(record.businessId);
      widget.onSelectionChanged(record.businessId ?? '', !isSelected);
    } else {
      // TODO: Navigate to vaccination detail screen or edit dialog
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Vaccination details for ${record.vaccineName}'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _onRecordLongPress(VaccinationRecordIsar record) {
    if (!widget.isSelectionMode) {
      widget.onSelectionModeChanged(true);
      widget.onSelectionChanged(record.businessId ?? '', true);
    }
  }
}
