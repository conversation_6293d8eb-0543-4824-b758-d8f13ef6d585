import 'package:flutter/material.dart';
import '../models/milk_record.dart';
import '../../Cattle/models/cattle.dart'; // Correct path to the existing Cattle model
import 'package:uuid/uuid.dart';

class MilkFormDialog extends StatefulWidget {
  final List<Cattle> cattleList;
  final MilkRecord? milkRecord;

  const MilkFormDialog({Key? key, required this.cattleList, this.milkRecord}) : super(key: key);

  @override
  MilkFormDialogState createState() => MilkFormDialogState();
}

class MilkFormDialogState extends State<MilkFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _morningYieldController = TextEditingController();
  final TextEditingController _eveningYieldController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  
  DateTime _selectedDate = DateTime.now();
  double _totalYield = 0.0;
  Cattle? _selectedCattle;
  String _selectedShift = 'Both';

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Milk Entry',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<Cattle>(
                decoration: const InputDecoration(
                  labelText: 'Select Cattle',
                  border: OutlineInputBorder(),
                ),
                value: _selectedCattle,
                items: widget.cattleList.map((Cattle cattle) {
                  return DropdownMenuItem<Cattle>(
                    value: cattle,
                    child: Text(cattle.name), // Assuming Cattle has a name property
                  );
                }).toList(),
                validator: (value) {
                  if (value == null) {
                    return 'Please select a cattle';
                  }
                  return null;
                },
                onChanged: (Cattle? newValue) {
                  setState(() {
                    _selectedCattle = newValue;
                  });
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _morningYieldController,
                      decoration: const InputDecoration(
                        labelText: 'Morning Yield (L)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Required';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Invalid number';
                        }
                        return null;
                      },
                      onChanged: _updateTotalYield,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _eveningYieldController,
                      decoration: const InputDecoration(
                        labelText: 'Evening Yield (L)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Required';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Invalid number';
                        }
                        return null;
                      },
                      onChanged: _updateTotalYield,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Total Yield:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${_totalYield.toStringAsFixed(2)} L',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('Date: '),
                  TextButton(
                    onPressed: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: _selectedDate,
                        firstDate: DateTime(2000),
                        lastDate: DateTime.now(),
                      );
                      if (picked != null) {
                        setState(() {
                          _selectedDate = picked;
                        });
                      }
                    },
                    child: Text(_selectedDate.toString().split(' ')[0]),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('Shift: '),
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Select Shift',
                      border: OutlineInputBorder(),
                    ),
                    value: _selectedShift,
                    items: const [
                      DropdownMenuItem<String>(
                        value: 'Morning',
                        child: Text('Morning'),
                      ),
                      DropdownMenuItem<String>(
                        value: 'Evening',
                        child: Text('Evening'),
                      ),
                      DropdownMenuItem<String>(
                        value: 'Both',
                        child: Text('Both'),
                      ),
                    ],
                    validator: (value) {
                      if (value == null) {
                        return 'Please select a shift';
                      }
                      return null;
                    },
                    onChanged: (String? newValue) {
                      setState(() {
                        _selectedShift = newValue!;
                      });
                    },
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'Notes',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _saveMilkRecord,
                  child: const Text('Save Record'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _updateTotalYield(String _) {
    setState(() {
      double morning = double.tryParse(_morningYieldController.text) ?? 0;
      double evening = double.tryParse(_eveningYieldController.text) ?? 0;
      _totalYield = morning + evening;
    });
  }

  void _saveMilkRecord() {
    if (_formKey.currentState!.validate()) {
      double quantity = _selectedShift == 'Both' ? _totalYield : double.parse(_selectedShift == 'Morning' ? _morningYieldController.text : _eveningYieldController.text);
      final milkRecord = MilkRecord(
        id: widget.milkRecord?.id ?? const Uuid().v4(),
        cattleId: _selectedCattle!.id, // Assuming Cattle has an id property
        quantity: quantity,
        date: _selectedDate,
        shift: _selectedShift,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        morningQuantity: _selectedShift == 'Morning' ? quantity : 0.0,
        eveningQuantity: _selectedShift == 'Evening' ? quantity : 0.0,
        fatContent: 0.0, // Default value, update when implementing fat content
        totalRevenue: 0.0, // Default value, update when implementing pricing
      );

      Navigator.of(context).pop(milkRecord);
    }
  }

  @override
  void dispose() {
    _morningYieldController.dispose();
    _eveningYieldController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
