           _buildSection(
              title: '9. Changes to Terms',
              content:
                  'We reserve the right to modify these Terms at any time. We will provide notice of significant changes by updating the "Last Updated" date at the top of these Terms. Your continued use of the App after such changes constitutes your acceptance of the new Terms.',
            ),
            _buildSection(
              title: '10. Governing Law',
              content:
                  'These Terms shall be governed by and construed in accordance with the laws of the jurisdiction in which the App developers operate, without regard to its conflict of law provisions.',
            ),
            const SizedBox(height: 24),
            const Text(
              'Contact Us',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'If you have any questions about these Terms, please contact us at:',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 8),
            const Text(
              '<EMAIL>',
              style: TextStyle(
                fontSize: 16,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({required String title, required String content}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(
              fontSize: 16,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            import 'package:google_sign_in/google_sign_in.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:http/http.dart' as http;
import 'package:logging/logging.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      drive.DriveApi.driveFileScope,
    ],
  );
  final _storage = const FlutterSecureStorage();
  final _logger = Logger('AuthService');
  GoogleSignInAccount? _currentUser;
  AuthClient? _authClient;

  factory AuthService() {
    return _instance;
  }

  AuthService._internal();

  GoogleSignInAccount? get currentUser => _currentUser;
  AuthClient? get authClient => _authClient;

  Future<bool> initialize() async {
    try {
      // Try to sign in silently with cached credentials
      _currentUser = await _googleSignIn.signInSilently();
      if (_currentUser != null) {
        await _getAuthClient();
        return true;
      }
      return false;
    } catch (e) {
      _logger.warning('Error initializing auth service: $e');
      return false;
    }
  }

  Future<bool> signIn() async {
    try {
      _currentUser = await _googleSignIn.signIn();
      if (_currentUser != null) {
        await _getAuthClient();
        return true;
      }
      return false;
    } catch (e) {
      _logger.warning('Error signing in: $e');
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await _storage.delete(key: 'auth_token');
      _currentUser = null;
      _authClient?.close();
      _authClient = null;
    } catch (e) {
      _logger.warning('Error during sign out: $e');
      // Ensure resources are cleaned up even if there's an error
      _currentUser = null;
      _authClient?.close();
      _authClient = null;
    }
  }

  Future<void> _getAuthClient() async {
    try {
      final auth = await _currentUser!.authentication;
      final accessToken = auth.accessToken;

      if (accessToken != null) {
        // Store tokens securely
        await _storage.write(key: 'accessToken', value: accessToken);

        // Create auth client
        final client = http.Client();
        _authClient = authenticatedClient(
          client,
          AccessCredentials(
            AccessToken(
              'Bearer',
              accessToken,
              DateTime.now().add(const Duration(hours: 1)),
            ),
            null,
            [drive.DriveApi.driveFileScope],
          ),
        );
      }
    } catch (e) {
      _logger.severe('Error getting auth client: $e');
      rethrow;
    }
  }

  bool isSignedIn() {
    return _currentUser != null && _authClient != null;
  }
}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              import 'dart:convert';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class GoogleDriveService {
  static const _clientId =
      '1234567890-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com';
  static const _clientSecret = 'GOCSPX-abcdefghijklmnopqrstuvwxyz1234';
  static const _scopes = [drive.DriveApi.driveFileScope];

  late drive.DriveApi _driveApi;
  late AuthClient _client;

  static final GoogleDriveService _instance = GoogleDriveService._internal();

  factory GoogleDriveService() {
    return _instance;
  }

  GoogleDriveService._internal();

  Future<bool> initialize() async {
    try {
      final clientId = ClientId(_clientId, _clientSecret);
      _client =
          await clientViaUserConsent(clientId, _scopes, _promptForConsent);
      _driveApi = drive.DriveApi(_client);
      return true;
    } catch (e) {
      debugPrint('Error initializing Google Drive service: $e');
      return false;
    }
  }

  void _promptForConsent(String url) {
    debugPrint('Please go to the following URL and grant access:');
    debugPrint(url);
    debugPrint('\nAfter granting access, return here and press enter.');
  }

  Future<String?> uploadBackup(String farmId, Map<String, dynamic> data) async {
    try {
      final timestamp = DateTime.now().toIso8601String();
      final fileName = 'cattle_manager_backup_${farmId}_$timestamp.json';
      final content = jsonEncode(data);

      final file = drive.File()
        ..name = fileName
        ..mimeType = 'application/json';

      final media = drive.Media(
        Stream.fromIterable([utf8.encode(content)]),
        content.length,
        contentType: 'application/json',
      );

      final result = await _driveApi.files.create(file, uploadMedia: media);
      return result.id;
    } catch (e) {
      debugPrint('Error uploading backup to Google Drive: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> downloadBackup(String fileId) async {
    try {
      final content = await _driveApi.files.get(fileId,
          downloadOptions: drive.DownloadOptions.fullMedia) as drive.Media;

      final bytes = await content.stream.toList();
      final jsonString = utf8.decode(bytes.expand((x) => x).toList());
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error downloading backup from Google Drive: $e');
      return null;
    }
  }

  Future<List<drive.File>> listBackups() async {
    try {
      final result = await _driveApi.files.list(
        q: "name contains 'cattle_manager_backup' and mimeType = 'application/json'",
        orderBy: 'createdTime desc',
      );

      return result.files ?? [];
    } catch (e) {
      debugPrint('Error listing backups from Google Drive: $e');
      return [];
    }
  }

  Future<bool> deleteBackup(String fileId) async {
    try {
      await _driveApi.files.delete(fileId);
      return true;
    } catch (e) {
      debugPrint('Error deleting backup from Google Drive: $e');
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      _client.close();
      _driveApi = drive.DriveApi(http.Client());
      _client = http.Client() as AuthClient;
    } catch (e) {
      debugPrint('Error signing out from Google Drive: $e');
    }
  }

  void dispose() {
    _client.close();
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppSettings {
  bool darkMode;
  bool notifications;
  String language;
  int dataRefreshInterval;
  
  AppSettings({
    this.darkMode = false,
    this.notifications = true,
    this.language = 'English',
    this.dataRefreshInterval = 15,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'darkMode': darkMode,
      'notifications': notifications,
      'language': language,
      'dataRefreshInterval': dataRefreshInterval,
    };
  }
  
  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      darkMode: json['darkMode'] ?? false,
      notifications: json['notifications'] ?? true,
      language: json['language'] ?? 'English',
      dataRefreshInterval: json['dataRefreshInterval'] ?? 15,
    );
  }
}

class SettingsService extends ChangeNotifier {
  static final SettingsService _instance = SettingsService._internal();
  
  factory SettingsService() => _instance;
  
  SettingsService._internal();
  
  late SharedPreferences _prefs;
  late AppSettings _settings;
  bool _initialized = false;
  
  // Getters
  bool get darkMode => _settings.darkMode;
  bool get notifications => _settings.notifications;
  String get language => _settings.language;
  int get dataRefreshInterval => _settings.dataRefreshInterval;
  bool get isInitialized => _initialized;
  
  // Initialize settings
  Future<void> init() async {
    if (_initialized) return;
    
    _prefs = await SharedPreferences.getInstance();
    _loadSettings();
    _initialized = true;
  }
  
  // Load settings from SharedPreferences
  void _loadSettings() {
    final darkMode = _prefs.getBool('darkMode') ?? false;
    final notifications = _prefs.getBool('notifications') ?? true;
    final language = _prefs.getString('language') ?? 'English';
    final dataRefreshInterval = _prefs.getInt('