# Cattle Manager App Reusable Widgets

This directory contains reusable widgets that are used across the Cattle Manager application, particularly in the breeding, pregnancy, and delivery views.

## Available Widgets

### Basic Components

- **InfoRow** (`info_row.dart`): A row with an icon, label, and value. Can be styled as a status, highlighted, or multiline.
- **StatItem** (`stat_item.dart`): A widget for displaying a statistic with an icon, label, and value.
- **CardHeader** (`card_header.dart`): A reusable header for cards with an icon, title, and optional tap action.

### Card Components

- **StatusCard** (`status_card.dart`): A card showing the current status of a cattle (pregnant/not pregnant, due date, etc.)
- **StatsCard** (`stats_card.dart`): A card showing multiple statistics with an optional success rate progress bar.
- **HistoryCard** (`history_card.dart`): A generic card for displaying a list of records.
  - **BreedingHistoryCard**: A specialized history card for breeding records.
  - **DeliveryHistoryCard**: A specialized history card for delivery records.

## Usage Examples

### Using InfoRow

```dart
InfoRow(
  icon: Icons.calendar_today,
  label: 'Due Date',
  value: DateFormat('MMM dd, yyyy').format(dueDate),
  color: Colors.purple,
  isHighlighted: true,
)
```

### Using StatusCard

```dart
StatusCard(
  isPregnant: cattle.isPregnant!,
  dueDate: cattle.expectedCalvingDate,
  startDate: cattle.lastBreedingDate,
)
```

### Using StatsCard

```dart
final statItems = [
  {
    'label': 'Total Calves',
    'value': totalCalves.toString(),
    'icon': Icons.child_care,
    'color': Colors.blue,
  },
  {
    'label': 'Male',
    'value': maleCalves.toString(),
    'icon': Icons.male,
    'color': Colors.indigo,
  },
  // Add more items as needed
];

StatsCard(
  title: 'Calf Statistics',
  statItems: statItems,
  successRate: successfulDeliveries / totalDeliveries,
  successRateLabel: 'Success Rate',
)
```

### Using HistoryCard

```dart
DeliveryHistoryCard(
  records: _deliveryRecords,
  title: 'Delivery History',
)
```

## Benefits of Using These Widgets

- **Consistency**: Ensures a consistent look and feel across the app.
- **Maintainability**: Makes it easier to update the UI in one place.
- **Reusability**: Reduces code duplication.
- **Flexibility**: Most widgets are customizable via parameters. 