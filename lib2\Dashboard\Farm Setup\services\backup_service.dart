import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:get_it/get_it.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/logging_service.dart';
import '../models/backup_settings_isar.dart';
import 'farm_setup_handler.dart';
import 'cloud_backup_service.dart';

/// Enhanced backup service with comprehensive backup and restore functionality
class BackupService {
  static final LoggingService _logger = LoggingService.instance;
  final IsarService _isarService;
  final FarmSetupHandler _farmSetupHandler;
  final CloudBackupService _cloudBackupService;

  // Singleton instance
  static final BackupService _instance = BackupService._internal();
  static BackupService get instance => _instance;

  // Private constructor
  BackupService._internal()
      : _isarService = GetIt.instance<IsarService>(),
        _farmSetupHandler = FarmSetupHandler.instance,
        _cloudBackupService = CloudBackupService.instance;

  /// Create a backup with enhanced features
  Future<BackupResult> createBackup({
    String? customPath,
    bool includeMetadata = true,
    Function(double)? onProgress,
  }) async {
    try {
      _logger.logInfo('Starting enhanced backup creation');
      
      // Get backup settings
      final settings = await _farmSetupHandler.getBackupSettings();
      
      // Determine backup path
      final backupPath = customPath ?? await _generateBackupPath(settings.backupLocation);
      
      // Create backup metadata
      final metadata = await _createBackupMetadata();
      
      onProgress?.call(0.1);
      
      // Create the actual database backup
      final success = await _farmSetupHandler.backupDatabase(
        backupPath,
        onProgress: (progress) => onProgress?.call(0.1 + progress * 0.7),
      );
      
      if (!success) {
        throw Exception('Database backup failed');
      }
      
      onProgress?.call(0.8);

      // Save metadata file if requested
      if (includeMetadata) {
        await _saveBackupMetadata(backupPath, metadata);
      }

      onProgress?.call(0.9);

      // Update backup settings - with retry mechanism for database reinitialization
      settings.lastBackupDate = DateTime.now();
      await _saveBackupSettingsWithRetry(settings);

      // Cleanup old backups if needed
      await _cleanupOldBackups(settings);

      // Upload to cloud if enabled
      CloudBackupResult? cloudResult;
      if (settings.cloudBackupEnabled && settings.syncToCloudEnabled) {
        try {
          _logger.logInfo('Uploading backup to cloud storage');
          cloudResult = await _cloudBackupService.uploadBackup(
            backupPath,
            onProgress: (progress) => onProgress?.call(0.9 + progress * 0.1),
          );

          if (cloudResult.success) {
            settings.lastCloudBackupDate = DateTime.now();
            await _saveBackupSettingsWithRetry(settings);
            _logger.logInfo('Backup successfully uploaded to cloud');
          } else {
            _logger.logWarning('Cloud backup failed: ${cloudResult.message}');
          }
        } catch (e) {
          _logger.logError('Error uploading backup to cloud: $e');
        }
      }

      onProgress?.call(1.0);

      final result = BackupResult(
        success: true,
        backupPath: backupPath,
        metadata: metadata,
        message: cloudResult?.success == true
            ? 'Backup created and uploaded to cloud successfully'
            : 'Backup created successfully',
        cloudBackupResult: cloudResult,
      );

      _logger.logInfo('Backup created successfully: $backupPath');
      return result;
      
    } catch (e) {
      _logger.logError('Error creating backup: $e');
      return BackupResult(
        success: false,
        message: 'Failed to create backup: $e',
      );
    }
  }

  /// Restore from backup with enhanced validation
  Future<RestoreResult> restoreFromBackup(
    String backupPath, {
    Function(double)? onProgress,
    bool validateBeforeRestore = true,
  }) async {
    try {
      _logger.logInfo('Starting enhanced restore from: $backupPath');
      
      onProgress?.call(0.05);
      
      // Validate backup file if requested
      if (validateBeforeRestore) {
        final isValid = await _farmSetupHandler.validateBackupFile(backupPath);
        if (!isValid) {
          throw Exception('Invalid or corrupted backup file');
        }
      }
      
      onProgress?.call(0.1);
      
      // Load backup metadata if available
      final metadata = await _loadBackupMetadata(backupPath);
      
      onProgress?.call(0.15);
      
      // Perform the restore
      final success = await _farmSetupHandler.restoreDatabase(
        backupPath,
        onProgress: (progress) => onProgress?.call(0.15 + progress * 0.8),
      );
      
      if (!success) {
        throw Exception('Database restore failed');
      }
      
      onProgress?.call(0.95);
      
      // Verify restore success
      final verificationResult = await _verifyRestoreSuccess();
      if (!verificationResult) {
        throw Exception('Restore verification failed');
      }
      
      onProgress?.call(1.0);
      
      final result = RestoreResult(
        success: true,
        backupPath: backupPath,
        metadata: metadata,
        message: 'Restore completed successfully',
      );
      
      _logger.logInfo('Restore completed successfully from: $backupPath');
      return result;
      
    } catch (e) {
      _logger.logError('Error restoring backup: $e');
      return RestoreResult(
        success: false,
        message: 'Failed to restore backup: $e',
      );
    }
  }

  /// Generate backup file path with timestamp
  Future<String> _generateBackupPath(String baseLocation) async {
    if (baseLocation.isEmpty) {
      final directory = await getApplicationDocumentsDirectory();
      baseLocation = '${directory.path}/CattleManager/Backups';
    }
    
    // Ensure directory exists
    final dir = Directory(baseLocation);
    if (!await dir.exists()) {
      await dir.create(recursive: true);
    }
    
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '$baseLocation/cattle_manager_$timestamp.isar';
  }

  /// Create backup metadata
  Future<Map<String, dynamic>> _createBackupMetadata() async {
    final farmId = await _farmSetupHandler.getActiveFarmId();
    final farm = await _farmSetupHandler.getActiveFarm();
    
    return {
      'version': '1.0',
      'createdAt': DateTime.now().toIso8601String(),
      'farmId': farmId,
      'farmName': farm?.name ?? 'Unknown',
      'appVersion': '1.0.0', // You might want to get this from package info
      'platform': Platform.operatingSystem,
      'databaseVersion': 'isar',
    };
  }

  /// Save backup metadata to a companion file
  Future<void> _saveBackupMetadata(String backupPath, Map<String, dynamic> metadata) async {
    try {
      final metadataPath = '$backupPath.metadata.json';
      final metadataFile = File(metadataPath);
      await metadataFile.writeAsString(jsonEncode(metadata));
      _logger.logInfo('Backup metadata saved: $metadataPath');
    } catch (e) {
      _logger.logWarning('Failed to save backup metadata: $e');
    }
  }

  /// Load backup metadata from companion file
  Future<Map<String, dynamic>?> _loadBackupMetadata(String backupPath) async {
    try {
      final metadataPath = '$backupPath.metadata.json';
      final metadataFile = File(metadataPath);
      
      if (await metadataFile.exists()) {
        final content = await metadataFile.readAsString();
        return jsonDecode(content) as Map<String, dynamic>;
      }
    } catch (e) {
      _logger.logWarning('Failed to load backup metadata: $e');
    }
    return null;
  }

  /// Cleanup old backups based on settings
  Future<void> _cleanupOldBackups(BackupSettingsIsar settings) async {
    try {
      if (settings.backupLocation.isNotEmpty) {
        await _farmSetupHandler.cleanupOldBackups(settings.backupLocation);
      }
    } catch (e) {
      _logger.logWarning('Failed to cleanup old backups: $e');
    }
  }

  /// Verify that restore was successful
  Future<bool> _verifyRestoreSuccess() async {
    try {
      // Try to perform a simple query to verify database integrity
      final farmCount = await _isarService.farmIsars.count();
      _logger.logInfo('Restore verification: Found $farmCount farms');
      return true;
    } catch (e) {
      _logger.logError('Restore verification failed: $e');
      return false;
    }
  }

  /// Get list of available backups
  Future<List<Map<String, dynamic>>> getAvailableBackups(String? backupLocation) async {
    if (backupLocation == null || backupLocation.isEmpty) {
      return [];
    }

    return await _farmSetupHandler.listBackupFiles(backupLocation);
  }

  /// Get list of cloud backups
  Future<List<CloudBackupFile>> getCloudBackups() async {
    try {
      return await _cloudBackupService.listCloudBackups();
    } catch (e) {
      _logger.logError('Error getting cloud backups: $e');
      return [];
    }
  }

  /// Download backup from cloud
  Future<RestoreResult> downloadAndRestoreFromCloud(
    String cloudFileId,
    String fileName, {
    Function(double)? onProgress,
  }) async {
    try {
      _logger.logInfo('Starting download and restore from cloud: $cloudFileId');

      // Create temporary local path for download
      final directory = await getApplicationDocumentsDirectory();
      final tempPath = '${directory.path}/temp_restore_$fileName';

      // Download from cloud
      final downloadResult = await _cloudBackupService.downloadBackup(
        cloudFileId,
        tempPath,
        onProgress: (progress) => onProgress?.call(progress * 0.5),
      );

      if (!downloadResult.success) {
        return RestoreResult(
          success: false,
          message: 'Failed to download backup: ${downloadResult.message}',
        );
      }

      // Restore from downloaded file
      final restoreResult = await restoreFromBackup(
        tempPath,
        onProgress: (progress) => onProgress?.call(0.5 + progress * 0.5),
      );

      // Clean up temporary file
      try {
        final tempFile = File(tempPath);
        if (await tempFile.exists()) {
          await tempFile.delete();
        }
      } catch (e) {
        _logger.logWarning('Failed to clean up temporary file: $e');
      }

      return restoreResult;

    } catch (e) {
      _logger.logError('Error downloading and restoring from cloud: $e');
      return RestoreResult(
        success: false,
        message: 'Failed to download and restore: $e',
      );
    }
  }

  /// Delete cloud backup
  Future<CloudBackupResult> deleteCloudBackup(String cloudFileId) async {
    return await _cloudBackupService.deleteCloudBackup(cloudFileId);
  }

  /// Sign in to cloud storage
  Future<CloudBackupResult> signInToCloud() async {
    return await _cloudBackupService.signIn();
  }

  /// Sign out from cloud storage
  Future<void> signOutFromCloud() async {
    await _cloudBackupService.signOut();
  }

  /// Check if signed in to cloud storage
  Future<bool> isSignedInToCloud() async {
    return await _cloudBackupService.isSignedIn();
  }

  /// Get current cloud user email
  Future<String?> getCloudUserEmail() async {
    return await _cloudBackupService.getCurrentUserEmail();
  }

  /// Save backup settings with retry mechanism to handle database reinitialization
  Future<void> _saveBackupSettingsWithRetry(BackupSettingsIsar settings, {int maxRetries = 3}) async {
    int attempts = 0;

    while (attempts < maxRetries) {
      try {
        // Wait a bit for database to be fully initialized after backup
        if (attempts > 0) {
          await Future.delayed(Duration(milliseconds: 500 * attempts));
        }

        // Check if database is initialized before attempting to save
        if (!_isarService.isInitialized) {
          _logger.logWarning('Database not initialized, waiting for initialization...');
          await Future.delayed(const Duration(milliseconds: 1000));

          // If still not initialized, try to initialize it
          if (!_isarService.isInitialized) {
            _logger.logInfo('Attempting to reinitialize database...');
            await _isarService.initialize();
          }
        }

        // Attempt to save backup settings
        await _farmSetupHandler.saveBackupSettings(settings);
        _logger.logInfo('Backup settings saved successfully after ${attempts + 1} attempt(s)');
        return; // Success, exit the retry loop

      } catch (e) {
        attempts++;
        _logger.logWarning('Attempt $attempts to save backup settings failed: $e');

        if (attempts >= maxRetries) {
          _logger.logError('Failed to save backup settings after $maxRetries attempts: $e');
          // Don't throw the error - backup was successful, just settings save failed
          // This prevents the entire backup from being marked as failed
          _logger.logWarning('Backup completed successfully but failed to update settings. This may cause the next backup to run sooner than expected.');
          return;
        }
      }
    }
  }
}

/// Result class for backup operations
class BackupResult {
  final bool success;
  final String? backupPath;
  final Map<String, dynamic>? metadata;
  final String message;
  final CloudBackupResult? cloudBackupResult;

  BackupResult({
    required this.success,
    this.backupPath,
    this.metadata,
    required this.message,
    this.cloudBackupResult,
  });
}

/// Result class for restore operations
class RestoreResult {
  final bool success;
  final String? backupPath;
  final Map<String, dynamic>? metadata;
  final String message;

  RestoreResult({
    required this.success,
    this.backupPath,
    this.metadata,
    required this.message,
  });
}
