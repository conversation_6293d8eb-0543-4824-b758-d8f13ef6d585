import 'package:get_it/get_it.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:isar/isar.dart';

import '../services/firebase_service.dart';
// Removed multi-farm services - single farm per user
import '../services/logging_service.dart';
import '../services/database/isar_service.dart';
import '../Dashboard/Farm Setup/services/farm_setup_handler.dart';
import '../Dashboard/Cattle/services/cattle_handler.dart';
import '../Dashboard/Farm Setup/models/farm_isar.dart';
import '../Dashboard/Cattle/models/cattle_isar.dart';

/// Service for synchronizing data between local database and Firestore
class CloudDataSyncService {
  static final LoggingService _logger = LoggingService();
  static final CloudDataSyncService _instance = CloudDataSyncService._internal();
  static CloudDataSyncService get instance => _instance;

  CloudDataSyncService._internal();

  bool _isSyncing = false;
  String? _currentUserId;

  /// Initialize the sync service for a user
  void initialize(String userId) {
    _currentUserId = userId;
    _logger.info('CloudDataSyncService initialized for user: $userId');
  }

  /// Check if user has existing data in the cloud and restore it to unified database
  Future<bool> restoreUserDataFromCloud(String userId) async {
    if (_isSyncing) {
      _logger.warning('Sync already in progress, skipping restore');
      return false;
    }

    try {
      _isSyncing = true;
      _logger.info('🔄 Starting multi-farm data restoration from cloud for user: $userId');

      // Check if user has existing data in Firestore
      final hasExistingData = await FirebaseService.hasExistingUserData(userId);
      if (!hasExistingData) {
        _logger.info('ℹ️ No existing cloud data found for user: $userId');
        return false;
      }

      _logger.info('✅ Found existing cloud data, starting unified restoration...');

      // Get services
      final farmSetupHandler = FarmSetupHandler.instance;
      // Multi-farm services removed - single farm per user

      // Clear existing local data to prevent conflicts
      await _clearLocalUserData(userId);

      // Restore farms with ownership information
      final cloudFarms = await FirebaseService.getUserFarms(userId);
      _logger.info('📋 Found ${cloudFarms.length} farms in cloud for user: $userId');

      final restoredFarms = <FarmIsar>[];
      for (final farmData in cloudFarms) {
        try {
          final farm = FarmIsar.fromMap(farmData);

          // Single farm per user - no ownership tracking needed

          await farmSetupHandler.saveFarm(farm);
          restoredFarms.add(farm);
          _logger.info('✅ Restored farm: ${farm.name}');
        } catch (e) {
          _logger.error('❌ Error restoring farm: $e');
        }
      }

      // Restore cattle for all farms
      final cloudCattle = await FirebaseService.getUserCattle(userId);
      _logger.i('🐄 Found ${cloudCattle.length} cattle records in cloud');

      final cattleHandler = CattleHandler.instance;

      // Debug: Check local cattle before sync
      final localCattleBefore = await cattleHandler.getAllCattle();
      _logger.i('🔍 DEBUG: Local cattle count BEFORE sync: ${localCattleBefore.length}');
      for (final cattle in localCattleBefore) {
        _logger.i('🔍 DEBUG: Local cattle BEFORE: ${cattle.name} (${cattle.tagId}) - Farm: ${cattle.farmBusinessId} - ID: ${cattle.businessId}');
      }

      for (final cattleData in cloudCattle) {
        try {
          final cattle = CattleIsar.fromMap(cattleData);
          _logger.i('🔍 DEBUG: Processing cloud cattle: ${cattle.name} (${cattle.tagId}) - Farm: ${cattle.farmBusinessId} - ID: ${cattle.businessId}');

          // Check if cattle already exists to prevent duplicates
          if (cattle.businessId != null && cattle.businessId!.isNotEmpty) {
            final existing = await cattleHandler.getCattleById(cattle.businessId!);
            if (existing != null) {
              _logger.i('⏭️ DEBUG: Skipping existing cattle: ${cattle.name} (${cattle.tagId}) - Already exists with ID: ${existing.businessId}');
              continue;
            }
          }

          _logger.i('💾 DEBUG: Saving cattle from cloud: ${cattle.name} (${cattle.tagId}) - Farm: ${cattle.farmBusinessId}');
          await cattleHandler.saveCattle(cattle);
          _logger.i('✅ Restored cattle: ${cattle.name} (${cattle.tagId}) for farm: ${cattle.farmBusinessId}');
        } catch (e) {
          _logger.e('❌ Error restoring cattle: $e');
        }
      }

      // Debug: Check local cattle after sync
      final localCattleAfter = await cattleHandler.getAllCattle();
      _logger.i('🔍 DEBUG: Local cattle count AFTER sync: ${localCattleAfter.length}');
      for (final cattle in localCattleAfter) {
        _logger.i('🔍 DEBUG: Local cattle AFTER: ${cattle.name} (${cattle.tagId}) - Farm: ${cattle.farmBusinessId} - ID: ${cattle.businessId}');
      }

      // Single farm per user - no farm ownership updates or activation needed

      _logger.i('🎉 Multi-farm data restoration completed successfully');
      _logger.i('📊 Restored ${restoredFarms.length} farms and ${cloudCattle.length} cattle records');

      return true;

    } catch (e) {
      _logger.e('❌ Error during multi-farm data restoration: $e');
      return false;
    } finally {
      _isSyncing = false;
    }
  }

  /// Sync all local user data to cloud (unified multi-farm approach)
  Future<bool> syncLocalDataToCloud(String userId) async {
    if (_isSyncing) {
      _logger.w('Sync already in progress, skipping upload');
      return false;
    }

    try {
      _isSyncing = true;
      _logger.i('🔄 Starting unified multi-farm data sync to cloud for user: $userId');

      // Single farm per user - get farms directly
      final cattleHandler = CattleHandler.instance;

      // Sync all farms for the user (single farm per user)
      final farmSetupHandler = FarmSetupHandler.instance;
      final localFarms = await farmSetupHandler.getAllFarms();
      _logger.i('📋 Syncing ${localFarms.length} user farms to cloud');

      int syncedFarms = 0;
      for (final farm in localFarms) {
        try {
          // Single farm per user - no ownership tracking needed

          final farmData = farm.toMap();
          await FirebaseService.saveFarmToCloud(userId, farmData);
          syncedFarms++;
          _logger.i('✅ Synced farm: ${farm.name}');
        } catch (e) {
          _logger.e('❌ Error syncing farm: ${farm.name} - $e');
        }
      }

      // Sync all cattle across all user farms
      final localCattle = await cattleHandler.getAllCattle();
      _logger.i('🐄 Syncing ${localCattle.length} cattle records across all farms to cloud');

      int syncedCattle = 0;
      for (final cattle in localCattle) {
        try {
          final cattleData = cattle.toMap();
          await FirebaseService.saveCattleToCloud(userId, cattleData);
          syncedCattle++;
          _logger.i('✅ Synced cattle: ${cattle.name} (${cattle.tagId}) from farm: ${cattle.farmBusinessId}');
        } catch (e) {
          _logger.e('❌ Error syncing cattle: ${cattle.name} - $e');
        }
      }

      _logger.i('🎉 Multi-farm data sync to cloud completed successfully');
      _logger.i('📊 Synced $syncedFarms farms and $syncedCattle cattle records');
      return true;

    } catch (e) {
      _logger.e('❌ Error during multi-farm data sync: $e');
      return false;
    } finally {
      _isSyncing = false;
    }
  }

  /// Sync a single farm to cloud with ownership validation
  Future<void> syncFarmToCloud(String userId, FarmIsar farm) async {
    try {
      // Single farm per user - no ownership tracking needed

      final farmData = farm.toMap();
      await FirebaseService.saveFarmToCloud(userId, farmData);
      _logger.i('✅ Farm synced to cloud: ${farm.name}');
    } catch (e) {
      _logger.e('❌ Error syncing farm to cloud: $e');
    }
  }

  /// Sync a single cattle record to cloud
  Future<void> syncCattleToCloud(String userId, CattleIsar cattle) async {
    try {
      _logger.i('🔍 DEBUG: syncCattleToCloud called for: ${cattle.name} (${cattle.tagId})');
      _logger.i('🔍 DEBUG: User: $userId, Farm: ${cattle.farmBusinessId}');

      final cattleData = cattle.toMap();
      _logger.i('🔍 DEBUG: Cattle data prepared for cloud sync');

      await FirebaseService.saveCattleToCloud(userId, cattleData);
      _logger.i('✅ Cattle synced to cloud: ${cattle.name} (${cattle.tagId}) from farm: ${cattle.farmBusinessId}');

      // Check if this triggers a sync back down
      _logger.i('🔍 DEBUG: Cloud sync completed, checking if this triggers restore...');
    } catch (e) {
      _logger.e('❌ Error syncing cattle to cloud: $e');
    }
  }

  /// Sync all farms for a specific user (useful for bulk operations)
  Future<bool> syncAllUserFarmsToCloud(String userId) async {
    try {
      _logger.i('🔄 Syncing all farms for user: $userId');

      final farmSetupHandler = FarmSetupHandler.instance;
      final farms = await farmSetupHandler.getAllFarms();

      int syncedCount = 0;
      for (final farm in farms) {
        try {
          await syncFarmToCloud(userId, farm);
          syncedCount++;
        } catch (e) {
          _logger.e('❌ Error syncing farm ${farm.name}: $e');
        }
      }

      _logger.i('✅ Synced $syncedCount/${farms.length} farms to cloud');
      return syncedCount == farms.length;

    } catch (e) {
      _logger.e('❌ Error syncing all user farms: $e');
      return false;
    }
  }

  /// Restore farms only (useful for partial restoration)
  Future<bool> restoreUserFarmsFromCloud(String userId) async {
    try {
      _logger.i('🔄 Restoring farms from cloud for user: $userId');

      final cloudFarms = await FirebaseService.getUserFarms(userId);
      _logger.i('📋 Found ${cloudFarms.length} farms in cloud');

      final farmSetupHandler = FarmSetupHandler.instance;

      int restoredCount = 0;
      for (final farmData in cloudFarms) {
        try {
          final farm = FarmIsar.fromMap(farmData);

          // Single farm per user - no ownership tracking needed

          await farmSetupHandler.saveFarm(farm);
          restoredCount++;
          _logger.i('✅ Restored farm: ${farm.name}');
        } catch (e) {
          _logger.e('❌ Error restoring farm: $e');
        }
      }

      // Single farm per user - no ownership updates needed

      _logger.i('✅ Restored $restoredCount/${cloudFarms.length} farms from cloud');
      return restoredCount > 0;

    } catch (e) {
      _logger.e('❌ Error restoring farms from cloud: $e');
      return false;
    }
  }

  /// Delete a farm from cloud and local database
  Future<bool> deleteFarmFromCloudAndLocal(String userId, String farmId) async {
    try {
      _logger.i('🗑️ Deleting farm from cloud and local: $farmId');

      // Delete from cloud first
      await FirebaseService.deleteFarmFromCloud(userId, farmId);
      _logger.i('✅ Farm deleted from cloud: $farmId');

      // Delete from local database
      final farmSetupHandler = FarmSetupHandler.instance;
      await farmSetupHandler.deleteFarm(farmId);
      _logger.i('✅ Farm deleted from local database: $farmId');

      return true;
    } catch (e) {
      _logger.e('❌ Error deleting farm from cloud and local: $e');
      return false;
    }
  }

  /// Get sync statistics
  Future<Map<String, dynamic>> getSyncStatistics(String userId) async {
    try {
      final cloudStats = await FirebaseService.getUserFarmStats(userId);
      final farmSetupHandler = FarmSetupHandler.instance;
      final localFarms = await farmSetupHandler.getAllFarms();
      final localStats = {'farmCount': localFarms.length};

      return {
        'cloud': cloudStats,
        'local': localStats,
        'lastSyncCheck': DateTime.now().toIso8601String(),
        'syncInProgress': _isSyncing,
      };
    } catch (e) {
      _logger.e('❌ Error getting sync statistics: $e');
      return {
        'error': e.toString(),
        'syncInProgress': _isSyncing,
      };
    }
  }

  /// Force full sync (both directions)
  Future<bool> forceFullSync(String userId) async {
    if (_isSyncing) {
      _logger.w('Sync already in progress, cannot force full sync');
      return false;
    }

    try {
      _logger.i('🔄 Starting forced full sync for user: $userId');

      // First sync local to cloud
      final uploadSuccess = await syncLocalDataToCloud(userId);
      if (!uploadSuccess) {
        _logger.w('⚠️ Upload to cloud failed during full sync');
      }

      // Then restore from cloud (to get any updates)
      final downloadSuccess = await restoreUserDataFromCloud(userId);
      if (!downloadSuccess) {
        _logger.w('⚠️ Download from cloud failed during full sync');
      }

      final success = uploadSuccess && downloadSuccess;
      _logger.i(success ? '✅ Full sync completed successfully' : '⚠️ Full sync completed with warnings');

      return success;
    } catch (e) {
      _logger.e('❌ Error during forced full sync: $e');
      return false;
    }
  }

  /// Check if sync is in progress
  bool get isSyncing => _isSyncing;

  /// Get current user ID
  String? get currentUserId => _currentUserId;

  /// Clear existing local user data to prevent conflicts during restoration
  Future<void> _clearLocalUserData(String userId) async {
    try {
      _logger.i('🧹 DEBUG: Clearing existing local data for clean restoration...');
      _logger.i('🔍 DEBUG: Clearing data for user: $userId');

      final isarService = GetIt.instance<IsarService>();
      final isar = isarService.isar;

      // Debug: Check what data exists before clearing
      final allFarmsBefore = await isar.farmIsars.where().findAll();
      final allCattleBefore = await isar.cattleIsars.where().findAll();
      _logger.i('🔍 DEBUG: Before clearing - Total Farms: ${allFarmsBefore.length}, Total Cattle: ${allCattleBefore.length}');

      // Single farm per user - get all farms in user's database
      final userFarms = await isar.farmIsars.where().findAll();
      _logger.i('🔍 DEBUG: Found ${userFarms.length} farms for user: $userId');

      // Delete cattle for user's farms
      for (final farm in userFarms) {
        _logger.i('🔍 DEBUG: Clearing cattle for farm: ${farm.name} (${farm.farmBusinessId})');
        final allCattle = await isar.cattleIsars.where().findAll();
        final farmCattle = allCattle.where((cattle) => cattle.farmBusinessId == (farm.farmBusinessId ?? '')).toList();
        _logger.i('🔍 DEBUG: Found ${farmCattle.length} cattle to clear for farm: ${farm.name}');

        if (farmCattle.isNotEmpty) {
          await isar.writeTxn(() async {
            await isar.cattleIsars.deleteAll(farmCattle.map((c) => c.id).toList());
          });
          _logger.i('🗑️ Cleared ${farmCattle.length} cattle records for farm: ${farm.name}');
        }
      }

      // Delete user's farms
      if (userFarms.isNotEmpty) {
        await isar.writeTxn(() async {
          await isar.farmIsars.deleteAll(userFarms.map((f) => f.id).toList());
        });
        _logger.i('🗑️ Cleared ${userFarms.length} farms for user');
      }

      _logger.i('✅ Local data cleared successfully');
    } catch (e) {
      _logger.e('❌ Error clearing local data: $e');
      // Don't throw - continue with restoration even if clearing fails
    }
  }

  /// Restore the last active farm from cloud preferences
  Future<void> _restoreLastActiveFarm(String userId, List<FarmIsar> restoredFarms) async {
    try {
      if (restoredFarms.isEmpty) {
        _logger.i('ℹ️ No farms to activate');
        return;
      }

      // Try to get last active farm from cloud user preferences
      final userDoc = await FirebaseService.getUserDocument(userId).get();
      String? lastActiveFarmId;

      if (userDoc.exists) {
        final userData = userDoc.data();
        lastActiveFarmId = userData?['lastActiveFarmId'] as String?;
      }

      // Single farm per user - no farm activation needed
      _logger.i('✅ Single farm per user - no farm activation needed');
    } catch (e) {
      _logger.e('❌ Error restoring last active farm: $e');
      // Don't throw - farm activation is not critical for data restoration
    }
  }

  /// Save user's last active farm preference to cloud
  Future<void> saveLastActiveFarmToCloud(String userId, String farmId) async {
    try {
      await FirebaseService.getUserDocument(userId).set({
        'lastActiveFarmId': farmId,
        'lastActiveFarmUpdatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      _logger.i('💾 Saved last active farm preference to cloud: $farmId');
    } catch (e) {
      _logger.e('❌ Error saving last active farm to cloud: $e');
    }
  }
}
