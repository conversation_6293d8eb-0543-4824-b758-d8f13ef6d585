import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'report_data.dart';
import '../../Events/models/event.dart';
import 'chart_data.dart';

class EventReportData extends ReportData {
  final List<FarmEvent> events;
  final String? eventType;
  final String? cattleId;
  final dateFormat = DateFormat('yyyy-MM-dd');

  EventReportData({
    required this.events,
    this.eventType,
    this.cattleId,
    super.startDate,
    super.endDate,
  });

  List<FarmEvent> get filteredEvents {
    return events.where((event) {
      final eventDate = event.date;
      if (startDate != null && eventDate.isBefore(startDate!)) return false;
      if (endDate != null && eventDate.isAfter(endDate!)) return false;
      if (eventType != null && event.type.toString() != eventType) return false;
      if (cattleId != null && event.cattleId != cattleId) return false;
      return true;
    }).toList()
      ..sort((a, b) {
        final aDate = a.date;
        final bDate = b.date;
        return aDate.compareTo(bDate);
      });
  }

  @override
  String get reportTitle => 'Events Report';

  @override
  List<DataColumn> get tableColumns => const [
        DataColumn(label: Text('Date')),
        DataColumn(label: Text('Cattle ID')),
        DataColumn(label: Text('Type')),
        DataColumn(label: Text('Description')),
        DataColumn(label: Text('Status')),
      ];

  @override
  List<DataRow> get tableRows => filteredEvents.map((event) {
        return DataRow(
          cells: [
            DataCell(Text(dateFormat.format(event.date))),
            DataCell(Text(event.cattleId ?? '')),
            DataCell(Text(event.type.toString())),
            DataCell(Text(event.description)),
            DataCell(Text(event.isCompleted ? 'Completed' : 'Pending')),
          ],
        );
      }).toList();

  @override
  Map<String, double> get summaryData {
    var healthCount = 0;
    var breedingCount = 0;
    var vaccinationCount = 0;
    var generalCount = 0;

    for (var event in filteredEvents) {
      switch (event.type) {
        case EventType.breeding:
          breedingCount++;
          break;
        case EventType.pregnancyCheck:
          breedingCount++;
          break;
        case EventType.dryOff:
          breedingCount++;
          break;
        case EventType.calving:
          breedingCount++;
          break;
        case EventType.vaccination:
          vaccinationCount++;
          break;
        case EventType.healthCheckup:
          healthCount++;
          break;
        case EventType.weightMeasurement:
          healthCount++;
          break;
        case EventType.deworming:
          healthCount++;
          break;
        default:
          generalCount++;
          break;
      }
    }

    return {
      'totalEvents': filteredEvents.length.toDouble(),
      'healthEvents': healthCount.toDouble(),
      'breedingEvents': breedingCount.toDouble(),
      'vaccinationEvents': vaccinationCount.toDouble(),
      'generalEvents': generalCount.toDouble(),
    };
  }

  @override
  List<ChartData> get chartData {
    final Map<String, int> eventsByType = {};
    for (var event in filteredEvents) {
      eventsByType[event.type.toString()] =
          (eventsByType[event.type.toString()] ?? 0) + 1;
    }

    return eventsByType.entries.map((entry) {
      return ChartData(
        date: DateTime.now(), // Not used for pie chart
        value: entry.value.toDouble(),
        label: entry.key,
      );
    }).toList();
  }

  List<ChartData> get eventTrend {
    final Map<DateTime, int> eventsByDate = {};

    for (var event in filteredEvents) {
      final date = event.date;
      final normalizedDate = DateTime(date.year, date.month, date.day);
      eventsByDate[normalizedDate] = (eventsByDate[normalizedDate] ?? 0) + 1;
    }

    final sortedDates = eventsByDate.keys.toList()
      ..sort((a, b) => a.compareTo(b));

    return sortedDates.map((date) {
      return ChartData(
        label: '${date.month}/${date.day}',
        value: eventsByDate[date]!.toDouble(),
        date: date,
      );
    }).toList();
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              