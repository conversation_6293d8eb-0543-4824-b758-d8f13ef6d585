import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../../Events/models/event.dart';
import '../../../services/database_helper.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class EventTypesScreen extends StatefulWidget {
  const EventTypesScreen({Key? key}) : super(key: key);

  @override
  State<EventTypesScreen> createState() => _EventTypesScreenState();
}

class _EventTypesScreenState extends State<EventTypesScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  List<CustomEventType> _customTypes = [];
  bool _isLoading = true;

  final List<Color> _availableColors = [
    Colors.blue,
    Colors.red,
    Colors.green,
    Colors.orange,
    Colors.purple,
    Colors.teal,
    Colors.pink,
    Colors.indigo,
  ];

  final List<IconData> _availableIcons = [
    Icons.event,
    Icons.star,
    Icons.favorite,
    Icons.flag,
    Icons.local_activity,
    Icons.bookmark,
    Icons.label,
    Icons.category,
  ];

  @override
  void initState() {
    super.initState();
    _loadCustomTypes();
  }

  Future<void> _loadCustomTypes() async {
    try {
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) {
        setState(() {
          _customTypes = [];
          _isLoading = false;
        });
        return;
      }

      final prefs = await SharedPreferences.getInstance();
      final key = '${selectedFarmId}_custom_event_types';
      final typesJson = prefs.getStringList(key) ?? [];
      setState(() {
        _customTypes = typesJson
            .map((json) => CustomEventType.fromMap(jsonDecode(json)))
            .toList();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading custom event types: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveCustomTypes() async {
    try {
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) return;

      final prefs = await SharedPreferences.getInstance();
      final key = '${selectedFarmId}_custom_event_types';
      final typesJson =
          _customTypes.map((type) => jsonEncode(type.toMap())).toList();
      await prefs.setStringList(key, typesJson);
    } catch (e) {
      debugPrint('Error saving custom event types: $e');
    }
  }

  Future<void> _addCustomType(CustomEventType type) async {
    setState(() => _customTypes.add(type));
    await _saveCustomTypes();
  }

  Future<void> _updateCustomType(CustomEventType updatedType) async {
    final index = _customTypes.indexWhere((t) => t.id == updatedType.id);
    if (index != -1) {
      setState(() => _customTypes[index] = updatedType);
      await _saveCustomTypes();
    }
  }

  Future<void> _deleteCustomType(String typeId) async {
    setState(() => _customTypes.removeWhere((t) => t.id == typeId));
    await _saveCustomTypes();
  }

  String _getColorName(Color color) {
    if (color == Colors.blue) return 'Blue';
    if (color == Colors.red) return 'Red';
    if (color == Colors.green) return 'Green';
    if (color == Colors.orange) return 'Orange';
    if (color == Colors.purple) return 'Purple';
    if (color == Colors.teal) return 'Teal';
    if (color == Colors.pink) return 'Pink';
    if (color == Colors.indigo) return 'Indigo';
    return 'Unknown';
  }

  String _getIconName(IconData icon) {
    final name = icon.toString().split('IconData(U+')[1].split(')')[0];
    switch (name) {
      case 'E878':
        return 'Event';
      case 'E838':
        return 'Star';
      case 'E87D':
        return 'Heart';
      case 'E153':
        return 'Flag';
      case 'E922':
        return 'Activity';
      case 'E866':
        return 'Bookmark';
      case 'E892':
        return 'Label';
      case 'E5C3':
        return 'Category';
      default:
        return 'Unknown';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Event Types'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCustomTypeDialog(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Standard Event Types',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
            _buildEventTypeCard(
              context,
              icon: Icons.favorite,
              title: 'Breeding',
              description: 'Record breeding activities and insemination',
              color: Colors.pink,
              type: EventType.breeding,
            ),
            _buildEventTypeCard(
              context,
              icon: Icons.pregnant_woman,
              title: 'Pregnancy Check',
              description: 'Schedule and record pregnancy check results',
              color: Colors.purple,
              type: EventType.pregnancyCheck,
            ),
            _buildEventTypeCard(
              context,
              icon: Icons.water_drop,
              title: 'Dry Off',
              description: 'Manage dry off periods for cattle',
              color: Colors.blue,
              type: EventType.dryOff,
            ),
            _buildEventTypeCard(
              context,
              icon: Icons.child_care,
              title: 'Calving',
              description: 'Record calving events and newborn details',
              color: Colors.orange,
              type: EventType.calving,
            ),
            _buildEventTypeCard(
              context,
              icon: Icons.medication,
              title: 'Vaccination',
              description: 'Schedule and track vaccinations',
              color: Colors.green,
              type: EventType.vaccination,
            ),
            _buildEventTypeCard(
              context,
              icon: Icons.medical_services,
              title: 'Health Checkup',
              description: 'Regular health examinations and treatments',
              color: Colors.red,
              type: EventType.healthCheckup,
            ),
            _buildEventTypeCard(
              context,
              icon: Icons.monitor_weight,
              title: 'Weight Measurement',
              description: 'Track cattle weight and growth',
              color: Colors.brown,
              type: EventType.weightMeasurement,
            ),
            _buildEventTypeCard(
              context,
              icon: Icons.healing,
              title: 'Deworming',
              description: 'Schedule and record deworming treatments',
              color: Colors.teal,
              type: EventType.deworming,
            ),
            _buildEventTypeCard(
              context,
              icon: Icons.add_shopping_cart,
              title: 'Purchased',
              description: 'Record new cattle purchases',
              color: Colors.indigo,
              type: EventType.purchased,
            ),
            _buildEventTypeCard(
              context,
              icon: Icons.shopping_cart,
              title: 'Sold',
              description: 'Record cattle sales',
              color: Colors.deepPurple,
              type: EventType.sold,
            ),
            _buildEventTypeCard(
              context,
              icon: Icons.more_horiz,
              title: 'Miscellaneous',
              description: 'Other general events',
              color: Colors.grey,
              type: EventType.miscellaneous,
            ),
            const SizedBox(height: 32),
            const Text(
              'Custom Event Types',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _customTypes.isEmpty
                    ? Center(
                        child: Column(
                          children: [
                            SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                            Icon(
                              Icons.add_circle_outline,
                              size: 48,
                              color: Theme.of(context)
                                  .primaryColor
                                  .withAlpha((0.5 * 255).round()),
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'No custom event types yet',
                              style: TextStyle(fontSize: 16),
                            ),
                            const SizedBox(height: 8),
                            ElevatedButton.icon(
                              onPressed: () => _showCustomTypeDialog(context),
                              icon: const Icon(Icons.add),
                              label: const Text('Add Custom Type'),
                            ),
                          ],
                        ),
                      )
                    : Column(
                        children: _customTypes.map((customType) {
                          return _buildCustomTypeCard(context, customType);
                        }).toList(),
                      ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCustomTypeDialog(context),
        tooltip: 'Add Custom Event Type',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEventTypeCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
    required Color color,
    required EventType type,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withAlpha((255 * 0.2).round()),
          child: Icon(icon, color: color),
        ),
        title: Text(title),
        subtitle: Text(description),
        trailing: const Icon(Icons.info_outline),
        onTap: () {
          // For standard event types, show a snackbar with more information
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  '$title is a standard event type and cannot be modified'),
              duration: const Duration(seconds: 2),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCustomTypeCard(
      BuildContext context, CustomEventType customType) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: customType.color.withAlpha((255 * 0.2).round()),
          child: Icon(customType.icon, color: customType.color),
        ),
        title: Text(customType.name),
        subtitle: Text(customType.description),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _showCustomTypeDialog(context, customType),
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _confirmDeleteCustomType(context, customType),
            ),
          ],
        ),
        onTap: () => _showCustomTypeDialog(context, customType),
      ),
    );
  }

  Future<void> _showCustomTypeDialog(BuildContext context,
      [CustomEventType? existingType]) async {
    final formKey = GlobalKey<FormState>();
    String name = existingType?.name ?? '';
    String description = existingType?.description ?? '';
    Color selectedColor = existingType?.color ?? _availableColors[0];
    IconData selectedIcon = existingType?.icon ?? _availableIcons[0];

    final result = await showDialog<CustomEventType>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(existingType == null
            ? 'Add Custom Event Type'
            : 'Edit Custom Event Type'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  initialValue: name,
                  decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Name'),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a name';
                    }
                    return null;
                  },
                  onSaved: (value) => name = value!,
                ),
                SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                TextFormField(
                  initialValue: description,
                  decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Description'),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a description';
                    }
                    return null;
                  },
                  onSaved: (value) => description = value!,
                ),
                SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                DropdownButtonFormField<Color>(
                  value: selectedColor,
                  decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Color'),
                  items: _availableColors.map((color) {
                    return DropdownMenuItem<Color>(
                      value: color,
                      child: Row(
                        children: [
                          Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: color,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(_getColorName(color)),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (color) {
                    if (color != null) {
                      selectedColor = color;
                    }
                  },
                ),
                SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                DropdownButtonFormField<IconData>(
                  value: selectedIcon,
                  decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Icon'),
                  items: _availableIcons.map((icon) {
                    return DropdownMenuItem<IconData>(
                      value: icon,
                      child: Row(
                        children: [
                          Icon(icon),
                          const SizedBox(width: 8),
                          Text(_getIconName(icon)),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (icon) {
                    if (icon != null) {
                      selectedIcon = icon;
                    }
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                formKey.currentState!.save();
                final customType = CustomEventType(
                  id: existingType?.id ?? const Uuid().v4(),
                  name: name,
                  description: description,
                  icon: selectedIcon,
                  color: selectedColor,
                );
                Navigator.pop(context, customType);
              }
            },
            child: Text(existingType == null ? 'Add' : 'Save'),
          ),
        ],
      ),
    );

    if (result != null) {
      if (existingType == null) {
        await _addCustomType(result);
      } else {
        await _updateCustomType(result);
      }
    }
  }

  Future<void> _confirmDeleteCustomType(
      BuildContext context, CustomEventType type) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Custom Event Type'),
        content: Text('Are you sure you want to delete "${type.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _deleteCustomType(type.id);
    }
  }
}
