import 'package:hive/hive.dart';

part 'cattle.g.dart';

class BreedingRecord {
  final DateTime date;
  final String type; // 'Natural' or 'Artificial'
  final String? partner;
  final String? notes;

  BreedingRecord({
    required this.date,
    required this.type,
    this.partner,
    this.notes,
  });

  Map<String, dynamic> toJson() => {
        'date': date.toIso8601String(),
        'type': type,
        'partner': partner,
        'notes': notes,
      };

  factory BreedingRecord.fromJson(Map<String, dynamic> json) => BreedingRecord(
        date: DateTime.parse(json['date']),
        type: json['type'],
        partner: json['partner'],
        notes: json['notes'],
      );
}

@HiveType(typeId: 0)
class Cattle extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String tagId;

  @HiveField(2)
  final String name;

  @HiveField(3)
  final String animalTypeId;

  @HiveField(4)
  final String breedId;

  @HiveField(5)
  final String gender;

  @HiveField(6)
  final String source;

  @HiveField(7)
  final String? motherTagId;

  @HiveField(8)
  final DateTime? dateOfBirth;

  @HiveField(9)
  final DateTime? purchaseDate;

  @HiveField(10)
  final double? purchasePrice;

  @HiveField(11)
  final double? weight;

  @HiveField(12)
  final String? color;

  @HiveField(13)
  final String? notes;

  @HiveField(14)
  final String? photoPath;

  @HiveField(15)
  final DateTime createdAt;

  @HiveField(16)
  final DateTime updatedAt;

  @HiveField(17)
  final String? reproductiveStatus;

  @HiveField(18)
  final DateTime? lastHeatDate;

  @HiveField(19)
  final bool? isPregnant;

  @HiveField(20)
  final DateTime? breedingDate;

  @HiveField(21)
  final List<BreedingRecord>? breedingHistory;

  @HiveField(22)
  final List<Cattle>? offspring;

  @HiveField(23)
  final String? category;

  @HiveField(24)
  final String? status;

  Cattle({
    required this.id,
    required this.tagId,
    required this.name,
    required this.animalTypeId,
    required this.breedId,
    required this.gender,
    required this.source,
    this.motherTagId,
    this.dateOfBirth,
    this.purchaseDate,
    this.purchasePrice,
    this.weight,
    this.color,
    this.notes,
    this.photoPath,
    required this.createdAt,
    required this.updatedAt,
    this.reproductiveStatus,
    this.lastHeatDate,
    this.isPregnant,
    this.breedingDate,
    this.breedingHistory,
    this.offspring,
    this.category,
    this.status,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'tagId': tagId,
      'name': name,
      'animalTypeId': animalTypeId,
      'breedId': breedId,
      'gender': gender,
      'source': source,
      'motherTagId': motherTagId,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'purchaseDate': purchaseDate?.toIso8601String(),
      'purchasePrice': purchasePrice,
      'weight': weight,
      'color': color,
      'notes': notes,
      'photoPath': photoPath,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'reproductiveStatus': reproductiveStatus,
      'lastHeatDate': lastHeatDate?.toIso8601String(),
      'isPregnant': isPregnant,
      'breedingDate': breedingDate?.toIso8601String(),
      'breedingHistory': breedingHistory?.map((x) => x.toJson()).toList(),
      'offspring': offspring?.map((x) => x.toMap()).toList(),
      'category': category,
      'status': status,
    };
  }

  factory Cattle.fromMap(Map<String, dynamic> map) {
    return Cattle(
      id: map['id'] ?? '',
      tagId: map['tagId'] ?? '',
      name: map['name'] ?? '',
      animalTypeId: map['animalTypeId'] ?? '',
      breedId: map['breedId'] ?? '',
      gender: map['gender'] ?? '',
      source: map['source'] ?? '',
      motherTagId: map['motherTagId'],
      dateOfBirth: map['dateOfBirth'] != null ? DateTime.parse(map['dateOfBirth']) : null,
      purchaseDate: map['purchaseDate'] != null ? DateTime.parse(map['purchaseDate']) : null,
      purchasePrice: map['purchasePrice'] != null ? double.parse(map['purchasePrice'].toString()) : null,
      weight: map['weight'] != null ? double.parse(map['weight'].toString()) : null,
      color: map['color'],
      notes: map['notes'],
      photoPath: map['photoPath'],
      createdAt: map['createdAt'] != null ? DateTime.parse(map['createdAt']) : DateTime.now(),
      updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : DateTime.now(),
      reproductiveStatus: map['reproductiveStatus'],
      lastHeatDate: map['lastHeatDate'] != null ? DateTime.parse(map['lastHeatDate']) : null,
      isPregnant: map['isPregnant'] ?? false,
      breedingDate: map['breedingDate'] != null ? DateTime.parse(map['breedingDate']) : null,
      breedingHistory: map['breedingHistory'] != null
          ? (map['breedingHistory'] as List)
              .map((record) => BreedingRecord.fromJson(Map<String, dynamic>.from(record)))
              .toList()
          : null,
      offspring: map['offspring'] != null
          ? (map['offspring'] as List)
              .map((child) => Cattle.fromMap(Map<String, dynamic>.from(child)))
              .toList()
          : null,
      category: map['category'] ?? 'Unknown',
      status: map['status'] ?? 'Active',
    );
  }

  Cattle copyWith({
    String? id,
    String? tagId,
    String? name,
    String? animalTypeId,
    String? breedId,
    String? gender,
    String? source,
    String? motherTagId,
    DateTime? dateOfBirth,
    DateTime? purchaseDate,
    double? purchasePrice,
    double? weight,
    String? color,
    String? notes,
    String? photoPath,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? reproductiveStatus,
    DateTime? lastHeatDate,
    bool? isPregnant,
    DateTime? breedingDate,
    List<BreedingRecord>? breedingHistory,
    List<Cattle>? offspring,
    String? category,
    String? status,
  }) {
    return Cattle(
      id: id ?? this.id,
      tagId: tagId ?? this.tagId,
      name: name ?? this.name,
      animalTypeId: animalTypeId ?? this.animalTypeId,
      breedId: breedId ?? this.breedId,
      gender: gender ?? this.gender,
      source: source ?? this.source,
      motherTagId: motherTagId ?? this.motherTagId,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      weight: weight ?? this.weight,
      color: color ?? this.color,
      notes: notes ?? this.notes,
      photoPath: photoPath ?? this.photoPath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      reproductiveStatus: reproductiveStatus ?? this.reproductiveStatus,
      lastHeatDate: lastHeatDate ?? this.lastHeatDate,
      isPregnant: isPregnant ?? this.isPregnant,
      breedingDate: breedingDate ?? this.breedingDate,
      breedingHistory: breedingHistory ?? this.breedingHistory,
      offspring: offspring ?? this.offspring,
      category: category ?? this.category,
      status: status ?? this.status,
    );
  }
}
