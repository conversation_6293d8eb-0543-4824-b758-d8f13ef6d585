import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';
import 'user_role_isar.dart';

part 'farm_user_isar.g.dart';

@collection
class FarmUserIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index(caseSensitive: false)
  String? name;

  @Index(unique: true, caseSensitive: false)
  String? email;

  String? phoneNumber;

  final role = IsarLink<UserRoleIsar>();

  @Index(type: IndexType.value)
  String? roleBusinessId;  // Will be deprecated after migration

  @Index()
  String? farmBusinessId;  // Reference to Farm.farmBusinessId

  bool isActive = true;

  DateTime? createdAt;
  DateTime? updatedAt;

  FarmUserIsar();

  factory FarmUserIsar.create({
    required String name,
    required String email,
    required String phoneNumber,
    required String roleBusinessId,
    required String farmBusinessId,
    bool isActive = true,
  }) {
    return FarmUserIsar()
      ..businessId = const Uuid().v4()
      ..name = name
      ..email = email
      ..phoneNumber = phoneNumber
      ..roleBusinessId = roleBusinessId  // Keep for backward compatibility
      ..farmBusinessId = farmBusinessId
      ..isActive = isActive
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'name': name,
      'email': email,
      'phoneNumber': phoneNumber,
      'roleId': roleBusinessId ?? role.value?.businessId,
      'farmBusinessId': farmBusinessId,
      'isActive': isActive,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory FarmUserIsar.fromMap(Map<String, dynamic> map) {
    return FarmUserIsar()
      ..businessId = map['id'] as String?
      ..name = map['name'] as String?
      ..email = map['email'] as String?
      ..phoneNumber = map['phoneNumber'] as String?
      ..roleBusinessId = map['roleId'] as String?
      ..farmBusinessId = map['farmBusinessId'] as String?
      ..isActive = map['isActive'] as bool? ?? true
      ..createdAt = map['createdAt'] != null ? DateTime.parse(map['createdAt'] as String) : DateTime.now()
      ..updatedAt = map['updatedAt'] != null ? DateTime.parse(map['updatedAt'] as String) : DateTime.now();
  }

  FarmUserIsar copyWith({
    String? businessId,
    String? name,
    String? email,
    String? phoneNumber,
    String? roleBusinessId,
    UserRoleIsar? role,
    String? farmBusinessId,
    bool? isActive,
  }) {
    final user = FarmUserIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..name = name ?? this.name
      ..email = email ?? this.email
      ..phoneNumber = phoneNumber ?? this.phoneNumber
      ..roleBusinessId = roleBusinessId ?? this.roleBusinessId
      ..farmBusinessId = farmBusinessId ?? this.farmBusinessId
      ..isActive = isActive ?? this.isActive
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();
    
    if (role != null) {
      user.role.value = role;
    } else if (this.role.value != null) {
      user.role.value = this.role.value;
    }
    
    return user;
  }
} 