import 'package:flutter/material.dart';

import '../../../constants/health_constants.dart';

class HealthStatusIndicator extends StatefulWidget {
  final String? healthStatus;
  final String? condition;
  final String? severity;
  final bool isEmergency;
  final bool isChronic;
  final bool isUnderTreatment;
  final bool requiresFollowUp;
  final double size;
  final bool showLabel;
  final bool showTooltip;

  const HealthStatusIndicator({
    super.key,
    this.healthStatus,
    this.condition,
    this.severity,
    this.isEmergency = false,
    this.isChronic = false,
    this.isUnderTreatment = false,
    this.requiresFollowUp = false,
    this.size = 24.0,
    this.showLabel = false,
    this.showTooltip = true,
  });

  @override
  State<HealthStatusIndicator> createState() => _HealthStatusIndicatorState();
}

class _HealthStatusIndicatorState extends State<HealthStatusIndicator>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );

    if (widget.isEmergency) {
      _pulseController.repeat();
    }
  }

  @override
  void didUpdateWidget(HealthStatusIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isEmergency != oldWidget.isEmergency) {
      if (widget.isEmergency) {
        _pulseController.repeat();
      } else {
        _pulseController.stop();
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final status = widget.healthStatus ?? 'Unknown';
    final color = HealthConstants.getHealthStatusColor(status);
    final icon = _getStatusIcon();

    Widget indicator = Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: color.withAlpha(77), // 0.3 * 255 = 77
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Icon(
        icon,
        color: Colors.white,
        size: widget.size * 0.6,
      ),
    );

    // Add emergency pulse animation
    if (widget.isEmergency) {
      indicator = _buildPulsingIndicator(indicator, color);
    }

    // Add tooltip
    if (widget.showTooltip) {
      indicator = Tooltip(
        message: _buildTooltipMessage(),
        child: indicator,
      );
    }

    // Add label if requested
    if (widget.showLabel) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          indicator,
          const SizedBox(height: 4),
          Text(
            status,
            style: TextStyle(
              fontSize: widget.size * 0.4,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }

    return indicator;
  }

  Widget _buildPulsingIndicator(Widget child, Color color) {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, _) {
        return Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: color.withAlpha((153 * _pulseController.value).round()), // 0.6 * 255 = 153
                blurRadius: widget.size * 0.5 * _pulseController.value,
                spreadRadius: widget.size * 0.2 * _pulseController.value,
              ),
            ],
          ),
          child: child,
        );
      },
    );
  }

  IconData _getStatusIcon() {
    if (widget.isEmergency) return Icons.warning;

    switch (widget.healthStatus?.toLowerCase()) {
      case 'healthy':
        return Icons.check;
      case 'sick':
        return Icons.sick;
      case 'injured':
        return Icons.healing;
      case 'recovering':
        return Icons.trending_up;
      case 'critical':
        return Icons.priority_high;
      case 'under observation':
        return Icons.visibility;
      default:
        return Icons.help_outline;
    }
  }

  String _buildTooltipMessage() {
    final parts = <String>[];

    if (widget.healthStatus != null) {
      parts.add('Status: ${widget.healthStatus}');
    }

    if (widget.condition != null) {
      parts.add('Condition: ${widget.condition}');
    }

    if (widget.severity != null) {
      parts.add('Severity: ${widget.severity}');
    }

    final flags = <String>[];
    if (widget.isEmergency) flags.add('Emergency');
    if (widget.isChronic) flags.add('Chronic');
    if (widget.isUnderTreatment) flags.add('Under Treatment');
    if (widget.requiresFollowUp) flags.add('Requires Follow-up');

    if (flags.isNotEmpty) {
      parts.add('Flags: ${flags.join(', ')}');
    }

    return parts.join('\n');
  }
}

class HealthStatusBadge extends StatelessWidget {
  final String? healthStatus;
  final String? condition;
  final bool isEmergency;
  final bool isChronic;
  final bool compact;

  const HealthStatusBadge({
    super.key,
    this.healthStatus,
    this.condition,
    this.isEmergency = false,
    this.isChronic = false,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    final status = healthStatus ?? 'Unknown';
    final color = HealthConstants.getHealthStatusColor(status);
    
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: compact ? 6 : 8,
        vertical: compact ? 2 : 4,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(compact ? 4 : 6),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isEmergency) ...[
            Icon(
              Icons.warning,
              size: compact ? 12 : 14,
              color: Colors.red,
            ),
            SizedBox(width: compact ? 2 : 4),
          ],
          if (isChronic) ...[
            Icon(
              Icons.schedule,
              size: compact ? 12 : 14,
              color: Colors.purple,
            ),
            SizedBox(width: compact ? 2 : 4),
          ],
          Text(
            compact ? status : (condition ?? status),
            style: TextStyle(
              fontSize: compact ? 10 : 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

class HealthTrendIndicator extends StatelessWidget {
  final List<String> recentStatuses;
  final double size;

  const HealthTrendIndicator({
    super.key,
    required this.recentStatuses,
    this.size = 20.0,
  });

  @override
  Widget build(BuildContext context) {
    if (recentStatuses.length < 2) {
      return Icon(
        Icons.trending_flat,
        size: size,
        color: Colors.grey,
      );
    }

    final trend = _calculateTrend();
    final color = _getTrendColor(trend);
    final icon = _getTrendIcon(trend);

    return Tooltip(
      message: _getTrendMessage(trend),
      child: Icon(
        icon,
        size: size,
        color: color,
      ),
    );
  }

  HealthTrend _calculateTrend() {
    if (recentStatuses.length < 2) return HealthTrend.stable;

    final latest = recentStatuses.first;
    final previous = recentStatuses[1];

    final latestScore = _getHealthScore(latest);
    final previousScore = _getHealthScore(previous);

    if (latestScore > previousScore) {
      return HealthTrend.improving;
    } else if (latestScore < previousScore) {
      return HealthTrend.declining;
    } else {
      return HealthTrend.stable;
    }
  }

  int _getHealthScore(String status) {
    switch (status.toLowerCase()) {
      case 'healthy':
        return 5;
      case 'recovering':
        return 4;
      case 'under observation':
        return 3;
      case 'sick':
        return 2;
      case 'injured':
        return 1;
      case 'critical':
        return 0;
      default:
        return 3;
    }
  }

  Color _getTrendColor(HealthTrend trend) {
    switch (trend) {
      case HealthTrend.improving:
        return Colors.green;
      case HealthTrend.declining:
        return Colors.red;
      case HealthTrend.stable:
        return Colors.blue;
    }
  }

  IconData _getTrendIcon(HealthTrend trend) {
    switch (trend) {
      case HealthTrend.improving:
        return Icons.trending_up;
      case HealthTrend.declining:
        return Icons.trending_down;
      case HealthTrend.stable:
        return Icons.trending_flat;
    }
  }

  String _getTrendMessage(HealthTrend trend) {
    switch (trend) {
      case HealthTrend.improving:
        return 'Health is improving';
      case HealthTrend.declining:
        return 'Health is declining';
      case HealthTrend.stable:
        return 'Health is stable';
    }
  }
}

enum HealthTrend {
  improving,
  declining,
  stable,
}
