import 'package:flutter/material.dart';
import '../../../routes/app_routes.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class HealthScreen extends StatelessWidget {
  const HealthScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Health Management'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.healthReport,
            ),
            tooltip: 'View Health Reports',
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: Show health record form
        },
        backgroundColor: const Color(0xFF2E7D32),
        child: const Icon(Icons.add),
      ),
      body: ListView(
        padding: ResponsiveHelper.getResponsivePadding(context),
        children: [
          Card(
            child: ListTile(
              leading: const Icon(Icons.medical_services, color: Colors.teal),
              title: const Text('Health Records'),
              subtitle: const Text('View and manage health records'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: Navigate to health records list
              },
            ),
          ),
          Card(
            child: ListTile(
              leading: const Icon(Icons.vaccines, color: Colors.blue),
              title: const Text('Vaccinations'),
              subtitle: const Text('Track vaccination schedules'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: Navigate to vaccinations
              },
            ),
          ),
          Card(
            child: ListTile(
              leading: const Icon(Icons.medication, color: Colors.orange),
              title: const Text('Treatments'),
              subtitle: const Text('Manage treatment records'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: Navigate to treatments
              },
            ),
          ),
          Card(
            child: ListTile(
              leading: const Icon(Icons.calendar_month, color: Colors.purple),
              title: const Text('Health Calendar'),
              subtitle: const Text('View upcoming health events'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: Navigate to health calendar
              },
            ),
          ),
        ],
      ),
    );
  }
}
