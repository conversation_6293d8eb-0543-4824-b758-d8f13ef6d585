import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:logging/logging.dart';
import '../models/event_isar.dart';
import 'auto_event_generator.dart';
import '../../../services/database/database_helper.dart';
import '../../../services/streams/stream_service.dart';

/// Advanced event stream service for real-time event management
class EventStreamService extends ChangeNotifier {
  static final Logger _logger = Logger('EventStreamService');
  static EventStreamService? _instance;
  
  // Stream controllers for different event categories
  final StreamController<List<EventIsar>> _allEventsController = 
      StreamController<List<EventIsar>>.broadcast();
  final StreamController<List<EventIsar>> _upcomingEventsController = 
      StreamController<List<EventIsar>>.broadcast();
  final StreamController<List<EventIsar>> _overdueEventsController = 
      StreamController<List<EventIsar>>.broadcast();
  final StreamController<List<EventIsar>> _todayEventsController = 
      StreamController<List<EventIsar>>.broadcast();
  final StreamController<Map<String, dynamic>> _eventAnalyticsController = 
      StreamController<Map<String, dynamic>>.broadcast();
  
  // Event change notifications
  final StreamController<Map<String, dynamic>> _eventChangeController = 
      StreamController<Map<String, dynamic>>.broadcast();
  
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  final StreamService _streamService = StreamService();
  
  Timer? _refreshTimer;
  bool _isInitialized = false;
  
  // Singleton pattern
  static EventStreamService get instance {
    _instance ??= EventStreamService._internal();
    return _instance!;
  }
  
  EventStreamService._internal();
  
  // Stream getters
  Stream<List<EventIsar>> get allEventsStream => _allEventsController.stream;
  Stream<List<EventIsar>> get upcomingEventsStream => _upcomingEventsController.stream;
  Stream<List<EventIsar>> get overdueEventsStream => _overdueEventsController.stream;
  Stream<List<EventIsar>> get todayEventsStream => _todayEventsController.stream;
  Stream<Map<String, dynamic>> get eventAnalyticsStream => _eventAnalyticsController.stream;
  Stream<Map<String, dynamic>> get eventChangeStream => _eventChangeController.stream;
  
  /// Initialize the event stream service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _logger.info('Initializing EventStreamService');
      
      // Listen to other module changes for auto-event generation
      _setupModuleListeners();
      
      // Start periodic refresh
      _startPeriodicRefresh();
      
      // Initial data load
      await _refreshAllStreams();
      
      _isInitialized = true;
      _logger.info('EventStreamService initialized successfully');
    } catch (e) {
      _logger.severe('Error initializing EventStreamService: $e');
      rethrow;
    }
  }
  
  /// Setup listeners for other module changes
  void _setupModuleListeners() {
    // Listen to breeding changes
    _streamService.breedingStream.listen((data) {
      _handleBreedingChange(data);
    });
    
    // Listen to health changes
    _streamService.healthStream.listen((data) {
      _handleHealthChange(data);
    });
    
    // Listen to milk record changes
    _streamService.milkStream.listen((data) {
      _handleMilkChange(data);
    });
    
    // Listen to cattle changes
    _streamService.cattleStream.listen((data) {
      _handleCattleChange(data);
    });
    
    // Listen to transaction changes
    _streamService.transactionStream.listen((data) {
      _handleTransactionChange(data);
    });
  }
  
  /// Handle breeding module changes
  Future<void> _handleBreedingChange(Map<String, dynamic> data) async {
    try {
      final action = data['action'] as String?;
      final cattleId = data['cattleId'] as String?;
      
      if (action == 'add' && cattleId != null) {
        // Auto-generate pregnancy check event
        await _generatePregnancyCheckEvent(cattleId, data);
        
        // Auto-generate expected calving event
        await _generateExpectedCalvingEvent(cattleId, data);
      }
      
      await _refreshAllStreams();
    } catch (e) {
      _logger.severe('Error handling breeding change: $e');
    }
  }
  
  /// Handle health module changes
  Future<void> _handleHealthChange(Map<String, dynamic> data) async {
    try {
      final action = data['action'] as String?;
      final cattleId = data['cattleId'] as String?;
      
      if (action == 'add' && cattleId != null) {
        // Auto-generate follow-up health check events
        await _generateFollowUpHealthEvent(cattleId, data);
      }
      
      await _refreshAllStreams();
    } catch (e) {
      _logger.severe('Error handling health change: $e');
    }
  }
  
  /// Handle milk record changes
  Future<void> _handleMilkChange(Map<String, dynamic> data) async {
    try {
      // Could generate milk recording reminders or quality check events
      await _refreshAllStreams();
    } catch (e) {
      _logger.severe('Error handling milk change: $e');
    }
  }
  
  /// Handle cattle changes
  Future<void> _handleCattleChange(Map<String, dynamic> data) async {
    try {
      final action = data['action'] as String?;
      final cattleId = data['cattleId'] as String?;
      
      if (action == 'add' && cattleId != null) {
        // Auto-generate initial health check event for new cattle
        await _generateInitialHealthCheckEvent(cattleId);
        
        // Auto-generate vaccination schedule
        await _generateVaccinationSchedule(cattleId);
      }
      
      await _refreshAllStreams();
    } catch (e) {
      _logger.severe('Error handling cattle change: $e');
    }
  }
  
  /// Handle transaction changes
  Future<void> _handleTransactionChange(Map<String, dynamic> data) async {
    try {
      // Could generate financial events or reminders
      await _refreshAllStreams();
    } catch (e) {
      _logger.severe('Error handling transaction change: $e');
    }
  }
  
  /// Start periodic refresh timer
  void _startPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _refreshAllStreams();
    });
  }
  
  /// Refresh all event streams
  Future<void> _refreshAllStreams() async {
    try {
      final allEvents = await _dbHelper.eventsHandler.getAllEvents();
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final tomorrow = today.add(const Duration(days: 1));
      
      // All events
      _allEventsController.add(allEvents);
      
      // Today's events
      final todayEvents = allEvents.where((event) {
        if (event.eventDate == null) return false;
        final eventDate = DateTime(
          event.eventDate!.year,
          event.eventDate!.month,
          event.eventDate!.day,
        );
        return eventDate.isAtSameMomentAs(today);
      }).toList();
      _todayEventsController.add(todayEvents);
      
      // Upcoming events (next 7 days)
      final upcomingEvents = allEvents.where((event) {
        if (event.eventDate == null) return false;
        return event.eventDate!.isAfter(tomorrow) &&
               event.eventDate!.isBefore(now.add(const Duration(days: 7))) &&
               !event.isCompleted;
      }).toList();
      _upcomingEventsController.add(upcomingEvents);
      
      // Overdue events
      final overdueEvents = allEvents.where((event) {
        if (event.eventDate == null) return false;
        return event.eventDate!.isBefore(today) && !event.isCompleted;
      }).toList();
      _overdueEventsController.add(overdueEvents);
      
      // Generate analytics
      final analytics = _generateAnalytics(allEvents);
      _eventAnalyticsController.add(analytics);
      
    } catch (e) {
      _logger.severe('Error refreshing event streams: $e');
    }
  }
  
  /// Generate event analytics
  Map<String, dynamic> _generateAnalytics(List<EventIsar> events) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    final totalEvents = events.length;
    final completedEvents = events.where((e) => e.isCompleted).length;
    final pendingEvents = events.where((e) => !e.isCompleted && !e.isMissed).length;
    final overdueEvents = events.where((e) => 
      e.eventDate != null && 
      e.eventDate!.isBefore(today) && 
      !e.isCompleted
    ).length;
    
    final todayEvents = events.where((e) {
      if (e.eventDate == null) return false;
      final eventDate = DateTime(e.eventDate!.year, e.eventDate!.month, e.eventDate!.day);
      return eventDate.isAtSameMomentAs(today);
    }).length;
    
    final upcomingEvents = events.where((e) => 
      e.eventDate != null && 
      e.eventDate!.isAfter(today) && 
      e.eventDate!.isBefore(now.add(const Duration(days: 7)))
    ).length;
    
    // Event type distribution
    final typeDistribution = <String, int>{};
    for (final event in events) {
      final typeName = event.type?.getDisplayName() ?? 'Unknown';
      typeDistribution[typeName] = (typeDistribution[typeName] ?? 0) + 1;
    }
    
    // Priority distribution
    final priorityDistribution = <String, int>{};
    for (final event in events) {
      final priority = event.priority.name;
      priorityDistribution[priority] = (priorityDistribution[priority] ?? 0) + 1;
    }
    
    return {
      'totalEvents': totalEvents,
      'completedEvents': completedEvents,
      'pendingEvents': pendingEvents,
      'overdueEvents': overdueEvents,
      'todayEvents': todayEvents,
      'upcomingEvents': upcomingEvents,
      'completionRate': totalEvents > 0 ? (completedEvents / totalEvents * 100).round() : 0,
      'typeDistribution': typeDistribution,
      'priorityDistribution': priorityDistribution,
    };
  }
  
  /// Notify about event changes
  void notifyEventChange(String action, EventIsar event) {
    _eventChangeController.add({
      'action': action,
      'event': event,
      'timestamp': DateTime.now().toIso8601String(),
    });

    // Refresh streams immediately for refresh action, otherwise with delay
    if (action == 'refresh') {
      _refreshAllStreams();
    } else {
      Future.delayed(const Duration(milliseconds: 500), () {
        _refreshAllStreams();
      });
    }
  }

  /// Force refresh all streams immediately
  Future<void> forceRefresh() async {
    _logger.info('🔄 EventStreamService: Force refreshing all streams...');
    await _refreshAllStreams();
    _logger.info('✅ EventStreamService: Force refresh completed');
  }
  
  /// Dispose resources
  @override
  void dispose() {
    _refreshTimer?.cancel();
    _allEventsController.close();
    _upcomingEventsController.close();
    _overdueEventsController.close();
    _todayEventsController.close();
    _eventAnalyticsController.close();
    _eventChangeController.close();
    super.dispose();
  }
  
  // Auto-generation methods using AutoEventGenerator
  Future<void> _generatePregnancyCheckEvent(String cattleId, Map<String, dynamic> data) async {
    try {
      final autoGenerator = AutoEventGenerator.instance;
      await autoGenerator.generatePregnancyCheckEvent(cattleId, data);
      _logger.info('Generated pregnancy check event for cattle $cattleId');
    } catch (e) {
      _logger.severe('Error generating pregnancy check event: $e');
    }
  }

  Future<void> _generateExpectedCalvingEvent(String cattleId, Map<String, dynamic> data) async {
    try {
      final autoGenerator = AutoEventGenerator.instance;
      await autoGenerator.generateExpectedCalvingEvent(cattleId, data);
      _logger.info('Generated expected calving event for cattle $cattleId');
    } catch (e) {
      _logger.severe('Error generating expected calving event: $e');
    }
  }

  Future<void> _generateFollowUpHealthEvent(String cattleId, Map<String, dynamic> data) async {
    try {
      final autoGenerator = AutoEventGenerator.instance;
      await autoGenerator.generateFollowUpHealthEvent(cattleId, data);
      _logger.info('Generated follow-up health event for cattle $cattleId');
    } catch (e) {
      _logger.severe('Error generating follow-up health event: $e');
    }
  }

  Future<void> _generateInitialHealthCheckEvent(String cattleId) async {
    try {
      final autoGenerator = AutoEventGenerator.instance;
      await autoGenerator.generateInitialHealthCheckEvent(cattleId);
      _logger.info('Generated initial health check event for cattle $cattleId');
    } catch (e) {
      _logger.severe('Error generating initial health check event: $e');
    }
  }

  Future<void> _generateVaccinationSchedule(String cattleId) async {
    try {
      final autoGenerator = AutoEventGenerator.instance;
      await autoGenerator.generateVaccinationSchedule(cattleId);
      _logger.info('Generated vaccination schedule for cattle $cattleId');
    } catch (e) {
      _logger.severe('Error generating vaccination schedule: $e');
    }
  }
}
