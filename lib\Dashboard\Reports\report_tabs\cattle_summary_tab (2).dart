import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/cattle_report_data.dart';
import '../models/chart_data.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class CattleSummaryTab extends StatelessWidget {
  final CattleReportData reportData;

  const CattleSummaryTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final summaryData = reportData.summaryData;
    final chartData = reportData.chartData;

    return SingleChildScrollView(
      child: Padding(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCard(summaryData),
            SizedBox(height: ResponsiveSpacing.getLG(context)),
            if (chartData.isNotEmpty) _buildCategoryChart(chartData),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(Map<String, dynamic> summaryData) {
    return ResponsiveCard(
      child: Padding(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Cattle Summary',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            _buildSummaryRow('Total Cattle', summaryData['totalCattle'].toString()),
            _buildSummaryRow('Healthy', summaryData['healthyCount'].toString()),
            _buildSummaryRow('Sick', summaryData['sickCount'].toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: ResponsiveTheme.getSubtitleStyle(context),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.blue,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChart(List<ChartData> chartData) {
    return ResponsiveCard(
      child: Padding(
        padding: ResponsiveHelper.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Cattle by Category',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: ResponsiveSpacing.getMD(context)),
            SizedBox(
              height: 300,
              child: PieChart(
                PieChartData(
                  sections: chartData.map((data) {
                    return PieChartSectionData(
                      value: data.value,
                      title: '${data.label}\n${data.value.toInt()}',
                      color: data.color ?? Colors.grey,
                      radius: 100,
                      titleStyle: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    );
                  }).toList(),
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
