import 'package:flutter/material.dart';


import '../models/health_record_isar.dart';
import '../models/treatment_record_isar.dart';
import '../models/vaccination_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../../constants/health_constants.dart';

class HealthAnalyticsTab extends StatefulWidget {
  final List<HealthRecordIsar> healthRecords;
  final List<TreatmentRecordIsar> treatmentRecords;
  final List<VaccinationRecordIsar> vaccinationRecords;
  final Map<String, CattleIsar> cattleMap;
  final Map<String, AnimalTypeIsar> animalTypeMap;
  final Map<String, BreedCategoryIsar> breedMap;
  final VoidCallback onRefresh;

  const HealthAnalyticsTab({
    super.key,
    required this.healthRecords,
    required this.treatmentRecords,
    required this.vaccinationRecords,
    required this.cattleMap,
    required this.animalTypeMap,
    required this.breedMap,
    required this.onRefresh,
  });

  @override
  State<HealthAnalyticsTab> createState() => _HealthAnalyticsTabState();
}

class _HealthAnalyticsTabState extends State<HealthAnalyticsTab> {
  String _selectedPeriod = 'Last 30 Days';
  final List<String> _periods = ['Last 7 Days', 'Last 30 Days', 'Last 90 Days', 'Last Year', 'All Time'];

  DateTime get _startDate {
    final now = DateTime.now();
    switch (_selectedPeriod) {
      case 'Last 7 Days':
        return now.subtract(const Duration(days: 7));
      case 'Last 30 Days':
        return now.subtract(const Duration(days: 30));
      case 'Last 90 Days':
        return now.subtract(const Duration(days: 90));
      case 'Last Year':
        return now.subtract(const Duration(days: 365));
      default:
        return DateTime(2000); // All time
    }
  }

  List<HealthRecordIsar> get _filteredHealthRecords {
    return widget.healthRecords.where((record) {
      return record.date != null && record.date!.isAfter(_startDate);
    }).toList();
  }

  List<TreatmentRecordIsar> get _filteredTreatmentRecords {
    return widget.treatmentRecords.where((record) {
      return record.startDate != null && record.startDate!.isAfter(_startDate);
    }).toList();
  }

  List<VaccinationRecordIsar> get _filteredVaccinationRecords {
    return widget.vaccinationRecords.where((record) {
      return record.vaccinationDate != null && record.vaccinationDate!.isAfter(_startDate);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async => widget.onRefresh(),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Period Selector
            _buildPeriodSelector(),
            const SizedBox(height: 24),

            // Overview Cards
            _buildOverviewCards(),
            const SizedBox(height: 24),

            // Health Status Distribution
            _buildHealthStatusSection(),
            const SizedBox(height: 24),

            // Common Conditions
            _buildCommonConditionsSection(),
            const SizedBox(height: 24),

            // Treatment Analysis
            _buildTreatmentAnalysisSection(),
            const SizedBox(height: 24),

            // Vaccination Coverage
            _buildVaccinationCoverageSection(),
            const SizedBox(height: 24),

            // Cost Analysis
            _buildCostAnalysisSection(),
            const SizedBox(height: 24),

            // Alerts and Recommendations
            _buildAlertsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(Icons.date_range, color: Colors.purple[600]),
            const SizedBox(width: 12),
            Text(
              'Analysis Period:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.purple[700],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButton<String>(
                value: _selectedPeriod,
                isExpanded: true,
                items: _periods.map((period) {
                  return DropdownMenuItem(
                    value: period,
                    child: Text(period),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() => _selectedPeriod = value);
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewCards() {
    final healthRecords = _filteredHealthRecords;
    final treatmentRecords = _filteredTreatmentRecords;

    final totalCattle = widget.cattleMap.length;
    final healthyCattle = widget.cattleMap.values.where((cattle) {
      final latestRecord = healthRecords
          .where((r) => r.cattleBusinessId == cattle.businessId)
          .fold<HealthRecordIsar?>(null, (latest, current) {
        if (latest == null) return current;
        if (current.date != null && latest.date != null) {
          return current.date!.isAfter(latest.date!) ? current : latest;
        }
        return latest;
      });
      return latestRecord?.healthStatus == 'Healthy';
    }).length;

    final activeTreatments = treatmentRecords.where((t) => t.status == 'Active').length;
    final emergencyRecords = healthRecords.where((h) => h.isEmergency).length;

    return Row(
      children: [
        Expanded(
          child: _buildOverviewCard(
            'Total Cattle',
            totalCattle.toString(),
            Icons.pets,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildOverviewCard(
            'Healthy',
            healthyCattle.toString(),
            Icons.favorite,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildOverviewCard(
            'Active Treatments',
            activeTreatments.toString(),
            Icons.medication,
            Colors.red,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildOverviewCard(
            'Emergencies',
            emergencyRecords.toString(),
            Icons.warning,
            Colors.deepPurple,
          ),
        ),
      ],
    );
  }

  Widget _buildOverviewCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthStatusSection() {
    final healthRecords = _filteredHealthRecords;
    final statusCounts = <String, int>{};

    for (final record in healthRecords) {
      final status = record.healthStatus ?? 'Unknown';
      statusCounts[status] = (statusCounts[status] ?? 0) + 1;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.health_and_safety, color: Colors.blue[600]),
                const SizedBox(width: 8),
                Text(
                  'Health Status Distribution',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (statusCounts.isEmpty)
              Center(
                child: Text(
                  'No health records found for the selected period',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              )
            else
              ...statusCounts.entries.map((entry) {
                final percentage = (entry.value / healthRecords.length * 100).round();
                final color = HealthConstants.getHealthStatusColor(entry.key);

                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Row(
                    children: [
                      Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: color,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          entry.key,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ),
                      Text(
                        '${entry.value} ($percentage%)',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildCommonConditionsSection() {
    final healthRecords = _filteredHealthRecords;
    final conditionCounts = <String, int>{};

    for (final record in healthRecords) {
      final condition = record.condition ?? 'Unknown';
      conditionCounts[condition] = (conditionCounts[condition] ?? 0) + 1;
    }

    final sortedConditions = conditionCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.medical_information, color: Colors.red[600]),
                const SizedBox(width: 8),
                Text(
                  'Most Common Conditions',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (sortedConditions.isEmpty)
              Center(
                child: Text(
                  'No conditions recorded for the selected period',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              )
            else
              ...sortedConditions.take(5).map((entry) {
                final color = HealthConstants.getConditionColor(entry.key);

                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Row(
                    children: [
                      Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: color,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          entry.key,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ),
                      Text(
                        '${entry.value} cases',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildTreatmentAnalysisSection() {
    final treatmentRecords = _filteredTreatmentRecords;
    final activeTreatments = treatmentRecords.where((t) => t.status == 'Active').length;
    final completedTreatments = treatmentRecords.where((t) => t.status == 'Completed').length;
    final discontinuedTreatments = treatmentRecords.where((t) => t.status == 'Discontinued').length;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.medication, color: Colors.green[600]),
                const SizedBox(width: 8),
                Text(
                  'Treatment Analysis',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard('Active', activeTreatments, Colors.blue),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard('Completed', completedTreatments, Colors.green),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard('Discontinued', discontinuedTreatments, Colors.red),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVaccinationCoverageSection() {
    final vaccinationRecords = _filteredVaccinationRecords;
    final totalCattle = widget.cattleMap.length;
    final vaccinatedCattle = vaccinationRecords.map((v) => v.cattleBusinessId).toSet().length;
    final coveragePercentage = totalCattle > 0 ? (vaccinatedCattle / totalCattle * 100).round() : 0;

    final coreVaccinations = vaccinationRecords.where((v) => v.isCoreVaccine).length;
    final boosters = vaccinationRecords.where((v) => v.isBooster).length;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.vaccines, color: Colors.purple[600]),
                const SizedBox(width: 8),
                Text(
                  'Vaccination Coverage',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '$coveragePercentage%',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.purple[600],
                        ),
                      ),
                      Text(
                        'Coverage Rate',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                      Text(
                        '$vaccinatedCattle of $totalCattle cattle',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 24),
                Expanded(
                  child: Column(
                    children: [
                      _buildStatCard('Core Vaccines', coreVaccinations, Colors.blue),
                      const SizedBox(height: 8),
                      _buildStatCard('Boosters', boosters, Colors.indigo),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCostAnalysisSection() {
    final treatmentRecords = _filteredTreatmentRecords;
    final vaccinationRecords = _filteredVaccinationRecords;

    final treatmentCosts = treatmentRecords
        .where((t) => t.cost != null)
        .map((t) => t.cost!)
        .fold(0.0, (sum, cost) => sum + cost);

    final vaccinationCosts = vaccinationRecords
        .where((v) => v.cost != null)
        .map((v) => v.cost!)
        .fold(0.0, (sum, cost) => sum + cost);

    final totalCosts = treatmentCosts + vaccinationCosts;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.attach_money, color: Colors.teal[600]),
                const SizedBox(width: 8),
                Text(
                  'Cost Analysis',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildCostCard('Total Health Costs', totalCosts, Colors.teal),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildCostCard('Treatment Costs', treatmentCosts, Colors.blue),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildCostCard('Vaccination Costs', vaccinationCosts, Colors.purple),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertsSection() {
    final now = DateTime.now();
    final alerts = <String>[];

    // Check for upcoming vaccinations
    final upcomingVaccinations = widget.vaccinationRecords.where((v) {
      return v.nextDueDate != null &&
             v.nextDueDate!.isAfter(now) &&
             v.nextDueDate!.isBefore(now.add(const Duration(days: 30)));
    }).length;

    if (upcomingVaccinations > 0) {
      alerts.add('$upcomingVaccinations vaccinations due in the next 30 days');
    }

    // Check for active withdrawal periods
    final activeWithdrawals = [
      ...widget.treatmentRecords.where((t) =>
          (t.milkWithdrawalEndDate != null && t.milkWithdrawalEndDate!.isAfter(now)) ||
          (t.meatWithdrawalEndDate != null && t.meatWithdrawalEndDate!.isAfter(now))),
      ...widget.vaccinationRecords.where((v) =>
          (v.milkWithdrawalEndDate != null && v.milkWithdrawalEndDate!.isAfter(now)) ||
          (v.meatWithdrawalEndDate != null && v.meatWithdrawalEndDate!.isAfter(now))),
    ].length;

    if (activeWithdrawals > 0) {
      alerts.add('$activeWithdrawals animals in withdrawal period');
    }

    // Check for follow-up required
    final followUpRequired = widget.healthRecords.where((h) => h.requiresFollowUp).length;
    if (followUpRequired > 0) {
      alerts.add('$followUpRequired health records require follow-up');
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.notifications_active, color: Colors.deepPurple[600]),
                const SizedBox(width: 8),
                Text(
                  'Alerts & Recommendations',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.deepPurple[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (alerts.isEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green[600]),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'All health management tasks are up to date!',
                        style: TextStyle(
                          color: Colors.green[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              )
            else
              ...alerts.map((alert) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange[600]),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        alert,
                        style: TextStyle(
                          color: Colors.orange[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              )).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, int value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha(26), // 0.1 * 255 = 26
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withAlpha(77)), // 0.3 * 255 = 77
      ),
      child: Column(
        children: [
          Text(
            value.toString(),
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCostCard(String title, double value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha(26), // 0.1 * 255 = 26
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withAlpha(77)), // 0.3 * 255 = 77
      ),
      child: Column(
        children: [
          Text(
            '\$${value.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
