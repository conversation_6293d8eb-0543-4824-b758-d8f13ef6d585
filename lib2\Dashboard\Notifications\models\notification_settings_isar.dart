import 'package:isar/isar.dart';

part 'notification_settings_isar.g.dart';

/// Represents notification settings in the system
@collection
class NotificationSettingsIsar {
  Id id = Isar.autoIncrement;

  /// Enable/disable all notifications
  bool notificationsEnabled = true;

  /// Enable/disable in-app notifications
  bool inAppNotificationsEnabled = true;

  /// Enable/disable push notifications (if applicable)
  bool pushNotificationsEnabled = false;

  /// Enable/disable email notifications (if applicable)
  bool emailNotificationsEnabled = false;

  /// Enable/disable SMS notifications (if applicable)
  bool smsNotificationsEnabled = false;

  /// Health-related notification settings
  bool healthNotificationsEnabled = true;

  /// Breeding-related notification settings
  bool breedingNotificationsEnabled = true;

  /// Milk record notification settings
  bool milkNotificationsEnabled = true;

  /// Cattle events notification settings
  bool eventsNotificationsEnabled = true;

  /// Maximum number of notifications to keep
  int maxNotificationsToKeep = 100;

  /// Auto-delete read notifications after days
  int autoDeleteReadAfterDays = 30;

  /// Time when settings were last updated
  DateTime? updatedAt;

  /// Constructor
  NotificationSettingsIsar({
    this.notificationsEnabled = true,
    this.inAppNotificationsEnabled = true,
    this.pushNotificationsEnabled = false,
    this.emailNotificationsEnabled = false,
    this.smsNotificationsEnabled = false,
    this.healthNotificationsEnabled = true,
    this.breedingNotificationsEnabled = true,
    this.milkNotificationsEnabled = true,
    this.eventsNotificationsEnabled = true,
    this.maxNotificationsToKeep = 100,
    this.autoDeleteReadAfterDays = 30,
    this.updatedAt,
  });
} 