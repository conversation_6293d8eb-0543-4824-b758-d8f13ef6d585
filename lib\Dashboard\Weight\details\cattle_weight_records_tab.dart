import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/weight_record_isar.dart';
import '../services/weight_service.dart';
import '../../widgets/history_record_card.dart';
import '../dialogs/weight_form_dialog.dart';
import '../../../utils/message_utils.dart';
import '../../../widgets/fab_styles.dart';
import '../../widgets/date_range_filter_widget.dart';
import '../../widgets/filter_status_bar.dart';
import '../../widgets/sort_widget.dart';
import '../../widgets/index.dart';

class CattleWeightRecordsTab extends StatefulWidget {
  final CattleIsar cattle;
  final List<WeightRecordIsar> weightRecords;
  final VoidCallback onRefresh;

  const CattleWeightRecordsTab({
    Key? key,
    required this.cattle,
    required this.weightRecords,
    required this.onRefresh,
  }) : super(key: key);

  @override
  State<CattleWeightRecordsTab> createState() => _CattleWeightRecordsTabState();
}

class _CattleWeightRecordsTabState extends State<CattleWeightRecordsTab> {
  final WeightService _weightService = WeightService();

  // Optimization: Make color a static constant.
  static const Color _recordsColor = Colors.green;

  // --- State Variables ---
  // Optimization: Store the filtered/sorted list in the state.
  late List<WeightRecordIsar> _filteredRecords;

  // Filter and sort states
  String? _sortBy;
  bool _sortAscending = true; // Default to true for consistency
  DateTime? _startDate;
  DateTime? _endDate;

  // Store default dates for easy reset and comparison
  late final DateTime _defaultStartDate;
  late final DateTime _defaultEndDate;

  @override
  void initState() {
    super.initState();
    _initializeDates();
    _applyFiltersAndSort();
  }

  // Optimization: Handle props changes if the parent widget rebuilds with new data.
  @override
  void didUpdateWidget(covariant CattleWeightRecordsTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.weightRecords != oldWidget.weightRecords) {
      // Re-apply filters if the base data changes.
      _applyFiltersAndSort();
    }
  }

  void _initializeDates() {
    // Set default to show ALL records (no date filtering by default)
    final now = DateTime.now();
    _defaultEndDate = DateTime(now.year, now.month, now.day);
    _defaultStartDate = _defaultEndDate.subtract(const Duration(days: 29)); // 30 days total

    // Start with NO date filtering - show all records
    _endDate = null;
    _startDate = null;
  }

  // Optimization: Central function to update the displayed list.
  // This is now the ONLY place where filtering and sorting happens.
  void _applyFiltersAndSort() {
    List<WeightRecordIsar> tempRecords = List.from(widget.weightRecords);

    // 1. Apply Date Filter (only if dates are set)
    if (_startDate != null && _endDate != null) {
      final inclusiveEndDate = _endDate!.add(const Duration(days: 1));
      tempRecords = tempRecords.where((record) {
        final date = record.measurementDate;
        if (date == null) return false;
        // Refined logic: not before start AND before the day after end.
        return !date.isBefore(_startDate!) && date.isBefore(inclusiveEndDate);
      }).toList();
    }

    // 2. Apply Sorting
    if (_sortBy != null) {
      tempRecords.sort((a, b) {
        int comparison;
        switch (_sortBy) {
          case 'Date':
            // Primary sort: measurement date
            comparison = (a.measurementDate ?? DateTime(0))
                .compareTo(b.measurementDate ?? DateTime(0));

            // Secondary sort: if dates are the same, sort by creation time
            if (comparison == 0) {
              comparison = (a.createdAt ?? DateTime(0))
                  .compareTo(b.createdAt ?? DateTime(0));
            }
            break;
          case 'Weight':
            comparison = a.weight.compareTo(b.weight);
            break;
          default:
            comparison = 0;
        }
        // Optimization: Handle sort direction within the comparison.
        return _sortAscending ? comparison : -comparison;
      });
    } else {
      // Default sorting: Newest first
      tempRecords.sort((a, b) {
        // Primary sort: measurement date (newest first)
        final dateComparison = (b.measurementDate ?? DateTime(0))
            .compareTo(a.measurementDate ?? DateTime(0));

        // Secondary sort: if dates are the same, sort by creation time (newest first)
        if (dateComparison == 0) {
          return (b.createdAt ?? DateTime(0))
              .compareTo(a.createdAt ?? DateTime(0));
        }
        return dateComparison;
      });
    }

    // 3. Update the state
    setState(() {
      _filteredRecords = tempRecords;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.weightRecords.isEmpty) {
      return _buildEmptyState();
    }

    return Scaffold(
      body: Column(
        children: [
          _buildControls(),
          Expanded(
            // Optimization: Build list from the pre-filtered state variable.
            child: _buildRecordsList(_filteredRecords),
          ),
        ],
      ),
      floatingActionButton: FabStyles.add(
        onPressed: _addWeightRecord,
        tooltip: 'Add Weight Record',
      ),
    );
  }

  Widget _buildControls() {
    return Padding(
      padding: EdgeInsets.all(_getResponsivePadding()),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                flex: 1,  // Equal space with sort widget
                child: DateRangeFilterWidget(
                  startDate: _startDate,  // Pass null when no filter active
                  endDate: _endDate,      // Pass null when no filter active
                  themeColor: Colors.purple,  // Use purple color for consistency
                  activeBackgroundOpacity: 0.7,  // 70% opacity for testing
                  onStartDateChanged: (date) {
                    setState(() {
                      _startDate = date;
                      // If only start date is set, also set end date to today
                      _endDate ??= DateTime.now();
                    });
                    _applyFiltersAndSort();
                  },
                  onEndDateChanged: (date) {
                    setState(() {
                      _endDate = date;
                      // If only end date is set, also set start date to 30 days ago
                      _startDate ??= _defaultStartDate;
                    });
                    _applyFiltersAndSort();
                  },
                  onClearFilter: () {
                    setState(() {
                      _startDate = null;
                      _endDate = null;
                    });
                    _applyFiltersAndSort();
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 1,  // Equal space with date filter
                child: SortWidget(
                  sortFields: const [
                    SortField(value: 'Date', label: 'Date', icon: Icons.calendar_today, iconColor: Colors.blue),
                    SortField(value: 'Weight', label: 'Weight', icon: Icons.scale, iconColor: Colors.blue),  // Changed to blue
                  ],
                  sortBy: _sortBy,
                  sortAscending: _sortAscending,
                  onSortByChanged: (sortBy) {
                    setState(() {
                      _sortBy = sortBy;
                    });
                    _applyFiltersAndSort();
                  },
                  onSortAscendingChanged: (ascending) {
                    setState(() {
                      _sortAscending = ascending;
                    });
                    _applyFiltersAndSort();
                  },
                  onApplySort: _applyFiltersAndSort,
                  themeColor: Colors.purple,  // Use purple color for consistency
                  compact: true,
                  showSortIndicator: true,
                  buttonHeight: 44,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          FilterStatusBar.weight(
            filterStates: {},
            totalCount: widget.weightRecords.length,
            filteredCount: _filteredRecords.length,
            onClearFilters: _clearFilters,
            defaultStartDate: DateTime.now().subtract(const Duration(days: 365)),
            defaultEndDate: DateTime.now(),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordsList(List<WeightRecordIsar> records) {
    if (records.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text('No records found', style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: Colors.grey[600])),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your date range or sort criteria',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: _getResponsivePadding()),
      itemCount: records.length,
      itemBuilder: (context, index) {
        final record = records[index];
        final cattleName = widget.cattle.name ?? 'Unknown Cattle';
        final tagId = widget.cattle.tagId ?? '';
        final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;

        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: HistoryRecordCard.weight(
            measurementDate: record.measurementDate,
            weight: record.weight,
            cattleName: displayName,
            measurementMethod: record.measurementMethod,
            bodyConditionScore: record.bodyConditionScore,
            notes: record.notes,
            isSelected: false,
            isSelectionMode: false,
            onTap: () => _editWeightRecord(record),
            onEdit: () => _editWeightRecord(record),
            onDelete: () => _deleteWeightRecord(record),
            compact: false,
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Scaffold(
      body: Container(
        width: double.infinity,
        constraints: const BoxConstraints(minHeight: 300),
        margin: const EdgeInsets.fromLTRB(16, 16, 16, 32),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.purple.withAlpha(76)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(25),
              spreadRadius: 2,
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: EmptyState.custom(
          icon: Icons.scale_outlined,
          message: 'No Weight Records',
          subtitle: 'Add the first weight record for ${widget.cattle.name}',
          color: Colors.purple,
          hasData: false,
          action: EmptyState.createActionButton(
            onPressed: () => _addWeightRecord(),
            icon: Icons.add,
            label: 'Add First Record',
            backgroundColor: Colors.purple,
          ),
        ),
      ),
    );
  }

  // --- Action Handlers ---
  void _addWeightRecord() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WeightFormDialog(
          cattle: [widget.cattle],
          preSelectedCattle: widget.cattle,
          onRecordAdded: widget.onRefresh,
        ),
      ),
    );
  }

  void _editWeightRecord(WeightRecordIsar record) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WeightFormDialog(
          cattle: [widget.cattle],
          existingRecord: record,
          onRecordAdded: widget.onRefresh,
        ),
      ),
    );
  }

  Future<void> _deleteWeightRecord(WeightRecordIsar record) async {
    // Robustness: Check for null businessId.
    final recordId = record.businessId;
    if (recordId == null) {
      MessageUtils.showError(context, 'Cannot delete record: Missing ID.');
      return;
    }
    
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Weight Record'),
        content: Text(
          'Are you sure you want to delete this weight record?\n\n'
          'Weight: ${record.weight.toStringAsFixed(1)} kg\n'
          'Date: ${record.measurementDate != null ? DateFormat('MMM dd, yyyy').format(record.measurementDate!) : 'No date'}',
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(false), child: const Text('Cancel')),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _weightService.deleteWeightRecord(recordId);
        widget.onRefresh();
        if (mounted) {
          MessageUtils.showSuccess(context, 'Weight record deleted successfully');
        }
      } catch (e) {
        if (mounted) {
          MessageUtils.showError(context, 'Error deleting record: $e');
        }
      }
    }
  }

  // --- Helper Methods ---
  bool _hasActiveFilters() {
    // Only consider filters active if they're actually set (not null)
    final hasDateFilter = _startDate != null && _endDate != null;
    final hasSortFilter = _sortBy != null;
    return hasDateFilter || hasSortFilter;
  }

  void _clearFilters() {
    setState(() {
      // Clear all filters - show ALL records
      _startDate = null;
      _endDate = null;
      _sortBy = null;
      _sortAscending = true;
    });
    _applyFiltersAndSort();
  }

  List<String> _getActiveFiltersList() {
    final List<String> activeFilters = [];

    // Only show date filter if it's actually set
    if (_startDate != null && _endDate != null) {
      final dateFormat = DateFormat('MMM dd');
      activeFilters.add('${dateFormat.format(_startDate!)} - ${dateFormat.format(_endDate!)}');
    }

    // Check sort filter
    if (_sortBy != null) {
      final direction = _sortAscending ? 'Ascending' : 'Descending';
      activeFilters.add('Sort: $_sortBy ($direction)');
    }

    return activeFilters;
  }



  double _getResponsivePadding() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 12.0; // Mobile
    if (screenWidth < 1200) return 16.0; // Tablet
    return 20.0; // Desktop
  }
}