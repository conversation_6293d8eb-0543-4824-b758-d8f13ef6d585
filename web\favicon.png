import 'package:flutter/material.dart';
import '../../../routes/app_routes.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class ReportsScreen extends StatelessWidget {
  const ReportsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reports'),
        backgroundColor: ResponsiveTheme.primaryColor,
        foregroundColor: Colors.white,
        toolbarHeight: ResponsiveHelper.getAppBarHeight(context),
      ),
      backgroundColor: ResponsiveTheme.scaffoldBackground,
      body: ResponsiveGridView(
        mobileColumns: 2,
        tabletColumns: 3,
        desktopColumns: 4,
        childAspectRatio: ResponsiveHelper.getResponsiveValue(
          context,
          mob