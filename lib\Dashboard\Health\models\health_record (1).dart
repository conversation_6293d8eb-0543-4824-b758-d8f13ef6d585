<template>
	<ion-page ref="tabpage">
		<ion-header>
			<ion-toolbar>
				<div class="page-logo" @click="resetIframe()">
					<ion-img :src="require('../../assets/images/logo.png')" />
				</div>
			</ion-toolbar>
		</ion-header>
		<ion-content :fullscreen="true" class="has-iframe">
			<iframe :src="iframeSrc" allowfullscreen id="iframeSite" @load="store.endProgress()"></iframe>
		</ion-content>
	</ion-page>
</template>

<script>
import {defineComponent} from "vue";
import {useRoute} from "vue-router";
import {
	useIonRouter,
	alertController,
	IonContent,
	IonHeader,
	IonImg,
	IonPage,
	IonToolbar
} from "@ionic/vue";
import {useMainStore} from 