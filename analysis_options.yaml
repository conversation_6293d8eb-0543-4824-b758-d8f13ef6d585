{
  "configVersion": 2,
  "packages": [
    {
      "name": "_discoveryapis_commons",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_discoveryapis_commons-1.0.7",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "_fe_analyzer_shared",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "_macros",
      "rootUri": "file:///C:/flutter/bin/cache/dart-sdk/pkg/_macros",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "analyzer",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer-6.11.0",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "archive",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/archive-3.6.1",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "args",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.6.0",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "async",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.11.0",
      "packageUri": "lib/",
      "languageVersion": "2.18"
    },
    {
      "name": "barcode",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/barcode-2.2.9",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "bidi",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/bidi-2.0.12",
      "packageUri": "lib/",
      "languageVersion": "2.12"