import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/health_record_isar.dart';
import '../models/treatment_record_isar.dart';
import '../models/vaccination_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../constants/health_constants.dart';

class HealthSummaryWidget extends StatelessWidget {
  final CattleIsar cattle;
  final List<HealthRecordIsar> healthRecords;
  final List<TreatmentRecordIsar> treatmentRecords;
  final List<VaccinationRecordIsar> vaccinationRecords;
  final VoidCallback? onTap;

  const HealthSummaryWidget({
    super.key,
    required this.cattle,
    required this.healthRecords,
    required this.treatmentRecords,
    required this.vaccinationRecords,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final latestHealthRecord = _getLatestHealthRecord();
    final activeTreatments = _getActiveTreatments();
    final upcomingVaccinations = _getUpcomingVaccinations();
    final hasEmergencyRecords = _hasEmergencyRecords();
    final hasChronicConditions = _hasChronicConditions();

    return Card(
      margin: const EdgeInsets.all(8.0),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Icon(Icons.pets, color: Colors.blue[600], size: 24),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          cattle.name ?? 'Unknown',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Tag: ${cattle.tagId ?? 'Unknown'}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Health Status Indicator
                  _buildHealthStatusIndicator(latestHealthRecord),
                ],
              ),
              const SizedBox(height: 16),

              // Health Summary Stats
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'Health Records',
                      healthRecords.length.toString(),
                      Icons.medical_information,
                      Colors.red,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatItem(
                      'Treatments',
                      treatmentRecords.length.toString(),
                      Icons.medication,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatItem(
                      'Vaccinations',
                      vaccinationRecords.length.toString(),
                      Icons.vaccines,
                      Colors.green,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Latest Health Information
              if (latestHealthRecord != null) ...[
                _buildLatestHealthInfo(latestHealthRecord),
                const SizedBox(height: 12),
              ],

              // Active Treatments
              if (activeTreatments.isNotEmpty) ...[
                _buildActiveTreatments(activeTreatments),
                const SizedBox(height: 12),
              ],

              // Upcoming Vaccinations
              if (upcomingVaccinations.isNotEmpty) ...[
                _buildUpcomingVaccinations(upcomingVaccinations),
                const SizedBox(height: 12),
              ],

              // Alert Flags
              if (hasEmergencyRecords || hasChronicConditions || activeTreatments.isNotEmpty) ...[
                _buildAlertFlags(hasEmergencyRecords, hasChronicConditions, activeTreatments),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHealthStatusIndicator(HealthRecordIsar? latestRecord) {
    final status = latestRecord?.healthStatus ?? 'Unknown';
    final color = HealthConstants.getHealthStatusColor(status);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        status,
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLatestHealthInfo(HealthRecordIsar record) {
    final conditionColor = HealthConstants.getConditionColor(record.condition);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.history, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 6),
              Text(
                'Latest Health Record',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[700],
                ),
              ),
              const Spacer(),
              Text(
                record.date != null 
                    ? DateFormat('MMM dd').format(record.date!)
                    : 'Unknown',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: conditionColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  record.condition ?? 'Unknown condition',
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          if (record.symptoms != null && record.symptoms!.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              record.symptoms!,
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey[600],
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActiveTreatments(List<TreatmentRecordIsar> treatments) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.medication, size: 16, color: Colors.blue[600]),
              const SizedBox(width: 6),
              Text(
                'Active Treatments (${treatments.length})',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...treatments.take(2).map((treatment) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: Colors.blue[600],
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    treatment.medicationName ?? 'Unknown medication',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ),
          )).toList(),
          if (treatments.length > 2) ...[
            Text(
              '... and ${treatments.length - 2} more',
              style: TextStyle(
                fontSize: 11,
                color: Colors.blue[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildUpcomingVaccinations(List<VaccinationRecordIsar> vaccinations) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.vaccines, size: 16, color: Colors.green[600]),
              const SizedBox(width: 6),
              Text(
                'Upcoming Vaccinations (${vaccinations.length})',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.green[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...vaccinations.take(2).map((vaccination) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: Colors.green[600],
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    vaccination.vaccineName ?? 'Unknown vaccine',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
                Text(
                  vaccination.nextDueDate != null 
                      ? DateFormat('MMM dd').format(vaccination.nextDueDate!)
                      : '',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.green[600],
                  ),
                ),
              ],
            ),
          )).toList(),
        ],
      ),
    );
  }

  Widget _buildAlertFlags(bool hasEmergency, bool hasChronic, List<TreatmentRecordIsar> activeTreatments) {
    return Wrap(
      spacing: 6,
      runSpacing: 4,
      children: [
        if (hasEmergency)
          _buildAlertFlag('Emergency', Colors.red),
        if (hasChronic)
          _buildAlertFlag('Chronic', Colors.purple),
        if (activeTreatments.isNotEmpty)
          _buildAlertFlag('Treatment', Colors.blue),
      ],
    );
  }

  Widget _buildAlertFlag(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  HealthRecordIsar? _getLatestHealthRecord() {
    if (healthRecords.isEmpty) return null;
    
    healthRecords.sort((a, b) {
      final dateA = a.date ?? DateTime(1900);
      final dateB = b.date ?? DateTime(1900);
      return dateB.compareTo(dateA);
    });
    
    return healthRecords.first;
  }

  List<TreatmentRecordIsar> _getActiveTreatments() {
    return treatmentRecords.where((t) => t.status == 'Active').toList();
  }

  List<VaccinationRecordIsar> _getUpcomingVaccinations() {
    final now = DateTime.now();
    final futureDate = now.add(const Duration(days: 30));
    
    return vaccinationRecords.where((v) {
      return v.nextDueDate != null && 
             v.nextDueDate!.isAfter(now) && 
             v.nextDueDate!.isBefore(futureDate);
    }).toList();
  }

  bool _hasEmergencyRecords() {
    return healthRecords.any((h) => h.isEmergency);
  }

  bool _hasChronicConditions() {
    return healthRecords.any((h) => h.isChronic);
  }
}
