// ignore_for_file: deprecated_member_use

import 'dart:io';
import 'package:flutter/material.dart';
// Explicit import for Flutter TextSpan
// Add import for TextSpan disambiguation
import '../models/cattle.dart';
import '../models/breed_category.dart';
import '../models/animal_type.dart';
import '../../../services/database_helper.dart';
import 'cattle_detail_screen.dart';
import '../dialogs/cattle_form_dialog.dart';
import 'package:path_provider/path_provider.dart';
import 'package:excel/excel.dart' hide Border;
import 'package:pdf/pdf.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';
import 'package:pdf/widgets.dart' as pw;

class CattleRecordsScreen extends StatefulWidget {
  const CattleRecordsScreen({Key? key}) : super(key: key);

  @override
  State<CattleRecordsScreen> createState() => _CattleRecordsScreenState();
}

class _CattleRecordsScreenState extends State<CattleRecordsScreen> {
  List<Cattle> _cattle = [];
  List<BreedCategory> _breeds = [];
  List<AnimalType> _animalTypes = [];
  final Set<String> _selectedCattle = {};
  bool _isSelectionMode = false;
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedGender = 'All';
  String _selectedBreed = 'All';
  String _selectedAnimalType = 'All';
  String _selectedAgeFilter = 'All';
  String _sortBy = 'Name';
  bool _sortAscending = true;

  List<Cattle> get filteredCattle {
    List<Cattle> result = List.from(_cattle);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      result = result
          .where((cattle) =>
              cattle.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              cattle.tagId.toLowerCase().contains(_searchQuery.toLowerCase()))
          .toList();
    }

    // Apply gender filter
    if (_selectedGender != 'All') {
      result =
          result.where((cattle) => cattle.gender == _selectedGender).toList();
    }

    // Apply breed filter
    if (_selectedBreed != 'All') {
      result = result
          .where((cattle) =>
              _breeds.firstWhere((b) => b.id == cattle.breedId).name ==
              _selectedBreed)
          .toList();
    }

    // Apply animal type filter
    if (_selectedAnimalType != 'All') {
      result = result
          .where((cattle) =>
              _animalTypes
                  .firstWhere((a) => a.id == cattle.animalTypeId)
                  .name ==
              _selectedAnimalType)
          .toList();
    }

    // Apply age filter
    if (_selectedAgeFilter != 'All') {
      final now = DateTime.now();
      result = result.where((cattle) {
        try {
          final birthDateStr = cattle.dateOfBirth?.toString() ?? '';
          if (birthDateStr.isEmpty) return false;
          final birthDate = DateTime.parse(birthDateStr);
          final age = now.difference(birthDate).inDays;
          switch (_selectedAgeFilter) {
            case 'Under 1 Year':
              return age < 365;
            case '1-2 Years':
              return age >= 365 && age < 730;
            case '2-5 Years':
              return age >= 730 && age < 1825;
            case 'Over 5 Years':
              return age >= 1825;
            default:
              return true;
          }
        } catch (e) {
          return false;
        }
      }).toList();
    }

    // Apply sorting
    result.sort((a, b) {
      int comparison;
      switch (_sortBy) {
        case 'Name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'Tag ID':
          comparison = a.tagId.compareTo(b.tagId);
          break;
        case 'Date of Birth':
          final aDateStr = a.dateOfBirth?.toString() ?? '';
          final bDateStr = b.dateOfBirth?.toString() ?? '';
          final aDate = DateTime.tryParse(aDateStr) ?? DateTime(1900);
          final bDate = DateTime.tryParse(bDateStr) ?? DateTime(1900);
          comparison = aDate.compareTo(bDate);
          break;
        default:
          comparison = 0;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return result;
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    final cattleList = await DatabaseHelper.instance.getAllCattles();
    final breeds = await DatabaseHelper.instance.getCattleBreeds();
    final animalTypes = await DatabaseHelper.instance.getAnimalTypes();

    setState(() {
      _cattle = cattleList;
      _breeds = breeds;
      _animalTypes = animalTypes;
      _isLoading = false;
    });
  }

  void _showAddCattleDialog() {
    showDialog(
      context: context,
      builder: (context) => CattleFormDialog(
        breeds: _breeds,
        animalTypes: _animalTypes,
        existingCattle: _cattle,
        onSave: (cattle) async {
          await DatabaseHelper.instance.createCattle(cattle);
          _loadData(); // Reload the list after adding
        },
      ),
    );
  }

  void _showEditCattleDialog(Cattle cattle) {
    showDialog(
      context: context,
      builder: (context) => CattleFormDialog(
        cattle: cattle, // Pass the existing cattle for editing
        breeds: _breeds,
        animalTypes: _animalTypes,
        existingCattle: _cattle,
        onSave: (updatedCattle) async {
          await DatabaseHelper.instance.updateCattle(updatedCattle);
          _loadData();
        },
      ),
    );
  }

  void _deleteSelectedRecords() {
    final currentContext = context;
    showDialog(
      context: currentContext,
      builder: (context) => AlertDialog(
        title: Text(
            'Delete ${_selectedCattle.isEmpty ? "All" : "Selected"} Records'),
        content: Text(
            'Are you sure you want to delete ${_selectedCattle.isEmpty ? "all" : "${_selectedCattle.length} selected"} records?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    ).then((confirmed) async {
      if (confirmed == true) {
        if (_selectedCattle.isEmpty) {
          await DatabaseHelper.instance.deleteAllCattles();
        } else {
          await DatabaseHelper.instance
              .deleteCattlesByIds(_selectedCattle.toList());
          setState(() {
            _selectedCattle.clear();
            _isSelectionMode = false;
          });
        }
        _loadData();
      }
    });
  }

  Future<String> _exportToCsv() async {
    final directory = await getDownloadsDirectory();
    if (directory == null) {
      throw Exception('Could not access downloads directory');
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final filePath = '${directory.path}/cattle_records_$timestamp.csv';
    final csvData = StringBuffer();
    csvData.writeln('Name,Tag ID,Gender,Animal Type,Date of Birth,Breed');

    final recordsToExport = _selectedCattle.isEmpty
        ? filteredCattle
        : await DatabaseHelper.instance
            .getCattlesByIds(_selectedCattle.toList());

    for (var cattle in recordsToExport) {
      final breed = _breeds.firstWhere((b) => b.id == cattle.breedId);
      final animalType =
          _animalTypes.firstWhere((a) => a.id == cattle.animalTypeId);

      csvData.writeln(
          '${_escapeCsvField(cattle.name)},${_escapeCsvField(cattle.tagId)},'
          '${_escapeCsvField(cattle.gender)},${_escapeCsvField(animalType.name)},'
          '${_escapeCsvField(cattle.dateOfBirth?.toString() ?? '')},${_escapeCsvField(breed.name)}');
    }

    await File(filePath).writeAsString(csvData.toString());
    return filePath;
  }

  Future<String> _exportToExcel() async {
    final directory = await getDownloadsDirectory();
    if (directory == null) {
      throw Exception('Could not access downloads directory');
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final filePath = '${directory.path}/cattle_records_$timestamp.xlsx';
    final excel = Excel.createExcel();
    final sheet = excel['Cattle Records'];

    final headers = [
      'Name',
      'Tag ID',
      'Gender',
      'Animal Type',
      'Date of Birth',
      'Breed'
    ];
    for (var i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = TextCellValue(headers[i]);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Center,
      );
    }

    final recordsToExport = _selectedCattle.isEmpty
        ? filteredCattle
        : await DatabaseHelper.instance
            .getCattlesByIds(_selectedCattle.toList());

    for (var i = 0; i < recordsToExport.length; i++) {
      final cattle = recordsToExport[i];
      final breed = _breeds.firstWhere((b) => b.id == cattle.breedId);
      final animalType =
          _animalTypes.firstWhere((a) => a.id == cattle.animalTypeId);

      final rowData = [
        cattle.name,
        cattle.tagId,
        cattle.gender,
        animalType.name,
        cattle.dateOfBirth?.toString() ?? '',
        breed.name,
      ];

      for (var j = 0; j < rowData.length; j++) {
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: i + 1))
            .value = TextCellValue(rowData[j]);
      }
    }

    final excelFile = File(filePath);
    await excelFile.writeAsBytes(excel.save()!);
    return filePath;
  }

  Future<String> _