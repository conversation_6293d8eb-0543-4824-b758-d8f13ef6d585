import 'package:flutter/material.dart';
import 'package:isar/isar.dart';

part 'chart_data_isar.g.dart';

/// Embedded class for chart data points
@embedded
class ChartDataIsar {
  String? label;
  double? value;
  int? colorValue;
  DateTime? date;

  /// Empty constructor
  ChartDataIsar();
  
  /// Named constructor with parameters
  ChartDataIsar.create({
    this.label,
    this.value,
    this.colorValue,
    this.date,
  });

  /// Convert colorValue to Flutter Color
  @ignore
  Color? get color => colorValue != null ? Color(colorValue!) : null;

  /// Set color from Flutter Color
  set color(Color? value) {
    if (value != null) {
      colorValue = value.value; // ignore: deprecated_member_use (value is correct for storing ARGB int)
    } else {
      colorValue = null;
    }
  }

  /// Convert to the original ChartData class
  Map<String, dynamic> toMap() {
    return {
      'label': label,
      'value': value,
      'colorValue': colorValue,
      'date': date?.toIso8601String(),
    };
  }

  /// Create from Map
  factory ChartDataIsar.fromMap(Map<String, dynamic> map) {
    return ChartDataIsar()
      ..label = map['label'] as String?
      ..value = map['value'] as double?
      ..colorValue = map['colorValue'] as int?
      ..date = map['date'] != null ? DateTime.parse(map['date'] as String) : null;
  }
} 