import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/event.dart';
import '../../../services/database_helper.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class EventHistoryTab extends StatefulWidget {
  const EventHistoryTab({Key? key}) : super(key: key);

  @override
  State<EventHistoryTab> createState() => _EventHistoryTabState();
}

class _EventHistoryTabState extends State<EventHistoryTab> {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  List<FarmEvent> _events = [];
  List<FarmEvent> _filteredEvents = [];
  bool _isLoading = true;
  
  // Filter states
  String? _selectedCattleId;
  DateTimeRange? _selectedDateRange;
  EventType? _selectedEventType;
  String _searchQuery = '';

  // Chart view states
  bool _showCharts = false;
  String _selectedChartType = 'pie'; // 'pie' or 'line'

  @override
  void initState() {
    super.initState();
    _loadEvents();
    _setupFarmChangeListener();
  }

  @override
  void dispose() {
    _dbHelper.removeListener(_onFarmChanged);
    super.dispose();
  }

  void _setupFarmChangeListener() {
    _dbHelper.addListener(_onFarmChanged);
  }

  void _onFarmChanged() {
    _loadEvents();
  }

  Future<void> _loadEvents() async {
    try {
      setState(() => _isLoading = true);

      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) {
        setState(() {
          _events = [];
          _filteredEvents = [];
          _isLoading = false;
        });
        return;
      }

      final prefs = await SharedPreferences.getInstance();
      final key = '${selectedFarmId}_events';
      final eventsJson = prefs.getStringList(key) ?? [];
      final events = eventsJson
          .map((json) => FarmEvent.fromMap(jsonDecode(json)))
          .where((event) => event.isCompleted)
          .toList();

      setState(() {
        _events = events;
        _applyFilters();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading events: $e');
      setState(() {
        _events = [];
        _filteredEvents = [];
        _isLoading = false;
      });
    }
  }

  void _applyFilters() {
    var filtered = List<FarmEvent>.from(_events);

    // Apply cattle filter
    if (_selectedCattleId != null) {
      filtered = filtered.where((e) => e.cattleId == _selectedCattleId).toList();
    }

    // Apply date range filter
    if (_selectedDateRange != null) {
      filtered = filtered.where((e) {
        final eventDate = DateTime(e.date.year, e.date.month, e.date.day);
        return eventDate.isAfter(_selectedDateRange!.start.subtract(const Duration(days: 1))) &&
               eventDate.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
    }

    // Apply event type filter
    if (_selectedEventType != null) {
      filtered = filtered.where((e) => e.type == _selectedEventType).toList();
    }

    // Apply search query
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((e) =>
        e.title.toLowerCase().contains(query) ||
        e.description.toLowerCase().contains(query)
      ).toList();
    }

    setState(() => _filteredEvents = filtered);
  }

  Widget _buildFilterBar() {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      hintText: 'Search events...',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                        _applyFilters();
                      });
                    },
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: Icon(_showCharts ? Icons.list : Icons.bar_chart),
                  onPressed: () => setState(() => _showCharts = !_showCharts),
                  tooltip: _showCharts ? 'Show List' : 'Show Charts',
                ),
              ],
            ),
            SizedBox(height: ResponsiveSpacing.getSM(context)),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // Cattle filter
                  DropdownButton<String>(
                    hint: const Text('Select Cattle'),
                    value: _selectedCattleId,
                    items: [
                      const DropdownMenuItem(
                        value: null,
                        child: Text('All Cattle'),
                      ),
                      ..._events
                          .map((e) => e.cattleId)
                          .toSet()
                          .map((id) => DropdownMenuItem(
                                value: id,
                                child: Text('Cattle #$id'),
                              )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedCattleId = value;
                        _applyFilters();
                      });
                    },
                  ),
                  const SizedBox(width: 16),
                  // Event type filter
                  DropdownButton<EventType>(
                    hint: const Text('Event Type'),
                    value: _selectedEventType,
                    items: [
                      const DropdownMenuItem(
                        value: null,
                        child: Text('All Types'),
                      ),
                      ...EventType.values.map((type) => DropdownMenuItem(
                            value: type,
                            child: Text(type.toString().split('.').last),
                          )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedEventType = value;
                        _applyFilters();
                      });
                    },
                  ),
                  const SizedBox(width: 16),
                  // Date range picker
                  TextButton.icon(
                    icon: const Icon(Icons.date_range),
                    label: Text(
                      _selectedDateRange == null
                          ? 'Select Date Range'
                          : '${_selectedDateRange!.start.toString().split(' ')[0]} to ${_selectedDateRange!.end.toString().split(' ')[0]}',
                    ),
                    onPressed: () async {
                      final picked = await showDateRangePicker(
                        context: context,
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now(),
                        initialDateRange: _selectedDateRange,
                      );
                      if (picked != null) {
                        setState(() {
                          _selectedDateRange = picked;
                          _applyFilters();
                        });
                      }
                    },
                  ),
                  if (_selectedDateRange != null ||
                      _selectedEventType != null ||
                      _selectedCattleId != null) ...[
                    const SizedBox(width: 16),
                    TextButton.icon(
                      icon: const Icon(Icons.clear),
                      label: const Text('Clear Filters'),
                      onPressed: () {
                        setState(() {
                          _selectedDateRange = null;
                          _selectedEventType = null;
                          _selectedCattleId = null;
                          _searchQuery = '';
                          _applyFilters();
                        });
                      },
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEventList() {
    if (_filteredEvents.isEmpty) {
      return const Center(
        child: Text(
          'No completed events found matching the filters',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return ListView.builder(
      itemCount: _filteredEvents.length,
      itemBuilder: (context, index) {
        final event = _filteredEvents[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.blue.shade100,
              child: Icon(
                _getEventTypeIcon(event.type),
                color: Colors.blue.shade700,
              ),
            ),
            title: Text(event.title),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(event.description),
                const SizedBox(height: 4),
                Text(
                  'Completed on: ${event.completedAt?.toString().split('.')[0] ?? 'Unknown'}',
                  style: TextStyle(color: Colors.green.shade700),
                ),
              ],
            ),
            trailing: Text('Cattle #${event.cattleId}'),
          ),
        );
      },
    );
  }

  Widget _buildCharts() {
    if (_filteredEvents.isEmpty) {
      return const Center(
        child: Text(
          'No data available for charts',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return Column(
      children: [
        // Chart type selector
        SegmentedButton<String>(
          segments: const [
            ButtonSegment(
              value: 'pie',
              icon: Icon(Icons.pie_chart),
              label: Text('Event Types'),
            ),
            ButtonSegment(
              value: 'line',
              icon: Icon(Icons.show_chart),
              label: Text('Event Trends'),
            ),
          ],
          selected: {_selectedChartType},
          onSelectionChanged: (Set<String> newSelection) {
            setState(() => _selectedChartType = newSelection.first);
          },
        ),
        SizedBox(height: ResponsiveSpacing.getMD(context)),
        Expanded(
          child: _selectedChartType == 'pie'
              ? _buildPieChart()
              : _buildLineChart(),
        ),
      ],
    );
  }

  Widget _buildPieChart() {
    // Group events by type
    final eventsByType = <EventType, int>{};
    for (final event in _filteredEvents) {
      eventsByType[event.type] = (eventsByType[event.type] ?? 0) + 1;
    }

    final sections = eventsByType.entries.map((entry) {
      final color = _getEventTypeColor(entry.key);
      return PieChartSectionData(
        value: entry.value.toDouble(),
        title: '${entry.value}\n${entry.key.toString().split('.').last}',
        radius: 100,
        color: color,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();

    return PieChart(
      PieChartData(
        sections: sections,
        centerSpaceRadius: 0,
        sectionsSpace: 2,
      ),
    );
  }

  Widget _buildLineChart() {
    // Group events by date
    final eventsByDate = <DateTime, int>{};
    for (final event in _filteredEvents) {
      final date = DateTime(event.date.year, event.date.month, event.date.day);
      eventsByDate[date] = (eventsByDate[date] ?? 0) + 1;
    }

    // Sort dates
    final sortedDates = eventsByDate.keys.toList()..sort();
    
    // Create spots for the line chart
    final spots = sortedDates.asMap().entries.map((entry) {
      return FlSpot(
        entry.key.toDouble(),
        eventsByDate[entry.value]!.toDouble(),
      );
    }).toList();

    return LineChart(
      LineChartData(
        gridData: const FlGridData(show: true),
        titlesData: FlTitlesData(
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < sortedDates.length) {
                  final date = sortedDates[value.toInt()];
                  return Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: Text(
                      '${date.month}/${date.day}',
                      style: const TextStyle(fontSize: 10),
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: const TextStyle(fontSize: 10),
                );
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: Colors.blue,
            barWidth: 3,
            dotData: const FlDotData(show: true),
          ),
        ],
      ),
    );
  }

  IconData _getEventTypeIcon(EventType type) {
    switch (type) {
      case EventType.breeding:
        return Icons.favorite;
      case EventType.pregnancyCheck:
        return Icons.pregnant_woman;
      case EventType.calving:
        return Icons.child_care;
      case EventType.vaccination:
        return Icons.medical_services;
      case EventType.healthCheckup:
        return Icons.local_hospital;
      case EventType.weightMeasurement:
        return Icons.monitor_weight;
      case EventType.deworming:
        return Icons.bug_report;
      case EventType.purchased:
        return Icons.shopping_cart;
      case EventType.sold:
        return Icons.attach_money;
      case EventType.miscellaneous:
        return Icons.more_horiz;
      case EventType.custom:
        return Icons.category;
      case EventType.dryOff:
        return Icons.opacity;
    }
  }

  Color _getEventTypeColor(EventType type) {
    switch (type) {
      case EventType.breeding:
        return Colors.pink;
      case EventType.pregnancyCheck:
        return Colors.purple;
      case EventType.calving:
        return Colors.blue;
      case EventType.vaccination:
        return Colors.green;
      case EventType.healthCheckup:
        return Colors.red;
      case EventType.weightMeasurement:
        return Colors.orange;
      case EventType.deworming:
        return Colors.brown;
      case EventType.purchased:
        return Colors.teal;
      case EventType.sold:
        return Colors.indigo;
      case EventType.miscellaneous:
        return Colors.grey;
      case EventType.custom:
        return Colors.cyan;
      case EventType.dryOff:
        return Colors.amber;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        _buildFilterBar(),
        Expanded(
          child: