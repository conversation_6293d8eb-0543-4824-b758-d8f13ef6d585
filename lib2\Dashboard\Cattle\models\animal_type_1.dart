import 'package:flutter/material.dart';

class AnimalType {
  final String id;
  final String name;
  final IconData icon;
  final int defaultGestationDays;
  final int defaultHeatCycleDays;
  final DateTime createdAt;
  final DateTime updatedAt;

  AnimalType({
    required this.id,
    required this.name,
    required this.icon,
    required this.defaultGestationDays,
    required this.defaultHeatCycleDays,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  factory AnimalType.fromMap(Map<String, dynamic> map) {
    return AnimalType(
      id: map['id'] as String,
      name: map['name'] as String,
      icon: IconData(
        map['iconCodePoint'] as int,
        fontFamily: map['iconFontFamily'] as String? ?? 'MaterialIcons',
      ),
      defaultGestationDays: map['defaultGestationDays'] as int,
      defaultHeatCycleDays: map['defaultHeatCycleDays'] as int,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'iconCodePoint': icon.codePoint,
      'iconFontFamily': icon.fontFamily,
      'defaultGestationDays': defaultGestationDays,
      'defaultHeatCycleDays': defaultHeatCycleDays,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  AnimalType copyWith({
    String? id,
    String? name,
    IconData? icon,
    int? defaultGestationDays,
    int? defaultHeatCycleDays,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AnimalType(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      defaultGestationDays: defaultGestationDays ?? this.defaultGestationDays,
      defaultHeatCycleDays: defaultHeatCycleDays ?? this.defaultHeatCycleDays,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  static const IconData defaultIcon = Icons.pets;

  static final List<AnimalType> defaultTypes = [
    AnimalType(
      id: 'cattle',
      name: 'Cattle',
      icon: defaultIcon,
      defaultGestationDays: 280,
      defaultHeatCycleDays: 21,
    ),
    AnimalType(
      id: 'buffalo',
      name: 'Buffalo',
      icon: defaultIcon,
      defaultGestationDays: 310,
      defaultHeatCycleDays: 21,
    ),
    AnimalType(
      id: 'goat',
      name: 'Goat',
      icon: defaultIcon,
      defaultGestationDays: 150,
      defaultHeatCycleDays: 21,
    ),
    AnimalType(
      id: 'sheep',
      name: 'Sheep',
      icon: defaultIcon,
      defaultGestationDays: 150,
      defaultHeatCycleDays: 16,
    ),
  ];
}
