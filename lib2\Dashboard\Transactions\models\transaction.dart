import 'package:hive/hive.dart';
import 'package:intl/intl.dart';

part 'transaction.g.dart';

@HiveType(typeId: 0)
class Transaction extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String type;

  @HiveField(2)
  String category;

  @HiveField(3)
  double amount;

  @HiveField(4)
  DateTime date;

  @HiveField(5)
  String description;

  @HiveField(6)
  String? paymentMethod;

  Transaction({
    required this.id,
    required this.type,
    required this.category,
    required this.amount,
    required this.date,
    required this.description,
    this.paymentMethod,
  });

  String get formattedDate => DateFormat('MMM dd, yyyy').format(date);
  String get formattedAmount =>
      NumberFormat.currency(symbol: '\$').format(amount);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'type': type,
      'category': category,
      'amount': amount,
      'paymentMethod': paymentMethod,
      'description': description,
    };
  }

  factory Transaction.fromMap(Map<String, dynamic> map) {
    return Transaction(
      id: map['id'],
      date: DateTime.parse(map['date']),
      type: map['type'],
      category: map['category'],
      amount: map['amount'],
      description: map['description'],
      paymentMethod: map['paymentMethod'] ?? '',
    );
  }

  void updateFrom(Transaction other) {
    id = other.id;
    type = other.type;
    category = other.category;
    amount = other.amount;
    date = other.date;
    description = other.description;
    paymentMethod = other.paymentMethod;
    save();
  }
}
