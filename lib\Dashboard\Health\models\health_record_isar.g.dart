// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'health_record_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetHealthRecordIsarCollection on Isar {
  IsarCollection<HealthRecordIsar> get healthRecordIsars => this.collection();
}

const HealthRecordIsarSchema = CollectionSchema(
  name: r'HealthRecordIsar',
  id: -8833675558909441122,
  properties: {
    r'attachments': PropertySchema(
      id: 0,
      name: r'attachments',
      type: IsarType.stringList,
    ),
    r'bodyConditionScore': PropertySchema(
      id: 1,
      name: r'bodyConditionScore',
      type: IsarType.double,
    ),
    r'businessId': PropertySchema(
      id: 2,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'cattleBusinessId': PropertySchema(
      id: 3,
      name: r'cattleBusinessId',
      type: IsarType.string,
    ),
    r'cattleName': PropertySchema(
      id: 4,
      name: r'cattleName',
      type: IsarType.string,
    ),
    r'cattleTagId': PropertySchema(
      id: 5,
      name: r'cattleTagId',
      type: IsarType.string,
    ),
    r'condition': PropertySchema(
      id: 6,
      name: r'condition',
      type: IsarType.string,
    ),
    r'createdAt': PropertySchema(
      id: 7,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'createdBy': PropertySchema(
      id: 8,
      name: r'createdBy',
      type: IsarType.string,
    ),
    r'date': PropertySchema(
      id: 9,
      name: r'date',
      type: IsarType.dateTime,
    ),
    r'diagnosis': PropertySchema(
      id: 10,
      name: r'diagnosis',
      type: IsarType.string,
    ),
    r'dosage': PropertySchema(
      id: 11,
      name: r'dosage',
      type: IsarType.string,
    ),
    r'duration': PropertySchema(
      id: 12,
      name: r'duration',
      type: IsarType.string,
    ),
    r'farmBusinessId': PropertySchema(
      id: 13,
      name: r'farmBusinessId',
      type: IsarType.string,
    ),
    r'followUpDate': PropertySchema(
      id: 14,
      name: r'followUpDate',
      type: IsarType.dateTime,
    ),
    r'frequency': PropertySchema(
      id: 15,
      name: r'frequency',
      type: IsarType.string,
    ),
    r'healthStatus': PropertySchema(
      id: 16,
      name: r'healthStatus',
      type: IsarType.string,
    ),
    r'heartRate': PropertySchema(
      id: 17,
      name: r'heartRate',
      type: IsarType.long,
    ),
    r'isChronic': PropertySchema(
      id: 18,
      name: r'isChronic',
      type: IsarType.bool,
    ),
    r'isEmergency': PropertySchema(
      id: 19,
      name: r'isEmergency',
      type: IsarType.bool,
    ),
    r'isUnderTreatment': PropertySchema(
      id: 20,
      name: r'isUnderTreatment',
      type: IsarType.bool,
    ),
    r'medication': PropertySchema(
      id: 21,
      name: r'medication',
      type: IsarType.string,
    ),
    r'notes': PropertySchema(
      id: 22,
      name: r'notes',
      type: IsarType.string,
    ),
    r'recordType': PropertySchema(
      id: 23,
      name: r'recordType',
      type: IsarType.string,
    ),
    r'recoveryDate': PropertySchema(
      id: 24,
      name: r'recoveryDate',
      type: IsarType.dateTime,
    ),
    r'requiresFollowUp': PropertySchema(
      id: 25,
      name: r'requiresFollowUp',
      type: IsarType.bool,
    ),
    r'respiratoryRate': PropertySchema(
      id: 26,
      name: r'respiratoryRate',
      type: IsarType.long,
    ),
    r'severity': PropertySchema(
      id: 27,
      name: r'severity',
      type: IsarType.string,
    ),
    r'symptoms': PropertySchema(
      id: 28,
      name: r'symptoms',
      type: IsarType.string,
    ),
    r'temperature': PropertySchema(
      id: 29,
      name: r'temperature',
      type: IsarType.double,
    ),
    r'treatment': PropertySchema(
      id: 30,
      name: r'treatment',
      type: IsarType.string,
    ),
    r'treatmentCost': PropertySchema(
      id: 31,
      name: r'treatmentCost',
      type: IsarType.double,
    ),
    r'updatedAt': PropertySchema(
      id: 32,
      name: r'updatedAt',
      type: IsarType.dateTime,
    ),
    r'updatedBy': PropertySchema(
      id: 33,
      name: r'updatedBy',
      type: IsarType.string,
    ),
    r'veterinarian': PropertySchema(
      id: 34,
      name: r'veterinarian',
      type: IsarType.string,
    ),
    r'veterinarianContact': PropertySchema(
      id: 35,
      name: r'veterinarianContact',
      type: IsarType.string,
    ),
    r'weight': PropertySchema(
      id: 36,
      name: r'weight',
      type: IsarType.double,
    )
  },
  estimateSize: _healthRecordIsarEstimateSize,
  serialize: _healthRecordIsarSerialize,
  deserialize: _healthRecordIsarDeserialize,
  deserializeProp: _healthRecordIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'farmBusinessId': IndexSchema(
      id: -7111559275757090744,
      name: r'farmBusinessId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'farmBusinessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'cattleBusinessId': IndexSchema(
      id: -1530790847330223488,
      name: r'cattleBusinessId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'cattleBusinessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'cattleTagId': IndexSchema(
      id: -2283963072638323009,
      name: r'cattleTagId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'cattleTagId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'recordType': IndexSchema(
      id: 1442468683301829598,
      name: r'recordType',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'recordType',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'condition': IndexSchema(
      id: -8516742099328010389,
      name: r'condition',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'condition',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'healthStatus': IndexSchema(
      id: 8423004856967894632,
      name: r'healthStatus',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'healthStatus',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'severity': IndexSchema(
      id: -5078743595604092464,
      name: r'severity',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'severity',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _healthRecordIsarGetId,
  getLinks: _healthRecordIsarGetLinks,
  attach: _healthRecordIsarAttach,
  version: '3.1.0+1',
);

int _healthRecordIsarEstimateSize(
  HealthRecordIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final list = object.attachments;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.cattleBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.cattleName;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.cattleTagId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.condition;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.createdBy;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.diagnosis;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.dosage;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.duration;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.farmBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.frequency;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.healthStatus;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.medication;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.notes;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.recordType;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.severity;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.symptoms;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.treatment;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.updatedBy;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.veterinarian;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.veterinarianContact;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _healthRecordIsarSerialize(
  HealthRecordIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeStringList(offsets[0], object.attachments);
  writer.writeDouble(offsets[1], object.bodyConditionScore);
  writer.writeString(offsets[2], object.businessId);
  writer.writeString(offsets[3], object.cattleBusinessId);
  writer.writeString(offsets[4], object.cattleName);
  writer.writeString(offsets[5], object.cattleTagId);
  writer.writeString(offsets[6], object.condition);
  writer.writeDateTime(offsets[7], object.createdAt);
  writer.writeString(offsets[8], object.createdBy);
  writer.writeDateTime(offsets[9], object.date);
  writer.writeString(offsets[10], object.diagnosis);
  writer.writeString(offsets[11], object.dosage);
  writer.writeString(offsets[12], object.duration);
  writer.writeString(offsets[13], object.farmBusinessId);
  writer.writeDateTime(offsets[14], object.followUpDate);
  writer.writeString(offsets[15], object.frequency);
  writer.writeString(offsets[16], object.healthStatus);
  writer.writeLong(offsets[17], object.heartRate);
  writer.writeBool(offsets[18], object.isChronic);
  writer.writeBool(offsets[19], object.isEmergency);
  writer.writeBool(offsets[20], object.isUnderTreatment);
  writer.writeString(offsets[21], object.medication);
  writer.writeString(offsets[22], object.notes);
  writer.writeString(offsets[23], object.recordType);
  writer.writeDateTime(offsets[24], object.recoveryDate);
  writer.writeBool(offsets[25], object.requiresFollowUp);
  writer.writeLong(offsets[26], object.respiratoryRate);
  writer.writeString(offsets[27], object.severity);
  writer.writeString(offsets[28], object.symptoms);
  writer.writeDouble(offsets[29], object.temperature);
  writer.writeString(offsets[30], object.treatment);
  writer.writeDouble(offsets[31], object.treatmentCost);
  writer.writeDateTime(offsets[32], object.updatedAt);
  writer.writeString(offsets[33], object.updatedBy);
  writer.writeString(offsets[34], object.veterinarian);
  writer.writeString(offsets[35], object.veterinarianContact);
  writer.writeDouble(offsets[36], object.weight);
}

HealthRecordIsar _healthRecordIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = HealthRecordIsar();
  object.attachments = reader.readStringList(offsets[0]);
  object.bodyConditionScore = reader.readDoubleOrNull(offsets[1]);
  object.businessId = reader.readStringOrNull(offsets[2]);
  object.cattleBusinessId = reader.readStringOrNull(offsets[3]);
  object.cattleName = reader.readStringOrNull(offsets[4]);
  object.cattleTagId = reader.readStringOrNull(offsets[5]);
  object.condition = reader.readStringOrNull(offsets[6]);
  object.createdAt = reader.readDateTimeOrNull(offsets[7]);
  object.createdBy = reader.readStringOrNull(offsets[8]);
  object.date = reader.readDateTimeOrNull(offsets[9]);
  object.diagnosis = reader.readStringOrNull(offsets[10]);
  object.dosage = reader.readStringOrNull(offsets[11]);
  object.duration = reader.readStringOrNull(offsets[12]);
  object.farmBusinessId = reader.readStringOrNull(offsets[13]);
  object.followUpDate = reader.readDateTimeOrNull(offsets[14]);
  object.frequency = reader.readStringOrNull(offsets[15]);
  object.healthStatus = reader.readStringOrNull(offsets[16]);
  object.heartRate = reader.readLongOrNull(offsets[17]);
  object.id = id;
  object.isChronic = reader.readBool(offsets[18]);
  object.isEmergency = reader.readBool(offsets[19]);
  object.isUnderTreatment = reader.readBool(offsets[20]);
  object.medication = reader.readStringOrNull(offsets[21]);
  object.notes = reader.readStringOrNull(offsets[22]);
  object.recordType = reader.readStringOrNull(offsets[23]);
  object.recoveryDate = reader.readDateTimeOrNull(offsets[24]);
  object.requiresFollowUp = reader.readBool(offsets[25]);
  object.respiratoryRate = reader.readLongOrNull(offsets[26]);
  object.severity = reader.readStringOrNull(offsets[27]);
  object.symptoms = reader.readStringOrNull(offsets[28]);
  object.temperature = reader.readDoubleOrNull(offsets[29]);
  object.treatment = reader.readStringOrNull(offsets[30]);
  object.treatmentCost = reader.readDoubleOrNull(offsets[31]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[32]);
  object.updatedBy = reader.readStringOrNull(offsets[33]);
  object.veterinarian = reader.readStringOrNull(offsets[34]);
  object.veterinarianContact = reader.readStringOrNull(offsets[35]);
  object.weight = reader.readDoubleOrNull(offsets[36]);
  return object;
}

P _healthRecordIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringList(offset)) as P;
    case 1:
      return (reader.readDoubleOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 8:
      return (reader.readStringOrNull(offset)) as P;
    case 9:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readStringOrNull(offset)) as P;
    case 12:
      return (reader.readStringOrNull(offset)) as P;
    case 13:
      return (reader.readStringOrNull(offset)) as P;
    case 14:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 15:
      return (reader.readStringOrNull(offset)) as P;
    case 16:
      return (reader.readStringOrNull(offset)) as P;
    case 17:
      return (reader.readLongOrNull(offset)) as P;
    case 18:
      return (reader.readBool(offset)) as P;
    case 19:
      return (reader.readBool(offset)) as P;
    case 20:
      return (reader.readBool(offset)) as P;
    case 21:
      return (reader.readStringOrNull(offset)) as P;
    case 22:
      return (reader.readStringOrNull(offset)) as P;
    case 23:
      return (reader.readStringOrNull(offset)) as P;
    case 24:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 25:
      return (reader.readBool(offset)) as P;
    case 26:
      return (reader.readLongOrNull(offset)) as P;
    case 27:
      return (reader.readStringOrNull(offset)) as P;
    case 28:
      return (reader.readStringOrNull(offset)) as P;
    case 29:
      return (reader.readDoubleOrNull(offset)) as P;
    case 30:
      return (reader.readStringOrNull(offset)) as P;
    case 31:
      return (reader.readDoubleOrNull(offset)) as P;
    case 32:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 33:
      return (reader.readStringOrNull(offset)) as P;
    case 34:
      return (reader.readStringOrNull(offset)) as P;
    case 35:
      return (reader.readStringOrNull(offset)) as P;
    case 36:
      return (reader.readDoubleOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _healthRecordIsarGetId(HealthRecordIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _healthRecordIsarGetLinks(HealthRecordIsar object) {
  return [];
}

void _healthRecordIsarAttach(
    IsarCollection<dynamic> col, Id id, HealthRecordIsar object) {
  object.id = id;
}

extension HealthRecordIsarByIndex on IsarCollection<HealthRecordIsar> {
  Future<HealthRecordIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  HealthRecordIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<HealthRecordIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<HealthRecordIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(HealthRecordIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(HealthRecordIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<HealthRecordIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<HealthRecordIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension HealthRecordIsarQueryWhereSort
    on QueryBuilder<HealthRecordIsar, HealthRecordIsar, QWhere> {
  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension HealthRecordIsarQueryWhere
    on QueryBuilder<HealthRecordIsar, HealthRecordIsar, QWhereClause> {
  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'farmBusinessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      farmBusinessIdEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [farmBusinessId],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      farmBusinessIdNotEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      cattleBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleBusinessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      cattleBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cattleBusinessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      cattleBusinessIdEqualTo(String? cattleBusinessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleBusinessId',
        value: [cattleBusinessId],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      cattleBusinessIdNotEqualTo(String? cattleBusinessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleBusinessId',
              lower: [],
              upper: [cattleBusinessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleBusinessId',
              lower: [cattleBusinessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleBusinessId',
              lower: [cattleBusinessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleBusinessId',
              lower: [],
              upper: [cattleBusinessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      cattleTagIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleTagId',
        value: [null],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      cattleTagIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cattleTagId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      cattleTagIdEqualTo(String? cattleTagId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleTagId',
        value: [cattleTagId],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      cattleTagIdNotEqualTo(String? cattleTagId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [],
              upper: [cattleTagId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [cattleTagId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [cattleTagId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [],
              upper: [cattleTagId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      recordTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'recordType',
        value: [null],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      recordTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'recordType',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      recordTypeEqualTo(String? recordType) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'recordType',
        value: [recordType],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      recordTypeNotEqualTo(String? recordType) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'recordType',
              lower: [],
              upper: [recordType],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'recordType',
              lower: [recordType],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'recordType',
              lower: [recordType],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'recordType',
              lower: [],
              upper: [recordType],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      conditionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'condition',
        value: [null],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      conditionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'condition',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      conditionEqualTo(String? condition) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'condition',
        value: [condition],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      conditionNotEqualTo(String? condition) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'condition',
              lower: [],
              upper: [condition],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'condition',
              lower: [condition],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'condition',
              lower: [condition],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'condition',
              lower: [],
              upper: [condition],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      healthStatusIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'healthStatus',
        value: [null],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      healthStatusIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'healthStatus',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      healthStatusEqualTo(String? healthStatus) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'healthStatus',
        value: [healthStatus],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      healthStatusNotEqualTo(String? healthStatus) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'healthStatus',
              lower: [],
              upper: [healthStatus],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'healthStatus',
              lower: [healthStatus],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'healthStatus',
              lower: [healthStatus],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'healthStatus',
              lower: [],
              upper: [healthStatus],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      severityIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'severity',
        value: [null],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      severityIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'severity',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      severityEqualTo(String? severity) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'severity',
        value: [severity],
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterWhereClause>
      severityNotEqualTo(String? severity) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'severity',
              lower: [],
              upper: [severity],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'severity',
              lower: [severity],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'severity',
              lower: [severity],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'severity',
              lower: [],
              upper: [severity],
              includeUpper: false,
            ));
      }
    });
  }
}

extension HealthRecordIsarQueryFilter
    on QueryBuilder<HealthRecordIsar, HealthRecordIsar, QFilterCondition> {
  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'attachments',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'attachments',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'attachments',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'attachments',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'attachments',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'attachments',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'attachments',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'attachments',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'attachments',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsElementMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'attachments',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'attachments',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'attachments',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'attachments',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'attachments',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'attachments',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'attachments',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'attachments',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      attachmentsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'attachments',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      bodyConditionScoreIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'bodyConditionScore',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      bodyConditionScoreIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'bodyConditionScore',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      bodyConditionScoreEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'bodyConditionScore',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      bodyConditionScoreGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'bodyConditionScore',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      bodyConditionScoreLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'bodyConditionScore',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      bodyConditionScoreBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'bodyConditionScore',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cattleBusinessId',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cattleBusinessId',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cattleBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cattleBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cattleBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleNameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cattleName',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleNameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cattleName',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleNameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleNameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cattleName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleNameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cattleName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleNameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cattleName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cattleName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cattleName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleNameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cattleName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleNameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cattleName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleName',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cattleName',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleTagIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cattleTagId',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleTagIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cattleTagId',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleTagIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleTagIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleTagIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleTagIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cattleTagId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleTagIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleTagIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleTagIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleTagIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cattleTagId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleTagIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleTagId',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      cattleTagIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cattleTagId',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      conditionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'condition',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      conditionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'condition',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      conditionEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'condition',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      conditionGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'condition',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      conditionLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'condition',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      conditionBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'condition',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      conditionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'condition',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      conditionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'condition',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      conditionContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'condition',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      conditionMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'condition',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      conditionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'condition',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      conditionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'condition',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdByIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdBy',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdByIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdBy',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdByEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdByGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdByLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdByBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdBy',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdByStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdByEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdByContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdByMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'createdBy',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdByIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdBy',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      createdByIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'createdBy',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'date',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'date',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'date',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      diagnosisIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'diagnosis',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      diagnosisIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'diagnosis',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      diagnosisEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'diagnosis',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      diagnosisGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'diagnosis',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      diagnosisLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'diagnosis',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      diagnosisBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'diagnosis',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      diagnosisStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'diagnosis',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      diagnosisEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'diagnosis',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      diagnosisContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'diagnosis',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      diagnosisMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'diagnosis',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      diagnosisIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'diagnosis',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      diagnosisIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'diagnosis',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dosageIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dosage',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dosageIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dosage',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dosageEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dosage',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dosageGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dosage',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dosageLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dosage',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dosageBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dosage',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dosageStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'dosage',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dosageEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'dosage',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dosageContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'dosage',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dosageMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'dosage',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dosageIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dosage',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      dosageIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'dosage',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      durationIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'duration',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      durationIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'duration',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      durationEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'duration',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      durationGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'duration',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      durationLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'duration',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      durationBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'duration',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      durationStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'duration',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      durationEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'duration',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      durationContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'duration',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      durationMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'duration',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      durationIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'duration',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      durationIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'duration',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      farmBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      farmBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      farmBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      farmBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'farmBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      farmBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      farmBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      farmBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      farmBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'farmBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      farmBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      farmBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      followUpDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'followUpDate',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      followUpDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'followUpDate',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      followUpDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'followUpDate',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      followUpDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'followUpDate',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      followUpDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'followUpDate',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      followUpDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'followUpDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      frequencyIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'frequency',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      frequencyIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'frequency',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      frequencyEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'frequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      frequencyGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'frequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      frequencyLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'frequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      frequencyBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'frequency',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      frequencyStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'frequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      frequencyEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'frequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      frequencyContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'frequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      frequencyMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'frequency',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      frequencyIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'frequency',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      frequencyIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'frequency',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      healthStatusIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'healthStatus',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      healthStatusIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'healthStatus',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      healthStatusEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'healthStatus',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      healthStatusGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'healthStatus',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      healthStatusLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'healthStatus',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      healthStatusBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'healthStatus',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      healthStatusStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'healthStatus',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      healthStatusEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'healthStatus',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      healthStatusContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'healthStatus',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      healthStatusMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'healthStatus',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      healthStatusIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'healthStatus',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      healthStatusIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'healthStatus',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      heartRateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'heartRate',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      heartRateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'heartRate',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      heartRateEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'heartRate',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      heartRateGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'heartRate',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      heartRateLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'heartRate',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      heartRateBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'heartRate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      isChronicEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isChronic',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      isEmergencyEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isEmergency',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      isUnderTreatmentEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isUnderTreatment',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      medicationIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'medication',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      medicationIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'medication',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      medicationEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'medication',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      medicationGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'medication',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      medicationLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'medication',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      medicationBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'medication',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      medicationStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'medication',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      medicationEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'medication',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      medicationContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'medication',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      medicationMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'medication',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      medicationIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'medication',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      medicationIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'medication',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      notesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      notesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      notesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      notesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      notesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      notesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'notes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      notesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      notesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      notesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      notesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'notes',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      notesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      notesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recordTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'recordType',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recordTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'recordType',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recordTypeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'recordType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recordTypeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'recordType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recordTypeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'recordType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recordTypeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'recordType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recordTypeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'recordType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recordTypeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'recordType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recordTypeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'recordType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recordTypeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'recordType',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recordTypeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'recordType',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recordTypeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'recordType',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recoveryDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'recoveryDate',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recoveryDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'recoveryDate',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recoveryDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'recoveryDate',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recoveryDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'recoveryDate',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recoveryDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'recoveryDate',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      recoveryDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'recoveryDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      requiresFollowUpEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'requiresFollowUp',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      respiratoryRateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'respiratoryRate',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      respiratoryRateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'respiratoryRate',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      respiratoryRateEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'respiratoryRate',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      respiratoryRateGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'respiratoryRate',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      respiratoryRateLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'respiratoryRate',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      respiratoryRateBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'respiratoryRate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      severityIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'severity',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      severityIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'severity',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      severityEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'severity',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      severityGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'severity',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      severityLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'severity',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      severityBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'severity',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      severityStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'severity',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      severityEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'severity',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      severityContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'severity',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      severityMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'severity',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      severityIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'severity',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      severityIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'severity',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      symptomsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'symptoms',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      symptomsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'symptoms',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      symptomsEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'symptoms',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      symptomsGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'symptoms',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      symptomsLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'symptoms',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      symptomsBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'symptoms',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      symptomsStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'symptoms',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      symptomsEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'symptoms',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      symptomsContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'symptoms',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      symptomsMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'symptoms',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      symptomsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'symptoms',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      symptomsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'symptoms',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      temperatureIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'temperature',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      temperatureIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'temperature',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      temperatureEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'temperature',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      temperatureGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'temperature',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      temperatureLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'temperature',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      temperatureBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'temperature',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'treatment',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'treatment',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'treatment',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'treatment',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'treatment',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'treatment',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'treatment',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'treatment',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'treatment',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'treatment',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'treatment',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'treatment',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentCostIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'treatmentCost',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentCostIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'treatmentCost',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentCostEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'treatmentCost',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentCostGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'treatmentCost',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentCostLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'treatmentCost',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      treatmentCostBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'treatmentCost',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedByIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedBy',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedByIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedBy',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedByEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedByGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedByLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedByBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedBy',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedByStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedByEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedByContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedByMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'updatedBy',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedByIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedBy',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      updatedByIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'updatedBy',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'veterinarian',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'veterinarian',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'veterinarian',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'veterinarian',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'veterinarian',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'veterinarian',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'veterinarian',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'veterinarian',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'veterinarian',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'veterinarian',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'veterinarian',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'veterinarian',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianContactIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'veterinarianContact',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianContactIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'veterinarianContact',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianContactEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'veterinarianContact',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianContactGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'veterinarianContact',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianContactLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'veterinarianContact',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianContactBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'veterinarianContact',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianContactStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'veterinarianContact',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianContactEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'veterinarianContact',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianContactContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'veterinarianContact',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianContactMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'veterinarianContact',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianContactIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'veterinarianContact',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      veterinarianContactIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'veterinarianContact',
        value: '',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      weightIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'weight',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      weightIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'weight',
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      weightEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      weightGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      weightLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterFilterCondition>
      weightBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weight',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }
}

extension HealthRecordIsarQueryObject
    on QueryBuilder<HealthRecordIsar, HealthRecordIsar, QFilterCondition> {}

extension HealthRecordIsarQueryLinks
    on QueryBuilder<HealthRecordIsar, HealthRecordIsar, QFilterCondition> {}

extension HealthRecordIsarQuerySortBy
    on QueryBuilder<HealthRecordIsar, HealthRecordIsar, QSortBy> {
  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByBodyConditionScore() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bodyConditionScore', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByBodyConditionScoreDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bodyConditionScore', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByCattleBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByCattleBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByCattleName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleName', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByCattleNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleName', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByCattleTagId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByCattleTagIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByCondition() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'condition', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByConditionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'condition', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByCreatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByCreatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy> sortByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByDiagnosis() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'diagnosis', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByDiagnosisDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'diagnosis', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByDosage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dosage', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByDosageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dosage', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByDuration() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'duration', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByDurationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'duration', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByFollowUpDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'followUpDate', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByFollowUpDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'followUpDate', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByFrequency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'frequency', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByFrequencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'frequency', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByHealthStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthStatus', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByHealthStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthStatus', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByHeartRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'heartRate', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByHeartRateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'heartRate', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByIsChronic() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isChronic', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByIsChronicDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isChronic', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByIsEmergency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEmergency', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByIsEmergencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEmergency', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByIsUnderTreatment() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isUnderTreatment', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByIsUnderTreatmentDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isUnderTreatment', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByMedication() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'medication', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByMedicationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'medication', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy> sortByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByRecordType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordType', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByRecordTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordType', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByRecoveryDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recoveryDate', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByRecoveryDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recoveryDate', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByRequiresFollowUp() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'requiresFollowUp', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByRequiresFollowUpDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'requiresFollowUp', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByRespiratoryRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'respiratoryRate', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByRespiratoryRateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'respiratoryRate', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortBySeverity() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'severity', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortBySeverityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'severity', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortBySymptoms() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'symptoms', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortBySymptomsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'symptoms', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByTemperature() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'temperature', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByTemperatureDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'temperature', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByTreatment() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'treatment', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByTreatmentDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'treatment', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByTreatmentCost() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'treatmentCost', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByTreatmentCostDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'treatmentCost', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByUpdatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByUpdatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByVeterinarian() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veterinarian', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByVeterinarianDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veterinarian', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByVeterinarianContact() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veterinarianContact', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByVeterinarianContactDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veterinarianContact', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weight', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      sortByWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weight', Sort.desc);
    });
  }
}

extension HealthRecordIsarQuerySortThenBy
    on QueryBuilder<HealthRecordIsar, HealthRecordIsar, QSortThenBy> {
  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByBodyConditionScore() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bodyConditionScore', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByBodyConditionScoreDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bodyConditionScore', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByCattleBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByCattleBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByCattleName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleName', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByCattleNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleName', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByCattleTagId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByCattleTagIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByCondition() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'condition', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByConditionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'condition', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByCreatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByCreatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy> thenByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByDiagnosis() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'diagnosis', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByDiagnosisDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'diagnosis', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByDosage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dosage', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByDosageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dosage', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByDuration() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'duration', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByDurationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'duration', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByFollowUpDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'followUpDate', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByFollowUpDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'followUpDate', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByFrequency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'frequency', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByFrequencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'frequency', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByHealthStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthStatus', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByHealthStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthStatus', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByHeartRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'heartRate', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByHeartRateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'heartRate', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByIsChronic() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isChronic', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByIsChronicDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isChronic', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByIsEmergency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEmergency', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByIsEmergencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEmergency', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByIsUnderTreatment() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isUnderTreatment', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByIsUnderTreatmentDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isUnderTreatment', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByMedication() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'medication', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByMedicationDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'medication', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy> thenByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByRecordType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordType', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByRecordTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordType', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByRecoveryDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recoveryDate', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByRecoveryDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recoveryDate', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByRequiresFollowUp() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'requiresFollowUp', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByRequiresFollowUpDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'requiresFollowUp', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByRespiratoryRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'respiratoryRate', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByRespiratoryRateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'respiratoryRate', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenBySeverity() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'severity', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenBySeverityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'severity', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenBySymptoms() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'symptoms', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenBySymptomsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'symptoms', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByTemperature() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'temperature', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByTemperatureDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'temperature', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByTreatment() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'treatment', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByTreatmentDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'treatment', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByTreatmentCost() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'treatmentCost', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByTreatmentCostDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'treatmentCost', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByUpdatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByUpdatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByVeterinarian() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veterinarian', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByVeterinarianDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veterinarian', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByVeterinarianContact() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veterinarianContact', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByVeterinarianContactDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'veterinarianContact', Sort.desc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weight', Sort.asc);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QAfterSortBy>
      thenByWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weight', Sort.desc);
    });
  }
}

extension HealthRecordIsarQueryWhereDistinct
    on QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct> {
  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByAttachments() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'attachments');
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByBodyConditionScore() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'bodyConditionScore');
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByCattleBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cattleBusinessId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByCattleName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cattleName', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByCattleTagId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cattleTagId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByCondition({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'condition', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByCreatedBy({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdBy', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct> distinctByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'date');
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByDiagnosis({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'diagnosis', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct> distinctByDosage(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'dosage', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByDuration({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'duration', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByFarmBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'farmBusinessId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByFollowUpDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'followUpDate');
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByFrequency({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'frequency', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByHealthStatus({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'healthStatus', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByHeartRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'heartRate');
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByIsChronic() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isChronic');
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByIsEmergency() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isEmergency');
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByIsUnderTreatment() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isUnderTreatment');
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByMedication({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'medication', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct> distinctByNotes(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notes', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByRecordType({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'recordType', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByRecoveryDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'recoveryDate');
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByRequiresFollowUp() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'requiresFollowUp');
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByRespiratoryRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'respiratoryRate');
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctBySeverity({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'severity', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctBySymptoms({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'symptoms', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByTemperature() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'temperature');
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByTreatment({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'treatment', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByTreatmentCost() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'treatmentCost');
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByUpdatedBy({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedBy', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByVeterinarian({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'veterinarian', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByVeterinarianContact({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'veterinarianContact',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<HealthRecordIsar, HealthRecordIsar, QDistinct>
      distinctByWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weight');
    });
  }
}

extension HealthRecordIsarQueryProperty
    on QueryBuilder<HealthRecordIsar, HealthRecordIsar, QQueryProperty> {
  QueryBuilder<HealthRecordIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<HealthRecordIsar, List<String>?, QQueryOperations>
      attachmentsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'attachments');
    });
  }

  QueryBuilder<HealthRecordIsar, double?, QQueryOperations>
      bodyConditionScoreProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'bodyConditionScore');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      cattleBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cattleBusinessId');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      cattleNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cattleName');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      cattleTagIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cattleTagId');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      conditionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'condition');
    });
  }

  QueryBuilder<HealthRecordIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      createdByProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdBy');
    });
  }

  QueryBuilder<HealthRecordIsar, DateTime?, QQueryOperations> dateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'date');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      diagnosisProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'diagnosis');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations> dosageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dosage');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations> durationProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'duration');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      farmBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'farmBusinessId');
    });
  }

  QueryBuilder<HealthRecordIsar, DateTime?, QQueryOperations>
      followUpDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'followUpDate');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      frequencyProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'frequency');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      healthStatusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'healthStatus');
    });
  }

  QueryBuilder<HealthRecordIsar, int?, QQueryOperations> heartRateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'heartRate');
    });
  }

  QueryBuilder<HealthRecordIsar, bool, QQueryOperations> isChronicProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isChronic');
    });
  }

  QueryBuilder<HealthRecordIsar, bool, QQueryOperations> isEmergencyProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isEmergency');
    });
  }

  QueryBuilder<HealthRecordIsar, bool, QQueryOperations>
      isUnderTreatmentProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isUnderTreatment');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      medicationProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'medication');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations> notesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notes');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      recordTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'recordType');
    });
  }

  QueryBuilder<HealthRecordIsar, DateTime?, QQueryOperations>
      recoveryDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'recoveryDate');
    });
  }

  QueryBuilder<HealthRecordIsar, bool, QQueryOperations>
      requiresFollowUpProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'requiresFollowUp');
    });
  }

  QueryBuilder<HealthRecordIsar, int?, QQueryOperations>
      respiratoryRateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'respiratoryRate');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations> severityProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'severity');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations> symptomsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'symptoms');
    });
  }

  QueryBuilder<HealthRecordIsar, double?, QQueryOperations>
      temperatureProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'temperature');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      treatmentProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'treatment');
    });
  }

  QueryBuilder<HealthRecordIsar, double?, QQueryOperations>
      treatmentCostProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'treatmentCost');
    });
  }

  QueryBuilder<HealthRecordIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      updatedByProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedBy');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      veterinarianProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'veterinarian');
    });
  }

  QueryBuilder<HealthRecordIsar, String?, QQueryOperations>
      veterinarianContactProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'veterinarianContact');
    });
  }

  QueryBuilder<HealthRecordIsar, double?, QQueryOperations> weightProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weight');
    });
  }
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HealthRecordIsar _$HealthRecordIsarFromJson(Map<String, dynamic> json) =>
    HealthRecordIsar()
      ..id = (json['id'] as num).toInt()
      ..businessId = json['businessId'] as String?
      ..farmBusinessId = json['farmBusinessId'] as String?
      ..cattleBusinessId = json['cattleBusinessId'] as String?
      ..cattleTagId = json['cattleTagId'] as String?
      ..cattleName = json['cattleName'] as String?
      ..recordType = json['recordType'] as String?
      ..date =
          json['date'] == null ? null : DateTime.parse(json['date'] as String)
      ..condition = json['condition'] as String?
      ..healthStatus = json['healthStatus'] as String?
      ..severity = json['severity'] as String?
      ..symptoms = json['symptoms'] as String?
      ..diagnosis = json['diagnosis'] as String?
      ..treatment = json['treatment'] as String?
      ..medication = json['medication'] as String?
      ..dosage = json['dosage'] as String?
      ..frequency = json['frequency'] as String?
      ..duration = json['duration'] as String?
      ..veterinarian = json['veterinarian'] as String?
      ..veterinarianContact = json['veterinarianContact'] as String?
      ..treatmentCost = (json['treatmentCost'] as num?)?.toDouble()
      ..followUpDate = json['followUpDate'] == null
          ? null
          : DateTime.parse(json['followUpDate'] as String)
      ..recoveryDate = json['recoveryDate'] == null
          ? null
          : DateTime.parse(json['recoveryDate'] as String)
      ..notes = json['notes'] as String?
      ..temperature = (json['temperature'] as num?)?.toDouble()
      ..heartRate = (json['heartRate'] as num?)?.toInt()
      ..respiratoryRate = (json['respiratoryRate'] as num?)?.toInt()
      ..weight = (json['weight'] as num?)?.toDouble()
      ..bodyConditionScore = (json['bodyConditionScore'] as num?)?.toDouble()
      ..attachments = (json['attachments'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList()
      ..isEmergency = json['isEmergency'] as bool
      ..isUnderTreatment = json['isUnderTreatment'] as bool
      ..requiresFollowUp = json['requiresFollowUp'] as bool
      ..isChronic = json['isChronic'] as bool
      ..createdAt = json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String)
      ..updatedAt = json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String)
      ..createdBy = json['createdBy'] as String?
      ..updatedBy = json['updatedBy'] as String?;

Map<String, dynamic> _$HealthRecordIsarToJson(HealthRecordIsar instance) =>
    <String, dynamic>{
      'id': instance.id,
      'businessId': instance.businessId,
      'farmBusinessId': instance.farmBusinessId,
      'cattleBusinessId': instance.cattleBusinessId,
      'cattleTagId': instance.cattleTagId,
      'cattleName': instance.cattleName,
      'recordType': instance.recordType,
      'date': instance.date?.toIso8601String(),
      'condition': instance.condition,
      'healthStatus': instance.healthStatus,
      'severity': instance.severity,
      'symptoms': instance.symptoms,
      'diagnosis': instance.diagnosis,
      'treatment': instance.treatment,
      'medication': instance.medication,
      'dosage': instance.dosage,
      'frequency': instance.frequency,
      'duration': instance.duration,
      'veterinarian': instance.veterinarian,
      'veterinarianContact': instance.veterinarianContact,
      'treatmentCost': instance.treatmentCost,
      'followUpDate': instance.followUpDate?.toIso8601String(),
      'recoveryDate': instance.recoveryDate?.toIso8601String(),
      'notes': instance.notes,
      'temperature': instance.temperature,
      'heartRate': instance.heartRate,
      'respiratoryRate': instance.respiratoryRate,
      'weight': instance.weight,
      'bodyConditionScore': instance.bodyConditionScore,
      'attachments': instance.attachments,
      'isEmergency': instance.isEmergency,
      'isUnderTreatment': instance.isUnderTreatment,
      'requiresFollowUp': instance.requiresFollowUp,
      'isChronic': instance.isChronic,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'createdBy': instance.createdBy,
      'updatedBy': instance.updatedBy,
    };
