                   fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color:
                              _morningColor.withAlpha(204), // 0.8 * 255 = 204
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    children: [
                      const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.nightlight_rounded,
                              color: _eveningColor, size: 16), // Match milk tab icon
                          SizedBox(width: 4),
                          Text(
                            'Evening',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              color: _eveningColor,
                              fontSize: 15,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${eveningTotal.toStringAsFixed(1)} L',
                        style: const TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.w900,
                          color: _eveningColor,
                        ),
                      ),
                      Text(
                        '(${eveningPercentage.toStringAsFixed(1)}%)',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color:
                              _eveningColor.withAlpha(204), // 0.8 * 255 = 204
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            headerColor: _secondaryColor,
          ),
          const SizedBox(height: 16),

          // Top Performers Card
          _buildCardWithHeader(
            'Top Producers',
            Icons.trending_up,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...top5Cattle.map((entry) {
                  final cattle = widget.cattleMap[entry.key];
                  final percentage = (entry.value / totalProduction * 100);
                  return _buildCattleListItem(
                    cattle,
                    entry.value,
                    percentage,
                    color: _secondaryColor, // Green for top producers
                  );
                }).toList(),
              ],
            ),
            headerColor: _secondaryColor, // Green for top producers
          ),
          const SizedBox(height: 16),

          // Low Producers Card
          _buildCardWithHeader(
            'Low Producers',
            Icons.trending_down,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...sortedCattle.reversed.take(3).map((entry) {
                  final cattle = widget.cattleMap[entry.key];
                  final percentage = (entry.value / totalProduction * 100);
                  return _buildCattleListItem(
                    cattle,
                    entry.value,
                    percentage,
                    color: _redColor, // Red for low producers (avoiding orange)
                  );
                }).toList(),
              ],
            ),
            headerColor: _redColor, // Red for low producers (avoiding orange)
          ),
          const SizedBox(height: 16),

          // Monthly Production Alerts Card
          if (sortedCattle.isNotEmpty)
            _buildCardWithHeader(
              'Production Alerts',
              Icons.warning_amber_rounded,
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _redColor.withAlpha(26), // 0.1 * 255 = 26
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.info_outline, color: _redColor, size: 18),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Cattle producing below 80% of monthly average',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),
                  ...sortedCattle
                      .where((entry) => entry.value < avgPerCattle * 0.8)
                      .take(3)
                      .map((entry) {
                    final cattle = widget.cattleMap[entry.key];
                    final percentage = (entry.value / avgPerCattle * 100);
                    return _buildCattleListItem(
                      cattle,
                      entry.value,
                      percentage,
                      showTrend: false,
                      color: _redColor,
                    );
                  }).toList(),
                  if (sortedCattle
                      .where((entry) => entry.value < avgPerCattle * 0.8)
                      .isEmpty)
                    const Padding(
                      padding: EdgeInsets.all(12.0),
                      child: Text(
                        'No cattle are significantly underperforming this month.',
                        style: TextStyle(
                          fontStyle: FontStyle.italic,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                ],
              ),
              headerColor: _redColor,
            ),
          const SizedBox(height: 16),

          // Weekly production breakdown with alerts
          _buildCardWithHeader(
            'Weekly Production Breakdown',
            Icons.bar_chart,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Production Alerts
                if (dailyProduction.entries
                    .any((e) => e.value < totalProduction * 0.8)) ...[
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: _redColor.withAlpha(26), // 0.1 * 255 = 26
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Icon(Icons.warning_amber_rounded,
                                color: _redColor, size: 18),
                            SizedBox(width: 8),
                            Text(
                              'Production Alerts',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: _redColor,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        ...dailyProduction.entries
                            .where((e) => e.value < totalProduction * 0.8)
                            .map((entry) {
                          final dropPercentage =
                              ((totalProduction - entry.value) /
                                  totalProduction *
                                  100);
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 8),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.arrow_downward,
                                  color: _redColor,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'Day ${entry.key.day}: ${entry.value.toStringAsFixed(1)} L '
                                    '(${dropPercentage.toStringAsFixed(1)}% below average)',
                                    style: const TextStyle(color: _redColor),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ],
                    ),
                  ),
                ],

                // Weekly average label
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      margin: const EdgeInsets.only(bottom: 8),
                      decoration: BoxDecoration(
                        color: _accentColor.withAlpha(26), // 0.1 * 255 = 26
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                            color:
                                _accentColor.withAlpha(76)), // 0.3 * 255 = 76
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.horizontal_rule,
                            color: _accentColor,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Monthly Average: ${totalProduction.toStringAsFixed(1)} L',
                            style: const TextStyle(
                              color: _accentColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                SizedBox(
                  height: 220,
                  child: BarChart(
                    BarChartData(
                      gridData: FlGridData(
                        show: true,
                        drawVerticalLine: true,
                        horizontalInterval: 5,
                        getDrawingHorizontalLine: (value) {
                          return FlLine(
                            color: Colors.grey.shade200,
                            strokeWidth: 1,
                          );
                        },
                        getDrawingVerticalLine: (value) {
                          return FlLine(
                            color: Colors.grey.shade200,
                            strokeWidth: 1,
                          );
                        },
                      ),
                      titlesData: FlTitlesData(
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            reservedSize: 40,
                            getTitlesWidget: (value, meta) {
                              return Text(
                                value.toInt().toString(),
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 12,
                                ),
                              );
                            },
                          ),
                        ),
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            getTitlesWidget: (value, meta) {
                              final day = value.toInt();
                              return Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Text(
                                  'Day $day',
                                  style: TextStyle(
                                    color: Colors.grey.shade600,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        rightTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        topTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                      ),
                      borderData: FlBorderData(
                        show: true,
                        border: Border.all(
                          color: Colors.grey.shade200,
                          width: 1,
                        ),
                      ),
                      minY: 0,
                      barTouchData: BarTouchData(
                        enabled: true,
                        handleBuiltInTouches: true,
                        touchTooltipData: BarTouchTooltipData(
                          getTooltipItem: (group, groupIndex, rod, rodIndex) {
                            final day = group.x + 1;
                            return BarTooltipItem(
                              'Day $day: ${rod.toY.toStringAsFixed(1)} L',
                              const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            );
                          },
                        ),
                      ),
                      barGroups: dailyProduction.entries.map((entry) {
                        return BarChartGroupData(
                          x: entry.key.day - 1,
                          barRods: [
                            BarChartRodData(
                              toY: entry.value,
                              color: _primaryColor,
                              width: 16,
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(4),
                              ),
                              backDrawRodData: BackgroundBarChartRodData(
                                show: true,
                                toY: totalProduction * 1.2,
                                color: Colors.grey.shade200,
                              ),
                            ),
                          ],
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ],
            ),
            headerColor: _primaryColor,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.filteredRecords.isEmpty) {
      return MilkEmptyState(
        icon: Icons.filter_alt_off_outlined,
        message: 'No Records Found',
        subtitle: 'No records found for the selected filters',
        color: _primaryColor,
        action: ElevatedButton.icon(
          onPressed: () {
            setState(() {
              _selectedMonth = null;
              widget.onMonthChanged(null);
            });
          },
          icon: const Icon(Icons.refresh),
          label: const Text('Show All Months'),
          style: ElevatedButton.styleFrom(
            backgroundColor: _primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      );
    }

    // Group records by month
    final recordsByMonth = <DateTime, List<MilkRecordIsar>>{};
    for (var record in widget.filteredRecords) {
      if (record.date != null) {
        final month = DateTime(record.date!.year, record.date!.month, 1);
        recordsByMonth.putIfAbsent(month, () => []).add(record);
      }
    }

    // Sort months in descending order
    final months = recordsByMonth.keys.toList()..sort((a, b) => b.compareTo(a));

    // If a month is selected, show only that month
    if (_selectedMonth != null) {
      final selectedMonth =
          DateTime(_selectedMonth!.year, _selectedMonth!.month, 1);

      if (recordsByMonth.containsKey(selectedMonth)) {
        return SingleChildScrollView(
          child: Padding(
            padding:
                const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildMonthCard(
                    selectedMonth, recordsByMonth[selectedMonth]!, months),
                const SizedBox(height: 16),
                Center(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _selectedMonth = null;
                        widget.onMonthChanged(null);
                      });
                    },
                    icon: const Icon(Icons.list),
                    label: const Text('Show All Months'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      } else {
        // If the selected month doesn't have records, show a message
        return MilkEmptyState(
          icon: Icons.calendar_month_outlined,
          message: 'No Records for Selected Month',
          subtitle: 'No records found for the selected month',
          color: _primaryColor,
          action: ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _selectedMonth = null;
                widget.onMonthChanged(null);
              });
            },
            icon: const Icon(Icons.list),
            label: const Text('Show All Months'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        );
      }
    }

    return SingleChildScrollView(
      child: Padding(
        padding:
            const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...months.map((month) {
              final monthRecords = recordsByMonth[month]!;
              return _buildMonthCard(month, monthRecords, months);
            }).toList(),
          ],
        ),
      ),
    );
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   import 'package:flutter/material.dart';
import '../models/milk_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../../widgets/empty_state.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';

class WeeklyTab extends StatefulWidget {
  final List<MilkRecordIsar> filteredRecords;
  final Map<String, CattleIsar> cattleMap;
  final Map<String, AnimalTypeIsar> animalTypeMap;
  final DateTime? selectedWeek;
  final Function(BuildContext, List<DateTime>) selectWeek;

  const WeeklyTab({
    Key? key,
    required this.filteredRecords,
    required this.cattleMap,
    required this.animalTypeMap,
    this.selectedWeek,
    required this.selectWeek,
  }) : super(key: key);

  @override
  State<WeeklyTab> createState() => _WeeklyTabState();
}

class _WeeklyTabState extends State<WeeklyTab> {
  DateTime? _selectedWeek;

  // Milk tab color palette - matching the main milk tab colors
  static const _primaryColor = Color(0xFF1976D2); // Blue (Weekly tab color)
  static const _secondaryColor = Color(0xFF2E7D32); // Green (Daily)
  static const _accentColor = Color(0xFF7B1FA2); // Purple (Evening)
  static const _redColor = Color(0xFFD32F2F); // Red (Total)
  static const _cardShadowColor = Color(0x1A000000);
  static const _cardBorderRadius = 12.0; // Match milk tab border radius

  // Animation duration
  static const _animDuration = Duration(milliseconds: 300);

  @override
  void initState() {
    super.initState();
    _selectedWeek = widget.selectedWeek;
  }

  @override
  void didUpdateWidget(WeeklyTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedWeek != oldWidget.selectedWeek) {
      _selectedWeek = widget.selectedWeek;
    }
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    String? subtitle,
    Color iconColor = Colors.white,
    Color textColor = Colors.black87,
    Color valueColor = Colors.black87,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      padding: const EdgeInsets.all(14),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and icon row
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 4,
                child: Text(
                  title,
                  style: TextStyle(
                    color: textColor.withAlpha(204), // 0.8 * 255 = 204
                    fontWeight: FontWeight.w700,
                    fontSize: 12,
                    letterSpacing: 0.5,
                  ),
                  softWrap: true,
                  maxLines: 2,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: iconColor.withAlpha(38), // 0.15 * 255 = 38
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: iconColor, size: 16),
              ),
            ],
          ),
          const SizedBox(height: 10),
          // Value with large font
          Text(
            value,
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.w900,
              color: valueColor,
              letterSpacing: -0.5,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: textColor.withAlpha(179), // 0.7 * 255 = 179
                fontWeight: FontWeight.w500,
              ),
              softWrap: true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCattleListItem(
    CattleIsar? cattle,
    double value,
    double percentage, {
    bool showTrend = true,
    Color progressColor = Colors.blue,
  }) {
    final greyColor = Colors.grey.shade600;
    final cattleName = cattle?.name ?? 'Unknown';
    final tagId = cattle?.tagId ?? '';
    final displayName =
        (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                cattle?.animalTypeId == 'cow' ? Icons.emoji_nature : Icons.pets,
                color: progressColor.withAlpha(179), // 0.7 * 255 = 179
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 2,
                child: Text(
                  displayName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  '${value.toStringAsFixed(1)} L',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.end,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: LinearProgressIndicator(
                    value: percentage / 100,
                    backgroundColor: Colors.grey.shade200,
                    color: progressColor,
                    minHeight: 5,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              if (showTrend)
                Text(
                  '${percentage.toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: percentage > 20 ? Colors.green : greyColor,
                    fontSize: 12,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCardWithHeader(
    String title,
    IconData icon,
    Widget content, {
    Color headerColor = Colors.black87,
  }) {
    return Card(
      elevation: 4,
      shadowColor: _cardShadowColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_cardBorderRadius),
      ),
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: headerColor.withAlpha(26), // 0.1 * 255 = 26
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(_cardBorderRadius),
                topRight: Radius.circular(_cardBorderRadius),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, color: headerColor, size: 18),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: headerColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          // Content
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(_cardBorderRadius),
                bottomRight: Radius.circular(_cardBorderRadius),
              ),
            ),
            child: content,
          ),
        ],
      ),
    );
  }

  Widget _buildWeekCard(
      DateTime weekStart, List<MilkRecordIsar> weekRecords, List<DateTime> weeks) {
    final weekEnd = weekStart.add(const Duration(days: 6));

    // Calculate weekly totals
    double totalProduction = 0;
    double morningTotal = 0;
    double eveningTotal = 0;
    final dailyProduction = <DateTime, double>{};
    final topCattleProduction = <String, double>{};

    // Initialize daily production for the entire week
    for (var i = 0; i < 7; i++) {
      final day = weekStart.add(Duration(days: i));
      dailyProduction[day] = 0;
    }

    for (var record in weekRecords) {
      totalProduction += record.totalYield;
      morningTotal += record.morningAmount ?? 0;
      eveningTotal += record.eveningAmount ?? 0;

      // Add to daily production
      final day =
          DateTime(record.date?.year ?? 0, record.date?.month ?? 0, record.date?.day ?? 0);
      dailyProduction[day] = (dailyProduction[day] ?? 0) + record.totalYield;

      // Track by cattle for top producers
      final tagId = record.cattleTagId ?? '';
      if (tagId.isNotEmpty) {
        topCattleProduction[tagId] =
            (topCattleProduction[tagId] ?? 0) + record.totalYield;
      }
    }

    // Get top and bottom performers
    final sortedCattle = topCattleProduction.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    final top3Cattle = sortedCattle.take(3).toList();
    final bottom3Cattle = sortedCattle.reversed.take(3).toList();

    // Calculate average daily production
    final avgDailyProduction = totalProduction / 7;
    final morningPercentage =
        totalProduction > 0 ? (morningTotal / totalProduction * 100) : 0;
    final eveningPercentage =
        totalProduction > 0 ? (eveningTotal / totalProduction * 100) : 0;

    // Find days with significant drops (>20% below average)
    final productionAlerts = dailyProduction.entries
        .where((entry) =>
            entry.value > 0 && entry.value < avgDailyProduction * 0.8)
        .toList();

    return AnimatedContainer(
      duration: _animDuration,
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with week selection
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(_cardBorderRadius),
              boxShadow: const [
                BoxShadow(
                  color: _cardShadowColor,
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Week of ${DateFormat('MMM d').format(weekStart)}',
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: _primaryColor,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.calendar_month,
                          color: _primaryColor),
                      onPressed: () => widget.selectWeek(context, weeks),
                      style: IconButton.styleFrom(
                        backgroundColor:
                            _primaryColor.withAlpha(26), // 0.1 * 255 = 26
                        padding: const EdgeInsets.all(8),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '${DateFormat('MMM d, yyyy').format(weekStart)} - ${DateFormat('MMM d, yyyy').format(weekEnd)}',
                  style: TextStyle(
                    fontSize: 15,
                    color: Colors.grey.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Key Stats Section
          _buildCardWithHeader(
            'Key Production Metrics',
            Icons.analytics,
            Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.local_drink,
                        iconColor: _primaryColor,
                        valueColor: _primaryColor,
                        title: 'TOTAL PRODUCTION',
                        value: '${totalProduction.toStringAsFixed(1)} L',
                        subtitle: 'Combined milk yield',
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.equalizer,
                        iconColor: _secondaryColor,
                        valueColor: _secondaryColor,
                        title: 'AVERAGE DAILY',
                        value: '${avgDailyProduction.toStringAsFixed(1)} L',
                        subtitle: 'Per day average',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.equalizer,
                        iconColor: _accentColor,
                        valueColor: _accentColor,
                        title: 'AVERAGE PER COW',
                        value:
                            '${(totalProduction / (topCattleProduction.keys.isNotEmpty ? topCattleProduction.keys.length : 1)).toStringAsFixed(1)} L',
                        subtitle: '${topCattleProduction.keys.length} cattle',
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildStatCard(
                        icon: Icons.star,
                        iconColor: Colors.green,
                        valueColor: Colors.green,
                        title: 'TOP PRODUCER',
                        value: top3Cattle.isNotEmpty
                            ? '${top3Cattle.first.value.toStringAsFixed(1)} L'
                            : 'N/A',
                        subtitle: top3Cattle.isNotEmpty
                            ? (() {
                                final cattle =
                                    widget.cattleMap[top3Cattle.first.key];
                                final cattleName = cattle?.name ?? 'Unknown';
                                final tagId = cattle?.tagId ?? '';
                                return (tagId.isNotEmpty)
                                    ? '$cattleName ($tagId)'
                                    : cattleName;
                              })()
                            : '',
                      ),
                    ),
                  ],
                ),
              ],
            ),
            headerColor: _primaryColor,
          ),
          const SizedBox(height: 16),

          // Morning/Evening Distribution Card
          _buildCardWithHeader(
            'Production Distribution',
            Icons.pie_chart,
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.wb_sunny_rounded, color: _primaryColor, size: 16), // Use blue for morning
                          SizedBox(width: 4),
                          Text(
                            'Morning',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              color: _primaryColor, // Blue for morning
                              fontSize: 15,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${morningTotal.toStringAsFixed(1)} L',
                        style: const TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.w900,
                          color: _primaryColor, // Blue for morning
                        ),
                      ),
                      Text(
                        '(${morningPercentage.toStringAsFixed(1)}%)',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: _primaryColor.withAlpha(204), // 0.8 * 255 = 204
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    children: [
                      const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.nightlight_rounded,
                              color: _accentColor, size: 16), // Purple for evening
                          SizedBox(width: 4),
                          Text(
                            'Evening',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              color: _accentColor, // Purple for evening
                              fontSize: 15,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${eveningTotal.toStringAsFixed(1)} L',
                        style: const TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.w900,
                          color: _accentColor, // Purple for evening
                        ),
                      ),
                      Text(
                        '(${eveningPercentage.toStringAsFixed(1)}%)',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: _accentColor.withAlpha(204), // 0.8 * 255 = 204
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            headerColor: _secondaryColor,
          ),
          const SizedBox(height: 16),

          // Top Performers Card
          _buildCardWithHeader(
            'Top Producers',
            Icons.trending_up,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...top3Cattle.map((entry) {
                  final cattle = widget.cattleMap[entry.key];
                  final percentage = (entry.value / totalProduction * 100);
                  return _buildCattleListItem(
                    cattle,
                    entry.value,
                    percentage,
                    progressColor: _secondaryColor, // Green for top performers
                  );
                }).toList(),
              ],
            ),
            headerColor: _secondaryColor, // Green for top performers
          ),
          const SizedBox(height: 16),

          // Bottom Performers Card
          _buildCardWithHeader(
            'Low Producers',
            Icons.trending_down,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...bottom3Cattle.map((entry) {
                  final cattle = widget.cattleMap[entry.key];
                  final percentage = (entry.value / totalProduction * 100);
                  return _buildCattleListItem(
                    cattle,
                    entry.value,
                    percentage,
                    progressColor: _redColor, // Red for low performers (avoiding orange)
                  );
                }).toList(),
              ],
            ),
            headerColor: _redColor, // Red for low performers (avoiding orange)
          ),
          const SizedBox(height: 16),

          // Production Alerts Card
          _buildCardWithHeader(
            'Production Alerts',
            Icons.warning_amber_rounded,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _redColor.withAlpha(26), // 0.1 * 255 = 26
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info_outline, color: _redColor, size: 18),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Days with production below 80% of weekly average',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                if (productionAlerts.isNotEmpty)
                  ...productionAlerts.map((entry) {
                    final day = entry.key;
                    final production = entry.value;
                    final dropPercentage = ((avgDailyProduction - production) /
                        avgDailyProduction *
                        100);
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: Row(
                        children: [
                          Text(
                            DateFormat('EEEE, MMM d').format(day),
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            '${production.toStringAsFixed(1)} L ',
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            '(${dropPercentage.toStringAsFixed(1)}% below avg)',
                            style: const TextStyle(color: _redColor),
                          ),
                        ],
                      ),
                    );
                  }).toList()
                else
                  const Padding(
                    padding: EdgeInsets.all(12.0),
                    child: Text(
                      'No days with significantly low production this week.',
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        color: Colors.grey,
                      ),
                    ),
                  ),
              ],
            ),
            headerColor: _redColor,
          ),
          const SizedBox(height: 16),

          // Weekly Chart Card
          _buildCardWithHeader(
            'Weekly Production Trend',
            Icons.show_chart,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                SizedBox(
                  height: 300,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Padding(
                        padding: EdgeInsets.only(left: 5, bottom: 8),
                        child: Text(
                          'Production (L)',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: _redColor, // Use red instead of orange
                          ),
                        ),
                      ),
                      Expanded(
                        child: LineChart(
                          LineChartData(
                            gridData: FlGridData(
                              show: true,
                              drawVerticalLine: true,
                              horizontalInterval: 3,
                              verticalInterval: 1,
                              getDrawingHorizontalLine: (value) {
                                return FlLine(
                                  color: Colors.grey.shade200,
                                  strokeWidth: 1,
                                );
                              },
                              getDrawingVerticalLine: (value) {
                                return FlLine(
                                  color: Colors.grey.shade200,
                                  strokeWidth: 1,
                                );
                              },
                            ),
                            titlesData: FlTitlesData(
                              leftTitles: AxisTitles(
                                sideTitles: SideTitles(
                                  showTitles: true,
                                  reservedSize: 40,
                                  interval: 3,
                                  getTitlesWidget: (value, meta) {
                                    // Only show titles at intervals of 3
                                    if (value % 3 != 0 && value != 0) {
                                      return const SizedBox.shrink();
                                    }
                                    return Text(
                                      value.toInt().toString(),
                                      style: const TextStyle(
                                        color: _redColor, // Use red instead of orange
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    );
                                  },
                                ),
                              ),
                              bottomTitles: AxisTitles(
                                sideTitles: SideTitles(
                                  showTitles: true,
                                  reservedSize: 24,
                                  getTitlesWidget: (value, meta) {
                                    if (value < 0 || value > 6) {
                                      return const Text('');
                                    }
                                    final day = weekStart
                                        .add(Duration(days: value.toInt()));
                                    return Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        DateFormat('E').format(day),
                                        style: const TextStyle(
                                          color: _secondaryColor,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                              rightTitles: const AxisTitles(
                                sideTitles: SideTitles(showTitles: false),
                              ),
                              topTitles: const AxisTitles(
                                sideTitles: SideTitles(showTitles: false),
                              ),
                            ),
                            borderData: FlBorderData(
                              show: true,
                              border: Border.all(
                                color: Colors.grey.shade200,
                                width: 1,
                              ),
                            ),
                            minX: 0,
                            maxX: 6,
                            minY: 0,
                            maxY: dailyProduction.values.isEmpty
                                ? 10
                                : (dailyProduction.values
                                        .reduce((a, b) => a > b ? a : b) *
                                    1.2),
                            lineTouchData: LineTouchData(
                              enabled: true,
                              handleBuiltInTouches: true,
                              touchTooltipData: LineTouchTooltipData(
                                tooltipRoundedRadius: 8,
                                tooltipPadding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                                tooltipMargin: 8,
                                fitInsideHorizontally: true,
                                fitInsideVertically: true,
                                getTooltipItems:
                                    (List<LineBarSpot> touchedBarSpots) {
                                  return touchedBarSpots.map((barSpot) {
                                    final day = weekStart
                                        .add(Duration(days: barSpot.x.toInt()));
                                    return LineTooltipItem(
                                      '${DateFormat('EEE, MMM d').format(day)}\n${barSpot.y.toStringAsFixed(1)} L',
                                      const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                      textAlign: TextAlign.center,
                                    );
                                  }).toList();
                                },
                              ),
                            ),
                            lineBarsData: [
                              LineChartBarData(
                                spots: dailyProduction.entries.map((entry) {
                                  final daysDiff =
                                      entry.key.difference(weekStart).inDays;
                                  return FlSpot(
                                      daysDiff.toDouble(), entry.value);
                                }).toList(),
                                isCurved: true,
                                curveSmoothness: 0.3,
                                preventCurveOverShooting: true,
                                preventCurveOvershootingThreshold: 5.0,
                                color: _primaryColor,
                                barWidth: 3,
                                isStrokeCapRound: true,
                                dotData: FlDotData(
                                  show: true,
                                  getDotPainter:
                                      (spot, percent, barData, index) {
                                    return FlDotCirclePainter(
                                      radius: 6,
                                      color: _primaryColor,
                                      strokeWidth: 3,
                                      strokeColor: Colors.white,
                                    );
                                  },
                                  checkToShowDot: (spot, barData) => true,
                                ),
                                belowBarData: BarAreaData(
                                  show: true,
                                  color: _primaryColor
                                      .withAlpha(26), // 0.1 * 255 = 26
                                  cutOffY: 0,
                                  applyCutOffY: true,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(top: 16, bottom: 8),
                        child: Center(
                          child: Text(
                            'Day of Week',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: _secondaryColor,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
            headerColor: _primaryColor,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.filteredRecords.isEmpty) {
      return const MilkEmptyState(
        icon: Icons.filter_alt_off_outlined,
        message: 'No Records Found',
        subtitle: 'No records found for the selected filters',
        color: _primaryColor,
      );
    }

    // Group records by week
    final recordsByWeek = <DateTime, List<MilkRecordIsar>>{};
    for (var record in widget.filteredRecords) {
      if (record.date != null) {
        // Get the start of the week (Monday)
        final date = record.date!;
        final weekStart = DateTime(
          date.year,
          date.month,
          date.day,
        ).subtract(Duration(days: date.weekday - 1));
        recordsByWeek.putIfAbsent(weekStart, () => []).add(record);
      }
    }

    // Sort weeks in descending order
    final weeks = recordsByWeek.keys.toList()..sort((a, b) => b.compareTo(a));

    // If a week is selected, show only that week
    if (_selectedWeek != null) {
      final selectedWeekStart = DateTime(
          _selectedWeek!.year, _selectedWeek!.month, _selectedWeek!.day);

      if (recordsByWeek.containsKey(selectedWeekStart)) {
        return SingleChildScrollView(
          child: Padding(
            padding:
                const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildWeekCard(selectedWeekStart,
                    recordsByWeek[selectedWeekStart]!, weeks),
                const SizedBox(height: 16),
                Center(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _selectedWeek = null;
                      });
                    },
                    icon: const Icon(Icons.list),
                    label: const Text('Show All Weeks'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      } else {
        // If the selected week doesn't have records, show a message
        return MilkEmptyState(
          icon: Icons.date_range_outlined,
          message: 'No Records for Selected Week',
          subtitle: 'No records found for the selected week',
          color: _primaryColor,
          action: ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _selectedWeek = null;
              });
            },
            icon: const Icon(Icons.list),
            label: const Text('Show All Weeks'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        );
      }
    }

    return SingleChildScrollView(
      child: Padding(
        padding:
            const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...weeks.map((weekStart) {
              final weekRecords = recordsByWeek[weekStart]!;
              return _buildWeekCard(weekStart, weekRecords, weeks);
            }).toList(),
          ],
        ),
      ),
    );
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        // GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetNotificationIsarCollection on Isar {
  IsarCollection<NotificationIsar> get notificationIsars => this.collection();
}

const NotificationIsarSchema = CollectionSchema(
  name: r'NotificationIsar',
  id: -398646555982393860,
  properties: {
    r'businessId': PropertySchema(
      id: 0,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'cattleId': PropertySchema(
      id: 1,
      name: r'cattleId',
      type: IsarType.string,
    ),
    r'createdAt': PropertySchema(
      id: 2,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'isRead': PropertySchema(
      id: 3,
      name: r'isRead',
      type: IsarType.bool,
    ),
    r'message': PropertySchema(
      id: 4,
      name: r'message',
      type: IsarType.string,
    ),
    r'priority': PropertySchema(
      id: 5,
      name: r'priority',
      type: IsarType.string,
    ),
    r'readAt': PropertySchema(
      id: 6,
      name: r'readAt',
      type: IsarType.dateTime,
    ),
    r'recordId': PropertySchema(
      id: 7,
      name: r'recordId',
      type: IsarType.string,
    ),
    r'title': PropertySchema(
      id: 8,
      name: r'title',
      type: IsarType.string,
    ),
    r'type': PropertySchema(
      id: 9,
      name: r'type',
      type: IsarType.string,
    )
  },
  estimateSize: _notificationIsarEstimateSize,
  serialize: _notificationIsarSerialize,
  deserialize: _notificationIsarDeserialize,
  deserializeProp: _notificationIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'type': IndexSchema(
      id: 5117122708147080838,
      name: r'type',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'type',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'isRead': IndexSchema(
      id: -944277114070112791,
      name: r'isRead',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'isRead',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    ),
    r'createdAt': IndexSchema(
      id: -3433535483987302584,
      name: r'createdAt',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'createdAt',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    ),
    r'cattleId': IndexSchema(
      id: 3179256717057104213,
      name: r'cattleId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'cattleId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'priority': IndexSchema(
      id: -6477851841645083544,
      name: r'priority',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'priority',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _notificationIsarGetId,
  getLinks: _notificationIsarGetLinks,
  attach: _notificationIsarAttach,
  version: '3.1.0+1',
);

int _notificationIsarEstimateSize(
  NotificationIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.cattleId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.message;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.priority;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.recordId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.title;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.type;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _notificationIsarSerialize(
  NotificationIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.businessId);
  writer.writeString(offsets[1], object.cattleId);
  writer.writeDateTime(offsets[2], object.createdA