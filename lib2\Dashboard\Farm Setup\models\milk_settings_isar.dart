import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

part 'milk_settings_isar.g.dart';

@collection
class MilkSettingsIsar {
  Id id = Isar.autoIncrement;
  
  @Index(unique: true)
  String? businessId;
  
  @Index()
  String? farmBusinessId; // Reference to the farm these settings belong to
  
  String? unit; // 'liters' or 'gallons'
  double? regularRate; // Rate per unit for regular milk
  double? premiumRate; // Rate per unit for premium quality milk
  double? bulkRate; // Rate per unit for bulk purchases

  DateTime? createdAt;
  DateTime? updatedAt;

  MilkSettingsIsar();

  factory MilkSettingsIsar.create({
    required String farmBusinessId,
    required String unit,
    required double regularRate,
    required double premiumRate,
    required double bulkRate,
  }) {
    return MilkSettingsIsar()
      ..businessId = const Uuid().v4()
      ..farmBusinessId = farmBusinessId
      ..unit = unit
      ..regularRate = regularRate
      ..premiumRate = premiumRate
      ..bulkRate = bulkRate
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'farmBusinessId': farmBusinessId,
      'unit': unit,
      'regularRate': regularRate,
      'premiumRate': premiumRate,
      'bulkRate': bulkRate,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory MilkSettingsIsar.fromMap(Map<String, dynamic> map) {
    return MilkSettingsIsar()
      ..businessId = map['id'] as String?
      ..farmBusinessId = map['farmBusinessId'] as String?
      ..unit = map['unit'] as String? ?? 'liters'
      ..regularRate = map['regularRate'] != null ? double.parse(map['regularRate'].toString()) : 0.0
      ..premiumRate = map['premiumRate'] != null ? double.parse(map['premiumRate'].toString()) : 0.0
      ..bulkRate = map['bulkRate'] != null ? double.parse(map['bulkRate'].toString()) : 0.0
      ..createdAt = map['createdAt'] != null ? DateTime.parse(map['createdAt'] as String) : DateTime.now()
      ..updatedAt = map['updatedAt'] != null ? DateTime.parse(map['updatedAt'] as String) : DateTime.now();
  }

  MilkSettingsIsar copyWith({
    String? businessId,
    String? farmBusinessId,
    String? unit,
    double? regularRate,
    double? premiumRate,
    double? bulkRate,
  }) {
    return MilkSettingsIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..farmBusinessId = farmBusinessId ?? this.farmBusinessId
      ..unit = unit ?? this.unit
      ..regularRate = regularRate ?? this.regularRate
      ..premiumRate = premiumRate ?? this.premiumRate
      ..bulkRate = bulkRate ?? this.bulkRate
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();
  }
} 