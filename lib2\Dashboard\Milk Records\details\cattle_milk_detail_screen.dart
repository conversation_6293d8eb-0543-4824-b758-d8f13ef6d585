import 'package:flutter/material.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/milk_record_isar.dart';
import '../services/milk_service.dart';
import '../../../constants/app_bar.dart';
import '../../../utils/message_utils.dart';
import '../../../widgets/reusable_tab_bar.dart';
import 'cattle_milk_analytics_tab.dart';
import 'cattle_milk_records_tab.dart';

class CattleMilkDetailScreen extends StatefulWidget {
  final CattleIsar cattle;
  final VoidCallback onRefresh;

  const CattleMilkDetailScreen({
    Key? key,
    required this.cattle,
    required this.onRefresh,
  }) : super(key: key);

  @override
  State<CattleMilkDetailScreen> createState() => _CattleMilkDetailScreenState();
}

class _CattleMilkDetailScreenState extends State<CattleMilkDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final MilkService _milkService = MilkService();

  List<MilkRecordIsar> _milkRecords = [];
  bool _isLoading = true;

  // Tab configuration using the reusable widget
  late List<TabItem> _tabs;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // Rebuild to update colors
    });

    // Define tabs using the reusable widget configuration
    _tabs = TabConfigurations.twoTabDetail(
      tab1Label: 'Analytics',
      tab1Icon: Icons.analytics,
      tab2Label: 'Records',
      tab2Icon: Icons.list,
    );

    _loadMilkRecords();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadMilkRecords() async {
    try {
      setState(() => _isLoading = true);

      final records = await _milkService.getMilkRecordsByCattle(
        widget.cattle.businessId!
      );

      setState(() {
        _milkRecords = records;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        MessageUtils.showError(context, 'Error loading milk records: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final cattleName = widget.cattle.name ?? 'Unknown Cattle';
    final tagId = widget.cattle.tagId ?? '';
    final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;

    return Scaffold(
      appBar: AppBarConfig.withBack(
        title: displayName,
        context: context,
        actions: _buildAppBarActions(),
      ),
      body: Column(
        children: [
          // Use the reusable tab bar widget
          ReusableTabBar(
            tabController: _tabController,
            tabs: _tabs,
            context: context,
          ),

          // TabBarView
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      CattleMilkAnalyticsTab(
                        cattle: widget.cattle,
                        milkRecords: _milkRecords,
                      ),
                      CattleMilkRecordsTab(
                        cattle: widget.cattle,
                        milkRecords: _milkRecords,
                        onRefresh: _refreshData,
                      ),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildAppBarActions() {
    return [
      IconButton(
        icon: const Icon(Icons.refresh),
        onPressed: _refreshData,
        tooltip: 'Refresh',
      ),
    ];
  }

  Future<void> _refreshData() async {
    await _loadMilkRecords();
    widget.onRefresh(); // Refresh parent screen data
  }
}
