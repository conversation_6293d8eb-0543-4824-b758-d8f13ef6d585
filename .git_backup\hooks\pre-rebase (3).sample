0000000000000000000000000000000000000000 008b6fadd44778269d3c7d8fb34a74ae8ee03d2d ask2206230 <<EMAIL>> 1739714546 +0500	WIP on (no branch): 9dba74e Enhanced line chart and transactions list (formatted): 1. Fixed line chart to show all values up to max (including 100) 2. Improved horizontal grid lines spacing 3. Updated transactions list alignments: - Left alignment for Sort column - Center alignment for Type, Category, Date, Payment, Amount - Right alignment for Actions
008b6fadd44778269d3c7d8fb34a74ae8ee03d2d acfe31ed7d74b0d13ea81f7a63242c5205dbd1e5 ask2206230 <<EMAIL>> 1739984804 +0500	WIP on (no branch): 131dbb0 V2.27: Enhanced pie chart UI with improved layout and legends
acfe31ed7d74b0d13ea81f7a63242c5205dbd1e5 41cfa0715238e485d89b775f3bed2adef2c5fa2f ask2206230 <<EMAIL>> 1740104380 +0500	WIP on (no branch): 1c32495 Save all files
41cfa0715238e485d89b775f3bed2adef2c5fa2f 562f8d3e77df626186cb11ded99947a6255a273a ask2206230 <<EMAIL>> 1740157440 +0500	WIP on (no branch): 6521d59 Save all changes and updates
562f8d3e77df626186cb11ded99947a6255a273a 33c2e50fa514988c5a855eec5bc77ac3f96ee0cd ask2206230 <<EMAIL>> 1740416380 +0500	WIP on (no branch): 991bd6c Fix Icon Picker and Categories, update logging service, and implement custom icon picker functionality
33c2e50fa514988c5a855eec5bc77ac3f96ee0cd 3ce9c30a44226d246bf43adc8febcb96df313f73 ask2206230 <<EMAIL>> 1740926189 +0500	WIP on (no branch): 6812ce1 Optimize cattle records screen loading performance
3ce9c30a44226d246bf43adc8febcb96df313f73 3d4fe240a3c5d2a828f90e18c7c198c8124e83a9 ask2206230 <<EMAIL>> 1740937066 +0500	WIP on (no branch): 80a6085 Update QR code functionality and reduce padding
3d4fe240a3c5d2a828f90e18c7c198c8124e83a9 fb195593c6fdaf5f222a5a758a1d3bc07d13a0ce ask2206230 <<EMAIL>> 1741862800 +0500	On (no branch): enter
fb195593c6fdaf5f222a5a758a1d3bc07d13a0ce 4c8799d54994622fbd819541a7778fb037a77fce ask2206230 <<EMAIL>> 1742010502 +0500	WIP on (no branch): ab34bac v3.32: UI and functionality improvements for breeding management - Unified breeding status handling across screens - Enhanced breeding eligibility checks - Fixed syntax errors and improved code quality - Standardized status colors and options - Improved error handling and user feedback
4c8799d54994622fbd819541a7778fb037a77fce 20a6a0d271cc9897ffc8dc3beb7faf00a95801de ask2206230 <<EMAIL>> 1742498908 +0500	WIP on (no branch): 10d394b Fix delivery record deletion issue. Now when deleting a delivery record, it properly removes both the breeding record and pregnancy record. Updated cattle age display on delivery records.
20a6a0d271cc9897ffc8dc3beb7faf00a95801de 451aa7b018348b54b96e5cf8b0ccfc5c893eeb2e ask2206230 <<EMAIL>> 1742542899 +0500	WIP on (no branch): f0d14d9 Fix delivery record deletion to properly delete associated breeding records
451aa7b018348b54b96e5cf8b0ccfc5c893eeb2e ed31cde6652f1e942ba5158f096dc91b93c5569a ask2206230 <<EMAIL>> 1742543285 +0500	WIP on (no branch): 2658166 Update related files for birth recording process
ed31cde6652f1e942ba5158f096dc91b93c5569a 0876345e5837e330f4e302c0e0031852ef5b7c34 ask2206230 <<EMAIL>> 1742543951 +0500	WIP on (no branch): f0d14d9 Fix delivery record deletion to properly delete associated breeding records
0876345e5837e330f4e302c0e0031852ef5b7c34 d668b5622a5e0f1473d34cc2732a28e27f2ef000 ask2206230 <<EMAIL>> 1743059453 +0500	WIP on (no branch): c5a8e1b Release v3.50: Fix form dialog parameter issues and refactor database architecture
d668b5622a5e0f1473d34cc2732a28e27f2ef000 c095b81ee816da3413f48e42be52c38bff5e5816 ask2206230 <<EMAIL>> 1743199200 +0500	WIP on (no branch): 6ca33cd Update color schemes for breeding and pregnancy views
c095b81ee816da3413f48e42be52c38bff5e5816 11734edf8c980294b6863e7bb063e269f423f47b ask2206230 <<EMAIL>> 1743202325 +0500	WIP on (no branch): 1ee6949 UI Improvements to Pregnancy View: 1. Aligned icons, labels, and values in single row 2. Enhanced icon colors with vibrant material design palette 3. Changed label text to pure black for better readability 4. Improved overall visual consistency
11734edf8c980294b6863e7bb063e269f423f47b 0aa5629995dedc36bf2fbfa78747c15a7d5c2006 ask2206230 <<EMAIL>> 1744055361 +0500	WIP on (no branch): dc388eb v3.48: Fix Flutter analyze issues - Remove unused imports, fix null-aware operators, update deprecated methods, mark unused code
0aa5629995dedc36bf2fbfa78747c15a7d5c2006 06cf7b84c8ce618a0b0c253e43125ea05a83c091 ask2206230 <<EMAIL>> 1744136200 +0500	WIP on (no branch): 7c9f207 Fix Flutter analyzer issues: Replace deprecated methods, fix BuildContext usage, rename constants to lowerCamelCase
06cf7b84c8ce618a0b0c253e43125ea05a83c091 5fbe1b47bbdd18f831dbdbe7a7a