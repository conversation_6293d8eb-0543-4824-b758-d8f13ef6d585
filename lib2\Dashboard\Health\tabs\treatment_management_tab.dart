import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/treatment_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../../constants/health_constants.dart';
import '../../../widgets/responsive_grid.dart';

class TreatmentManagementTab extends StatefulWidget {
  final List<TreatmentRecordIsar> treatmentRecords;
  final Map<String, CattleIsar> cattleMap;
  final Map<String, AnimalTypeIsar> animalTypeMap;
  final Map<String, BreedCategoryIsar> breedMap;
  final bool isSelectionMode;
  final Set<String> selectedRecords;
  final Function(bool) onSelectionModeChanged;
  final Function(String, bool) onSelectionChanged;
  final VoidCallback onRefresh;

  const TreatmentManagementTab({
    super.key,
    required this.treatmentRecords,
    required this.cattleMap,
    required this.animalTypeMap,
    required this.breedMap,
    required this.isSelectionMode,
    required this.selectedRecords,
    required this.onSelectionModeChanged,
    required this.onSelectionChanged,
    required this.onRefresh,
  });

  @override
  State<TreatmentManagementTab> createState() => _TreatmentManagementTabState();
}

class _TreatmentManagementTabState extends State<TreatmentManagementTab> {
  String _searchQuery = '';
  String? _selectedStatus;
  String? _selectedCategory;
  String? _selectedCattleId;
  bool _showActiveOnly = false;
  bool _showEmergencyOnly = false;
  bool _showWithdrawalPeriod = false;

  List<TreatmentRecordIsar> get _filteredRecords {
    return widget.treatmentRecords.where((record) {
      // Search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final cattle = widget.cattleMap[record.cattleBusinessId];
        final cattleName = cattle?.name?.toLowerCase() ?? '';
        final cattleTag = cattle?.tagId?.toLowerCase() ?? '';
        final medicationName = record.medicationName?.toLowerCase() ?? '';
        final reason = record.reason?.toLowerCase() ?? '';

        if (!cattleName.contains(query) &&
            !cattleTag.contains(query) &&
            !medicationName.contains(query) &&
            !reason.contains(query)) {
          return false;
        }
      }

      // Status filter
      if (_selectedStatus != null && record.status != _selectedStatus) {
        return false;
      }

      // Category filter
      if (_selectedCategory != null && record.medicationCategory != _selectedCategory) {
        return false;
      }

      // Cattle filter
      if (_selectedCattleId != null && record.cattleBusinessId != _selectedCattleId) {
        return false;
      }

      // Active only filter
      if (_showActiveOnly && record.status != 'Active') {
        return false;
      }

      // Emergency filter
      if (_showEmergencyOnly && !record.isEmergency) {
        return false;
      }

      // Withdrawal period filter
      if (_showWithdrawalPeriod) {
        final now = DateTime.now();
        final milkWithdrawal = record.milkWithdrawalEndDate;
        final meatWithdrawal = record.meatWithdrawalEndDate;

        if ((milkWithdrawal == null || milkWithdrawal.isBefore(now)) &&
            (meatWithdrawal == null || meatWithdrawal.isBefore(now))) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _selectedStatus = null;
      _selectedCategory = null;
      _selectedCattleId = null;
      _showActiveOnly = false;
      _showEmergencyOnly = false;
      _showWithdrawalPeriod = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final filteredRecords = _filteredRecords;

    return Column(
      children: [
        // Filters Section
        _buildFiltersSection(),

        // Records List
        Expanded(
          child: filteredRecords.isEmpty
              ? _buildEmptyState()
              : RefreshIndicator(
                  onRefresh: () async => widget.onRefresh(),
                  child: ResponsiveGrid(
                    children: filteredRecords.map((record) => _buildTreatmentCard(record)).toList(),
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        children: [
          // Search Bar
          TextField(
            decoration: InputDecoration(
              hintText: 'Search by cattle name, medication, or reason...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () => setState(() => _searchQuery = ''),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onChanged: (value) => setState(() => _searchQuery = value),
          ),
          const SizedBox(height: 12),

          // Filter Chips Row
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // Status Filter
                _buildFilterChip(
                  'Status',
                  _selectedStatus,
                  HealthConstants.treatmentStatus,
                  (value) => setState(() => _selectedStatus = value),
                  Colors.blue,
                ),
                const SizedBox(width: 8),

                // Category Filter
                _buildFilterChip(
                  'Category',
                  _selectedCategory,
                  HealthConstants.medicationCategories,
                  (value) => setState(() => _selectedCategory = value),
                  Colors.indigo,
                ),
                const SizedBox(width: 8),

                // Active Only Filter
                FilterChip(
                  label: const Text('Active Only'),
                  selected: _showActiveOnly,
                  onSelected: (selected) => setState(() => _showActiveOnly = selected),
                  selectedColor: Colors.green[100],
                  checkmarkColor: Colors.green,
                ),
                const SizedBox(width: 8),

                // Emergency Filter
                FilterChip(
                  label: const Text('Emergency'),
                  selected: _showEmergencyOnly,
                  onSelected: (selected) => setState(() => _showEmergencyOnly = selected),
                  selectedColor: Colors.red[100],
                  checkmarkColor: Colors.red,
                ),
                const SizedBox(width: 8),

                // Withdrawal Period Filter
                FilterChip(
                  label: const Text('Withdrawal Period'),
                  selected: _showWithdrawalPeriod,
                  onSelected: (selected) => setState(() => _showWithdrawalPeriod = selected),
                  selectedColor: Colors.purple[100],
                  checkmarkColor: Colors.purple,
                ),
                const SizedBox(width: 8),

                // Clear Filters
                if (_searchQuery.isNotEmpty ||
                    _selectedStatus != null ||
                    _selectedCategory != null ||
                    _showActiveOnly ||
                    _showEmergencyOnly ||
                    _showWithdrawalPeriod)
                  ActionChip(
                    label: const Text('Clear All'),
                    onPressed: _clearFilters,
                    backgroundColor: Colors.grey[200],
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    String label,
    String? selectedValue,
    List<String> options,
    Function(String?) onChanged,
    Color color,
  ) {
    return PopupMenuButton<String>(
      itemBuilder: (context) => options.map((option) {
        return PopupMenuItem<String>(
          value: option,
          child: Text(option),
        );
      }).toList(),
      onSelected: onChanged,
      child: Chip(
        label: Text(selectedValue ?? label),
        backgroundColor: selectedValue != null ? color.withValues(alpha: 0.1) : null,
        side: selectedValue != null ? BorderSide(color: color) : null,
        deleteIcon: selectedValue != null ? const Icon(Icons.close, size: 18) : null,
        onDeleted: selectedValue != null ? () => onChanged(null) : null,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.medication_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No treatment records found',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add treatment records to track medications and therapies',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTreatmentCard(TreatmentRecordIsar record) {
    final cattle = widget.cattleMap[record.cattleBusinessId];
    final isSelected = widget.selectedRecords.contains(record.businessId);
    final statusColor = HealthConstants.getTreatmentStatusColor(record.status);
    final now = DateTime.now();

    // Check withdrawal periods
    final milkWithdrawal = record.milkWithdrawalEndDate;
    final meatWithdrawal = record.meatWithdrawalEndDate;
    final inMilkWithdrawal = milkWithdrawal != null && milkWithdrawal.isAfter(now);
    final inMeatWithdrawal = meatWithdrawal != null && meatWithdrawal.isAfter(now);

    return Card(
      margin: const EdgeInsets.all(8.0),
      elevation: isSelected ? 8 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected
            ? BorderSide(color: Colors.blue, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _onRecordTap(record),
        onLongPress: () => _onRecordLongPress(record),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  // Cattle Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          cattle?.name ?? 'Unknown Cattle',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Tag: ${cattle?.tagId ?? 'Unknown'}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Status
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                    ),
                    child: Text(
                      record.status ?? 'Unknown',
                      style: TextStyle(
                        fontSize: 12,
                        color: statusColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Medication Info
              Row(
                children: [
                  Icon(Icons.medication, size: 16, color: Colors.blue[600]),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      record.medicationName ?? 'Unknown Medication',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Dosage and Frequency
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Dosage',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          '${record.dosageAmount ?? 0} ${record.dosageUnit ?? ''}',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Frequency',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          record.frequency ?? 'Unknown',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Treatment Period
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Start Date',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          record.startDate != null
                              ? DateFormat('MMM dd, yyyy').format(record.startDate!)
                              : 'Unknown',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                  if (record.endDate != null)
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'End Date',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            DateFormat('MMM dd, yyyy').format(record.endDate!),
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                ],
              ),

              // Withdrawal Periods (if applicable)
              if (inMilkWithdrawal || inMeatWithdrawal) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: Colors.red[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.warning, size: 16, color: Colors.red[600]),
                          const SizedBox(width: 6),
                          Text(
                            'Withdrawal Period Active',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Colors.red[700],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      if (inMilkWithdrawal)
                        Text(
                          'Milk: Until ${DateFormat('MMM dd, yyyy').format(milkWithdrawal)}',
                          style: TextStyle(fontSize: 11, color: Colors.red[600]),
                        ),
                      if (inMeatWithdrawal)
                        Text(
                          'Meat: Until ${DateFormat('MMM dd, yyyy').format(meatWithdrawal)}',
                          style: TextStyle(fontSize: 11, color: Colors.red[600]),
                        ),
                    ],
                  ),
                ),
              ],

              // Flags Row
              const SizedBox(height: 8),
              Wrap(
                spacing: 6,
                runSpacing: 4,
                children: [
                  if (record.isEmergency)
                    _buildFlag('Emergency', Colors.red),
                  if (record.isPreventive)
                    _buildFlag('Preventive', Colors.green),
                  if (record.requiresFollowUp)
                    _buildFlag('Follow-up', Colors.blue),
                  if (record.medicationCategory != null)
                    _buildFlag(record.medicationCategory!, Colors.purple),
                ],
              ),

              // Cost (if available)
              if (record.cost != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.attach_money, size: 14, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      '\$${record.cost!.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],

              // Selection Checkbox
              if (widget.isSelectionMode) ...[
                const SizedBox(height: 8),
                Align(
                  alignment: Alignment.centerRight,
                  child: Checkbox(
                    value: isSelected,
                    onChanged: (selected) => widget.onSelectionChanged(
                      record.businessId ?? '',
                      selected ?? false,
                    ),
                    activeColor: Colors.blue,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFlag(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _onRecordTap(TreatmentRecordIsar record) {
    if (widget.isSelectionMode) {
      final isSelected = widget.selectedRecords.contains(record.businessId);
      widget.onSelectionChanged(record.businessId ?? '', !isSelected);
    } else {
      // TODO: Navigate to treatment detail screen or edit dialog
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Treatment details for ${record.medicationName}'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  void _onRecordLongPress(TreatmentRecordIsar record) {
    if (!widget.isSelectionMode) {
      widget.onSelectionModeChanged(true);
      widget.onSelectionChanged(record.businessId ?? '', true);
    }
  }

}
