import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/breeding_report_data.dart';

class BreedingDetailsTab extends StatefulWidget {
  final BreedingReportData reportData;

  const BreedingDetailsTab({
    Key? key,
    required this.reportData,
  }) : super(key: key);

  @override
  BreedingDetailsTabState createState() => BreedingDetailsTabState();
}

class BreedingDetailsTabState extends State<BreedingDetailsTab> {
  String? _sortColumn;
  bool _sortAscending = true;
  final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');

  @override
  Widget build(BuildContext context) {
    final records = List.from(widget.reportData.filteredRecords);

    // Apply sorting
    if (_sortColumn != null) {
      records.sort((a, b) {
        int comparison;
        switch (_sortColumn) {
          case 'date':
            comparison = a.date.compareTo(b.date);
            break;
          case 'cattle':
            comparison = a.cattleId.compareTo(b.cattleId);
            break;
          case 'bull':
            comparison = a.bullIdOrType.compareTo(b.bullIdOrType);
            break;
          case 'method':
            comparison = a.method.compareTo(b.method);
            break;
          case 'status':
            comparison = a.status.compareTo(b.status);
            break;
          case 'expected':
            final aDate = a.expectedDate ?? DateTime(0);
            final bDate = b.expectedDate ?? DateTime(0);
            comparison = aDate.compareTo(bDate);
            break;
          case 'cost':
            comparison = a.cost.compareTo(b.cost);
            break;
          default:
            comparison = 0;
        }
        return _sortAscending ? comparison : -comparison;
      });
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          sortColumnIndex: _getSortColumnIndex(),
          sortAscending: _sortAscending,
          columns: [
            DataColumn(
              label: const Text('Date'),
              onSort: (_, __) => _onSort('date'),
            ),
            DataColumn(
              label: const Text('Cattle'),
              onSort: (_, __) => _onSort('cattle'),
            ),
            DataColumn(
              label: const Text('Bull ID/Type'),
              onSort: (_, __) => _onSort('bull'),
            ),
            DataColumn(
              label: const Text('Method'),
              onSort: (_, __) => _onSort('method'),
            ),
            DataColumn(
              label: const Text('Status'),
              onSort: (_, __) => _onSort('status'),
            ),
            DataColumn(
              label: const Text('Expected Date'),
              onSort: (_, __) => _onSort('expected'),
            ),
            DataColumn(
              label: const Text('Cost'),
              numeric: true,
              onSort: (_, __) => _onSort('cost'),
            ),
          ],
          rows: records.map((record) {
            return DataRow(
              cells: [
                DataCell(Text(DateFormat('yyyy-MM-dd').format(record.date))),
                DataCell(Text(record.cattleId)),
                DataCell(Text(record.bullIdOrType)),
                DataCell(
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: record.method.toLowerCase() == 'natural'
                          ? const Color(0xFF2E7D32)
                          : const Color(0xFF1976D2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      record.method,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                DataCell(
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(record.status),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      record.status,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                DataCell(
                  Text(
                    record.expectedDate != null
                        ? DateFormat('yyyy-MM-dd').format(record.expectedDate!)
                        : '-',
                    style: TextStyle(
                      color: record.expectedDate != null
                          ? _getExpectedDateColor(record.expectedDate!)
                          : null,
                      fontWeight: record.expectedDate != null
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
                ),
                DataCell(
                  Text(
                    record.cost > 0 ? currencyFormat.format(record.cost) : '-',
                    style: TextStyle(
                      fontWeight:
                          record.cost > 0 ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  void _onSort(String column) {
    setState(() {
      if (_sortColumn == column) {
        _sortAscending = !_sortAscending;
      } else {
        _sortColumn = column;
        _sortAscending = true;
      }
    });
  }

  int? _getSortColumnIndex() {
    switch (_sortColumn) {
      case 'date':
        return 0;
      case 'cattle':
        return 1;
      case 'bull':
        return 2;
      case 'method':
        return 3;
      case 'status':
        return 4;
      case 'expected':
        return 5;
      case 'cost':
        return 6;
      default:
        return null;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'successful':
        return const Color(0xFF2E7D32);
      case 'pending':
        return const Color(0xFFFFA000);
      case 'failed':
        return const Color(0xFFD32F2F);
      default:
        return const Color(0xFF757575);
    }
  }

  Color _getExpectedDateColor(DateTime expectedDate) {
    final daysUntil = expectedDate.difference(DateTime.now()).inDays;
    if (daysUntil <= 7) {
      return Colors.red;
    } else if (daysUntil <= 30) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }
}
