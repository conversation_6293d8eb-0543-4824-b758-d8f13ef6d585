enum FarmType {
  dairy,
  breeding,
  mixed,
}

class Farm {
  String id;
  String name;
  String ownerName;
  String ownerContact;
  String ownerEmail;
  double? latitude;
  double? longitude;
  String? address;
  FarmType farmType;
  int cattleCount;
  int capacity;
  DateTime lastUpdated;

  Farm({
    required this.id,
    required this.name,
    required this.ownerName,
    required this.ownerContact,
    required this.ownerEmail,
    this.latitude,
    this.longitude,
    this.address,
    required this.farmType,
    required this.cattleCount,
    required this.capacity,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'ownerName': ownerName,
      'ownerContact': ownerContact,
      'ownerEmail': ownerEmail,
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'farmType': farmType.toString().split('.').last,
      'cattleCount': cattleCount,
      'capacity': capacity,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory Farm.fromJson(Map<String, dynamic> json) {
    return Farm(
      id: json['id'],
      name: json['name'],
      ownerName: json['ownerName'],
      ownerContact: json['ownerContact'],
      ownerEmail: json['ownerEmail'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      address: json['address'],
      farmType: FarmType.values.firstWhere(
        (e) => e.toString().split('.').last == json['farmType'],
      ),
      cattleCount: json['cattleCount'],
      capacity: json['capacity'],
      lastUpdated: DateTime.parse(json['lastUpdated']),
    );
  }
}
                                                                        