import 'package:flutter/material.dart';
import '../constants/app_icons.dart';

class IconPicker extends StatelessWidget {
  final IconData? selectedIcon;
  final Function(IconData) onIconSelected;

  const IconPicker({
    Key? key,
    required this.selectedIcon,
    required this.onIconSelected,
  }) : super(key: key);

  static const Map<String, List<IconData>> categorizedIcons = {
    'Farm & Animals': [
      AppIcons.cow, // Cow
      AppIcons.buffalo, // Buffalo
      AppIcons.goat, // Goat
      AppIcons.sheep, // Sheep
      AppIcons.horse, // Horse
      Icons.pets, // Cat/Dog
      Icons.pets_outlined, // Cat/Dog outline
      Icons.cruelty_free, // Animal face
      Icons.cruelty_free_outlined, // Animal face outline
      Icons.emoji_nature, // Butterfly
      Icons.emoji_nature_outlined, // Butterfly outline
      Icons.bug_report, // Bug/insect
      Icons.bug_report_outlined, // Bug/insect outline
      Icons.spa, // Leaf/plant life
      Icons.spa_outlined, // Leaf/plant life outline
      Icons.park, // Tree with animal
      Icons.park_outlined, // Tree with animal outline
    ],
    'Farm Operations': [
      AppIcons.farm, // Farm icon
      AppIcons.pasture, // Pasture icon
      AppIcons.grass, // Grass/feed icon
      AppIcons.water, // Water icon
      AppIcons.fence, // Fence icon
      AppIcons.nature, // Nature/eco icon
      Icons.agriculture, // Tractor/farming
      Icons.agriculture_outlined, // Tractor/farming outline
      Icons.grass, // Grass/pasture
      Icons.grass_outlined, // Grass/pasture outline
      Icons.eco, // Leaf
      Icons.eco_outlined, // Leaf outline
      Icons.water_drop, // Water
      Icons.water_drop_outlined, // Water outline
      Icons.fence, // Fence
      Icons.fence_outlined, // Fence outline
      Icons.yard, // House with yard
      Icons.yard_outlined, // House with yard outline
    ],
    'Breeding & Health': [
      AppIcons.breeding, // Breeding icon
      AppIcons.health, // Health icon
      AppIcons.medicine, // Medicine icon
      AppIcons.vaccination, // Vaccination icon
      AppIcons.weight, // Weight icon
      Icons.monitor_heart, // Heart monitor
      Icons.favorite, // Heart
      Icons.medical_services, // Medical kit
      Icons.medication, // Medicine
      Icons.healing, // Bandage
      Icons.vaccines, // Vaccine
      Icons.fitness_center, // Fitness/health
    ],
    'Money & Finance': [
      AppIcons.transaction, // Transaction icon
      AppIcons.income, // Income icon
      AppIcons.expense, // Expense icon
      AppIcons.money, // Money icon
      AppIcons.milk, // Milk sales icon
      AppIcons.receipt, // Receipt icon
      AppIcons.store, // Store/shop icon
      Icons.attach_money, // Dollar sign
      Icons.money_off, // Money crossed out
      Icons.account_balance_wallet, // Wallet
      Icons.credit_card, // Credit card
      Icons.payment, // Payment
      Icons.receipt_long, // Receipt
      Icons.currency_exchange, // Currency exchange
      Icons.savings, // Savings/piggy bank
    ],
    'Equipment & Tools': [
      Icons.build, // Wrench
      Icons.handyman, // Tools
      Icons.construction, // Construction
      Icons.precision_manufacturing, // Manufacturing
      Icons.home_repair_service, // Repair service
      Icons.plumbing, // Plumbing
      Icons.electrical_services, // Electrical
    ],
    'Transport & Logistics': [
      Icons.local_shipping, // Shipping truck
      Icons.delivery_dining, // Delivery
      Icons.fire_truck, // Fire truck
      Icons.two_wheeler, // Two-wheeler
      Icons.directions_car, // Car
      Icons.garage, // Garage
      Icons.warehouse, // Warehouse
    ],
    'Business & Services': [
      AppIcons.store, // Store icon
      AppIcons.people, // People/staff icon
      Icons.store, // Store
      Icons.shopping_cart, // Shopping cart
      Icons.point_of_sale, // Point of sale
      Icons.request_quote, // Quote
      Icons.receipt, // Receipt
      Icons.inventory, // Inventory
      Icons.assignment, // Assignment/tasks
      Icons.support_agent, // Support agent
    ],
    'Weather & Environment': [
      Icons.wb_sunny, // Sun
      Icons.cloud, // Cloud
      Icons.water, // Water
      Icons.thermostat, // Thermostat
      Icons.air, // Air
      Icons.landscape, // Landscape
      Icons.terrain, // Terrain
      Icons.waves, // Waves
    ],
  };

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Select Icon',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2E7D32),
                ),
              ),
              const SizedBox(height: 16),
              ...categorizedIcons.entries
                  .map((category) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Text(
                              category.key,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF2E7D32),
                              ),
                            ),
                          ),
                          GridView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 6,
                              mainAxisSpacing: 8,
                              crossAxisSpacing: 8,
                              childAspectRatio: 1,
                            ),
                            itemCount: category.value.length,
                            itemBuilder: (context, index) {
                              final icon = category.value[index];
                              final isSelected =
                                  _compareIcons(selectedIcon, icon);

                              return InkWell(
                                onTap: () {
                                  onIconSelected(icon);
                                  Navigator.pop(context, icon);
                                },
                                borderRadius: BorderRadius.circular(8),
                                child: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: isSelected
                                          ? const Color(0xFF2E7D32)
                                          : Colors.grey,
                                      width: isSelected ? 2 : 1,
                                    ),
                                    borderRadius: BorderRadius.circular(8),
                                    color: isSelected
                                        ? const Color(0xFFE8F5E9)
                                        : null,
                                  ),
                                  child: Icon(
                                    icon,
                                    size: 24,
                                    color: isSelected
                                        ? const Color(0xFF2E7D32)
                                        : null,
                                  ),
                                ),
                              );
                            },
                          ),
                          const SizedBox(height: 8),
                        ],
                      ))
                  .toList(),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'Close',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Compare icons considering both codePoint and fontFamily
  bool _compareIcons(IconData? icon1, IconData? icon2) {
    if (icon1 == null || icon2 == null) return false;
    return icon1.codePoint == icon2.codePoint &&
        icon1.fontFamily == icon2.fontFamily;
  }
}
