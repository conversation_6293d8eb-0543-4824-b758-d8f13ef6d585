import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';

part 'animal_stage_isar.g.dart';

/// Represents a growth stage for animals in the system
@collection
class AnimalStageIsar {
  /// Isar database ID (auto-incremented)
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) for the stage - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// Name of the stage (e.g., "Heifer Calf", "Yearling Bull")
  @Index(caseSensitive: false)
  String? name;

  /// Description of this stage
  String? description;

  /// Icon code point to represent this stage in the UI
  int? iconCodePoint;

  /// Icon font family
  String? iconFontFamily;

  /// Color value for the stage
  int? colorValue;

  /// Minimum age in days for this stage (-1 for no minimum)
  int? minAgeDays;

  /// Maximum age in days for this stage (-1 for no maximum/unlimited)
  int? maxAgeDays;
  
  /// The ID of the animal type this stage belongs to
  @Index(type: IndexType.value)
  String? animalTypeId;
  
  /// Gender this stage applies to ("Male", "Female", or null for both)
  @Index()
  String? gender;
  
  /// Display order for this stage (lower values come first)
  int? displayOrder;

  /// Date when the record was created
  DateTime? createdAt;

  /// Date when the record was last updated
  DateTime? updatedAt;

  /// Default constructor for Isar
  AnimalStageIsar();

  /// Named constructor for creating a stage with values
  factory AnimalStageIsar.create({
    required String name,
    required String description,
    required IconData icon,
    required Color color,
    required String animalTypeId,
    required String? gender,
    int minAgeDays = 0,
    required int maxAgeDays,
    required int displayOrder,
  }) {
    return AnimalStageIsar()
      ..businessId = const Uuid().v4()
      ..name = name
      ..description = description
      ..iconCodePoint = icon.codePoint
      ..iconFontFamily = icon.fontFamily
      ..colorValue = color.value // ignore: deprecated_member_use (value is correct for storing ARGB int)
      ..minAgeDays = minAgeDays
      ..maxAgeDays = maxAgeDays
      ..animalTypeId = animalTypeId
      ..gender = gender
      ..displayOrder = displayOrder
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  /// Get the icon for this stage
  @ignore
  IconData get icon {
    if (iconCodePoint != null) {
      return IconData(
        iconCodePoint!,
        fontFamily: iconFontFamily ?? 'MaterialIcons',
      );
    }
    
    // Default icon
    return Icons.pets;
  }

  /// Set the icon data
  set icon(IconData value) {
    iconCodePoint = value.codePoint;
    iconFontFamily = value.fontFamily;
  }

  /// Get color for this stage
  @ignore
  Color get color {
    if (colorValue != null) {
      return Color(colorValue!);
    }
    
    // Default color
    return const Color(0xFF2E7D32);
  }

  /// Set the color
  set color(Color value) {
    colorValue = value.value; // ignore: deprecated_member_use (value is correct for storing ARGB int)
  }

  factory AnimalStageIsar.fromMap(Map<String, dynamic> map) {
    final stage = AnimalStageIsar()
      ..businessId = map['id'] as String?
      ..name = map['name'] as String?
      ..description = map['description'] as String?
      ..minAgeDays = map['minAgeDays'] as int?
      ..maxAgeDays = map['maxAgeDays'] as int?
      ..animalTypeId = map['animalTypeId'] as String?
      ..gender = map['gender'] as String?
      ..displayOrder = map['displayOrder'] as int?;

    // Parse icon data
    if (map['iconCodePoint'] != null) {
      stage.iconCodePoint = map['iconCodePoint'] as int;
      stage.iconFontFamily = map['iconFontFamily'] as String? ?? 'MaterialIcons';
    }

    // Parse color
    if (map['color'] != null) {
      stage.colorValue = int.parse(map['color'].toString());
    }

    // Parse dates
    if (map['createdAt'] != null) {
      stage.createdAt = DateTime.parse(map['createdAt'] as String);
    }

    if (map['updatedAt'] != null) {
      stage.updatedAt = DateTime.parse(map['updatedAt'] as String);
    }

    return stage;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'name': name,
      'description': description,
      'iconCodePoint': iconCodePoint,
      'iconFontFamily': iconFontFamily,
      'color': colorValue,
      'minAgeDays': minAgeDays,
      'maxAgeDays': maxAgeDays,
      'animalTypeId': animalTypeId,
      'gender': gender,
      'displayOrder': displayOrder,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  AnimalStageIsar copyWith({
    String? businessId,
    String? name,
    String? description,
    IconData? icon,
    Color? color,
    int? minAgeDays,
    int? maxAgeDays,
    String? animalTypeId,
    String? gender,
    int? displayOrder,
  }) {
    final result = AnimalStageIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..name = name ?? this.name
      ..description = description ?? this.description
      ..minAgeDays = minAgeDays ?? this.minAgeDays
      ..maxAgeDays = maxAgeDays ?? this.maxAgeDays
      ..animalTypeId = animalTypeId ?? this.animalTypeId
      ..gender = gender ?? this.gender
      ..displayOrder = displayOrder ?? this.displayOrder
      ..createdAt = createdAt
      ..updatedAt = DateTime.now();

    if (icon != null) {
      result.icon = icon;
    } else if (iconCodePoint != null) {
      result.iconCodePoint = iconCodePoint;
      result.iconFontFamily = iconFontFamily;
    }

    if (color != null) {
      result.color = color;
    } else if (colorValue != null) {
      result.colorValue = colorValue;
    }

    return result;
  }

  /// Simple JSON serialization without using generated code
  Map<String, dynamic> toJson() => toMap();
  
  /// Simple JSON deserialization without using generated code
  factory AnimalStageIsar.fromJson(Map<String, dynamic> json) => 
      AnimalStageIsar.fromMap(json);
} 