import 'package:flutter/material.dart';
import '../models/milk_report_data.dart';
import '../report_tabs/milk_summary_tab.dart';
import '../report_tabs/milk_details_tab.dart';
import '../dialogs/export_milk_report_dialog.dart';
import '../../Milk Records/models/milk_record.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class MilkReportScreen extends StatefulWidget {
  const MilkReportScreen({Key? key}) : super(key: key);

  @override
  MilkReportScreenState createState() => MilkReportScreenState();
}

class MilkReportScreenState extends State<MilkReportScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late MilkReportData reportData;
  DateTime? startDate;
  DateTime? endDate;
  String? selectedCattleId;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadMilkRecords();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadMilkRecords() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      final box = await Hive.openBox<MilkRecord>('milk_records');
      
      setState(() {
        