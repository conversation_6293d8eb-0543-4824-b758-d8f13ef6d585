// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'treatment_record_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetTreatmentRecordIsarCollection on Isar {
  IsarCollection<TreatmentRecordIsar> get treatmentRecordIsars =>
      this.collection();
}

const TreatmentRecordIsarSchema = CollectionSchema(
  name: r'TreatmentRecordIsar',
  id: -1898908446747357461,
  properties: {
    r'administeredBy': PropertySchema(
      id: 0,
      name: r'administeredBy',
      type: IsarType.string,
    ),
    r'batchNumber': PropertySchema(
      id: 1,
      name: r'batchNumber',
      type: IsarType.string,
    ),
    r'businessId': PropertySchema(
      id: 2,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'cattleBusinessId': PropertySchema(
      id: 3,
      name: r'cattleBusinessId',
      type: IsarType.string,
    ),
    r'cattleName': PropertySchema(
      id: 4,
      name: r'cattleName',
      type: IsarType.string,
    ),
    r'cattleTagId': PropertySchema(
      id: 5,
      name: r'cattleTagId',
      type: IsarType.string,
    ),
    r'cost': PropertySchema(
      id: 6,
      name: r'cost',
      type: IsarType.double,
    ),
    r'createdAt': PropertySchema(
      id: 7,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'createdBy': PropertySchema(
      id: 8,
      name: r'createdBy',
      type: IsarType.string,
    ),
    r'dosageAmount': PropertySchema(
      id: 9,
      name: r'dosageAmount',
      type: IsarType.double,
    ),
    r'dosageUnit': PropertySchema(
      id: 10,
      name: r'dosageUnit',
      type: IsarType.string,
    ),
    r'durationDays': PropertySchema(
      id: 11,
      name: r'durationDays',
      type: IsarType.long,
    ),
    r'effectiveness': PropertySchema(
      id: 12,
      name: r'effectiveness',
      type: IsarType.string,
    ),
    r'endDate': PropertySchema(
      id: 13,
      name: r'endDate',
      type: IsarType.dateTime,
    ),
    r'expiryDate': PropertySchema(
      id: 14,
      name: r'expiryDate',
      type: IsarType.dateTime,
    ),
    r'farmBusinessId': PropertySchema(
      id: 15,
      name: r'farmBusinessId',
      type: IsarType.string,
    ),
    r'frequency': PropertySchema(
      id: 16,
      name: r'frequency',
      type: IsarType.string,
    ),
    r'healthRecordId': PropertySchema(
      id: 17,
      name: r'healthRecordId',
      type: IsarType.string,
    ),
    r'instructions': PropertySchema(
      id: 18,
      name: r'instructions',
      type: IsarType.string,
    ),
    r'isCompleted': PropertySchema(
      id: 19,
      name: r'isCompleted',
      type: IsarType.bool,
    ),
    r'isEmergency': PropertySchema(
      id: 20,
      name: r'isEmergency',
      type: IsarType.bool,
    ),
    r'isPreventive': PropertySchema(
      id: 21,
      name: r'isPreventive',
      type: IsarType.bool,
    ),
    r'meatWithdrawalDays': PropertySchema(
      id: 22,
      name: r'meatWithdrawalDays',
      type: IsarType.long,
    ),
    r'meatWithdrawalEndDate': PropertySchema(
      id: 23,
      name: r'meatWithdrawalEndDate',
      type: IsarType.dateTime,
    ),
    r'medicationCategory': PropertySchema(
      id: 24,
      name: r'medicationCategory',
      type: IsarType.string,
    ),
    r'medicationName': PropertySchema(
      id: 25,
      name: r'medicationName',
      type: IsarType.string,
    ),
    r'milkWithdrawalDays': PropertySchema(
      id: 26,
      name: r'milkWithdrawalDays',
      type: IsarType.long,
    ),
    r'milkWithdrawalEndDate': PropertySchema(
      id: 27,
      name: r'milkWithdrawalEndDate',
      type: IsarType.dateTime,
    ),
    r'nextAdministrationDate': PropertySchema(
      id: 28,
      name: r'nextAdministrationDate',
      type: IsarType.dateTime,
    ),
    r'notes': PropertySchema(
      id: 29,
      name: r'notes',
      type: IsarType.string,
    ),
    r'prescribedBy': PropertySchema(
      id: 30,
      name: r'prescribedBy',
      type: IsarType.string,
    ),
    r'reason': PropertySchema(
      id: 31,
      name: r'reason',
      type: IsarType.string,
    ),
    r'reminderSet': PropertySchema(
      id: 32,
      name: r'reminderSet',
      type: IsarType.bool,
    ),
    r'requiresFollowUp': PropertySchema(
      id: 33,
      name: r'requiresFollowUp',
      type: IsarType.bool,
    ),
    r'route': PropertySchema(
      id: 34,
      name: r'route',
      type: IsarType.string,
    ),
    r'sideEffects': PropertySchema(
      id: 35,
      name: r'sideEffects',
      type: IsarType.string,
    ),
    r'startDate': PropertySchema(
      id: 36,
      name: r'startDate',
      type: IsarType.dateTime,
    ),
    r'status': PropertySchema(
      id: 37,
      name: r'status',
      type: IsarType.string,
    ),
    r'supplier': PropertySchema(
      id: 38,
      name: r'supplier',
      type: IsarType.string,
    ),
    r'updatedAt': PropertySchema(
      id: 39,
      name: r'updatedAt',
      type: IsarType.dateTime,
    ),
    r'updatedBy': PropertySchema(
      id: 40,
      name: r'updatedBy',
      type: IsarType.string,
    )
  },
  estimateSize: _treatmentRecordIsarEstimateSize,
  serialize: _treatmentRecordIsarSerialize,
  deserialize: _treatmentRecordIsarDeserialize,
  deserializeProp: _treatmentRecordIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'farmBusinessId': IndexSchema(
      id: -7111559275757090744,
      name: r'farmBusinessId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'farmBusinessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'cattleBusinessId': IndexSchema(
      id: -1530790847330223488,
      name: r'cattleBusinessId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'cattleBusinessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'cattleTagId': IndexSchema(
      id: -2283963072638323009,
      name: r'cattleTagId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'cattleTagId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'healthRecordId': IndexSchema(
      id: -1518986408428023496,
      name: r'healthRecordId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'healthRecordId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'medicationCategory': IndexSchema(
      id: 2427748267229645926,
      name: r'medicationCategory',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'medicationCategory',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'status': IndexSchema(
      id: -107785170620420283,
      name: r'status',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'status',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _treatmentRecordIsarGetId,
  getLinks: _treatmentRecordIsarGetLinks,
  attach: _treatmentRecordIsarAttach,
  version: '3.1.0+1',
);

int _treatmentRecordIsarEstimateSize(
  TreatmentRecordIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.administeredBy;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.batchNumber;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.cattleBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.cattleName;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.cattleTagId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.createdBy;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.dosageUnit;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.effectiveness;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.farmBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.frequency;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.healthRecordId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.instructions;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.medicationCategory;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.medicationName;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.notes;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.prescribedBy;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.reason;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.route;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.sideEffects;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.status;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.supplier;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.updatedBy;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _treatmentRecordIsarSerialize(
  TreatmentRecordIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.administeredBy);
  writer.writeString(offsets[1], object.batchNumber);
  writer.writeString(offsets[2], object.businessId);
  writer.writeString(offsets[3], object.cattleBusinessId);
  writer.writeString(offsets[4], object.cattleName);
  writer.writeString(offsets[5], object.cattleTagId);
  writer.writeDouble(offsets[6], object.cost);
  writer.writeDateTime(offsets[7], object.createdAt);
  writer.writeString(offsets[8], object.createdBy);
  writer.writeDouble(offsets[9], object.dosageAmount);
  writer.writeString(offsets[10], object.dosageUnit);
  writer.writeLong(offsets[11], object.durationDays);
  writer.writeString(offsets[12], object.effectiveness);
  writer.writeDateTime(offsets[13], object.endDate);
  writer.writeDateTime(offsets[14], object.expiryDate);
  writer.writeString(offsets[15], object.farmBusinessId);
  writer.writeString(offsets[16], object.frequency);
  writer.writeString(offsets[17], object.healthRecordId);
  writer.writeString(offsets[18], object.instructions);
  writer.writeBool(offsets[19], object.isCompleted);
  writer.writeBool(offsets[20], object.isEmergency);
  writer.writeBool(offsets[21], object.isPreventive);
  writer.writeLong(offsets[22], object.meatWithdrawalDays);
  writer.writeDateTime(offsets[23], object.meatWithdrawalEndDate);
  writer.writeString(offsets[24], object.medicationCategory);
  writer.writeString(offsets[25], object.medicationName);
  writer.writeLong(offsets[26], object.milkWithdrawalDays);
  writer.writeDateTime(offsets[27], object.milkWithdrawalEndDate);
  writer.writeDateTime(offsets[28], object.nextAdministrationDate);
  writer.writeString(offsets[29], object.notes);
  writer.writeString(offsets[30], object.prescribedBy);
  writer.writeString(offsets[31], object.reason);
  writer.writeBool(offsets[32], object.reminderSet);
  writer.writeBool(offsets[33], object.requiresFollowUp);
  writer.writeString(offsets[34], object.route);
  writer.writeString(offsets[35], object.sideEffects);
  writer.writeDateTime(offsets[36], object.startDate);
  writer.writeString(offsets[37], object.status);
  writer.writeString(offsets[38], object.supplier);
  writer.writeDateTime(offsets[39], object.updatedAt);
  writer.writeString(offsets[40], object.updatedBy);
}

TreatmentRecordIsar _treatmentRecordIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = TreatmentRecordIsar();
  object.administeredBy = reader.readStringOrNull(offsets[0]);
  object.batchNumber = reader.readStringOrNull(offsets[1]);
  object.businessId = reader.readStringOrNull(offsets[2]);
  object.cattleBusinessId = reader.readStringOrNull(offsets[3]);
  object.cattleName = reader.readStringOrNull(offsets[4]);
  object.cattleTagId = reader.readStringOrNull(offsets[5]);
  object.cost = reader.readDoubleOrNull(offsets[6]);
  object.createdAt = reader.readDateTimeOrNull(offsets[7]);
  object.createdBy = reader.readStringOrNull(offsets[8]);
  object.dosageAmount = reader.readDoubleOrNull(offsets[9]);
  object.dosageUnit = reader.readStringOrNull(offsets[10]);
  object.durationDays = reader.readLongOrNull(offsets[11]);
  object.effectiveness = reader.readStringOrNull(offsets[12]);
  object.endDate = reader.readDateTimeOrNull(offsets[13]);
  object.expiryDate = reader.readDateTimeOrNull(offsets[14]);
  object.farmBusinessId = reader.readStringOrNull(offsets[15]);
  object.frequency = reader.readStringOrNull(offsets[16]);
  object.healthRecordId = reader.readStringOrNull(offsets[17]);
  object.id = id;
  object.instructions = reader.readStringOrNull(offsets[18]);
  object.isCompleted = reader.readBool(offsets[19]);
  object.isEmergency = reader.readBool(offsets[20]);
  object.isPreventive = reader.readBool(offsets[21]);
  object.meatWithdrawalDays = reader.readLongOrNull(offsets[22]);
  object.meatWithdrawalEndDate = reader.readDateTimeOrNull(offsets[23]);
  object.medicationCategory = reader.readStringOrNull(offsets[24]);
  object.medicationName = reader.readStringOrNull(offsets[25]);
  object.milkWithdrawalDays = reader.readLongOrNull(offsets[26]);
  object.milkWithdrawalEndDate = reader.readDateTimeOrNull(offsets[27]);
  object.nextAdministrationDate = reader.readDateTimeOrNull(offsets[28]);
  object.notes = reader.readStringOrNull(offsets[29]);
  object.prescribedBy = reader.readStringOrNull(offsets[30]);
  object.reason = reader.readStringOrNull(offsets[31]);
  object.reminderSet = reader.readBool(offsets[32]);
  object.requiresFollowUp = reader.readBool(offsets[33]);
  object.route = reader.readStringOrNull(offsets[34]);
  object.sideEffects = reader.readStringOrNull(offsets[35]);
  object.startDate = reader.readDateTimeOrNull(offsets[36]);
  object.status = reader.readStringOrNull(offsets[37]);
  object.supplier = reader.readStringOrNull(offsets[38]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[39]);
  object.updatedBy = reader.readStringOrNull(offsets[40]);
  return object;
}

P _treatmentRecordIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readDoubleOrNull(offset)) as P;
    case 7:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 8:
      return (reader.readStringOrNull(offset)) as P;
    case 9:
      return (reader.readDoubleOrNull(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readLongOrNull(offset)) as P;
    case 12:
      return (reader.readStringOrNull(offset)) as P;
    case 13:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 14:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 15:
      return (reader.readStringOrNull(offset)) as P;
    case 16:
      return (reader.readStringOrNull(offset)) as P;
    case 17:
      return (reader.readStringOrNull(offset)) as P;
    case 18:
      return (reader.readStringOrNull(offset)) as P;
    case 19:
      return (reader.readBool(offset)) as P;
    case 20:
      return (reader.readBool(offset)) as P;
    case 21:
      return (reader.readBool(offset)) as P;
    case 22:
      return (reader.readLongOrNull(offset)) as P;
    case 23:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 24:
      return (reader.readStringOrNull(offset)) as P;
    case 25:
      return (reader.readStringOrNull(offset)) as P;
    case 26:
      return (reader.readLongOrNull(offset)) as P;
    case 27:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 28:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 29:
      return (reader.readStringOrNull(offset)) as P;
    case 30:
      return (reader.readStringOrNull(offset)) as P;
    case 31:
      return (reader.readStringOrNull(offset)) as P;
    case 32:
      return (reader.readBool(offset)) as P;
    case 33:
      return (reader.readBool(offset)) as P;
    case 34:
      return (reader.readStringOrNull(offset)) as P;
    case 35:
      return (reader.readStringOrNull(offset)) as P;
    case 36:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 37:
      return (reader.readStringOrNull(offset)) as P;
    case 38:
      return (reader.readStringOrNull(offset)) as P;
    case 39:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 40:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _treatmentRecordIsarGetId(TreatmentRecordIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _treatmentRecordIsarGetLinks(
    TreatmentRecordIsar object) {
  return [];
}

void _treatmentRecordIsarAttach(
    IsarCollection<dynamic> col, Id id, TreatmentRecordIsar object) {
  object.id = id;
}

extension TreatmentRecordIsarByIndex on IsarCollection<TreatmentRecordIsar> {
  Future<TreatmentRecordIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  TreatmentRecordIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<TreatmentRecordIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<TreatmentRecordIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(TreatmentRecordIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(TreatmentRecordIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<TreatmentRecordIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<TreatmentRecordIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension TreatmentRecordIsarQueryWhereSort
    on QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QWhere> {
  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension TreatmentRecordIsarQueryWhere
    on QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QWhereClause> {
  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'farmBusinessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      farmBusinessIdEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'farmBusinessId',
        value: [farmBusinessId],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      farmBusinessIdNotEqualTo(String? farmBusinessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [farmBusinessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'farmBusinessId',
              lower: [],
              upper: [farmBusinessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      cattleBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleBusinessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      cattleBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cattleBusinessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      cattleBusinessIdEqualTo(String? cattleBusinessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleBusinessId',
        value: [cattleBusinessId],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      cattleBusinessIdNotEqualTo(String? cattleBusinessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleBusinessId',
              lower: [],
              upper: [cattleBusinessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleBusinessId',
              lower: [cattleBusinessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleBusinessId',
              lower: [cattleBusinessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleBusinessId',
              lower: [],
              upper: [cattleBusinessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      cattleTagIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleTagId',
        value: [null],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      cattleTagIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cattleTagId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      cattleTagIdEqualTo(String? cattleTagId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleTagId',
        value: [cattleTagId],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      cattleTagIdNotEqualTo(String? cattleTagId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [],
              upper: [cattleTagId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [cattleTagId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [cattleTagId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [],
              upper: [cattleTagId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      healthRecordIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'healthRecordId',
        value: [null],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      healthRecordIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'healthRecordId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      healthRecordIdEqualTo(String? healthRecordId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'healthRecordId',
        value: [healthRecordId],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      healthRecordIdNotEqualTo(String? healthRecordId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'healthRecordId',
              lower: [],
              upper: [healthRecordId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'healthRecordId',
              lower: [healthRecordId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'healthRecordId',
              lower: [healthRecordId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'healthRecordId',
              lower: [],
              upper: [healthRecordId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      medicationCategoryIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'medicationCategory',
        value: [null],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      medicationCategoryIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'medicationCategory',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      medicationCategoryEqualTo(String? medicationCategory) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'medicationCategory',
        value: [medicationCategory],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      medicationCategoryNotEqualTo(String? medicationCategory) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'medicationCategory',
              lower: [],
              upper: [medicationCategory],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'medicationCategory',
              lower: [medicationCategory],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'medicationCategory',
              lower: [medicationCategory],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'medicationCategory',
              lower: [],
              upper: [medicationCategory],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      statusIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'status',
        value: [null],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      statusIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'status',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      statusEqualTo(String? status) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'status',
        value: [status],
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterWhereClause>
      statusNotEqualTo(String? status) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'status',
              lower: [],
              upper: [status],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'status',
              lower: [status],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'status',
              lower: [status],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'status',
              lower: [],
              upper: [status],
              includeUpper: false,
            ));
      }
    });
  }
}

extension TreatmentRecordIsarQueryFilter on QueryBuilder<TreatmentRecordIsar,
    TreatmentRecordIsar, QFilterCondition> {
  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      administeredByIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'administeredBy',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      administeredByIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'administeredBy',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      administeredByEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'administeredBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      administeredByGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'administeredBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      administeredByLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'administeredBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      administeredByBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'administeredBy',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      administeredByStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'administeredBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      administeredByEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'administeredBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      administeredByContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'administeredBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      administeredByMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'administeredBy',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      administeredByIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'administeredBy',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      administeredByIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'administeredBy',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      batchNumberIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'batchNumber',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      batchNumberIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'batchNumber',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      batchNumberEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'batchNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      batchNumberGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'batchNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      batchNumberLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'batchNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      batchNumberBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'batchNumber',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      batchNumberStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'batchNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      batchNumberEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'batchNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      batchNumberContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'batchNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      batchNumberMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'batchNumber',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      batchNumberIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'batchNumber',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      batchNumberIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'batchNumber',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cattleBusinessId',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cattleBusinessId',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cattleBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cattleBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cattleBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleNameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cattleName',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleNameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cattleName',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleNameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleNameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cattleName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleNameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cattleName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleNameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cattleName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cattleName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cattleName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleNameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cattleName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleNameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cattleName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleName',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cattleName',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleTagIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cattleTagId',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleTagIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cattleTagId',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleTagIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleTagIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleTagIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleTagIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cattleTagId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleTagIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleTagIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleTagIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleTagIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cattleTagId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleTagIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleTagId',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      cattleTagIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cattleTagId',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      costIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cost',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      costIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cost',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      costEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cost',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      costGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cost',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      costLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cost',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      costBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cost',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdByIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdBy',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdByIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdBy',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdByEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdByGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdByLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdByBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdBy',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdByStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdByEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdByContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'createdBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdByMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'createdBy',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdByIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdBy',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      createdByIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'createdBy',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageAmountIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dosageAmount',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageAmountIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dosageAmount',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageAmountEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dosageAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageAmountGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dosageAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageAmountLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dosageAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageAmountBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dosageAmount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageUnitIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dosageUnit',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageUnitIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dosageUnit',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageUnitEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dosageUnit',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageUnitGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dosageUnit',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageUnitLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dosageUnit',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageUnitBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dosageUnit',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageUnitStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'dosageUnit',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageUnitEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'dosageUnit',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageUnitContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'dosageUnit',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageUnitMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'dosageUnit',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageUnitIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dosageUnit',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      dosageUnitIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'dosageUnit',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      durationDaysIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'durationDays',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      durationDaysIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'durationDays',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      durationDaysEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'durationDays',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      durationDaysGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'durationDays',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      durationDaysLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'durationDays',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      durationDaysBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'durationDays',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      effectivenessIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'effectiveness',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      effectivenessIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'effectiveness',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      effectivenessEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'effectiveness',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      effectivenessGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'effectiveness',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      effectivenessLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'effectiveness',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      effectivenessBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'effectiveness',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      effectivenessStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'effectiveness',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      effectivenessEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'effectiveness',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      effectivenessContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'effectiveness',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      effectivenessMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'effectiveness',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      effectivenessIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'effectiveness',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      effectivenessIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'effectiveness',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      endDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      endDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      endDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      endDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      endDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      endDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'endDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      expiryDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'expiryDate',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      expiryDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'expiryDate',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      expiryDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'expiryDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      expiryDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'expiryDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      expiryDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'expiryDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      expiryDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'expiryDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      farmBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      farmBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'farmBusinessId',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      farmBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      farmBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      farmBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      farmBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'farmBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      farmBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      farmBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      farmBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'farmBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      farmBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'farmBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      farmBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      farmBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'farmBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      frequencyIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'frequency',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      frequencyIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'frequency',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      frequencyEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'frequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      frequencyGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'frequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      frequencyLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'frequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      frequencyBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'frequency',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      frequencyStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'frequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      frequencyEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'frequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      frequencyContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'frequency',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      frequencyMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'frequency',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      frequencyIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'frequency',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      frequencyIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'frequency',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      healthRecordIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'healthRecordId',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      healthRecordIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'healthRecordId',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      healthRecordIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'healthRecordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      healthRecordIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'healthRecordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      healthRecordIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'healthRecordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      healthRecordIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'healthRecordId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      healthRecordIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'healthRecordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      healthRecordIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'healthRecordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      healthRecordIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'healthRecordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      healthRecordIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'healthRecordId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      healthRecordIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'healthRecordId',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      healthRecordIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'healthRecordId',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      instructionsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'instructions',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      instructionsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'instructions',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      instructionsEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'instructions',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      instructionsGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'instructions',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      instructionsLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'instructions',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      instructionsBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'instructions',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      instructionsStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'instructions',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      instructionsEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'instructions',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      instructionsContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'instructions',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      instructionsMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'instructions',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      instructionsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'instructions',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      instructionsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'instructions',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      isCompletedEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isCompleted',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      isEmergencyEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isEmergency',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      isPreventiveEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isPreventive',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      meatWithdrawalDaysIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'meatWithdrawalDays',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      meatWithdrawalDaysIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'meatWithdrawalDays',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      meatWithdrawalDaysEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'meatWithdrawalDays',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      meatWithdrawalDaysGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'meatWithdrawalDays',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      meatWithdrawalDaysLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'meatWithdrawalDays',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      meatWithdrawalDaysBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'meatWithdrawalDays',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      meatWithdrawalEndDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'meatWithdrawalEndDate',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      meatWithdrawalEndDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'meatWithdrawalEndDate',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      meatWithdrawalEndDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'meatWithdrawalEndDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      meatWithdrawalEndDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'meatWithdrawalEndDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      meatWithdrawalEndDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'meatWithdrawalEndDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      meatWithdrawalEndDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'meatWithdrawalEndDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationCategoryIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'medicationCategory',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationCategoryIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'medicationCategory',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationCategoryEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'medicationCategory',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationCategoryGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'medicationCategory',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationCategoryLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'medicationCategory',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationCategoryBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'medicationCategory',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationCategoryStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'medicationCategory',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationCategoryEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'medicationCategory',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationCategoryContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'medicationCategory',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationCategoryMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'medicationCategory',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationCategoryIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'medicationCategory',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationCategoryIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'medicationCategory',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationNameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'medicationName',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationNameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'medicationName',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationNameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'medicationName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationNameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'medicationName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationNameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'medicationName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationNameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'medicationName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'medicationName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'medicationName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationNameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'medicationName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationNameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'medicationName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'medicationName',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      medicationNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'medicationName',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      milkWithdrawalDaysIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'milkWithdrawalDays',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      milkWithdrawalDaysIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'milkWithdrawalDays',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      milkWithdrawalDaysEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'milkWithdrawalDays',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      milkWithdrawalDaysGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'milkWithdrawalDays',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      milkWithdrawalDaysLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'milkWithdrawalDays',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      milkWithdrawalDaysBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'milkWithdrawalDays',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      milkWithdrawalEndDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'milkWithdrawalEndDate',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      milkWithdrawalEndDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'milkWithdrawalEndDate',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      milkWithdrawalEndDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'milkWithdrawalEndDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      milkWithdrawalEndDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'milkWithdrawalEndDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      milkWithdrawalEndDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'milkWithdrawalEndDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      milkWithdrawalEndDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'milkWithdrawalEndDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      nextAdministrationDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'nextAdministrationDate',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      nextAdministrationDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'nextAdministrationDate',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      nextAdministrationDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'nextAdministrationDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      nextAdministrationDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'nextAdministrationDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      nextAdministrationDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'nextAdministrationDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      nextAdministrationDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'nextAdministrationDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      notesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      notesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      notesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      notesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      notesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      notesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'notes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      notesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      notesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      notesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      notesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'notes',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      notesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      notesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      prescribedByIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'prescribedBy',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      prescribedByIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'prescribedBy',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      prescribedByEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'prescribedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      prescribedByGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'prescribedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      prescribedByLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'prescribedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      prescribedByBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'prescribedBy',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      prescribedByStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'prescribedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      prescribedByEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'prescribedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      prescribedByContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'prescribedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      prescribedByMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'prescribedBy',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      prescribedByIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'prescribedBy',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      prescribedByIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'prescribedBy',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      reasonIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reason',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      reasonIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reason',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      reasonEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reason',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      reasonGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'reason',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      reasonLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'reason',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      reasonBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'reason',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      reasonStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'reason',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      reasonEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'reason',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      reasonContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'reason',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      reasonMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'reason',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      reasonIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reason',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      reasonIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'reason',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      reminderSetEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reminderSet',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      requiresFollowUpEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'requiresFollowUp',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      routeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'route',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      routeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'route',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      routeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'route',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      routeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'route',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      routeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'route',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      routeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'route',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      routeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'route',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      routeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'route',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      routeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'route',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      routeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'route',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      routeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'route',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      routeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'route',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      sideEffectsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'sideEffects',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      sideEffectsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'sideEffects',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      sideEffectsEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sideEffects',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      sideEffectsGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sideEffects',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      sideEffectsLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sideEffects',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      sideEffectsBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sideEffects',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      sideEffectsStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'sideEffects',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      sideEffectsEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'sideEffects',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      sideEffectsContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'sideEffects',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      sideEffectsMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'sideEffects',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      sideEffectsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sideEffects',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      sideEffectsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'sideEffects',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      startDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      startDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      startDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      startDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      startDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      startDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'startDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      statusIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'status',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      statusIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'status',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      statusEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      statusGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      statusLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      statusBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'status',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      statusStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      statusEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      statusContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'status',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      statusMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'status',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      statusIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      statusIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'status',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      supplierIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'supplier',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      supplierIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'supplier',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      supplierEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'supplier',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      supplierGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'supplier',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      supplierLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'supplier',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      supplierBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'supplier',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      supplierStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'supplier',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      supplierEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'supplier',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      supplierContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'supplier',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      supplierMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'supplier',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      supplierIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'supplier',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      supplierIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'supplier',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedByIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedBy',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedByIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedBy',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedByEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedByGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedByLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedByBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedBy',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedByStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedByEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedByContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'updatedBy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedByMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'updatedBy',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedByIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedBy',
        value: '',
      ));
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterFilterCondition>
      updatedByIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'updatedBy',
        value: '',
      ));
    });
  }
}

extension TreatmentRecordIsarQueryObject on QueryBuilder<TreatmentRecordIsar,
    TreatmentRecordIsar, QFilterCondition> {}

extension TreatmentRecordIsarQueryLinks on QueryBuilder<TreatmentRecordIsar,
    TreatmentRecordIsar, QFilterCondition> {}

extension TreatmentRecordIsarQuerySortBy
    on QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QSortBy> {
  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByAdministeredBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'administeredBy', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByAdministeredByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'administeredBy', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByBatchNumber() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'batchNumber', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByBatchNumberDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'batchNumber', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByCattleBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByCattleBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByCattleName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleName', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByCattleNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleName', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByCattleTagId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByCattleTagIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByCost() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cost', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByCostDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cost', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByCreatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByCreatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByDosageAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dosageAmount', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByDosageAmountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dosageAmount', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByDosageUnit() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dosageUnit', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByDosageUnitDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dosageUnit', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByDurationDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'durationDays', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByDurationDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'durationDays', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByEffectiveness() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'effectiveness', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByEffectivenessDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'effectiveness', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByExpiryDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'expiryDate', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByExpiryDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'expiryDate', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByFrequency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'frequency', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByFrequencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'frequency', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByHealthRecordId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthRecordId', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByHealthRecordIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthRecordId', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByInstructions() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'instructions', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByInstructionsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'instructions', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByIsCompleted() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByIsCompletedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByIsEmergency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEmergency', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByIsEmergencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEmergency', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByIsPreventive() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPreventive', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByIsPreventiveDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPreventive', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByMeatWithdrawalDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'meatWithdrawalDays', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByMeatWithdrawalDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'meatWithdrawalDays', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByMeatWithdrawalEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'meatWithdrawalEndDate', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByMeatWithdrawalEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'meatWithdrawalEndDate', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByMedicationCategory() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'medicationCategory', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByMedicationCategoryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'medicationCategory', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByMedicationName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'medicationName', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByMedicationNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'medicationName', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByMilkWithdrawalDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkWithdrawalDays', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByMilkWithdrawalDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkWithdrawalDays', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByMilkWithdrawalEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkWithdrawalEndDate', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByMilkWithdrawalEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkWithdrawalEndDate', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByNextAdministrationDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'nextAdministrationDate', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByNextAdministrationDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'nextAdministrationDate', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByPrescribedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'prescribedBy', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByPrescribedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'prescribedBy', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByReason() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reason', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByReasonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reason', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByReminderSet() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reminderSet', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByReminderSetDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reminderSet', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByRequiresFollowUp() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'requiresFollowUp', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByRequiresFollowUpDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'requiresFollowUp', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByRoute() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'route', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByRouteDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'route', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortBySideEffects() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sideEffects', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortBySideEffectsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sideEffects', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortBySupplier() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supplier', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortBySupplierDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supplier', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByUpdatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      sortByUpdatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.desc);
    });
  }
}

extension TreatmentRecordIsarQuerySortThenBy
    on QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QSortThenBy> {
  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByAdministeredBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'administeredBy', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByAdministeredByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'administeredBy', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByBatchNumber() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'batchNumber', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByBatchNumberDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'batchNumber', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByCattleBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByCattleBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByCattleName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleName', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByCattleNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleName', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByCattleTagId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByCattleTagIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByCost() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cost', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByCostDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cost', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByCreatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByCreatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdBy', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByDosageAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dosageAmount', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByDosageAmountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dosageAmount', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByDosageUnit() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dosageUnit', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByDosageUnitDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dosageUnit', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByDurationDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'durationDays', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByDurationDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'durationDays', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByEffectiveness() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'effectiveness', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByEffectivenessDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'effectiveness', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByExpiryDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'expiryDate', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByExpiryDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'expiryDate', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByFarmBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByFarmBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'farmBusinessId', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByFrequency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'frequency', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByFrequencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'frequency', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByHealthRecordId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthRecordId', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByHealthRecordIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'healthRecordId', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByInstructions() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'instructions', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByInstructionsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'instructions', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByIsCompleted() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByIsCompletedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCompleted', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByIsEmergency() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEmergency', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByIsEmergencyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isEmergency', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByIsPreventive() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPreventive', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByIsPreventiveDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isPreventive', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByMeatWithdrawalDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'meatWithdrawalDays', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByMeatWithdrawalDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'meatWithdrawalDays', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByMeatWithdrawalEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'meatWithdrawalEndDate', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByMeatWithdrawalEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'meatWithdrawalEndDate', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByMedicationCategory() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'medicationCategory', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByMedicationCategoryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'medicationCategory', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByMedicationName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'medicationName', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByMedicationNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'medicationName', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByMilkWithdrawalDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkWithdrawalDays', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByMilkWithdrawalDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkWithdrawalDays', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByMilkWithdrawalEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkWithdrawalEndDate', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByMilkWithdrawalEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'milkWithdrawalEndDate', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByNextAdministrationDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'nextAdministrationDate', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByNextAdministrationDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'nextAdministrationDate', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByPrescribedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'prescribedBy', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByPrescribedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'prescribedBy', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByReason() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reason', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByReasonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reason', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByReminderSet() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reminderSet', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByReminderSetDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reminderSet', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByRequiresFollowUp() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'requiresFollowUp', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByRequiresFollowUpDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'requiresFollowUp', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByRoute() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'route', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByRouteDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'route', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenBySideEffects() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sideEffects', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenBySideEffectsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sideEffects', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenBySupplier() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supplier', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenBySupplierDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'supplier', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByUpdatedBy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.asc);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QAfterSortBy>
      thenByUpdatedByDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedBy', Sort.desc);
    });
  }
}

extension TreatmentRecordIsarQueryWhereDistinct
    on QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct> {
  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByAdministeredBy({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'administeredBy',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByBatchNumber({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'batchNumber', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByCattleBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cattleBusinessId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByCattleName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cattleName', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByCattleTagId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cattleTagId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByCost() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cost');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByCreatedBy({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdBy', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByDosageAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'dosageAmount');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByDosageUnit({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'dosageUnit', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByDurationDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'durationDays');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByEffectiveness({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'effectiveness',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'endDate');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByExpiryDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'expiryDate');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByFarmBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'farmBusinessId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByFrequency({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'frequency', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByHealthRecordId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'healthRecordId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByInstructions({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'instructions', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByIsCompleted() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isCompleted');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByIsEmergency() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isEmergency');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByIsPreventive() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isPreventive');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByMeatWithdrawalDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'meatWithdrawalDays');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByMeatWithdrawalEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'meatWithdrawalEndDate');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByMedicationCategory({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'medicationCategory',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByMedicationName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'medicationName',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByMilkWithdrawalDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'milkWithdrawalDays');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByMilkWithdrawalEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'milkWithdrawalEndDate');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByNextAdministrationDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'nextAdministrationDate');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByNotes({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notes', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByPrescribedBy({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'prescribedBy', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByReason({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'reason', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByReminderSet() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'reminderSet');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByRequiresFollowUp() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'requiresFollowUp');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByRoute({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'route', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctBySideEffects({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'sideEffects', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'startDate');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByStatus({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'status', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctBySupplier({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'supplier', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }

  QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QDistinct>
      distinctByUpdatedBy({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedBy', caseSensitive: caseSensitive);
    });
  }
}

extension TreatmentRecordIsarQueryProperty
    on QueryBuilder<TreatmentRecordIsar, TreatmentRecordIsar, QQueryProperty> {
  QueryBuilder<TreatmentRecordIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      administeredByProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'administeredBy');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      batchNumberProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'batchNumber');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      cattleBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cattleBusinessId');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      cattleNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cattleName');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      cattleTagIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cattleTagId');
    });
  }

  QueryBuilder<TreatmentRecordIsar, double?, QQueryOperations> costProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cost');
    });
  }

  QueryBuilder<TreatmentRecordIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      createdByProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdBy');
    });
  }

  QueryBuilder<TreatmentRecordIsar, double?, QQueryOperations>
      dosageAmountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dosageAmount');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      dosageUnitProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dosageUnit');
    });
  }

  QueryBuilder<TreatmentRecordIsar, int?, QQueryOperations>
      durationDaysProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'durationDays');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      effectivenessProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'effectiveness');
    });
  }

  QueryBuilder<TreatmentRecordIsar, DateTime?, QQueryOperations>
      endDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'endDate');
    });
  }

  QueryBuilder<TreatmentRecordIsar, DateTime?, QQueryOperations>
      expiryDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'expiryDate');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      farmBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'farmBusinessId');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      frequencyProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'frequency');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      healthRecordIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'healthRecordId');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      instructionsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'instructions');
    });
  }

  QueryBuilder<TreatmentRecordIsar, bool, QQueryOperations>
      isCompletedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isCompleted');
    });
  }

  QueryBuilder<TreatmentRecordIsar, bool, QQueryOperations>
      isEmergencyProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isEmergency');
    });
  }

  QueryBuilder<TreatmentRecordIsar, bool, QQueryOperations>
      isPreventiveProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isPreventive');
    });
  }

  QueryBuilder<TreatmentRecordIsar, int?, QQueryOperations>
      meatWithdrawalDaysProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'meatWithdrawalDays');
    });
  }

  QueryBuilder<TreatmentRecordIsar, DateTime?, QQueryOperations>
      meatWithdrawalEndDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'meatWithdrawalEndDate');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      medicationCategoryProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'medicationCategory');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      medicationNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'medicationName');
    });
  }

  QueryBuilder<TreatmentRecordIsar, int?, QQueryOperations>
      milkWithdrawalDaysProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'milkWithdrawalDays');
    });
  }

  QueryBuilder<TreatmentRecordIsar, DateTime?, QQueryOperations>
      milkWithdrawalEndDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'milkWithdrawalEndDate');
    });
  }

  QueryBuilder<TreatmentRecordIsar, DateTime?, QQueryOperations>
      nextAdministrationDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'nextAdministrationDate');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations> notesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notes');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      prescribedByProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'prescribedBy');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      reasonProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reason');
    });
  }

  QueryBuilder<TreatmentRecordIsar, bool, QQueryOperations>
      reminderSetProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reminderSet');
    });
  }

  QueryBuilder<TreatmentRecordIsar, bool, QQueryOperations>
      requiresFollowUpProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'requiresFollowUp');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations> routeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'route');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      sideEffectsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'sideEffects');
    });
  }

  QueryBuilder<TreatmentRecordIsar, DateTime?, QQueryOperations>
      startDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'startDate');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      statusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'status');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      supplierProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'supplier');
    });
  }

  QueryBuilder<TreatmentRecordIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }

  QueryBuilder<TreatmentRecordIsar, String?, QQueryOperations>
      updatedByProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedBy');
    });
  }
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TreatmentRecordIsar _$TreatmentRecordIsarFromJson(Map<String, dynamic> json) =>
    TreatmentRecordIsar()
      ..id = (json['id'] as num).toInt()
      ..businessId = json['businessId'] as String?
      ..farmBusinessId = json['farmBusinessId'] as String?
      ..cattleBusinessId = json['cattleBusinessId'] as String?
      ..cattleTagId = json['cattleTagId'] as String?
      ..cattleName = json['cattleName'] as String?
      ..healthRecordId = json['healthRecordId'] as String?
      ..medicationName = json['medicationName'] as String?
      ..medicationCategory = json['medicationCategory'] as String?
      ..dosageAmount = (json['dosageAmount'] as num?)?.toDouble()
      ..dosageUnit = json['dosageUnit'] as String?
      ..frequency = json['frequency'] as String?
      ..route = json['route'] as String?
      ..startDate = json['startDate'] == null
          ? null
          : DateTime.parse(json['startDate'] as String)
      ..endDate = json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String)
      ..durationDays = (json['durationDays'] as num?)?.toInt()
      ..status = json['status'] as String?
      ..reason = json['reason'] as String?
      ..instructions = json['instructions'] as String?
      ..prescribedBy = json['prescribedBy'] as String?
      ..administeredBy = json['administeredBy'] as String?
      ..cost = (json['cost'] as num?)?.toDouble()
      ..supplier = json['supplier'] as String?
      ..batchNumber = json['batchNumber'] as String?
      ..expiryDate = json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String)
      ..milkWithdrawalDays = (json['milkWithdrawalDays'] as num?)?.toInt()
      ..meatWithdrawalDays = (json['meatWithdrawalDays'] as num?)?.toInt()
      ..milkWithdrawalEndDate = json['milkWithdrawalEndDate'] == null
          ? null
          : DateTime.parse(json['milkWithdrawalEndDate'] as String)
      ..meatWithdrawalEndDate = json['meatWithdrawalEndDate'] == null
          ? null
          : DateTime.parse(json['meatWithdrawalEndDate'] as String)
      ..sideEffects = json['sideEffects'] as String?
      ..effectiveness = json['effectiveness'] as String?
      ..notes = json['notes'] as String?
      ..isPreventive = json['isPreventive'] as bool
      ..isEmergency = json['isEmergency'] as bool
      ..isCompleted = json['isCompleted'] as bool
      ..requiresFollowUp = json['requiresFollowUp'] as bool
      ..nextAdministrationDate = json['nextAdministrationDate'] == null
          ? null
          : DateTime.parse(json['nextAdministrationDate'] as String)
      ..reminderSet = json['reminderSet'] as bool
      ..createdAt = json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String)
      ..updatedAt = json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String)
      ..createdBy = json['createdBy'] as String?
      ..updatedBy = json['updatedBy'] as String?;

Map<String, dynamic> _$TreatmentRecordIsarToJson(
        TreatmentRecordIsar instance) =>
    <String, dynamic>{
      'id': instance.id,
      'businessId': instance.businessId,
      'farmBusinessId': instance.farmBusinessId,
      'cattleBusinessId': instance.cattleBusinessId,
      'cattleTagId': instance.cattleTagId,
      'cattleName': instance.cattleName,
      'healthRecordId': instance.healthRecordId,
      'medicationName': instance.medicationName,
      'medicationCategory': instance.medicationCategory,
      'dosageAmount': instance.dosageAmount,
      'dosageUnit': instance.dosageUnit,
      'frequency': instance.frequency,
      'route': instance.route,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'durationDays': instance.durationDays,
      'status': instance.status,
      'reason': instance.reason,
      'instructions': instance.instructions,
      'prescribedBy': instance.prescribedBy,
      'administeredBy': instance.administeredBy,
      'cost': instance.cost,
      'supplier': instance.supplier,
      'batchNumber': instance.batchNumber,
      'expiryDate': instance.expiryDate?.toIso8601String(),
      'milkWithdrawalDays': instance.milkWithdrawalDays,
      'meatWithdrawalDays': instance.meatWithdrawalDays,
      'milkWithdrawalEndDate':
          instance.milkWithdrawalEndDate?.toIso8601String(),
      'meatWithdrawalEndDate':
          instance.meatWithdrawalEndDate?.toIso8601String(),
      'sideEffects': instance.sideEffects,
      'effectiveness': instance.effectiveness,
      'notes': instance.notes,
      'isPreventive': instance.isPreventive,
      'isEmergency': instance.isEmergency,
      'isCompleted': instance.isCompleted,
      'requiresFollowUp': instance.requiresFollowUp,
      'nextAdministrationDate':
          instance.nextAdministrationDate?.toIso8601String(),
      'reminderSet': instance.reminderSet,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'createdBy': instance.createdBy,
      'updatedBy': instance.updatedBy,
    };
