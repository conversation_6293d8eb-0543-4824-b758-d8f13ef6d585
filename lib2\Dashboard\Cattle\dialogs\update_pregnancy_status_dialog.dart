                ),
                  SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                  ElevatedButton.icon(
                    onPressed: () => _addFarm(context),
                    icon: const Icon(Icons.add),
                    label: const Text('Add Farm'),
                  ),
                ],
              ),
            )
          : ListView.builder(
              itemCount: _farms.length,
              itemBuilder: (context, index) {
                final farm = _farms[index];
                final isSelected = farm.id == _selectedFarm?.id;

                return Card(
                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: ListTile(
                    leading: Icon(
                      _getFarmTypeIcon(farm.farmType),
                      color: Theme.of(context).primaryColor,
                    ),
                    title: Text(farm.name),
                    subtitle: Text('Owner: ${farm.ownerName}'),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (isSelected)
                          const Icon(Icons.check_circle, color: Colors.green)
                        else
                          IconButton(
                            icon: const Icon(Icons.check_circle_outline),
                            onPressed: () => _selectFarm(farm),
                            tooltip: 'Select Farm',
                          ),
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: () => _editFarm(context, farm),
                          tooltip: 'Edit Farm',
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: () => _deleteFarm(context, farm),
                          tooltip: 'Delete Farm',
                        ),
                      ],
                    ),
                    onTap: () => _editFarm(context, farm),
                  ),
                );
              },
            ),
    );
  }

  IconData _getFarmTypeIcon(FarmType type) {
    switch (type) {
      case FarmType.dairy:
        return Icons.local_drink;
      case FarmType.breeding:
        return Icons.pets;
      case FarmType.mixed:
        return Icons.all_inclusive;
    }
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  