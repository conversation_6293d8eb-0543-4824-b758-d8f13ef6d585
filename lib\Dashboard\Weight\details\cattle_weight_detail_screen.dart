import 'package:flutter/material.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/weight_record_isar.dart';
import '../services/weight_service.dart';
import '../../../constants/app_bar.dart';
import '../../../utils/message_utils.dart';
import '../../../widgets/reusable_tab_bar.dart';
import 'cattle_weight_analytics_tab.dart';
import 'cattle_weight_records_tab.dart';

class CattleWeightDetailScreen extends StatefulWidget {
  final CattleIsar cattle;
  final VoidCallback onRefresh;

  const CattleWeightDetailScreen({
    Key? key,
    required this.cattle,
    required this.onRefresh,
  }) : super(key: key);

  @override
  State<CattleWeightDetailScreen> createState() => _CattleWeightDetailScreenState();
}

class _CattleWeightDetailScreenState extends State<CattleWeightDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final WeightService _weightService = WeightService();
  
  List<WeightRecordIsar> _weightRecords = [];
  bool _isLoading = true;

  // Tab configuration using the reusable widget
  late List<TabItem> _tabs;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // Rebuild to update colors
    });

    // Define tabs using the reusable widget configuration
    _tabs = TabConfigurations.twoTabDetail(
      tab1Label: 'Analytics',
      tab1Icon: Icons.analytics,
      tab2Label: 'Records',
      tab2Icon: Icons.list_alt,
    );

    _loadWeightRecords();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadWeightRecords() async {
    try {
      setState(() => _isLoading = true);
      
      final records = await _weightService.getWeightRecordsByCattle(
        widget.cattle.businessId!
      );
      
      setState(() {
        _weightRecords = records;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        MessageUtils.showError(context, 'Error loading weight records: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final cattleName = widget.cattle.name ?? 'Unknown Cattle';
    final tagId = widget.cattle.tagId ?? '';
    final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;

    return Scaffold(
      appBar: AppBarConfig.withBack(
        title: displayName,
        context: context,
        actions: _buildAppBarActions(),
      ),
      body: Column(
        children: [
          // Use the reusable tab bar widget
          ReusableTabBar(
            tabController: _tabController,
            tabs: _tabs,
            context: context,
          ),
          // TabBarView
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      CattleWeightAnalyticsTab(
                        cattle: widget.cattle,
                        weightRecords: _weightRecords,
                      ),
                      CattleWeightRecordsTab(
                        cattle: widget.cattle,
                        weightRecords: _weightRecords,
                        onRefresh: _refreshData,
                      ),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildAppBarActions() {
    return [
      IconButton(
        icon: const Icon(Icons.refresh),
        onPressed: _refreshData,
        tooltip: 'Refresh',
      ),
    ];
  }

  Future<void> _refreshData() async {
    await _loadWeightRecords();
    widget.onRefresh(); // Refresh parent screen data
  }
}
