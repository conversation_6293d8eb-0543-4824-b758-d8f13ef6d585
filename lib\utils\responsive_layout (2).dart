import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import '../models/milk_settings.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../services/database_helper.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class MilkSettingsScreen extends StatefulWidget {
  const MilkSettingsScreen({Key? key}) : super(key: key);

  @override
  State<MilkSettingsScreen> createState() => _MilkSettingsScreenState();
}

class _MilkSettingsScreenState extends State<MilkSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  late MilkSettings _settings;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) {
        setState(() {
          _settings = MilkSettings(
            unit: 'liters',
            regularRate: 0.0,
            premiumRate: 0.0,
            bulkRate: 0.0,
          );
          _isLoading = false;
        });
        return;
      }

      final key = '${selectedFarmId}_milk_settings';
      final settingsJson = prefs.getString(key);

      setState(() {
        if (settingsJson != null) {
          _settings = MilkSettings.fromMap(Map<String, dynamic>.from(
            Map.castFrom(json.decode(settingsJson)),
          ));
        } else {
          _settings = MilkSettings(
            unit: 'liters',
            regularRate: 0.0,
            premiumRate: 0.0,
            bulkRate: 0.0,
          );
        }
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading milk settings: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId == null) return;

      final key = '${selectedFarmId}_milk_settings';
      await prefs.setString(key, jsonEncode(_settings.toMap()));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Settings saved successfully')),
        );
      }
    } catch (e) {
      debugPrint('Error saving milk settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error saving settings')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Milk Settings'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: ResponsiveHelper.getResponsivePadding(context),
          children: [
            Card(
              child: Padding(
                padding: ResponsiveHelper.getResponsivePadding(context),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Measurement Unit',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                    SegmentedButton<String>(
                      segments: const [
                        ButtonSegment<String>(
                          value: 'liters',
                          label: Text('Liters'),
                        ),
                        ButtonSegment<String>(
                          value: 'gallons',
                          label: Text('Gallons'),
                        ),
                      ],
                      selected: {_settings.unit},
                      onSelectionChanged: (Set<String> newSelection) {
                        setState(() {
                          _settings = _settings.copyWith(
                            unit: newSelection.first,
                          );
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
            Card(
              child: Padding(
                padding: ResponsiveHelper.getResponsivePadding(context),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Milk Sale Rates',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                    TextFormField(
                      decoration: InputDecoration(
                        labelText: 'Regular Rate (per ${_settings.unit})',
                        prefixText: '\$',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                      ],
                      initialValue: _settings.regularRate.toString(),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a rate';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        setState(() {
                          _settings = _settings.copyWith(
                            regularRate: double.tryParse(value) ?? 0.0,
                          );
                        });
                      },
                    ),
                    SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                    TextFormField(
                      decoration: InputDecoration(
                        labelText: 'Premium Rate (per ${_settings.unit})',
                        prefixText: '\$',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                      ],
                      initialValue: _settings.premiumRate.toString(),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a rate';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        setState(() {
                          _settings = _settings.copyWith(
                            premiumRate: double.tryParse(value) ?? 0.0,
                          );
                        });
                      },
                    ),
                    SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
                    TextFormField(
                      decoration: InputDecoration(
                        labelText: 'Bulk Rate (per ${_settings.unit})',
                        prefixText: '\$',
                        helperText: 'Special rate for bulk purchases',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                      ],
                      initialValue: _settings.bulkRate.toString(),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a rate';
                        }
                        if (double.tryParse(value) == null) {
                          retu