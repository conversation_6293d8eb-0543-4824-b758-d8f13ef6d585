import 'package:flutter/material.dart';

class AnimalType {
  final String id;
  final String name;
  final IconData icon;
  final int defaultGestationDays;
  final int defaultHeatCycleDays;
  final DateTime createdAt;
  final DateTime updatedAt;

  AnimalType({
    required this.id,
    required this.name,
    required this.icon,
    required this.defaultGestationDays,
    required this.defaultHeatCycleDays,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  factory AnimalType.fromMap(Map<String, dynamic> map) {
    return AnimalType(
      id: map['id'] as String,
      name: map['name'] as String,
      icon: IconData(
        map['iconCodePoint'] as int,
        fontFamily: map['ico