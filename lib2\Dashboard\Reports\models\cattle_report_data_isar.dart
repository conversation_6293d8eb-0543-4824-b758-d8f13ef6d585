import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import 'chart_data_isar.dart';
import 'report_data_isar.dart';

part 'cattle_report_data_isar.g.dart';

@collection
class CattleReportDataIsar with ReportDataIsarMixin {
  Id id = Isar.autoIncrement;

  @override
  @Index()
  String? get reportType => super.reportType;
  @override
  set reportType(String? value) => super.reportType = value;

  @override
  @Index(unique: true)
  String? get businessId => super.businessId;
  @override
  set businessId(String? value) => super.businessId = value;

  // Filtered cattle records for the report
  @ignore
  List<CattleIsar> get filteredCattle {
    // This is a placeholder implementation
    // In a real app, this data would be retrieved from a database
    return [];
  }

  int? totalCattle;
  int? activeCattle;
  int? inactiveCattle;
  int? calves;
  int? heifers;
  int? cows;
  int? bulls;
  int? steers;
  double? totalWeight;
  double? averageAge;

  // Lists for breed and group distributions
  List<String>? breedNames;
  List<int>? breedCounts;
  List<int>? breedColors;

  List<String>? groupNames;
  List<int>? groupCounts;
  List<int>? groupColors;

  CattleReportDataIsar();

  /// Factory constructor to create a new cattle report with common report data
  factory CattleReportDataIsar.create({
    required String title,
    DateTime? startDate,
    DateTime? endDate,
    String? filterCriteria,
    int? totalCattle,
    int? activeCattle,
    int? inactiveCattle,
    int? calves,
    int? heifers,
    int? cows,
    int? bulls,
    int? steers,
    double? totalWeight,
    double? averageAge,
    List<String>? breedNames,
    List<int>? breedCounts,
    List<int>? breedColors,
    List<String>? groupNames,
    List<int>? groupCounts,
    List<int>? groupColors,
  }) {
    final report = CattleReportDataIsar();

    // Initialize the base report properties
    report.initializeReport(
      reportType: 'cattle',
      title: title,
      startDate: startDate,
      endDate: endDate,
      filterCriteria: filterCriteria,
    );

    // Set the cattle-specific properties
    report.totalCattle = totalCattle;
    report.activeCattle = activeCattle;
    report.inactiveCattle = inactiveCattle;
    report.calves = calves;
    report.heifers = heifers;
    report.cows = cows;
    report.bulls = bulls;
    report.steers = steers;
    report.totalWeight = totalWeight;
    report.averageAge = averageAge;
    report.breedNames = breedNames;
    report.breedCounts = breedCounts;
    report.breedColors = breedColors;
    report.groupNames = groupNames;
    report.groupCounts = groupCounts;
    report.groupColors = groupColors;

    return report;
  }

  @override
  List<DataColumn> getTableColumns() {
    return [
      const DataColumn(label: Text('Category')),
      const DataColumn(label: Text('Count'), numeric: true),
      const DataColumn(label: Text('Percentage'), numeric: true),
    ];
  }

  @override
  List<DataRow> getTableRows() {
    final total = totalCattle ?? 0;
    final active = activeCattle ?? 0;
    final inactive = inactiveCattle ?? 0;
    final calvesCount = calves ?? 0;
    final heifersCount = heifers ?? 0;
    final cowsCount = cows ?? 0;
    final bullsCount = bulls ?? 0;
    final steersCount = steers ?? 0;

    return [
      DataRow(cells: [
        const DataCell(Text('Active')),
        DataCell(Text('$active')),
        DataCell(Text(
            '${total > 0 ? ((active / total) * 100).toStringAsFixed(1) : '0.0'}%')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Inactive')),
        DataCell(Text('$inactive')),
        DataCell(Text(
            '${total > 0 ? ((inactive / total) * 100).toStringAsFixed(1) : '0.0'}%')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Calves')),
        DataCell(Text('$calvesCount')),
        DataCell(Text(
            '${total > 0 ? ((calvesCount / total) * 100).toStringAsFixed(1) : '0.0'}%')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Heifers')),
        DataCell(Text('$heifersCount')),
        DataCell(Text(
            '${total > 0 ? ((heifersCount / total) * 100).toStringAsFixed(1) : '0.0'}%')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Cows')),
        DataCell(Text('$cowsCount')),
        DataCell(Text(
            '${total > 0 ? ((cowsCount / total) * 100).toStringAsFixed(1) : '0.0'}%')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Bulls')),
        DataCell(Text('$bullsCount')),
        DataCell(Text(
            '${total > 0 ? ((bullsCount / total) * 100).toStringAsFixed(1) : '0.0'}%')),
      ]),
      DataRow(cells: [
        const DataCell(Text('Steers')),
        DataCell(Text('$steersCount')),
        DataCell(Text(
            '${total > 0 ? ((steersCount / total) * 100).toStringAsFixed(1) : '0.0'}%')),
      ]),
    ];
  }

  @override
  Map<String, dynamic> getSummaryData() {
    return {
      'Total Cattle': totalCattle ?? 0,
      'Active': activeCattle ?? 0,
      'Inactive': inactiveCattle ?? 0,
      'Average Age (months)': averageAge?.toStringAsFixed(1) ?? '0',
      'Total Weight (kg)': totalWeight?.toStringAsFixed(1) ?? '0',
    };
  }

  @override
  List<ChartDataIsar> getChartData() {
    final result = <ChartDataIsar>[];

    // Add breed distribution chart data
    if (breedNames != null && breedCounts != null && breedColors != null) {
      for (int i = 0;
          i < breedNames!.length &&
              i < breedCounts!.length &&
              i < breedColors!.length;
          i++) {
        result.add(ChartDataIsar()
          ..label = breedNames![i]
          ..value = breedCounts![i].toDouble()
          ..colorValue = breedColors![i]);
      }
    }

    return result;
  }

  // Helper method to get group distribution chart data
  List<ChartDataIsar> getGroupChartData() {
    final result = <ChartDataIsar>[];

    // Add group distribution chart data
    if (groupNames != null && groupCounts != null && groupColors != null) {
      for (int i = 0;
          i < groupNames!.length &&
              i < groupCounts!.length &&
              i < groupColors!.length;
          i++) {
        result.add(ChartDataIsar()
          ..label = groupNames![i]
          ..value = groupCounts![i].toDouble()
          ..colorValue = groupColors![i]);
      }
    }

    return result;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map.addAll({
      'totalCattle': totalCattle,
      'activeCattle': activeCattle,
      'inactiveCattle': inactiveCattle,
      'calves': calves,
      'heifers': heifers,
      'cows': cows,
      'bulls': bulls,
      'steers': steers,
      'totalWeight': totalWeight,
      'averageAge': averageAge,
      'breedNames': breedNames,
      'breedCounts': breedCounts,
      'breedColors': breedColors,
      'groupNames': groupNames,
      'groupCounts': groupCounts,
      'groupColors': groupColors,
    });
    return map;
  }

  factory CattleReportDataIsar.fromMap(Map<String, dynamic> map) {
    final report = CattleReportDataIsar();

    // Initialize the base properties
    report.initFromMap(map);

    // Set cattle-specific properties
    report.totalCattle = map['totalCattle'] as int?;
    report.activeCattle = map['activeCattle'] as int?;
    report.inactiveCattle = map['inactiveCattle'] as int?;
    report.calves = map['calves'] as int?;
    report.heifers = map['heifers'] as int?;
    report.cows = map['cows'] as int?;
    report.bulls = map['bulls'] as int?;
    report.steers = map['steers'] as int?;
    report.totalWeight = map['totalWeight'] as double?;
    report.averageAge = map['averageAge'] as double?;

    // Handle lists
    report.breedNames = map['breedNames'] as List<String>?;
    report.breedCounts = map['breedCounts'] as List<int>?;
    report.breedColors = map['breedColors'] as List<int>?;
    report.groupNames = map['groupNames'] as List<String>?;
    report.groupCounts = map['groupCounts'] as List<int>?;
    report.groupColors = map['groupColors'] as List<int>?;

    return report;
  }

  /// Empty constructor for use when no data is available
  CattleReportDataIsar.empty() {
    // Initialize with default values
    initializeReport(
      reportType: 'cattle',
      title: 'Cattle Report',
      startDate: DateTime.now().subtract(const Duration(days: 90)),
      endDate: DateTime.now(),
      filterCriteria: '',
    );
    totalCattle = 0;
    activeCattle = 0;
    inactiveCattle = 0;
    averageAge = 0;
  }
}
