import 'package:flutter/material.dart';

class AppTheme {
  // Primary Colors
  static const Color primaryColor =
      Color(0xFF2E7D32); // Dark green for app bar and buttons

  // Background Colors
  static final Color scaffoldBackground =
      Colors.grey[50]!; // Light grey for screen backgrounds
  static const Color cardBackground = Colors.white; // White for cards

  // Text Colors
  static const Color primaryText = Colors.black87;
  static const Color secondaryText = Colors.black54;

  // Icon Colors
  static const Color iconColor = Color(0xFF2E7D32); // Dark green for icons
  static const Color actionButtonColor =
      Color(0xFFFF8C00); // Orange for action buttons like sync

  // Border Radius
  static final BorderRadius cardRadius = BorderRadius.circular(8.0);
  static final BorderRadius buttonRadius = BorderRadius.circular(8.0);

  // Padding
  static const EdgeInsets