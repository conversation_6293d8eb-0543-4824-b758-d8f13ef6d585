 ),
                ),
                const SizedBox(width: 8),

                // Age Filter
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButton<String>(
                    value: _selectedAgeFilter,
                    underline: const SizedBox(),
                    items: const [
                      DropdownMenuItem(
                          value: 'All', child: Text('Sort by Age')),
                      DropdownMenuItem(
                          value: 'Under 1 Year', child: Text('Under 1 Year')),
                      DropdownMenuItem(
                          value: '1-2 Years', child: Text('1-2 Years')),
                      DropdownMenuItem(
                          value: '2-5 Years', child: Text('2-5 Years')),
                      DropdownMenuItem(
                          value: 'Over 5 Years', child: Text('Over 5 Years')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedAgeFilter = value!;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 8),

                // Sort Options
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      DropdownButton<String>(
                        value: _sortBy,
                        underline: const SizedBox(),
                        items: const [
                          DropdownMenuItem(
                              value: 'Name', child: Text('Sort by Name')),
                          DropdownMenuItem(
                              value: 'Tag ID', child: Text('Sort by Tag ID')),
                          DropdownMenuItem(
                              value: 'Date of Birth',
                              child: Text('Sort by Date of Birth')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _sortBy = value!;
                          });
                        },
                      ),
                      IconButton(
                        icon: Icon(_sortAscending
                            ? Icons.arrow_upward
                            : Icons.arrow_downward),
                        onPressed: () {
                          setState(() {
                            _sortAscending = !_sortAscending;
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(List<String> cattleIds) async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        final bool isSelectAll = cattleIds.isEmpty;
        final int count =
            isSelectAll ? filteredCattle.length : cattleIds.length;

        return AlertDialog(
          title: Text('Delete ${isSelectAll ? 'All' : 'Selected'} Records'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                  'Are you sure you want to delete ${isSelectAll ? 'all' : count} cattle record${count > 1 ? 's' : ''}?'),
              SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
              const Text(
                'Warning: This action cannot be undone.',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('DELETE'),
            ),
          ],
        );
      },
    ).then((confirmed) async {
      if (confirmed == true) {
        if (cattleIds.isEmpty) {
          await DatabaseHelper.instance.deleteAllCattles();
        } else {
          await DatabaseHelper.instance.deleteCattlesByIds(cattleIds);
          setState(() {
            _selectedCattle.clear();
            _isSelectionMode = false;
          });
        }
        _loadData();
      }
    });
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    