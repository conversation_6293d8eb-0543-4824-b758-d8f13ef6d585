import 'package:flutter/material.dart';
import '../models/cattle.dart';
import '../models/health_record.dart';
import '../models/medication.dart';
import '../models/vaccination.dart';
import '../services/health_service.dart';
import 'package:uuid/uuid.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class HealthRecordsView extends StatefulWidget {
  final Cattle cattle;

  const HealthRecordsView({Key? key, required this.cattle}) : super(key: key);

  @override
  State<HealthRecordsView> createState() => _HealthRecordsViewState();
}

class _HealthRecordsViewState extends State<HealthRecordsView> {
  final HealthService _healthService = HealthService();
  List<HealthRecord> healthRecords = [];
  List<Medication> medications = [];
  List<Vaccination> vaccinations = [];

  @override
  void initState() {
    super.initState();
    _loadHealthData();
  }

  Future<void> _showAddHealthRecordDialog() async {
    String condition = '';
    String treatment = '';
    String veterinarian = '';
    double cost = 0.0;
    String notes = '';

    if (!mounted) return;
    final BuildContext dialogContext = context;

    await showDialog(
      context: dialogContext,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Add Health Record'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(labelText: 'Condition'),
                onChanged: (value) => condition = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Treatment'),
                onChanged: (value) => treatment = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Veterinarian'),
                onChanged: (value) => veterinarian = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Cost'),
                keyboardType: TextInputType.number,
                onChanged: (value) => cost = double.tryParse(value) ?? 0.0,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Notes'),
                onChanged: (value) => notes = value,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              if (condition.isNotEmpty && treatment.isNotEmpty) {
                final record = HealthRecord(
                  id: const Uuid().v4(),
                  cattleId: widget.cattle.id,
                  condition: condition,
                  treatment: treatment,
                  veterinarian: veterinarian,
                  cost: cost,
                  notes: notes,
                  date: DateTime.now(),
                );
                Navigator.pop(context);
                await _healthService.addHealthRecord(record);
                if (!mounted) return;
                _loadHealthData();
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _showAddMedicationDialog() async {
    String medName = '';
    String dosage = '';
    String frequency = '';
    double medCost = 0.0;
    String medNotes = '';

    if (!mounted) return;
    final BuildContext dialogContext = context;

    await showDialog(
      context: dialogContext,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Add Medication'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(labelText: 'Medication Name'),
                onChanged: (value) => medName = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Dosage'),
                onChanged: (value) => dosage = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Frequency'),
                onChanged: (value) => frequency = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Cost'),
                keyboardType: TextInputType.number,
                onChanged: (value) => medCost = double.tryParse(value) ?? 0.0,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Notes'),
                onChanged: (value) => medNotes = value,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              if (medName.isNotEmpty && dosage.isNotEmpty) {
                final medication = Medication(
                  id: const Uuid().v4(),
                  cattleId: widget.cattle.id,
                  name: medName,
                  dosage: dosage,
                  frequency: frequency,
                  startDate: DateTime.now(),
                  notes: medNotes,
                  cost: medCost,
                );
                Navigator.pop(context);
                await _healthService.addMedication(medication);
                if (!mounted) return;
                _loadHealthData();
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _showAddVaccinationDialog() async {
    String vacName = '';
    String batchNumber = '';
    String manufacturer = '';
    double vacCost = 0.0;
    String vacNotes = '';

    if (!mounted) return;
    final BuildContext dialogContext = context;

    await showDialog(
      context: dialogContext,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Add Vaccination'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: const InputDecoration(labelText: 'Vaccine Name'),
                onChanged: (value) => vacName = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Batch Number'),
                onChanged: (value) => batchNumber = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Manufacturer'),
                onChanged: (value) => manufacturer = value,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Cost'),
                keyboardType: TextInputType.number,
                onChanged: (value) => vacCost = double.tryParse(value) ?? 0.0,
              ),
              TextField(
                decoration: const InputDecoration(labelText: 'Notes'),
                onChanged: (value) => vacNotes = value,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              if (vacName.isNotEmpty && batchNumber.isNotEmpty) {
                final vaccination = Vaccination(
                  id: const Uuid().v4(),
                  cattleId: widget.cattle.id,
                  name: vacName,
                  batchNumber: batchNumber,
                  manufacturer: manufacturer,
                  cost: vacCost,
                  notes: vacNotes,
                  date: DateTime.now(),
                );
                Navigator.pop(conte