import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class MilkAlert {
  final String id;
  final String title;
  final String message;
  final String type;
  final DateTime timestamp;

  MilkAlert({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'message': message,
    'type': type,
    'timestamp': timestamp.toIso8601String(),
  };

  factory MilkAlert.fromJson(Map<String, dynamic> json) => MilkAlert(
    id: json['id'],
    title: json['title'],
    message: json['message'],
    type: json['type'],
    timestamp: DateTime.parse(json['timestamp']),
  );
}

class AlertsTab extends StatefulWidget {
  const AlertsTab({Key? key}) : super(key: key);

  @override
  AlertsTabState createState() => AlertsTabState();
}

class AlertsTabState extends State<AlertsTab> {
  bool _lowProductionAlerts = true;
  bool _missingEntryAlerts = true;
  bool _overproductionAlerts = true;
  List<MilkAlert> _alerts = [];
  Set<String> _dismissedAlerts = {};

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _loadAlerts();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _lowProductionAlerts = prefs.getBool('milk_low_production_alerts') ?? true;
      _missingEntryAlerts = prefs.getBool('milk_missing_entry_alerts') ?? true;
      _overproductionAlerts = prefs.getBool('milk_overproduction_alerts') ?? true;
      _dismissedAlerts = Set<String>.from(prefs.getStringList('milk_dismissed_alerts') ?? []);
    });
  }

  Future<void> _loadAlerts() async {
    // In a real app, these would come from a database or API
    setState(() {
      _alerts = [
        MilkAlert(
          id: '1',
          title: 'Low Production Alert',
          message: 'Cow #123 produced 20% below average today',
          type: 'low_production',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        MilkAlert(
          id: '2',
          title: 'Missing Entry Alert',
          message: 'No evening milk record for Cow #456',
          type: 'missing_entry',
          timestamp: DateTime.now().subtract(const Duration(hours: 5)),
        ),
      ];
    });
  }

  Future<void> _saveSetting(String key, bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, value);
  }

  Future<void> _dismissAlert(String alertId) async {
    setState(() {
      _dismissedAlerts.add(alertId);
    });

    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList('milk_dismissed_alerts', _dismissedAlerts.toList());
  }

  IconData _getAlertIcon(String type) {
    switch (type) {
      case 'low_production':
        return Icons.warning;
      case 'missing_entry':
        return Icons.error_outline;
      case 'overproduction':
        return Icons.trending_up;
      default:
        return Icons.notifications;
    }
  }

  Color _getAlertColor(String type) {
    switch (type) {
      case 'low_production':
        return Colors.orange;
      case 'missing_entry':
        return Colors.red;
      case 'overproduction':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    final activeAlerts = _alerts.where((alert) => !_dismissedAlerts.contains(alert.id)).toList();

    return Padding(
      padding: ResponsiveHelper.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveCard(
      child: Padding(
              padding: ResponsiveHelper.getResponsivePadding(context),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Alert Settings',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: ResponsiveSpacing.getMD(context)),
                  SwitchListTile(
                    title: const Text('Low Production Alerts'),
                    subtitle: const Text('Notify when production is below average'),
                    value: _lowProductionAlerts,
                    onChanged: (bool value) {
                      setState(() => _lowProductionAlerts = value);
                      _saveSetting('milk_low_production_alerts', value);
                    },
                  ),
                  SwitchListTile(
                    title: const Text('Missing Entry Alerts'),
                    subtitle: const Text('Notify when milk entries are missing'),
                    value: _missingEntryAlerts,
                    onChanged: (bool value) {
                      setState(() => _missingEntryAlerts = value);
                      _saveSetting('milk_missing_entry_alerts', value);
                    },
                  ),
                  SwitchListTile(
                    title: const Text('Overproduction Alerts'),
                    subtitle: const Text('Notify when production exceeds threshold'),
                    value: _overproductionAlerts,
                    onChanged: (bool value) {
                      setState(() => _overproductionAlerts = value);
                      _saveSetting('milk_overproduction_alerts', value);
                    },
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: ResponsiveSpacing.getMD(context)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Recent Alerts',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (_dismissedAlerts.isNotEmpty)
                TextButton(
                  onPressed: () async {
                    setState(() {
                      _dismissedAlerts.clear();
                    });
                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setStringList('milk_dismissed_alerts', []);
                  },
                  child: const Text('Show All'),
                ),
            ],
          ),
          SizedBox(height: ResponsiveSpacing.getSM(context)),
          Expanded(
            child: activeAlerts.isEmpty
                ? const Center(
                    child: Text(
                      'No active alerts',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  )
                : ListView.builder(
                    itemCount: activeAlerts.length,
                    itemBuilder: (context, index) {
                      final alert = activeAlerts[index];
                      return _buildAlertCard(alert);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertCard(MilkAlert alert) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          _getAlertIcon(alert.type),
          color: _getAlertColor(alert.type),
        ),
        title: Text(alert.title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(alert.message),
            const SizedBox(height: 4),
            Text(
              _getTimeAgo(alert.timestamp),
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        trailing: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => _dismissAlert(alert.id),
        ),
        isThreeLine: true,
      ),
    );
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'just now';
    }
  }
}
