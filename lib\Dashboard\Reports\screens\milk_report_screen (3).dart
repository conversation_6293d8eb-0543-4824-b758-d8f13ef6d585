      borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor: const Color(0xFF9C27B0).withOpacity(0.2),
                      child: const Icon(
                        Icons.analytics,
                        color: Color(0xFF9C27B0),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Calving Statistics',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem('Births', totalDeliveries.toString(),
                            Icons.child_care, Colors.green),
                        _buildStatItem('Calves', totalCalves.toString(), Icons.pets,
                            Colors.blue),
                        _buildStatItem('Normal', normalDeliveries.toString(),
                            Icons.check_circle, Colors.teal),
                        _buildStatItem('Assisted', assistedDeliveries.toString(),
                            Icons.medical_services, const Color(0xFF1565C0)),
                      ],
                    ),
                    SizedBox(height: ResponsiveSpacing.getMD(context)),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Calf Health',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                ),
                              ),
                              const SizedBox(height: 4),
                              LinearProgressIndicator(
                                value: totalCalves > 0
                                    ? healthyCalves / totalCalves
                                    : 0,
                                backgroundColor: Colors.grey[200],
                                color: Colors.green,
                                minHeight: 8,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 16),
                        CircleAvatar(
                          radius: 24,
                          backgroundColor: Colors.green.withOpacity(0.2),
                          child: Text(
                            '$healthPercentage%',
                            style: const TextStyle(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (totalCalves > 0) ...[
                      SizedBox(height: ResponsiveSpacing.getSM(context)),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          _buildHealthIndicator(
                              'Healthy', healthyCalves, Colors.green),
                          const SizedBox(width: 16),
                          _buildHealthIndicator('Weak', weakCalves, const Color(0xFF2196F3)),
                          const SizedBox(width: 16),
                          _buildHealthIndicator(
                              'Stillborn', stillbornCalves, const Color(0xFF1565C0)),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      }
    );
  }

  Widget _buildStatItem(
      String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        CircleAvatar(
          radius: 20,
          backgroundColor: color.withOpacity(0.2),
          child: Icon(icon, color: color, size: 24),
        ),
        SizedBox(height: ResponsiveSpacing.getSM(context)),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildHealthIndicator(String label, int count, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '$label: $count',
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    // Check if the pregnancy has progressed sufficiently
    bool canRecordBirth = false;
    String? pregnancyStatusMessage;
    
    if (widget.cattle.isPregnant == true) {
      if (widget.cattle.breedingDate != null) {
        // Calculate days since breeding
        final daysSinceBreeding = DateTime.now().difference(widget.cattle.breedingDate!).inDays;
        
        // Get the animal type's gestation period (or use default)
        final animalTypeId = widget.cattle.animalTypeId;
        final animalType = _animalTypes.firstWhere(
          (type) => type.id == animalTypeId,
          orElse: () => AnimalType(
            id: '',
            name: 'Unknown',
            icon: Icons.pets,
            defaultGestationDays: 283,
            defaultHeatCycleDays: 21,
            c