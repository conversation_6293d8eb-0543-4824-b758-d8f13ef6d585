import 'package:flutter/material.dart';
import 'dart:async';
import '../../Cattle/models/cattle_isar.dart';
import '../dialogs/vaccination_form_dialog.dart';
import '../widgets/vaccination_record_card.dart';
import '../../../utils/message_utils.dart';
import 'package:get_it/get_it.dart';
import '../../../services/streams/stream_service.dart';
import '../models/vaccination_record_isar.dart';
import '../services/health_service.dart';

class CattleVaccinationRecordsTab extends StatefulWidget {
  final CattleIsar cattle;
  final VoidCallback onCattleUpdated;

  const CattleVaccinationRecordsTab({
    Key? key,
    required this.cattle,
    required this.onCattleUpdated,
  }) : super(key: key);

  @override
  State<CattleVaccinationRecordsTab> createState() => _CattleVaccinationRecordsTabState();
}

class _CattleVaccinationRecordsTabState extends State<CattleVaccinationRecordsTab> {
  final HealthService _healthService = HealthService.instance;

  List<VaccinationRecordIsar> _vaccinationRecords = [];
  bool _isLoading = true;

  // Stream subscriptions
  StreamSubscription<Map<String, dynamic>>? _vaccinationChangeSubscription;

  @override
  void initState() {
    super.initState();
    _setupStreamSubscriptions();
    _loadVaccinationRecords();
  }

  @override
  void dispose() {
    _vaccinationChangeSubscription?.cancel();
    super.dispose();
  }

  void _setupStreamSubscriptions() {
    try {
      final streamService = GetIt.instance<StreamService>();
      _vaccinationChangeSubscription = streamService.vaccinationStream.listen((_) {
        _loadVaccinationRecords();
      });
    } catch (e) {
      debugPrint('Warning: Could not set up stream subscriptions: $e');
    }
  }

  Future<void> _loadVaccinationRecords() async {
    if (!mounted) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      await _healthService.initialize();
      final records = await _healthService.getVaccinationRecordsForCattle(widget.cattle.businessId ?? '');
      
      if (mounted) {
        setState(() {
          _vaccinationRecords = records;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        MessageUtils.showErrorSnackBar(context, 'Failed to load vaccination records: $e');
      }
    }
  }

  void _addVaccinationRecord() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VaccinationFormDialog(
          cattle: [widget.cattle],
          preSelectedCattle: widget.cattle,
          onRecordAdded: () {
            _loadVaccinationRecords();
            widget.onCattleUpdated();
          },
        ),
      ),
    );
  }

  void _editVaccinationRecord(VaccinationRecordIsar record) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VaccinationFormDialog(
          cattle: [widget.cattle],
          existingRecord: record,
          onRecordAdded: () {
            _loadVaccinationRecords();
            widget.onCattleUpdated();
          },
        ),
      ),
    );
  }

  Future<void> _deleteVaccinationRecord(VaccinationRecordIsar record) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Vaccination Record'),
        content: const Text('Are you sure you want to delete this vaccination record?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _healthService.deleteVaccinationRecord(record.businessId ?? '');
        _loadVaccinationRecords();
        widget.onCattleUpdated();
        if (mounted) {
          MessageUtils.showSuccessSnackBar(context, 'Vaccination record deleted successfully');
        }
      } catch (e) {
        if (mounted) {
          MessageUtils.showErrorSnackBar(context, 'Failed to delete vaccination record: $e');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Vaccination Status Summary
                _buildVaccinationStatusSummary(),
                
                // Vaccination Records List
                Expanded(
                  child: _vaccinationRecords.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          padding: const EdgeInsets.all(16.0),
                          itemCount: _vaccinationRecords.length,
                          itemBuilder: (context, index) {
                            final record = _vaccinationRecords[index];
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 8.0),
                              child: VaccinationRecordCard(
                                record: record,
                                cattle: widget.cattle,
                                onEdit: () => _editVaccinationRecord(record),
                                onDelete: () => _deleteVaccinationRecord(record),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addVaccinationRecord,
        backgroundColor: Colors.green,
        child: const Icon(Icons.vaccines, color: Colors.white),
      ),
    );
  }

  Widget _buildVaccinationStatusSummary() {
    final totalRecords = _vaccinationRecords.length;
    final upToDateRecords = _vaccinationRecords.where((record) {
      if (record.nextDueDate == null) return false;
      return record.nextDueDate!.isAfter(DateTime.now());
    }).length;
    final overdueRecords = _vaccinationRecords.where((record) {
      if (record.nextDueDate == null) return false;
      return record.nextDueDate!.isBefore(DateTime.now());
    }).length;

    return Container(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.vaccines, color: Colors.green, size: 24),
              const SizedBox(width: 12),
              const Text(
                'Vaccination Summary',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Total Vaccines',
                  totalRecords.toString(),
                  Icons.vaccines,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  'Up to Date',
                  upToDateRecords.toString(),
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  'Overdue',
                  overdueRecords.toString(),
                  Icons.warning,
                  Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.vaccines_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Vaccination Records',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add the first vaccination record for ${widget.cattle.name ?? 'this cattle'}',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _addVaccinationRecord,
            icon: const Icon(Icons.add),
            label: const Text('Add Vaccination Record'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
