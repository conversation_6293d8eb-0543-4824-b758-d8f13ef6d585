import 'package:flutter/material.dart';
import 'responsive_helper.dart';

/// A widget that builds different layouts based on screen size
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveLayout({
    Key? key,
    required this.mobile,
    this.tablet,
    this.desktop,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (ResponsiveHelper.isDesktop(context) && desktop != null) {
          return desktop!;
        } else if (ResponsiveHelper.isTablet(context) && tablet != null) {
          return tablet!;
        } else {
          return mobile;
        }
      },
    );
  }
}

/// A responsive grid view that adjusts columns based on screen size
class ResponsiveGridView extends StatelessWidget {
  final List<Widget> children;
  final int mobileColumns;
  final int tabletColumns;
  final int desktopColumns;
  final double? childAspectRatio;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;

  const ResponsiveGridView({
    Key? key,
    required this.children,
    this.mobileColumns = 2,
    this.tabletColumns = 3,
    this.desktopColumns = 4,
    this.childAspectRatio,
    this.mainAxisSpacing = 8.0,
    this.crossAxisSpacing = 8.0,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final crossAxisCount = ResponsiveHelper.getGridCrossAxisCount(
      context,
      mobileCount: mobileColumns,
      tabletCount: tabletColumns,
      desktopCount: desktopColumns,
    );

    final aspectRatio = childAspectRatio ?? 
        ResponsiveHelper.getGridChildAspectRatio(context);

    return GridView.count(
      crossAxisCount: crossAxisCount,
      childAspectRatio: aspectRatio,
      mainAxisSpacing: mainAxisSpacing,
      crossAxisSpacing: crossAxisSpacing,
      padding: padding ?? ResponsiveHelper.getResponsivePadding(context),
      physics: physics,
      shrinkWrap: shrinkWrap,
      children: children,
    );
  }
}

/// A responsive container that adjusts its constraints based on screen size
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double? mobileMaxWidth;
  final double? tabletMaxWidth;
  final double? desktopMaxWidth;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Alignment alignment;

  const ResponsiveContainer({
    Key? key,
    required this.child,
    this.mobileMaxWidth,
    this.tabletMaxWidth,
    this.desktopMaxWidth,
    this.padding,
    this.margin,
    this.alignment = Alignment.center,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double? maxWidth;
    
    if (ResponsiveHelper.isDesktop(context)) {
      maxWidth = desktopMaxWidth ?? 1200;
    } else if (ResponsiveHelper.isTablet(context)) {
      maxWidth = tabletMaxWidth ?? 800;
    } else {
      maxWidth = mobileMaxWidth;
    }

    return Container(
      alignment: alignment,
      padding: padding ?? ResponsiveHelper.getResponsivePadding(context),
      margin: margin,
      constraints: maxWidth != null ? BoxConstraints(maxWidth: maxWidth) : null,
      child: child,
    );
  }
}

/// A responsive dialog that adjusts its size based on screen size
class ResponsiveDialog extends StatelessWidget {
  final Widget child;
  final String? title;
  final List<Widget>? actions;
  final EdgeInsetsGeometry? contentPadding;
  final bool scrollable;

  const ResponsiveDialog({
    Key? key,
    required this.child,
    this.title,
    this.actions,
    this.contentPadding,
    this.scrollable = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final dialogWidth = ResponsiveHelper.getDialogWidth(context);
    final maxHeight = ResponsiveHelper.getDialogMaxHeight(context);

    Widget content = child;
    
    if (scrollable) {
      content = SingleChildScrollView(
        child: child,
      );
    }

    return Dialog(
      child: Container(
        width: dialogWidth,
        constraints: BoxConstraints(
          maxHeight: maxHeight,
          maxWidth: dialogWidth,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (title != null)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  title!,
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ),
            Flexible(
              child: Padding(
                padding: contentPadding ?? 
                    ResponsiveHelper.getResponsivePadding(context),
                child: content,
              ),
            ),
            if (actions != null)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: actions!,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// A responsive card that adjusts its properties based on screen size
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final VoidCallback? onTap;

  const ResponsiveCard({
    Key? key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final card = Card(
      elevation: ResponsiveHelper.getCardElevation(context),
      shape: RoundedRectangleBorder(
        borderRadius: ResponsiveHelper.getCardBorderRadius(context),
      ),
      color: color,
      margin: margin ?? EdgeInsets.all(ResponsiveHelper.getResponsiveSpacing(context)),
      child: Padding(
        padding: padding ?? ResponsiveHelper.getResponsivePadding(context),
        child: child,
      ),
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: ResponsiveHelper.getCardBorderRadius(context),
        child: card,
      );
    }

    return card;
  }
}

/// A responsive list tile that adjusts its height based on screen size
class ResponsiveListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? contentPadding;

  const ResponsiveListTile({
    Key? key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.contentPadding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: ResponsiveHelper.getListItemHeight(context),
      child: ListTile(
        leading: leading,
        title: title,
        subtitle: subtitle,
        trailing: trailing,
        onTap: onTap,
        contentPadding: contentPadding ?? 
            ResponsiveHelper.getResponsiveHorizontalPadding(context),
      ),
    );
  }
}

/// A responsive text widget that scales font size based on screen size
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final double? scaleFactor;

  const ResponsiveText(
    this.text, {
    Key? key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.scaleFactor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final baseStyle = style ?? Theme.of(context).textTheme.bodyMedium!;
    final baseFontSize = baseStyle.fontSize ?? 14.0;
    final scaledFontSize = ResponsiveHelper.getScaledFontSize(
      context, 
      baseFontSize * (scaleFactor ?? 1.0),
    );

    return Text(
      text,
      style: baseStyle.copyWith(fontSize: scaledFontSize),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// A responsive button that adjusts its size based on screen size
class ResponsiveButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final bool isFullWidth;

  const ResponsiveButton({
    Key? key,
    required this.child,
    this.onPressed,
    this.style,
    this.isFullWidth = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final buttonSize = ResponsiveHelper.getResponsiveButtonSize(context);
    
    return SizedBox(
      width: isFullWidth ? buttonSize.width : null,
      height: buttonSize.height,
      child: ElevatedButton(
        onPressed: onPressed,
        style: style,
        child: child,
      ),
    );
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              