# Cattle Manager App - Breeding Module Directory Structure

## Complete Directory Structure for Recreation

This document contains the exact directory structure and file names for the Breeding module in the Cattle Manager App, extracted from `lib2/Dashboard/Breeding/`.

## 📁 Directory Structure

```
lib/Dashboard/Breeding/
├── details/
│   ├── cattle_breeding_analytics_tab.dart
│   ├── cattle_breeding_detail_screen.dart
│   ├── cattle_breeding_records_tab.dart
│   ├── cattle_delivery_records_tab.dart
│   └── cattle_pregnancy_records_tab.dart
├── dialogs/
│   ├── breeding_form_dialog.dart
│   ├── delivery_form_dialog.dart
│   └── pregnancy_form_dialog.dart
├── models/
│   ├── breeding_event_isar.dart
│   ├── breeding_event_isar.g.dart
│   ├── breeding_record.dart
│   ├── breeding_record.g.dart
│   ├── breeding_record_isar.dart
│   ├── breeding_record_isar.g.dart
│   ├── breeding_status.dart
│   ├── breeding_status.g.dart
│   ├── delivery_record_isar.dart
│   ├── delivery_record_isar.g.dart
│   ├── pregnancy_record.dart
│   ├── pregnancy_record.g.dart
│   ├── pregnancy_record_isar.dart
│   └── pregnancy_record_isar.g.dart
├── screens/
│   ├── breeding_screen.dart
│   └── delivery_records_screen.dart
├── services/
│   └── breeding_handler.dart
├── tabs/
│   ├── breeding_analytics_tab.dart
│   ├── breeding_insights_tab.dart
│   ├── breeding_records_tab.dart
│   ├── delivery_records_tab.dart
│   └── pregnancy_records_tab.dart
└── widgets/
    ├── breeding_record_card.dart
    ├── delivery_record_card.dart
    └── pregnancy_record_card.dart
```

## 📋 File Categories

### **Details (5 files)**
- `cattle_breeding_analytics_tab.dart` - Analytics view for cattle breeding
- `cattle_breeding_detail_screen.dart` - Detailed breeding information screen
- `cattle_breeding_records_tab.dart` - Breeding records tab view
- `cattle_delivery_records_tab.dart` - Delivery records tab view
- `cattle_pregnancy_records_tab.dart` - Pregnancy records tab view

### **Dialogs (3 files)**
- `breeding_form_dialog.dart` - Form for adding/editing breeding records
- `delivery_form_dialog.dart` - Form for recording deliveries
- `pregnancy_form_dialog.dart` - Form for managing pregnancy records

### **Models (14 files)**
- `breeding_event_isar.dart` - Breeding event data model (Isar)
- `breeding_event_isar.g.dart` - Generated Isar code for breeding events
- `breeding_record.dart` - Core breeding record model
- `breeding_record.g.dart` - Generated code for breeding records
- `breeding_record_isar.dart` - Isar-specific breeding record model
- `breeding_record_isar.g.dart` - Generated Isar code for breeding records
- `breeding_status.dart` - Breeding status enumeration/model
- `breeding_status.g.dart` - Generated code for breeding status
- `delivery_record_isar.dart` - Delivery record data model (Isar)
- `delivery_record_isar.g.dart` - Generated Isar code for delivery records
- `pregnancy_record.dart` - Pregnancy record model
- `pregnancy_record.g.dart` - Generated code for pregnancy records
- `pregnancy_record_isar.dart` - Isar-specific pregnancy record model
- `pregnancy_record_isar.g.dart` - Generated Isar code for pregnancy records

### **Screens (2 files)**
- `breeding_screen.dart` - Main breeding management screen
- `delivery_records_screen.dart` - Dedicated delivery records screen

### **Services (1 file)**
- `breeding_handler.dart` - Business logic and data operations for breeding

### **Tabs (5 files)**
- `breeding_analytics_tab.dart` - Analytics and statistics tab
- `breeding_insights_tab.dart` - Insights and recommendations tab
- `breeding_records_tab.dart` - Breeding records listing tab
- `delivery_records_tab.dart` - Delivery records listing tab
- `pregnancy_records_tab.dart` - Pregnancy tracking tab

### **Widgets (3 files)**
- `breeding_record_card.dart` - UI card component for breeding records
- `delivery_record_card.dart` - UI card component for delivery records
- `pregnancy_record_card.dart` - UI card component for pregnancy records

## 🏗️ Architecture Pattern

The Breeding module follows the **Clean Architecture** pattern:

1. **Models Layer**: Data entities and Isar database models
2. **Services Layer**: Business logic and data operations
3. **Presentation Layer**: Screens, tabs, dialogs, and widgets
4. **Details Layer**: Specialized detail views and analytics

## 📊 Module Statistics

- **Total Files**: 30 files
- **Total Directories**: 6 directories
- **Generated Files**: 8 files (*.g.dart - Isar code generation)
- **Core Logic Files**: 22 files
- **UI Components**: 16 files (screens, tabs, dialogs, widgets)
- **Data Models**: 14 files

## 🔧 Key Features Supported

1. **Breeding Management**: Record and track breeding activities
2. **Pregnancy Tracking**: Monitor pregnancy status and progress
3. **Delivery Records**: Document birth events and outcomes
4. **Analytics**: Breeding performance metrics and insights
5. **Status Management**: Track breeding status throughout lifecycle

## 📝 Notes for Recreation

- All `.g.dart` files are generated by Isar build runner
- The module integrates with the main app's database service
- UI components follow Material Design 3 patterns
- Service layer handles all business logic and data persistence
- Tab-based navigation for different breeding aspects

---

**Generated on**: 2025-06-24  
**Source**: `lib2/Dashboard/Breeding/` directory  
**Purpose**: Complete module recreation reference
