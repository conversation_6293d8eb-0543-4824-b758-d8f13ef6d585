import 'package:hive/hive.dart';

part 'breeding_record.g.dart';

@HiveType(typeId: 3)
class BreedingRecord extends HiveObject {
  @HiveField(0)
  final String cattleId;

  @HiveField(1)
  final DateTime date;

  @HiveField(2)
  final String bullIdOrType;

  @HiveField(3)
  final String method;

  @HiveField(4)
  final String status;

  @HiveField(5)
  final DateTime? expectedDate;

  @HiveField(6)
  final double cost;

  BreedingRecord({
    required this.cattleId,
    required this.date,
    required this.bullIdOrType,
    required this.method,
    required this.status,
    this.expectedDate,
    required this.cost,
  });
}
