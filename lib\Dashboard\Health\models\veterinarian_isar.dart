import 'package:isar/isar.dart';
import 'package:json_annotation/json_annotation.dart';

part 'veterinarian_isar.g.dart';

/// Represents a veterinarian in the Isar database
@collection
@JsonSerializable()
class VeterinarianIsar {
  factory VeterinarianIsar.fromJson(Map<String, dynamic> json) =>
      _$VeterinarianIsarFromJson(json);
  Map<String, dynamic> toJson() => _$VeterinarianIsarToJson(this);

  /// Isar database ID
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) for the veterinarian - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// ID of the farm this veterinarian is associated with
  @Index()
  String? farmBusinessId;

  /// Full name of the veterinarian
  String? name;

  /// Professional title (Dr., DVM, etc.)
  String? title;

  /// License number
  String? licenseNumber;

  /// Specialization (Large Animal, Small Animal, Mixed Practice, etc.)
  String? specialization;

  /// Clinic or practice name
  String? clinicName;

  /// Primary phone number
  String? phoneNumber;

  /// Secondary phone number
  String? secondaryPhone;

  /// Emergency phone number
  String? emergencyPhone;

  /// Email address
  String? email;

  /// Website URL
  String? website;

  /// Street address
  String? address;

  /// City
  String? city;

  /// State or province
  String? state;

  /// Postal code
  String? postalCode;

  /// Country
  String? country;

  /// Services offered (list of services)
  List<String>? services;

  /// Operating hours (e.g., "Mon-Fri: 8AM-6PM, Sat: 8AM-2PM")
  String? operatingHours;

  /// Emergency availability
  bool emergencyAvailable = false;

  /// Emergency hours
  String? emergencyHours;

  /// Consultation fee
  double? consultationFee;

  /// Emergency call fee
  double? emergencyFee;

  /// Travel fee per km
  double? travelFeePerKm;

  /// Minimum travel fee
  double? minimumTravelFee;

  /// Maximum travel distance (in km)
  double? maxTravelDistance;

  /// Preferred payment methods
  List<String>? paymentMethods;

  /// Languages spoken
  List<String>? languages;

  /// Years of experience
  int? yearsOfExperience;

  /// Education/qualifications
  String? education;

  /// Professional associations
  List<String>? associations;

  /// Rating (1-5 stars)
  double? rating;

  /// Number of reviews
  int? reviewCount;

  /// Notes about the veterinarian
  String? notes;

  /// Whether this veterinarian is currently active
  bool isActive = true;

  /// Whether this is the primary veterinarian for the farm
  bool isPrimary = false;

  /// Last contact date
  DateTime? lastContactDate;

  /// Date when the record was created
  DateTime? createdAt;

  /// Date when the record was last updated
  DateTime? updatedAt;

  /// User who created the record
  String? createdBy;

  /// User who last updated the record
  String? updatedBy;

  /// Default constructor
  VeterinarianIsar();

  /// Generate a business ID for the veterinarian
  static String generateBusinessId(String name) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final cleanName = name.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '').toLowerCase();
    return 'vet_${cleanName}_$timestamp';
  }

  /// Factory constructor for creating a new veterinarian record
  factory VeterinarianIsar.create({
    required String name,
    required String farmBusinessId,
    String? title,
    String? licenseNumber,
    String? specialization,
    String? clinicName,
    String? phoneNumber,
    String? secondaryPhone,
    String? emergencyPhone,
    String? email,
    String? website,
    String? address,
    String? city,
    String? state,
    String? postalCode,
    String? country,
    List<String>? services,
    String? operatingHours,
    bool emergencyAvailable = false,
    String? emergencyHours,
    double? consultationFee,
    double? emergencyFee,
    double? travelFeePerKm,
    double? minimumTravelFee,
    double? maxTravelDistance,
    List<String>? paymentMethods,
    List<String>? languages,
    int? yearsOfExperience,
    String? education,
    List<String>? associations,
    double? rating,
    int? reviewCount,
    String? notes,
    bool isActive = true,
    bool isPrimary = false,
    String? createdBy,
  }) {
    final veterinarian = VeterinarianIsar()
      ..businessId = generateBusinessId(name)
      ..farmBusinessId = farmBusinessId
      ..name = name
      ..title = title
      ..licenseNumber = licenseNumber
      ..specialization = specialization
      ..clinicName = clinicName
      ..phoneNumber = phoneNumber
      ..secondaryPhone = secondaryPhone
      ..emergencyPhone = emergencyPhone
      ..email = email
      ..website = website
      ..address = address
      ..city = city
      ..state = state
      ..postalCode = postalCode
      ..country = country
      ..services = services
      ..operatingHours = operatingHours
      ..emergencyAvailable = emergencyAvailable
      ..emergencyHours = emergencyHours
      ..consultationFee = consultationFee
      ..emergencyFee = emergencyFee
      ..travelFeePerKm = travelFeePerKm
      ..minimumTravelFee = minimumTravelFee
      ..maxTravelDistance = maxTravelDistance
      ..paymentMethods = paymentMethods
      ..languages = languages
      ..yearsOfExperience = yearsOfExperience
      ..education = education
      ..associations = associations
      ..rating = rating
      ..reviewCount = reviewCount
      ..notes = notes
      ..isActive = isActive
      ..isPrimary = isPrimary
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now()
      ..createdBy = createdBy;

    return veterinarian;
  }

  /// Create a copy with updated values
  VeterinarianIsar copyWith({
    String? businessId,
    String? farmBusinessId,
    String? name,
    String? title,
    String? licenseNumber,
    String? specialization,
    String? clinicName,
    String? phoneNumber,
    String? secondaryPhone,
    String? emergencyPhone,
    String? email,
    String? website,
    String? address,
    String? city,
    String? state,
    String? postalCode,
    String? country,
    List<String>? services,
    String? operatingHours,
    bool? emergencyAvailable,
    String? emergencyHours,
    double? consultationFee,
    double? emergencyFee,
    double? travelFeePerKm,
    double? minimumTravelFee,
    double? maxTravelDistance,
    List<String>? paymentMethods,
    List<String>? languages,
    int? yearsOfExperience,
    String? education,
    List<String>? associations,
    double? rating,
    int? reviewCount,
    String? notes,
    bool? isActive,
    bool? isPrimary,
    DateTime? lastContactDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) {
    final veterinarian = VeterinarianIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..farmBusinessId = farmBusinessId ?? this.farmBusinessId
      ..name = name ?? this.name
      ..title = title ?? this.title
      ..licenseNumber = licenseNumber ?? this.licenseNumber
      ..specialization = specialization ?? this.specialization
      ..clinicName = clinicName ?? this.clinicName
      ..phoneNumber = phoneNumber ?? this.phoneNumber
      ..secondaryPhone = secondaryPhone ?? this.secondaryPhone
      ..emergencyPhone = emergencyPhone ?? this.emergencyPhone
      ..email = email ?? this.email
      ..website = website ?? this.website
      ..address = address ?? this.address
      ..city = city ?? this.city
      ..state = state ?? this.state
      ..postalCode = postalCode ?? this.postalCode
      ..country = country ?? this.country
      ..services = services ?? this.services
      ..operatingHours = operatingHours ?? this.operatingHours
      ..emergencyAvailable = emergencyAvailable ?? this.emergencyAvailable
      ..emergencyHours = emergencyHours ?? this.emergencyHours
      ..consultationFee = consultationFee ?? this.consultationFee
      ..emergencyFee = emergencyFee ?? this.emergencyFee
      ..travelFeePerKm = travelFeePerKm ?? this.travelFeePerKm
      ..minimumTravelFee = minimumTravelFee ?? this.minimumTravelFee
      ..maxTravelDistance = maxTravelDistance ?? this.maxTravelDistance
      ..paymentMethods = paymentMethods ?? this.paymentMethods
      ..languages = languages ?? this.languages
      ..yearsOfExperience = yearsOfExperience ?? this.yearsOfExperience
      ..education = education ?? this.education
      ..associations = associations ?? this.associations
      ..rating = rating ?? this.rating
      ..reviewCount = reviewCount ?? this.reviewCount
      ..notes = notes ?? this.notes
      ..isActive = isActive ?? this.isActive
      ..isPrimary = isPrimary ?? this.isPrimary
      ..lastContactDate = lastContactDate ?? this.lastContactDate
      ..createdAt = createdAt ?? this.createdAt
      ..updatedAt = updatedAt ?? DateTime.now()
      ..createdBy = createdBy ?? this.createdBy
      ..updatedBy = updatedBy ?? this.updatedBy;

    return veterinarian;
  }

  /// Get full contact information as a formatted string
  String get fullContactInfo {
    final parts = <String>[];
    if (phoneNumber != null) parts.add('Phone: $phoneNumber');
    if (email != null) parts.add('Email: $email');
    if (emergencyPhone != null) parts.add('Emergency: $emergencyPhone');
    return parts.join(' | ');
  }

  /// Get full address as a formatted string
  String get fullAddress {
    final parts = <String>[];
    if (address != null) parts.add(address!);
    if (city != null) parts.add(city!);
    if (state != null) parts.add(state!);
    if (postalCode != null) parts.add(postalCode!);
    if (country != null) parts.add(country!);
    return parts.join(', ');
  }

  @override
  String toString() {
    return 'VeterinarianIsar{id: $id, businessId: $businessId, name: $name, clinicName: $clinicName, phoneNumber: $phoneNumber}';
  }
}
