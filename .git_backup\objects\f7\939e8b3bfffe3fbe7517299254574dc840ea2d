ingYieldController,
                      decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Evening Yield (L)'),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Required';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Invalid number';
                        }
                        return null;
                      },
                      onChanged: _updateTotalYield,
                    ),
                  ),
                ],
              ),
              SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
              Card(
                child: Padding(
                  padding: ResponsiveHelper.getResponsivePadding(context),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Total Yield:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${_totalYield.toStringAsFixed(2)} L',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
              Row(
                children: [
                  const Text('Date: '),
                  TextButton(
                    onPressed: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: _selectedDate,
                        firstDate: DateTime(2000),
                        lastDate: DateTime.now(),
                      );
                      if (picked != null) {
                        setState(() {
                          _selectedDate = picked;
                        });
                      }
                    },
                    child: Text(_selectedDate.toString().split(' ')[0]),
                  ),
                ],
              ),
              SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
              Row(
                children: [
                  const Text('Shift: '),
                  DropdownButtonFormField<String>(
                    decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Select Shift'),
                    value: _selectedShift,
                    items: const [
                      DropdownMenuItem<String>(
                        value: 'Morning',
                        child: Text('Morning'),
                      ),
                      DropdownMenuItem<String>(
                        value: 'Evening',
                        child: Text('Evening'),
                      ),
                      DropdownMenuItem<String>(
                        value: 'Both',
                        child: Text('Both'),
                      ),
                    ],
                    validator: (value) {
                      if (value == null) {
                        return 'Please select a shift';
                      }
                      return null;
                    },
                    onChanged: (String? newValue) {
                      setState(() {
                        _selectedShift = newValue!;
                      });
                    },
                  ),
                ],
              ),
              SizedBox(height: ResponsiveTheme.getFormSpacing(context)),
              TextFormField(
                controller: _notesController,
                decoration: ResponsiveTheme.getInputDecoration(context, labelText: 'Notes'),
                maxLines: 3,
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _saveMilkRecord,
                  child: const Text('Save Record'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _updateTotalYield(String _) {
    setState(() {
      double morning = double.tryParse(_morningYieldController.text) ?? 0;
      double evening = double.tryParse(_eveningYieldController.text) ?? 0;
      _totalYield = morning + evening;
    });
  }

  void _saveMilkRecord() {
    if (_formKey.currentState!.validate()) {
      double quantity = _selectedShift == 'Both' ? _totalYield : double.parse(_selectedShift == 'Morning' ? _morningYieldController.text : _eveningYieldController.text);
      final milkRecord = MilkRecord(
        id: widget.milkRecord?.id ?? const Uuid().v4(),
        cattleId: _selectedCattle!.id, // Assuming Cattle has an id property
        quantity: quantity,
        date: _selectedDate,
        shift: _selectedShift,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        morningQuantity: _selectedShift == 'Morning' ? quantity : 0.0,
        eveningQuantity: _selectedShift == 'Evening' ? quantity : 0.0,
        fatContent: 0.0, // Default value, update when implementing fat content
        totalRevenue: 0.0, // Default value, update when implementing pricing
      );

      Navigator.of(context).pop(milkRecord);
    }
  }

  @override
  void dispose() {
    _morningYieldController.dispose();
    _eveningYieldController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         