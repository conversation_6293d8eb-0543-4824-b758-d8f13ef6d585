            } else {
                                              _selectedCattle.remove(cattle.id);
                                            }
                                          });
                                        },
                                      )
                                    : CircleAvatar(
                                        backgroundColor:
                                            cattle.gender.toLowerCase() ==
                                                    'male'
                                                ? Colors.blue[200]
                                                : Colors.pink[200],
                                        child: Text(
                                          cattle.name[0].toUpperCase(),
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                title: Row(
                                  children: [
                                    Expanded(
                                      flex: 3,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            cattle.name,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: Colors.black,
                                            ),
                                          ),
                                          Text(
                                            'Tag ID: ${cattle.tagId}',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color:
                                                  Color.fromARGB(155, 0, 0, 0),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(
                                        width:
                                            16), // Add spacing between columns
                                    Expanded(
                                      flex: 2,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            animalType.name,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: Colors.black,
                                            ),
                                          ),
                                          Text(
                                            breed.name,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color:
                                                  Color.fromARGB(155, 0, 0, 0),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                trailing: PopupMenuButton<String>(
                                  onSelected: (String choice) {
                                    if (choice == 'Edit') {
                                      _showEditCattleDialog(cattle);
                                    } else if (choice == 'Delete') {
                                      _showDeleteConfirmation([cattle.id]);
                                    } else if (choice == 'View') {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              CattleDetailScreen(
                                            cattle: cattle,
                                            breed: breed,
                                            animalType: animalType,
                                            onCattleUpdated: (_) => _loadData(),
                                          ),
                                        ),
                                      );
                                    }
                                  },
                                  itemBuilder: (BuildContext context) => [
                                    const PopupMenuItem(
                                      value: 'View',
                                      child: Text('View Details'),
                                    ),
                                    const PopupMenuItem(
                                      value: 'Edit',
                                      child: Text('Edit'),
                                    ),
                                    const PopupMenuItem(
                                      value: 'Delete',
                                      child: Text('Delete'),
                                    ),
                                  ],
                                ),
                                onTap: () {
                                  if (_isSelectionMode) {
                                    setState(() {
                                      if (_selectedCattle.contains(cattle.id)) {
                                        _selectedCattle.remove(cattle.id);
                                      } else {
                                        _selectedCattle.add(cattle.id);
                                      }
                                    });
                                  } else {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            CattleDetailScreen(
                                          cattle: cattle,
                                          breed: breed,
                                          animalType: animalType,
                                          onCattleUpdated: