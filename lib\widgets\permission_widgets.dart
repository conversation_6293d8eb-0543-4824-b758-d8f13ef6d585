import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../services/permission_service.dart';
import '../Dashboard/User Account/services/cloud_authentication_service.dart';

/// Helper to safely get permission service
PermissionService? _getPermissionService() {
  try {
    if (GetIt.instance.isRegistered<PermissionService>()) {
      return GetIt.instance<PermissionService>();
    }
  } catch (e) {
    // Service not available
  }
  return null;
}

/// Widget that shows its child only if the user has the required permission
class PermissionWidget extends StatelessWidget {
  final String permission;
  final Widget child;
  final Widget? fallback;
  final bool showFallbackOnNoPermission;

  const PermissionWidget({
    Key? key,
    required this.permission,
    required this.child,
    this.fallback,
    this.showFallbackOnNoPermission = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final permissionService = _getPermissionService();

    if (permissionService == null) {
      // Service not available - show child by default during initialization
      return child;
    }

    if (permissionService.hasPermission(permission)) {
      return child;
    } else if (showFallbackOnNoPermission && fallback != null) {
      return fallback!;
    } else {
      return const SizedBox.shrink();
    }
  }
}

/// Widget that shows its child only if the user has ANY of the required permissions
class AnyPermissionWidget extends StatelessWidget {
  final List<String> permissions;
  final Widget child;
  final Widget? fallback;
  final bool showFallbackOnNoPermission;

  const AnyPermissionWidget({
    Key? key,
    required this.permissions,
    required this.child,
    this.fallback,
    this.showFallbackOnNoPermission = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final permissionService = _getPermissionService();

    if (permissionService == null) {
      // Service not available - show child by default during initialization
      return child;
    }

    if (permissionService.hasAnyPermission(permissions)) {
      return child;
    } else if (showFallbackOnNoPermission && fallback != null) {
      return fallback!;
    } else {
      return const SizedBox.shrink();
    }
  }
}

/// Widget that shows its child only if the user has ALL of the required permissions
class AllPermissionsWidget extends StatelessWidget {
  final List<String> permissions;
  final Widget child;
  final Widget? fallback;
  final bool showFallbackOnNoPermission;

  const AllPermissionsWidget({
    Key? key,
    required this.permissions,
    required this.child,
    this.fallback,
    this.showFallbackOnNoPermission = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final permissionService = _getPermissionService();

    if (permissionService == null) {
      // Service not available - show child by default during initialization
      return child;
    }

    if (permissionService.hasAllPermissions(permissions)) {
      return child;
    } else if (showFallbackOnNoPermission && fallback != null) {
      return fallback!;
    } else {
      return const SizedBox.shrink();
    }
  }
}

/// FloatingActionButton that only shows if user has permission
class PermissionFAB extends StatelessWidget {
  final String permission;
  final VoidCallback onPressed;
  final Widget? child;
  final String? tooltip;
  final Color? backgroundColor;
  final IconData? icon;

  const PermissionFAB({
    Key? key,
    required this.permission,
    required this.onPressed,
    this.child,
    this.tooltip,
    this.backgroundColor,
    this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PermissionWidget(
      permission: permission,
      child: FloatingActionButton(
        onPressed: onPressed,
        tooltip: tooltip,
        backgroundColor: backgroundColor,
        child: child ?? (icon != null ? Icon(icon) : const Icon(Icons.add)),
      ),
    );
  }
}

/// IconButton that only shows if user has permission
class PermissionIconButton extends StatelessWidget {
  final String permission;
  final VoidCallback onPressed;
  final IconData icon;
  final String? tooltip;
  final Color? color;

  const PermissionIconButton({
    Key? key,
    required this.permission,
    required this.onPressed,
    required this.icon,
    this.tooltip,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PermissionWidget(
      permission: permission,
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon),
        tooltip: tooltip,
        color: color,
      ),
    );
  }
}

/// ElevatedButton that only shows if user has permission
class PermissionElevatedButton extends StatelessWidget {
  final String permission;
  final VoidCallback onPressed;
  final Widget child;
  final ButtonStyle? style;

  const PermissionElevatedButton({
    Key? key,
    required this.permission,
    required this.onPressed,
    required this.child,
    this.style,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PermissionWidget(
      permission: permission,
      child: ElevatedButton(
        onPressed: onPressed,
        style: style,
        child: child,
      ),
    );
  }
}

/// ListTile that only shows if user has permission
class PermissionListTile extends StatelessWidget {
  final String permission;
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;

  const PermissionListTile({
    Key? key,
    required this.permission,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PermissionWidget(
      permission: permission,
      child: ListTile(
        leading: leading,
        title: title,
        subtitle: subtitle,
        trailing: trailing,
        onTap: onTap,
      ),
    );
  }
}

/// Card that only shows if user has permission
class PermissionCard extends StatelessWidget {
  final String permission;
  final Widget child;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final double? elevation;

  const PermissionCard({
    Key? key,
    required this.permission,
    required this.child,
    this.margin,
    this.color,
    this.elevation,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PermissionWidget(
      permission: permission,
      child: Card(
        margin: margin,
        color: color,
        elevation: elevation,
        child: child,
      ),
    );
  }
}

/// Tab that only shows if user has permission
class PermissionTab extends StatelessWidget {
  final String permission;
  final String text;
  final IconData? icon;

  const PermissionTab({
    Key? key,
    required this.permission,
    required this.text,
    this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final permissionService = _getPermissionService();

    if (permissionService == null) {
      // Service not available - show tab by default during initialization
      return Tab(
        text: text,
        icon: icon != null ? Icon(icon) : null,
      );
    }

    if (permissionService.hasPermission(permission)) {
      return Tab(
        text: text,
        icon: icon != null ? Icon(icon) : null,
      );
    } else {
      return const SizedBox.shrink();
    }
  }
}

/// Helper function to check permission
bool hasPermission(String permission) {
  final permissionService = _getPermissionService();
  if (permissionService == null) {
    // Service not available - allow access by default during initialization
    return true;
  }
  return permissionService.hasPermission(permission);
}

/// Helper function to check any permission
bool hasAnyPermission(List<String> permissions) {
  final permissionService = _getPermissionService();
  if (permissionService == null) {
    // Service not available - allow access by default during initialization
    return true;
  }
  return permissionService.hasAnyPermission(permissions);
}

/// Helper function to check all permissions
bool hasAllPermissions(List<String> permissions) {
  final permissionService = _getPermissionService();
  if (permissionService == null) {
    // Service not available - allow access by default during initialization
    return true;
  }
  return permissionService.hasAllPermissions(permissions);
}

/// Widget that shows user info and role
class UserRoleDisplay extends StatelessWidget {
  final bool showRole;
  final bool showPermissions;

  const UserRoleDisplay({
    Key? key,
    this.showRole = true,
    this.showPermissions = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Check authentication status using CloudAuthenticationService
    CloudAuthenticationService? authService;
    try {
      authService = GetIt.instance<CloudAuthenticationService>();
    } catch (e) {
      // CloudAuthenticationService not available
      authService = null;
    }

    final isAuthenticated = authService?.isAuthenticated ?? false;

    if (!isAuthenticated) {
      return const Text('Guest User');
    }

    // Get permission service for role and permission info
    final permissionService = _getPermissionService();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          permissionService?.currentUserDisplayName ?? authService?.currentUserDisplayName ?? 'User',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        if (showRole && permissionService != null) ...[
          const SizedBox(height: 4),
          Text(
            'Role: ${permissionService.currentUserRoleName}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
        if (showPermissions && permissionService != null && permissionService.currentPermissions.isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(
            'Permissions: ${permissionService.currentPermissions.length}',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[500],
            ),
          ),
        ],
      ],
    );
  }
}
