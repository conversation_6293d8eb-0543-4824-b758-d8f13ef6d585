import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import '../../Breeding/models/pregnancy_record.dart';
import '../models/pregnancy_report_data.dart';
import '../report_tabs/pregnancy_summary_tab.dart';
import '../report_tabs/pregnancy_details_tab.dart';
import 'package:intl/intl.dart';

class PregnanciesReportScreen extends StatefulWidget {
  const PregnanciesReportScreen({Key? key}) : super(key: key);

  @override
  PregnanciesReportScreenState createState() => PregnanciesReportScreenState();
}

class PregnanciesReportScreenState extends State<PregnanciesReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PregnancyReportData reportData;
  DateTime? startDate;
  DateTime? endDate;
  String? selectedStage;
  bool isLoading = true;
  String? errorMessage;

  final List<String> stages = [
    'First Trimester',
    'Second Trimester',
    'Third Trimester',
    'Due'
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      final pregnancyBox = await Hive.openBox<PregnancyRecord>('pregnancy_records');

      setState(() {
        reportData = PregnancyReportData(
          pregnancies: pregnancyBox.values.toList(),
          stage: selectedStage,
          startDate: startDate ?? DateTime.now().subtract(const Duration(days: 365)),
          endDate: endDate ?? DateTime.now(),
        );
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load data: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pregnancy Report'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Summary'),
            Tab(text: 'Details'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFilters(),
          if (isLoading)
            const Expanded(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          else if (errorMessage != null)
            Expanded(
              child: Center(
                child: Text(
                  errorMessage!,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            )
          else
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  PregnancySummaryTab(reportData: reportData),
                  PregnancyDetailsTab(reportData: reportData),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Stage',
                    border: OutlineInputBorder(),
                  ),
                  value: selectedStage,
                  items: [
                    const DropdownMenuItem<String>(
                      value: null,
                      child: Text('All Stages'),
                    ),
                    ...stages.map((stage) {
                      return DropdownMenuItem<String>(
                        value: stage,
                        child: Text(stage),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedStage = value;
                      _loadData();
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Start Date',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  controller: TextEditingController(
                    text: startDate != null
                        ? DateFormat('yyyy-MM-dd').format(startDate!)
                        : '',
                  ),
                  readOnly: true,
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: startDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        startDate = date;
                        _loadData();
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'End Date',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  controller: TextEditingController(
                    text: endDate != null
                        ? DateFormat('yyyy-MM-dd').format(endDate!)
                        : '',
                  ),
                  readOnly: true,
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: endDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        endDate = date;
                        _loadData();
                      });
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
