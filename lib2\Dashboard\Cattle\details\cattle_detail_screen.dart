import 'package:flutter/material.dart';
import '../widgets/dashboard_menu_item.dart';
import '../services/database_helper.dart';
import '../utils/responsive_helper.dart';
import '../utils/responsive_layout.dart';
import '../theme/responsive_theme.dart';
import 'Farm Setup/models/farm.dart';
import 'Farm Setup/screens/farm_setup_screen.dart';
import 'Cattle/screens/cattle_records_screen.dart';
import 'Reports/screens/reports_screen.dart';
import 'Transactions/screens/transactions_screen.dart';
import 'Milk Records/screens/milk_records_screen.dart';
import 'Events/screens/events_screen.dart';
import 'Breeding/screens/breeding_screen.dart';
import 'Health/screens/health_screen.dart';
import 'widgets/farm_selection_drawer.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  Farm? _selectedFarm;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSelectedFarm();
    _setupFarmChangeListener();
  }

  @override
  void dispose() {
    _dbHelper.removeListener(_onFarmChanged);
    super.dispose();
  }

  void _setupFarmChangeListener() {
    _dbHelper.addListener(_onFarmChanged);
  }

  void _onFarmChanged() {
    _loadSelectedFarm();
  }

  Future<void> _loadSelectedFarm() async {
    try {
      setState(() => _isLoading = true);
      final selectedFarmId = await _dbHelper.getSelectedFarmId();
      if (selectedFarmId != null) {
        final farms = await _dbHelper.getFarms();
        setState(() {
          _selectedFarm = farms.firstWhere(
            (farm) => farm.id == selectedFarmId,
            orElse: () => farms.first,
          );
          _isLoading = false;
        });
      } else {
        setState(() => _isLoading = false);
      }
    } catch (e) {
      debugPrint('Error loading selected farm: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    final selectedFarmName = _selectedFarm?.name ?? 'My Cattle Manager';

    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText(
          selectedFarmName,
          style: ResponsiveTheme.getTitleStyle(context, color: Colors.white),
        ),
        backgroundColor: ResponsiveTheme.primaryColor,
        elevation: ResponsiveHelper.getCardElevation(context),
        toolbarHeight: ResponsiveHelper.getAppBarHeight(context),
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(
              Icons.menu_outlined,
              color: Colors.white,
              size: ResponsiveHelper.getIconSize(context),
            ),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.notifications_outlined,
              color: Colors.white,
              size: ResponsiveHelper.getIconSize(context),
            ),
            onPressed: () {
              // Handle notifications button tap
            },
          ),
        ],
      ),
      drawer: const FarmSelectionDrawer(),
      backgroundColor: ResponsiveTheme.scaffoldBackground,
      body: SafeArea(
        child: ResponsiveContainer(
          child: Column(
            children: [
              Expanded(
                child: ResponsiveGridView(
                  mobileColumns: 2,
                  tabletColumns: 3,
                  desktopColumns: 4,
                  childAspectRatio: ResponsiveHelper.getResponsiveValue(
                    context,
                    mobile: 1.0,
                    tablet: 1.1,
                    desktop: 1.2,
                  ),
                  mainAxisSpacing: ResponsiveTheme.getGridSpacing(context),
                  crossAxisSpacing: ResponsiveTheme.getGridSpacing(context),
                  shrinkWrap: false,
                  children: [
                          DashboardMenuItem(
                            title: 'Cattle Records',
                            icon: Icons.pets,
                            color: Colors.blue,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const CattleRecordsScreen(),
                                ),
                              );
                            },
                          ),
                          DashboardMenuItem(
                            title: 'Milk Records',
                            icon: Icons.local_drink,
                            color: Colors.green,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const MilkRecordsScreen(),
                                ),
                              );
                            },
                          ),
                          DashboardMenuItem(
                            title: 'Breeding',
                            icon: Icons.favorite,
                            color: Colors.pink,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const BreedingScreen(),
                                ),
                              );
                            },
                          ),
                          DashboardMenuItem(
                            title: 'Health',
                            icon: Icons.medical_services,
                            color: Colors.teal,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const HealthScreen(),
                                ),
                              );
                            },
                          ),
                          DashboardMenuItem(
                            title: 'Events',
                            icon: Icons.event,
                            color: Colors.orange,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const EventsScreen(),
                                ),
                              );
                            },
                          ),
                          DashboardMenuItem(
                            title: 'Transactions',
                            icon: Icons.account_balance_wallet,
                            color: Colors.purple,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const TransactionsScreen(),
                                ),
                              );
                            },
                          ),
                          DashboardMenuItem(
                            title: 'Reports',
                            icon: Icons.bar_chart,
                            color: Colors.red,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const ReportsScreen(),
                                ),
                              );
                            },
                          ),
                          DashboardMenuItem(
                            title: 'Farm Setup',
                            icon: Icons.settings,
                            color: Colors.deepPurple,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const FarmSetupScreen(),
                                ),
                              );
                            },
                          ),
                  ],
                ),
              ),
              SizedBox(height: ResponsiveSpacing.getMD(context)),
              ResponsiveButton(
                onPressed: () {
                  // Handle sync button tap
                },
                style: ResponsiveTheme.getPrimaryButtonStyle(context),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.sync,
                      size: ResponsiveHelper.getIconSize(context, mobile: 20.0),
                    ),
                    SizedBox(width: ResponsiveSpacing.getSM(context)),
                    ResponsiveText(
                      'Sync Data',
                      style: ResponsiveTheme.getBodyStyle(context, color: Colors.white),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  