import 'package:flutter/material.dart';
import '../../Events/models/event.dart';

class CattleEvent {
  final String id;
  final String cattleId;
  final String title;
  final String description;
  final DateTime date;
  final TimeOfDay time;
  final EventType type;
  final String? customTypeId;
  final EventPriority priority;
  final bool isCompleted;
  final DateTime? completedAt;
  final DateTime? reminderDate;
  final bool isRecurring;
  final Duration? recurringInterval;

  CattleEvent({
    required this.id,
    required this.cattleId,
    required this.title,
    required this.description,
    required this.date,
    required this.time,
    required this.type,
    this.customTypeId,
    required this.priority,
    this.isCompleted = false,
    this.completedAt,
    this.reminderDate,
    this.isRecurring = false,
    this.recurringInterval,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'cattleId': cattleId,
      'title': title,
      'description': description,
      'date': date.toIso8601String(),
      'time': {'hour': time.hour, 'minute': time.minute},
      'type': type.toString(),
      'customTypeId': customTypeId,
   