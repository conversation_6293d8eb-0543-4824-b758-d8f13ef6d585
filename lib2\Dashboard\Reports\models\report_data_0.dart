import 'package:flutter/material.dart';
import 'chart_data.dart';

abstract class ReportData {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? filterCriteria;

  ReportData({
    this.startDate,
    this.endDate,
    this.filterCriteria,
  });

  // Abstract methods that each report type must implement
  String get reportTitle;
  List<DataColumn> get tableColumns;
  List<DataRow> get tableRows;
  Map<String, dynamic> get summaryData;
  
  // Optional chart data
  List<ChartData> get chartData;
}
