import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../models/cattle.dart';
import '../models/cattle_milk_record.dart';
import '../services/cattle_milk_service.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../../../utils/responsive_helper.dart';
import '../../../utils/responsive_layout.dart';
import '../../../theme/responsive_theme.dart';

class MilkTab extends StatefulWidget {
  final Cattle cattle;

  const MilkTab({
    Key? key,
    required this.cattle,
  }) : super(key: key);

  @override
  State<MilkTab> createState() => _MilkTabState();
}

class _MilkTabState extends State<MilkTab> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final CattleMilkService _milkService = CattleMilkService();
  List<CattleMilkRecord> _records = [];
  Map<String, double> _summary = {
    'morningAverage': 0,
    'eveningAverage': 0,
    'totalAverage': 0,
  };
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      final records = await _