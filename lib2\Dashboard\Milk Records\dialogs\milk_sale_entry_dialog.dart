import 'package:flutter/material.dart';

import '../models/milk_sale_isar.dart';
import '../services/milk_sales_service.dart';
import 'package:keyboard_dismisser/keyboard_dismisser.dart';
import '../services/milk_service.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';

import '../../../utils/message_utils.dart';
import '../../../widgets/dialogs/standard_form_dialog.dart';
import '../../../widgets/dialogs/standard_form_builder.dart';

class MilkSaleEntryDialog extends StatefulWidget {
  final DateTime selectedDate;
  final double availableMilk;

  const MilkSaleEntryDialog({
    super.key,
    required this.selectedDate,
    required this.availableMilk,
  });

  @override
  State<MilkSaleEntryDialog> createState() => _MilkSaleEntryDialogState();
}

class _MilkSaleEntryDialogState extends State<MilkSaleEntryDialog> {
  final _formKey = GlobalKey<FormState>();
  final _milkSalesService = MilkSalesService();
  final _milkService = MilkService();
  final _farmSetupHandler = FarmSetupHandler.instance;

  final _quantityController = TextEditingController();
  final _rateController = TextEditingController();
  final _calfUsageController = TextEditingController();
  final _homeUsageController = TextEditingController();
  final _notesController = TextEditingController();
  final _totalProductionController = TextEditingController();
  bool _isPaid = true;
  double _totalAmount = 0;
  DateTime _selectedDate = DateTime.now();
  double _availableMilk = 0;
  bool _isSaving = false;

  // Currency settings
  String _currencySymbol = '₹';
  bool _symbolBeforeAmount = true;

  // Milk settings
  String _milkUnit = 'liters';
  double _defaultRate = 60.0;



  @override
  void initState() {
    super.initState();
    _selectedDate = widget.selectedDate;
    _availableMilk = widget.availableMilk;
    _totalProductionController.text = '${_availableMilk.toStringAsFixed(1)} ${_getUnitAbbreviation()}';
    _loadCurrencySettings();
    _loadMilkSettings();

    // Load fresh data for the selected date to ensure accuracy
    _loadAvailableMilk(_selectedDate);
    _updateTotalAmount();

    // Add listeners with debouncing to avoid excessive rebuilds
    _quantityController.addListener(_onQuantityChanged);
    _calfUsageController.addListener(_onUsageChanged);
    _homeUsageController.addListener(_onUsageChanged);
    _rateController.addListener(_updateTotalAmount);
  }

  // Debounced listeners to improve performance
  void _onQuantityChanged() {
    _updateTotalAmount();
  }

  void _onUsageChanged() {
    _autoFillQuantityOnUsageChange();
  }



  Future<void> _loadCurrencySettings() async {
    try {
      final currencySettings = await _farmSetupHandler.getCurrencySettings();
      if (mounted) {
        setState(() {
          _currencySymbol = currencySettings.currencySymbol;
          _symbolBeforeAmount = currencySettings.symbolBeforeAmount;
        });
      }
    } catch (e) {
      // Use default values if loading fails
      if (mounted) {
        setState(() {
          _currencySymbol = '\$';
          _symbolBeforeAmount = true;
        });
      }
    }
  }

  Future<void> _loadMilkSettings() async {
    try {
      final milkSettings = await _farmSetupHandler.getMilkSettings();
      if (mounted) {
        setState(() {
          _milkUnit = milkSettings.unit ?? 'liters';
          _defaultRate = milkSettings.regularRate ?? 60.0;
          // Set the default rate in the controller
          _rateController.text = _defaultRate.toStringAsFixed(1);
        });
      }
    } catch (e) {
      // Use default values if loading fails
      if (mounted) {
        setState(() {
          _milkUnit = 'liters';
          _defaultRate = 60.0;
          _rateController.text = _defaultRate.toStringAsFixed(1);
        });
      }
    }
  }

  Future<void> _loadAvailableMilk(DateTime date) async {
    try {
      // Get milk records for the selected date
      final records = await _milkService.getMilkRecordsForDate(date);

      // Calculate total production
      double totalProduction = records.fold<double>(
          0.0,
          (sum, record) =>
              sum + (record.morningAmount ?? 0) + (record.eveningAmount ?? 0));

      // Get existing sales for the selected date
      final existingSales = await _milkSalesService.getMilkSalesForDate(date);

      // Calculate total already sold
      double totalSold = existingSales.fold<double>(0.0, (sum, sale) => sum + sale.quantity);

      // Calculate available milk (total production - already sold)
      double availableMilk = totalProduction - totalSold;

      if (mounted) {
        setState(() {
          _availableMilk = availableMilk.clamp(0.0, double.infinity);
          _totalProductionController.text = '${_availableMilk.toStringAsFixed(1)} ${_getUnitAbbreviation()}';
          _autoFillQuantityOnUsageChange(); // Auto-fill quantity when data loads
        });
      }
    } catch (e) {
      if (mounted) {
        MilkMessageUtils.showError(context, 'Error loading milk data: $e');
      }
    }
  }

  void _updateTotalAmount() {
    final quantity = double.tryParse(_quantityController.text) ?? 0;
    final rate = double.tryParse(_rateController.text) ?? 0;
    final newTotal = quantity * rate;

    // Only update if the value actually changed to avoid unnecessary rebuilds
    if (_totalAmount != newTotal) {
      setState(() {
        _totalAmount = newTotal;
      });
    }
  }



  // Auto-fill quantity when usage changes
  void _autoFillQuantityOnUsageChange() {
    final calfUsage = double.tryParse(_calfUsageController.text) ?? 0;
    final homeUsage = double.tryParse(_homeUsageController.text) ?? 0;
    final availableForSale = _availableMilk - calfUsage - homeUsage;

    // Auto-fill with available quantity for sale
    if (availableForSale >= 0) {
      _quantityController.text = availableForSale.toStringAsFixed(1);
    } else {
      _quantityController.text = '0.0';
    }
  }



  List<FormFieldConfig> _buildFormFields() {
    return [
      // Date Selection
      DateFieldConfig(
        labelText: 'Date',
        prefixIcon: Icons.calendar_today,
        iconColor: Colors.indigo,
        value: _selectedDate,
        firstDate: DateTime(2020),
        lastDate: DateTime.now().add(const Duration(days: 1)),
        onChanged: (date) {
          setState(() {
            _selectedDate = date;
          });
          _loadAvailableMilk(_selectedDate);
        },
      ),

      // Total Production (Read-only)
      TextFieldConfig(
        labelText: 'Total Production',
        controller: _totalProductionController,
        prefixIcon: Icons.inventory,
        iconColor: Colors.teal,
        enabled: false,
      ),

      // Calves Usage
      TextFieldConfig(
        labelText: 'Calves Usage (${_getUnitAbbreviation()})',
        controller: _calfUsageController,
        prefixIcon: Icons.pets,
        iconColor: Colors.brown,
        hintText: '0.0',
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        validator: _validateUsage,
      ),

      // Home Usage
      TextFieldConfig(
        labelText: 'Home Usage (${_getUnitAbbreviation()})',
        controller: _homeUsageController,
        prefixIcon: Icons.home,
        iconColor: Colors.purple,
        hintText: '0.0',
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        validator: _validateUsage,
      ),

      // Quantity for Sale
      TextFieldConfig(
        labelText: 'Quantity (${_getUnitAbbreviation()})',
        controller: _quantityController,
        prefixIcon: Icons.water_drop,
        iconColor: Colors.cyan,
        hintText: '0.0',
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        validator: _validateQuantity,
      ),

      // Rate
      TextFieldConfig(
        labelText: 'Rate ($_currencySymbol/${_getUnitAbbreviation()})',
        controller: _rateController,
        prefixIcon: Icons.attach_money,
        iconColor: Colors.green,
        hintText: _defaultRate.toStringAsFixed(1),
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        validator: _validateRate,
      ),

      // Total Amount Display (Custom field)
      CustomFieldConfig(
        builder: () => Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Total Amount:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                _formatCurrency(_totalAmount),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2E7D32),
                ),
              ),
            ],
          ),
        ),
      ),

      // Payment Status (Custom field)
      CustomFieldConfig(
        builder: () => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Payment Status',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<bool>(
                    title: const Text('Paid'),
                    value: true,
                    groupValue: _isPaid,
                    onChanged: (value) => setState(() => _isPaid = value!),
                    activeColor: const Color(0xFF2E7D32),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                Expanded(
                  child: RadioListTile<bool>(
                    title: const Text('Pending'),
                    value: false,
                    groupValue: _isPaid,
                    onChanged: (value) => setState(() => _isPaid = value!),
                    activeColor: const Color(0xFF2E7D32),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),

      // Notes
      MultilineFieldConfig(
        labelText: 'Notes (Optional)',
        controller: _notesController,
        prefixIcon: Icons.note_add,
        iconColor: Colors.deepPurple,
        hintText: 'Enter any additional information...',
        maxLines: 3,
      ),
    ];
  }

  Future<void> _saveMilkSale() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final sale = MilkSaleIsar()
        ..saleId = MilkSaleIsar.generateSaleId() // Generate unique sale ID
        ..date = _selectedDate
        ..buyer = "Default" // Changed from buyerName to buyer
        ..quantity = double.parse(_quantityController.text) // Changed from quantitySold to quantity
        ..price = double.parse(_rateController.text) // Changed from ratePerLiter to price
        ..total = _totalAmount
        ..paymentStatus = _isPaid ? 'Paid' : 'Pending' // Store payment status as string
        ..notes = _notesController.text.trim();

      await _milkSalesService.addMilkSale(sale);

      if (mounted) {
        MilkMessageUtils.showSuccess(context, MilkMessageUtils.milkSaleRecorded());
        // Close dialog after a short delay to show success message
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            Navigator.of(context).pop(true);
          }
        });
      }
    } catch (e) {
      if (mounted) {
        MilkMessageUtils.showError(context, 'Error saving milk sale: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return StandardFormDialog(
      title: 'Milk Sale Entry',
      headerColor: DialogThemes.milk,
      headerIcon: Icons.point_of_sale,
      isSaving: _isSaving,
      onSave: _saveMilkSale,
      saveText: 'SAVE MILK SALE',
      content: KeyboardDismisser(
        gestures: const [
          GestureType.onTap,
          GestureType.onPanUpdateDownDirection
        ],
        child: StandardFormBuilder(
          formKey: _formKey,
          fields: _buildFormFields(),
        ),
      ),
    );
  }





  String? _validateUsage(String? value) {
    if (value == null || value.isEmpty) return null;
    final usage = double.tryParse(value);
    if (usage == null) return 'Invalid number';
    if (usage < 0) return 'Cannot be negative';
    if (usage > _availableMilk) return 'Exceeds available milk';
    return null;
  }

  String? _validateQuantity(String? value) {
    if (value == null || value.isEmpty) {
      return 'Required';
    }
    final quantity = double.tryParse(value);
    if (quantity == null) return 'Invalid number';
    if (quantity <= 0) return 'Must be greater than 0';

    final calfUsage = double.tryParse(_calfUsageController.text) ?? 0;
    final homeUsage = double.tryParse(_homeUsageController.text) ?? 0;
    final availableForSale = _availableMilk - calfUsage - homeUsage;

    if (quantity > availableForSale) {
      return 'Exceeds available (${availableForSale.toStringAsFixed(1)}${_getUnitAbbreviation()})';
    }
    return null;
  }

  String? _validateRate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Required';
    }
    final rate = double.tryParse(value);
    if (rate == null) return 'Invalid number';
    if (rate <= 0) return 'Must be greater than 0';
    if (rate > 1000) return 'Rate seems too high';
    return null;
  }

  // Format currency based on user settings
  String _formatCurrency(double amount) {
    return _symbolBeforeAmount
        ? '$_currencySymbol${amount.toStringAsFixed(2)}'
        : '${amount.toStringAsFixed(2)}$_currencySymbol';
  }

  // Get unit abbreviation based on milk settings
  String _getUnitAbbreviation() {
    switch (_milkUnit.toLowerCase()) {
      case 'liters':
      case 'litres':
        return 'L';
      case 'gallons':
        return 'gal';
      case 'quarts':
        return 'qt';
      case 'pints':
        return 'pt';
      default:
        return 'L'; // Default to liters
    }
  }





  @override
  void dispose() {
    _quantityController.dispose();
    _rateController.dispose();
    _calfUsageController.dispose();
    _homeUsageController.dispose();
    _notesController.dispose();
    _totalProductionController.dispose();
    super.dispose();
  }
}
