import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:flutter/rendering.dart';
import 'dart:ui' as ui;
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../models/cattle_isar.dart';
import '../../../constants/app_colors.dart';

class QRCodeDialog extends StatefulWidget {
  final CattleIsar cattle;

  const QRCodeDialog({
    Key? key,
    required this.cattle,
  }) : super(key: key);

  @override
  State<QRCodeDialog> createState() => _QRCodeDialogState();
}

class _QRCodeDialogState extends State<QRCodeDialog> {
  final GlobalKey _qrKey = GlobalKey();
  bool _isGeneratingPDF = false;

  @override
  Widget build(BuildContext context) {
    final qrData = _generateQRData();
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenWidth < 600;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: isSmallScreen ? screenWidth * 0.9 : 380,
        constraints: BoxConstraints(
          maxHeight: screenHeight * 0.7,
          maxWidth: screenWidth * 0.9,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Fixed header
            Padding(
              padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
              child: Row(
                children: [
                  const Icon(Icons.qr_code, size: 28, color: AppColors.primary),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '${widget.cattle.name ?? 'Unnamed Cattle'} (${widget.cattle.tagId ?? 'Unknown'})',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            // QR Code - Centered with proper padding
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: isSmallScreen ? 24 : 32,
                vertical: isSmallScreen ? 20 : 24,
              ),
              child: Center(
                child: RepaintBoundary(
                  key: _qrKey,
                  child: QrImageView(
                    data: qrData,
                    version: QrVersions.auto,
                    size: isSmallScreen ? 280.0 : 300.0,
                    backgroundColor: Colors.white,
                    eyeStyle: const QrEyeStyle(
                      eyeShape: QrEyeShape.square,
                      color: Colors.black,
                    ),
                    dataModuleStyle: const QrDataModuleStyle(
                      dataModuleShape: QrDataModuleShape.square,
                      color: Colors.black,
                    ),
                    errorCorrectionLevel: QrErrorCorrectLevel.H,
                  ),
                ),
              ),
            ),
            // Fixed action buttons at bottom
            Padding(
              padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
              child: isSmallScreen
                  ? Column(
                      children: [
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: _isGeneratingPDF ? null : _generateEarTagPDF,
                            icon: _isGeneratingPDF
                                ? const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                                  )
                                : const Icon(Icons.print, color: Colors.white),
                            label: Text(
                              _isGeneratingPDF ? 'Generating...' : 'Print Tag',
                              style: const TextStyle(color: Colors.white),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primary,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: () => _copyQRData(qrData),
                                icon: const Icon(Icons.copy, color: Colors.white),
                                label: const Text('Copy', style: TextStyle(color: Colors.white)),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: _shareQRCode,
                                icon: const Icon(Icons.share, color: Colors.white),
                                label: const Text('Share', style: TextStyle(color: Colors.white)),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.purple,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () => _copyQRData(qrData),
                            icon: const Icon(Icons.copy, color: Colors.white),
                            label: const Text('Copy Data', style: TextStyle(color: Colors.white)),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _shareQRCode,
                            icon: const Icon(Icons.share, color: Colors.white),
                            label: const Text('Share', style: TextStyle(color: Colors.white)),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.purple,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isGeneratingPDF ? null : _generateEarTagPDF,
                            icon: _isGeneratingPDF
                                ? const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                                  )
                                : const Icon(Icons.print, color: Colors.white),
                            label: Text(
                              _isGeneratingPDF ? 'Generating...' : 'Print Tag',
                              style: const TextStyle(color: Colors.white),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primary,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  String _generateQRData() {
    // Create a comprehensive QR data string with all basic and source cattle information
    final Map<String, dynamic> cattleData = {
      // Core identification
      'id': widget.cattle.businessId,
      'tagId': widget.cattle.tagId,
      'name': widget.cattle.name,

      // Basic information
      'gender': widget.cattle.gender,
      'breed': widget.cattle.breedId,
      'type': widget.cattle.animalTypeId,
      'category': widget.cattle.category,
      'status': widget.cattle.status,
      'stage': widget.cattle.stage,
      'color': widget.cattle.color,
      'weight': widget.cattle.weight?.toString(),

      // Dates
      'dob': widget.cattle.dateOfBirth?.toIso8601String(),
      'purchaseDate': widget.cattle.purchaseDate?.toIso8601String(),

      // Source information
      'source': widget.cattle.source,
      'purchasePrice': widget.cattle.purchasePrice?.toString(),

      // Family information
      'motherTagId': widget.cattle.motherTagId,
      'motherBusinessId': widget.cattle.motherBusinessId,

      // Additional info
      'notes': widget.cattle.notes,
      'createdAt': widget.cattle.createdAt?.toIso8601String(),

      // App identifier
      'app': 'CattleManager',
      'version': '1.0',
    };

    // Remove null values
    cattleData.removeWhere((key, value) => value == null || value == '');

    // Create a compact string format
    final List<String> parts = [];
    cattleData.forEach((key, value) {
      parts.add('$key:$value');
    });

    return parts.join('|');
  }

  void _copyQRData(String data) {
    Clipboard.setData(ClipboardData(text: data));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white),
            SizedBox(width: 8),
            Text('QR data copied to clipboard'),
          ],
        ),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  Future<void> _shareQRCode() async {
    try {
      // Capture QR code as image
      final boundary = _qrKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) return;

      final image = await boundary.toImage(pixelRatio: 3.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final pngBytes = byteData?.buffer.asUint8List();

      if (pngBytes != null) {
        final tempDir = await getTemporaryDirectory();
        final file = await File('${tempDir.path}/cattle_qr_${widget.cattle.tagId ?? 'unknown'}.png').create();
        await file.writeAsBytes(pngBytes);

        await Share.shareXFiles(
          [XFile(file.path)],
          text: 'QR Code for ${widget.cattle.name ?? 'Cattle'} (${widget.cattle.tagId ?? 'Unknown Tag'})',
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sharing QR code: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _generateEarTagPDF() async {
    setState(() => _isGeneratingPDF = true);

    // Capture context before async operation
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      // TODO: Implement PDF generation for printable ear tags
      await Future.delayed(const Duration(seconds: 2)); // Simulate processing

      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('PDF ear tag generation will be implemented'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Error generating PDF: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isGeneratingPDF = false);
      }
    }
  }
}
