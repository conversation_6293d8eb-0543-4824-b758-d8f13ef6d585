import 'package:flutter/material.dart';

import '../models/veterinarian_isar.dart';
import '../services/health_service.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';

// Removed farm context service - single farm per user


class VeterinarianFormDialog extends StatefulWidget {
  final VeterinarianIsar? existingVeterinarian;
  final VoidCallback onVeterinarianAdded;

  const VeterinarianFormDialog({
    super.key,
    this.existingVeterinarian,
    required this.onVeterinarianAdded,
  });

  @override
  State<VeterinarianFormDialog> createState() => _VeterinarianFormDialogState();
}

class _VeterinarianFormDialogState extends State<VeterinarianFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _healthService = HealthService.instance;

  // Form controllers
  late TextEditingController _nameController;
  late TextEditingController _titleController;
  late TextEditingController _licenseController;
  late TextEditingController _specializationController;
  late TextEditingController _clinicController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;
  late TextEditingController _addressController;
  late TextEditingController _cityController;
  late TextEditingController _stateController;
  late TextEditingController _postalCodeController;
  late TextEditingController _consultationFeeController;
  late TextEditingController _emergencyFeeController;
  late TextEditingController _notesController;

  // Form values
  bool _emergencyAvailable = false;
  bool _isActive = true;
  bool _isPrimary = false;

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadExistingData();
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  void _initializeControllers() {
    _nameController = TextEditingController();
    _titleController = TextEditingController();
    _licenseController = TextEditingController();
    _specializationController = TextEditingController();
    _clinicController = TextEditingController();
    _phoneController = TextEditingController();
    _emailController = TextEditingController();
    _addressController = TextEditingController();
    _cityController = TextEditingController();
    _stateController = TextEditingController();
    _postalCodeController = TextEditingController();
    _consultationFeeController = TextEditingController();
    _emergencyFeeController = TextEditingController();
    _notesController = TextEditingController();
  }

  void _disposeControllers() {
    _nameController.dispose();
    _titleController.dispose();
    _licenseController.dispose();
    _specializationController.dispose();
    _clinicController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _postalCodeController.dispose();
    _consultationFeeController.dispose();
    _emergencyFeeController.dispose();
    _notesController.dispose();
  }

  void _loadExistingData() {
    if (widget.existingVeterinarian != null) {
      final vet = widget.existingVeterinarian!;
      
      _nameController.text = vet.name ?? '';
      _titleController.text = vet.title ?? '';
      _licenseController.text = vet.licenseNumber ?? '';
      _specializationController.text = vet.specialization ?? '';
      _clinicController.text = vet.clinicName ?? '';
      _phoneController.text = vet.phoneNumber ?? '';
      _emailController.text = vet.email ?? '';
      _addressController.text = vet.address ?? '';
      _cityController.text = vet.city ?? '';
      _stateController.text = vet.state ?? '';
      _postalCodeController.text = vet.postalCode ?? '';
      _consultationFeeController.text = vet.consultationFee?.toString() ?? '';
      _emergencyFeeController.text = vet.emergencyFee?.toString() ?? '';
      _notesController.text = vet.notes ?? '';
      
      _emergencyAvailable = vet.emergencyAvailable;
      _isActive = vet.isActive;
      _isPrimary = vet.isPrimary;
    }
  }

  Future<void> _saveVeterinarian() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Single farm per user - no farm context needed
      // Single farm per user - get farm directly
      final farmSetupHandler = FarmSetupHandler.instance;
      final farms = await farmSetupHandler.getAllFarms();

      if (farms.isEmpty) {
        throw Exception('No farm found');
      }

      final farmId = farms.first.farmBusinessId ?? 'default_farm';

      final veterinarian = widget.existingVeterinarian?.copyWith(
        name: _nameController.text.trim(),
        title: _titleController.text.trim().isEmpty ? null : _titleController.text.trim(),
        licenseNumber: _licenseController.text.trim().isEmpty ? null : _licenseController.text.trim(),
        specialization: _specializationController.text.trim().isEmpty ? null : _specializationController.text.trim(),
        clinicName: _clinicController.text.trim().isEmpty ? null : _clinicController.text.trim(),
        phoneNumber: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
        address: _addressController.text.trim().isEmpty ? null : _addressController.text.trim(),
        city: _cityController.text.trim().isEmpty ? null : _cityController.text.trim(),
        state: _stateController.text.trim().isEmpty ? null : _stateController.text.trim(),
        postalCode: _postalCodeController.text.trim().isEmpty ? null : _postalCodeController.text.trim(),
        consultationFee: double.tryParse(_consultationFeeController.text.trim()),
        emergencyFee: double.tryParse(_emergencyFeeController.text.trim()),
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        emergencyAvailable: _emergencyAvailable,
        isActive: _isActive,
        isPrimary: _isPrimary,
      ) ?? VeterinarianIsar.create(
        name: _nameController.text.trim(),
        farmBusinessId: farmId,
        title: _titleController.text.trim().isEmpty ? null : _titleController.text.trim(),
        licenseNumber: _licenseController.text.trim().isEmpty ? null : _licenseController.text.trim(),
        specialization: _specializationController.text.trim().isEmpty ? null : _specializationController.text.trim(),
        clinicName: _clinicController.text.trim().isEmpty ? null : _clinicController.text.trim(),
        phoneNumber: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
        address: _addressController.text.trim().isEmpty ? null : _addressController.text.trim(),
        city: _cityController.text.trim().isEmpty ? null : _cityController.text.trim(),
        state: _stateController.text.trim().isEmpty ? null : _stateController.text.trim(),
        postalCode: _postalCodeController.text.trim().isEmpty ? null : _postalCodeController.text.trim(),
        consultationFee: double.tryParse(_consultationFeeController.text.trim()),
        emergencyFee: double.tryParse(_emergencyFeeController.text.trim()),
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        emergencyAvailable: _emergencyAvailable,
        isActive: _isActive,
        isPrimary: _isPrimary,
      );

      if (widget.existingVeterinarian != null) {
        await _healthService.updateVeterinarian(veterinarian);
      } else {
        await _healthService.addVeterinarian(veterinarian);
      }

      if (mounted) {
        Navigator.pop(context);
        widget.onVeterinarianAdded();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.existingVeterinarian != null 
                  ? 'Veterinarian updated successfully'
                  : 'Veterinarian added successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save veterinarian: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.existingVeterinarian != null ? 'Edit Veterinarian' : 'Add Veterinarian'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveVeterinarian,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'Save',
                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // Basic Information Section
            _buildSectionHeader('Basic Information', Icons.person, Colors.teal),
            const SizedBox(height: 16),
            
            // Name
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Full Name *',
                border: OutlineInputBorder(),
              ),
              validator: (value) => value?.trim().isEmpty ?? true ? 'Please enter veterinarian name' : null,
            ),
            const SizedBox(height: 16),

            // Title and License Row
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _titleController,
                    decoration: const InputDecoration(
                      labelText: 'Title',
                      border: OutlineInputBorder(),
                      hintText: 'Dr., DVM, etc.',
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _licenseController,
                    decoration: const InputDecoration(
                      labelText: 'License Number',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Specialization and Clinic Row
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _specializationController,
                    decoration: const InputDecoration(
                      labelText: 'Specialization',
                      border: OutlineInputBorder(),
                      hintText: 'Large Animal, Mixed Practice, etc.',
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _clinicController,
                    decoration: const InputDecoration(
                      labelText: 'Clinic Name',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Contact Information Section
            _buildSectionHeader('Contact Information', Icons.contact_phone, Colors.blue),
            const SizedBox(height: 16),

            // Phone and Email Row
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _phoneController,
                    decoration: const InputDecoration(
                      labelText: 'Phone Number',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.phone,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _emailController,
                    decoration: const InputDecoration(
                      labelText: 'Email',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.emailAddress,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Address Section
            _buildSectionHeader('Address', Icons.location_on, Colors.purple),
            const SizedBox(height: 16),

            // Address
            TextFormField(
              controller: _addressController,
              decoration: const InputDecoration(
                labelText: 'Street Address',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),

            // City, State, Postal Code Row
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _cityController,
                    decoration: const InputDecoration(
                      labelText: 'City',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _stateController,
                    decoration: const InputDecoration(
                      labelText: 'State',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _postalCodeController,
                    decoration: const InputDecoration(
                      labelText: 'Postal Code',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Fees Section
            _buildSectionHeader('Fees', Icons.attach_money, Colors.green),
            const SizedBox(height: 16),

            // Consultation and Emergency Fee Row
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _consultationFeeController,
                    decoration: const InputDecoration(
                      labelText: 'Consultation Fee',
                      border: OutlineInputBorder(),
                      prefixText: '\$ ',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _emergencyFeeController,
                    decoration: const InputDecoration(
                      labelText: 'Emergency Fee',
                      border: OutlineInputBorder(),
                      prefixText: '\$ ',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Settings Section
            _buildSectionHeader('Settings', Icons.settings, Colors.indigo),
            const SizedBox(height: 16),

            // Emergency Available
            CheckboxListTile(
              title: const Text('Emergency Services Available'),
              subtitle: const Text('Available for emergency calls'),
              value: _emergencyAvailable,
              onChanged: (value) => setState(() => _emergencyAvailable = value ?? false),
              activeColor: Colors.red,
            ),

            // Is Active
            CheckboxListTile(
              title: const Text('Active'),
              subtitle: const Text('Currently active veterinarian'),
              value: _isActive,
              onChanged: (value) => setState(() => _isActive = value ?? true),
              activeColor: Colors.green,
            ),

            // Is Primary
            CheckboxListTile(
              title: const Text('Primary Veterinarian'),
              subtitle: const Text('Primary veterinarian for this farm'),
              value: _isPrimary,
              onChanged: (value) => setState(() => _isPrimary = value ?? false),
              activeColor: Colors.blue,
            ),
            const SizedBox(height: 24),

            // Notes Section
            _buildSectionHeader('Additional Notes', Icons.notes, Colors.blueGrey),
            const SizedBox(height: 16),

            // Notes
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes',
                border: OutlineInputBorder(),
                hintText: 'Additional information about the veterinarian...',
              ),
              maxLines: 4,
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(child: Divider(color: color)),
      ],
    );
  }
}
