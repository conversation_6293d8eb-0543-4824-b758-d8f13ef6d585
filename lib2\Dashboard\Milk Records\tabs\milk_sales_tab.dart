import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/milk_sale_isar.dart';
import '../services/milk_sales_service.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import '../dialogs/milk_sale_entry_dialog.dart';
import '../../../widgets/empty_state.dart';
import '../../../utils/message_utils.dart';
import '../../widgets/filter_widget.dart';
import '../../widgets/sort_widget.dart';
import '../../widgets/search_widget.dart';
import '../../widgets/date_range_filter_widget.dart';
import '../../widgets/filter_status_bar.dart';
// import '../../widgets/common_filter_helper.dart'; // Not needed
// import '../../widgets/module_specific_filter_helper.dart'; // Not needed

class MilkSalesTab extends StatefulWidget {
  final VoidCallback? onClearFilters;

  const MilkSalesTab({
    Key? key,
    this.onClearFilters,
  }) : super(key: key);

  @override
  State<MilkSalesTab> createState() => _MilkSalesTabState();
}

class _MilkSalesTabState extends State<MilkSalesTab> {
  // Color scheme - Sales tab (No color repetition within same widget/section)
  static const _salesColor = Colors.purple;     // Purple for sales count
  static const _revenueColor = Colors.blue;     // Blue for revenue
  static const _profitColor = Colors.green;     // Green for average/profit
  static const _pendingColor = Colors.red;      // Red for pending

  // Services
  final MilkSalesService _salesService = MilkSalesService();
  final FarmSetupHandler _farmSetupHandler = FarmSetupHandler.instance;

  // Data
  List<MilkSaleIsar> _allSales = [];
  List<MilkSaleIsar> _filteredSales = [];
  bool _isLoading = true;

  // Filters
  String _searchQuery = '';
  String _selectedPaymentStatus = 'All';
  DateTime? _startDate;
  DateTime? _endDate;
  String? _sortBy; // null means no sort applied (following weight module pattern)
  bool _sortAscending = true; // Default to true when sort is applied

  // Selection mode
  bool _isSelectionMode = false;
  final Set<String> _selectedSales = {};

  // Currency settings
  String _currencySymbol = '\$';
  bool _symbolBeforeAmount = true;

  @override
  void initState() {
    super.initState();
    _loadData();
    _loadCurrencySettings();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      final sales = await _salesService.getMilkSales();
      if (mounted) {
        setState(() {
          _allSales = sales;
          _filteredSales = sales;
          _isLoading = false;
        });
        _applyFilters();
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        MessageUtils.showError(context, 'Error loading sales: $e');
      }
    }
  }

  Future<void> _loadCurrencySettings() async {
    try {
      final currencySettings = await _farmSetupHandler.getCurrencySettings();
      if (mounted) {
        setState(() {
          _currencySymbol = currencySettings.currencySymbol;
          _symbolBeforeAmount = currencySettings.symbolBeforeAmount;
        });
      }
    } catch (e) {
      // Use defaults
    }
  }

  String _formatCurrency(double amount) {
    return _symbolBeforeAmount
        ? '$_currencySymbol${amount.toStringAsFixed(2)}'
        : '${amount.toStringAsFixed(2)}$_currencySymbol';
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_filteredSales.isEmpty) {
      // When empty, make the whole screen scrollable
      return RefreshIndicator(
        onRefresh: _loadData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              _buildFiltersSection(),
              _buildAnalyticsCards(),
              _buildEmptyState(),
              const SizedBox(height: 24), // Bottom padding for the screen
            ],
          ),
        ),
      );
    }

    // When there are sales, use the normal layout
    return RefreshIndicator(
      onRefresh: _loadData,
      child: Column(
        children: [
          _buildFiltersSection(),
          _buildAnalyticsCards(),
          Expanded(child: _buildSalesContent()),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Column(
      children: [
        // Row 1: Filter + Date + Sort (following weight module pattern exactly)
        Row(
          children: [
            // Payment Status Filter
            Expanded(
              child: FilterWidget(
                filterFields: [
                  FilterField(
                    key: 'paymentStatus',
                    label: 'Payment Status',
                    icon: Icons.payment,
                    iconColor: _salesColor,
                    options: const [
                      FilterOption(value: 'All', label: 'All'),
                      FilterOption(value: 'Paid', label: 'Paid'),
                      FilterOption(value: 'Pending', label: 'Pending'),
                    ],
                    currentValue: _selectedPaymentStatus,
                  ),
                ],
                onFilterChanged: (key, value) {
                  if (key == 'paymentStatus') {
                    setState(() => _selectedPaymentStatus = value ?? 'All');
                    _applyFilters();
                  }
                },
                onApplyFilters: _applyFilters,
                onClearFilters: _clearFilters,
                themeColor: _salesColor,
                dialogTitle: 'Filter Sales',
                compact: true,
                totalCount: _allSales.length,
                filteredCount: _filteredSales.length,
                showFilterCount: true,
                buttonHeight: 44,
              ),
            ),
            const SizedBox(width: 8),

            // Date Range Filter
            Expanded(
              child: DateRangeFilterWidget(
                startDate: _startDate,
                endDate: _endDate,
                onStartDateChanged: (date) {
                  setState(() => _startDate = date);
                  _applyFilters();
                },
                onEndDateChanged: (date) {
                  setState(() => _endDate = date);
                  _applyFilters();
                },
                onFiltersChanged: () => _applyFilters(),
                onClearFilter: _clearFilters,
                themeColor: _salesColor,
                activeBackgroundOpacity: 0.7,
                compact: true,
                buttonHeight: 44,
              ),
            ),
            const SizedBox(width: 8),

            // Sort Widget
            Expanded(
              child: SortWidget(
                sortFields: const [
                  SortField(
                    value: 'Date',
                    label: 'Date',
                    icon: Icons.calendar_today,
                    iconColor: Colors.blue,
                  ),
                  SortField(
                    value: 'Amount',
                    label: 'Amount',
                    icon: Icons.attach_money,
                    iconColor: Colors.green,
                  ),
                  SortField(
                    value: 'Quantity',
                    label: 'Quantity',
                    icon: Icons.local_drink,
                    iconColor: Colors.cyan,
                  ),
                  SortField(
                    value: 'Buyer',
                    label: 'Buyer',
                    icon: Icons.person,
                    iconColor: Colors.purple,
                  ),
                ],
                sortBy: _sortBy,
                sortAscending: _sortAscending,
                onSortByChanged: (value) {
                  setState(() => _sortBy = value);
                  _applyFilters();
                },
                onSortAscendingChanged: (value) {
                  setState(() => _sortAscending = value);
                  _applyFilters();
                },
                onApplySort: _applyFilters,
                themeColor: _salesColor,
                compact: true,
                showSortIndicator: true,
                buttonHeight: 44,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // Filter Status Bar (Clear Filters + Count)
        FilterStatusBar.milk(
          filterStates: {
            'startDate': _startDate,
            'endDate': _endDate,
            'sortBy': _sortBy,
            'searchQuery': _searchQuery,
          },
          totalCount: _allSales.length,
          filteredCount: _filteredSales.length,
          onClearFilters: _clearFilters,
          defaultStartDate: DateTime.now().subtract(const Duration(days: 30)),
          defaultEndDate: DateTime.now(),
        ),

        // Row 2: Search
        SearchWidget(
          searchQuery: _searchQuery,
          onSearchChanged: (query) {
            setState(() => _searchQuery = query);
            _applyFilters();
          },
          config: const SearchConfig(hint: 'Search sales...'),
          themeColor: _salesColor,
          resultCount: _filteredSales.length,
          showResultCount: true,
          height: 44,
        ),
      ],
    );
  }

  Widget _buildAnalyticsCards() {
    final analytics = _calculateSalesAnalytics();

    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sales Analytics',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _salesColor,
            ),
          ),
          SizedBox(height: _getResponsiveSpacing()),

          // Analytics Cards - Use responsive layout instead of GridView
          LayoutBuilder(
            builder: (context, constraints) {
              final isDesktop = constraints.maxWidth > 800;

              if (isDesktop) {
                // Desktop: Single row with 4 cards
                return Row(
                  children: [
                    Expanded(
                      child: _buildAnalyticsCard(
                        'Total Sales',
                        '${analytics['totalSales']}',
                        Icons.receipt_long,
                        _salesColor,
                      ),
                    ),
                    SizedBox(width: _getResponsiveSpacing()),
                    Expanded(
                      child: _buildAnalyticsCard(
                        'Total Revenue',
                        _formatCurrency(analytics['totalRevenue']),
                        Icons.attach_money,
                        _revenueColor,
                      ),
                    ),
                    SizedBox(width: _getResponsiveSpacing()),
                    Expanded(
                      child: _buildAnalyticsCard(
                        'Avg Sale',
                        _formatCurrency(analytics['avgSale']),
                        Icons.trending_up,
                        _profitColor,
                      ),
                    ),
                    SizedBox(width: _getResponsiveSpacing()),
                    Expanded(
                      child: _buildAnalyticsCard(
                        'Pending',
                        _formatCurrency(analytics['pendingAmount']),
                        Icons.pending_actions,
                        _pendingColor,
                      ),
                    ),
                  ],
                );
              } else {
                // Mobile/Tablet: Two rows with 2 cards each
                return Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: _buildAnalyticsCard(
                            'Total Sales',
                            '${analytics['totalSales']}',
                            Icons.receipt_long,
                            _salesColor,
                          ),
                        ),
                        SizedBox(width: _getResponsiveSpacing()),
                        Expanded(
                          child: _buildAnalyticsCard(
                            'Total Revenue',
                            _formatCurrency(analytics['totalRevenue']),
                            Icons.attach_money,
                            _revenueColor,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: _getResponsiveSpacing()),
                    Row(
                      children: [
                        Expanded(
                          child: _buildAnalyticsCard(
                            'Avg Sale',
                            _formatCurrency(analytics['avgSale']),
                            Icons.trending_up,
                            _profitColor,
                          ),
                        ),
                        SizedBox(width: _getResponsiveSpacing()),
                        Expanded(
                          child: _buildAnalyticsCard(
                            'Pending',
                            _formatCurrency(analytics['pendingAmount']),
                            Icons.pending_actions,
                            _pendingColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color, // Match the icon and value color
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }



  Widget _buildSalesContent() {
    return ListView.builder(
      padding: EdgeInsets.all(_getResponsivePadding()),
      itemCount: _filteredSales.length,
      itemBuilder: (context, index) {
        final sale = _filteredSales[index];
        return _buildSaleCard(sale);
      },
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 300),
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 32), // Added bottom margin for padding
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: _salesColor.withAlpha(76)), // 0.3 * 255 = 76
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25), // 0.1 * 255 = 25
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: EmptyState.custom(
        icon: _allSales.isEmpty ? Icons.point_of_sale : Icons.filter_alt_off_outlined,
        message: _allSales.isEmpty ? 'No Sales Records' : 'No Sales Found',
        subtitle: _allSales.isEmpty
            ? 'Start by recording your first milk sale'
            : 'No sales match your current filters',
        color: _salesColor, // Use sales color to match analytics cards
        hasData: _allSales.isNotEmpty,
        action: _allSales.isEmpty
            ? EmptyState.createActionButton(
                onPressed: _showAddSaleDialog,
                icon: Icons.add,
                label: 'Record First Sale',
                backgroundColor: _salesColor,
              )
            : EmptyState.createActionButton(
                onPressed: _clearFilters,
                icon: Icons.clear_all,
                label: 'Clear Filters',
                backgroundColor: _salesColor,
              ),
      ),
    );
  }

  Widget _buildSaleCard(MilkSaleIsar sale) {
    final isSelected = _selectedSales.contains(sale.businessId);
    final isPaid = sale.paymentStatus?.toLowerCase() == 'paid';

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: () => _isSelectionMode ? _toggleSelection(sale) : _viewSaleDetails(sale),
        onLongPress: () => _toggleSelectionMode(sale),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: isSelected ? Border.all(color: _salesColor, width: 2) : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Row
                Row(
                  children: [
                    // Sale Icon
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _salesColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.point_of_sale,
                        color: _salesColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),

                    // Sale Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Sale #${sale.saleId}',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            DateFormat('MMM dd, yyyy').format(sale.date),
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.black87, // Dark text instead of grey
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Payment Status Badge
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: isPaid ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isPaid ? Colors.green : Colors.red,
                          width: 1,
                        ),
                      ),
                      child: Text(
                        sale.paymentStatus ?? 'Pending',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Colors.white, // Always white text on dark colors
                        ),
                      ),
                    ),

                    // Selection checkbox
                    if (_isSelectionMode)
                      Checkbox(
                        value: isSelected,
                        onChanged: (_) => _toggleSelection(sale),
                        activeColor: _salesColor,
                      ),
                  ],
                ),

                const SizedBox(height: 12),

                // Sale Details Grid
                Row(
                  children: [
                    Expanded(
                      child: _buildSaleDetailItem(
                        'Quantity',
                        '${sale.quantity.toStringAsFixed(1)} L',
                        Icons.local_drink,
                        Colors.blue,
                      ),
                    ),
                    Expanded(
                      child: _buildSaleDetailItem(
                        'Price/L',
                        _formatCurrency(sale.pricePerLiter),
                        Icons.attach_money,
                        Colors.green,
                      ),
                    ),
                    Expanded(
                      child: _buildSaleDetailItem(
                        'Total',
                        _formatCurrency(sale.totalAmount),
                        Icons.receipt,
                        _salesColor,
                      ),
                    ),
                  ],
                ),

                // Buyer Info
                if (sale.buyerName != null && sale.buyerName!.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  const Divider(),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.person, size: 16, color: Colors.indigo),
                      const SizedBox(width: 8),
                      Text(
                        'Buyer: ${sale.buyerName}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black87, // Dark text instead of grey
                        ),
                      ),
                    ],
                  ),
                ],

                // Notes
                if (sale.notes != null && sale.notes!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.note, size: 16, color: Colors.purple),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          sale.notes!,
                          style: const TextStyle(
                            fontSize: 13,
                            color: Colors.black87, // Dark text instead of grey
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSaleDetailItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black87, // Dark text instead of grey
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper Methods
  Map<String, dynamic> _calculateSalesAnalytics() {
    if (_filteredSales.isEmpty) {
      return {
        'totalSales': 0,
        'totalRevenue': 0.0,
        'avgSale': 0.0,
        'pendingAmount': 0.0,
      };
    }

    final totalRevenue = _filteredSales.fold<double>(0.0, (sum, sale) => sum + sale.totalAmount);
    final avgSale = totalRevenue / _filteredSales.length;
    final pendingAmount = _filteredSales
        .where((sale) => sale.paymentStatus?.toLowerCase() != 'paid')
        .fold<double>(0.0, (sum, sale) => sum + sale.totalAmount);

    return {
      'totalSales': _filteredSales.length,
      'totalRevenue': totalRevenue,
      'avgSale': avgSale,
      'pendingAmount': pendingAmount,
    };
  }

  void _applyFilters() {
    var filtered = _allSales.where((sale) {
      // Search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final matchesSearch =
            (sale.saleId.toLowerCase().contains(query)) ||
            (sale.buyerName?.toLowerCase().contains(query) ?? false) ||
            (sale.notes?.toLowerCase().contains(query) ?? false);
        if (!matchesSearch) return false;
      }

      // Payment status filter
      if (_selectedPaymentStatus != 'All') {
        final status = sale.paymentStatus?.toLowerCase() ?? 'pending';
        if (_selectedPaymentStatus.toLowerCase() != status) return false;
      }

      // Date range filter
      if (_startDate != null) {
        if (sale.date.isBefore(_startDate!)) return false;
      }
      if (_endDate != null) {
        if (sale.date.isAfter(_endDate!.add(const Duration(days: 1)))) return false;
      }

      return true;
    }).toList();

    // Apply sorting (following weight module pattern exactly)
    if (_sortBy != null) {
      filtered.sort((a, b) {
        int comparison = 0;
        switch (_sortBy) {
          case 'Date':
            comparison = a.date.compareTo(b.date);
            break;
          case 'Amount':
            comparison = a.totalAmount.compareTo(b.totalAmount);
            break;
          case 'Quantity':
            comparison = a.quantity.compareTo(b.quantity);
            break;
          case 'Buyer':
            comparison = (a.buyerName ?? '').compareTo(b.buyerName ?? '');
            break;
        }
        return _sortAscending ? comparison : -comparison;
      });
    } else {
      // Default sorting by date in descending order if no specific sort is selected (newest first)
      filtered.sort((a, b) {
        return b.date.compareTo(a.date);
      });
    }

    setState(() => _filteredSales = filtered);
  }

  bool _hasActiveFilters() {
    // Check if any filters are applied (following weight module pattern exactly)
    bool hasFilters = _selectedPaymentStatus != 'All';

    // Check if sort is applied (following weight module pattern exactly)
    bool hasSort = _sortBy != null;

    // Check if search is applied
    bool hasSearch = _searchQuery.isNotEmpty;

    // Check if date range is active (only when both dates are set)
    bool hasDateFilter = _startDate != null && _endDate != null;

    return hasFilters || hasSort || hasSearch || hasDateFilter;
  }

  List<String> _getActiveFiltersList() {
    final List<String> activeFilters = [];

    // Check payment status filter
    if (_selectedPaymentStatus != 'All') {
      activeFilters.add('Payment: $_selectedPaymentStatus');
    }

    // Check date range (only show if dates are actually set)
    if (_startDate != null && _endDate != null) {
      final dateFormat = DateFormat('MMM dd');
      activeFilters.add('${dateFormat.format(_startDate!)} - ${dateFormat.format(_endDate!)}');
    }

    // Check sort (following weight module pattern exactly)
    if (_sortBy != null) {
      final direction = _sortAscending ? 'Ascending' : 'Descending';
      activeFilters.add('Sort: $_sortBy ($direction)');
    }

    // Check search
    if (_searchQuery.isNotEmpty) {
      activeFilters.add('Search: "$_searchQuery"');
    }

    return activeFilters;
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _selectedPaymentStatus = 'All';
      _startDate = null;
      _endDate = null;
      _sortBy = null; // Reset to no sort (following weight module pattern)
      _sortAscending = true; // Reset to default order
    });
    _applyFilters();
    widget.onClearFilters?.call();
  }

  void _showAddSaleDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => MilkSaleEntryDialog(
        availableMilk: 0.0, // Default value
        selectedDate: DateTime.now(),
      ),
    );

    if (result == true) {
      _loadData(); // Reload data if sale was added
    }
  }

  void _viewSaleDetails(MilkSaleIsar sale) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => MilkSaleEntryDialog(
        availableMilk: sale.quantity,
        selectedDate: sale.date,
      ),
    );

    if (result == true) {
      _loadData(); // Reload data if sale was modified
    }
  }

  void _toggleSelectionMode(MilkSaleIsar sale) {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (_isSelectionMode && sale.businessId != null) {
        _selectedSales.add(sale.businessId!);
      } else {
        _selectedSales.clear();
      }
    });
  }

  void _toggleSelection(MilkSaleIsar sale) {
    if (sale.businessId == null) return;

    setState(() {
      if (_selectedSales.contains(sale.businessId)) {
        _selectedSales.remove(sale.businessId);
        if (_selectedSales.isEmpty) {
          _isSelectionMode = false;
        }
      } else {
        _selectedSales.add(sale.businessId!);
      }
    });
  }

  // Responsive design helpers
  double _getResponsivePadding() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 12.0;
    if (screenWidth < 1200) return 16.0;
    return 20.0;
  }

  double _getResponsiveSpacing() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 8.0;
    if (screenWidth < 1200) return 12.0;
    return 16.0;
  }
}
