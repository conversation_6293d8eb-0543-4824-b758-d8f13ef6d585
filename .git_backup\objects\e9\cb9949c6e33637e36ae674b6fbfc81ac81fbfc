rsion": "2.18"
    },
    {
      "name": "petitparser",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.0.2",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "platform",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "plugin_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "pool",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pool-1.5.1",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "printing",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/printing-5.14.2",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "process_run",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/process_run-1.2.2+1",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "provider",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.2",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "pub_semver",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pub_semver-2.1.5",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "pubspec_parse",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pubspec_parse-1.5.0",
      "packageUri": "lib/",
      "languageVersion": "3.6"
    },
    {
      "name": "qr",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/qr-3.0.2",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "qr_code_scanner",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/qr_code_scanner-1.0.1",
      "packageUri": "lib/",
      "languageVersion": "2.17"
    },
    {
      "name": "qr_flutter",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/qr_flutter-4.1.0",
      "packageUri": "lib/",
      "languageVersion": "2.19"
    },
    {
      "name": "share_plus",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus-7.2.2",
      "packageUri": "lib/",
      "languageVersion": "2.18"
    },
    {
      "name": "share_plus_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share_plus_platform_interface-3.4.0",
      "packageUri": "lib/",
      "languageVersion": "2.18"
    },
    {
      "name": "shared_preferences",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.2",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "shared_preferences_android",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.6",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "shared_preferences_foundation",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "shared_preferences_linux",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "shared_preferences_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "shared_preferences_web",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "shared_preferences_windows",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "shelf",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf-1.4.2",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "shelf_web_socket",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shelf_web_socket-3.0.0",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sky_engine",
      "rootUri": "file:///C:/flutter/bin/cache/pkg/sky_engine",
      "packageUri": "lib/",
      "languageVersion": "3.2"
    },
    {
      "name": "source_gen",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_gen-1.5.0",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "source_helper",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_helper-1.3.5",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "source_span",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.0",
      "packageUri": "lib/",
      "languageVersion": "2.18"
    },
    {
      "name": "sprintf",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "sqflite",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite-2.4.1",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sqflite_android",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_android-2.4.0",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sqflite_common",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common-2.5.4+6",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sqflite_common_ffi",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common_ffi-2.3.4+4",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sqflite_common_ffi_web",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common_ffi_web-0.4.5+4",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sqflite_darwin",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.1+1",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sqflite_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_platform_interface-2.4.0",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "sqlite3",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqlite3-2.7.4",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "stack_trace",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.0",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "stream_channel",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.2",
      "packageUri": "lib/",
      "languageVersion": "2.19"
    },
    {
      "name": "stream_transform",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1",
      "packageUri": "lib/",
      "languageVersion": "3.1"
    },
    {
      "name": "string_scanner",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.3.0",
      "packageUri": "lib/",
      "languageVersion": "3.1"
    },
    {
      "name": "syncfusion_flutter_core",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/syncfusion_flutter_core-28.2.6",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "syncfusion_flutter_pdf",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/syncfusion_flutter_pdf-28.2.6",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "syncfusion_flutter_xlsio",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/syncfusion_flutter_xlsio-28.2.6",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "syncfusion_officecore",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/syncfusion_officecore-28.2.6",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "synchronized",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/synchronized-3.3.0+3",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "term_glyph",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.1",
      "packageUri": "lib/",
      "languageVersion": "2.12"
    },
    {
      "name": "test_api",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.3",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "timing",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timing-1.0.2",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "typed_data",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
      "name": "universal_html",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/universal_html-2.2.4",
      "packageUri": "lib/",
      "languageVersion": "2.17"
    },
    {
      "name": "universal_io",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/universal_io-2.2.2",
      "packageUri": "lib/",
      "languageVersion": "2.17"
    },
    {
      "name": "url_launcher_linux",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "url_launcher_platform_interface",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2",
      "packageUri": "lib/",
      "languageVersion": "3.1"
    },
    {
      "name": "url_launcher_web",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.0",
      "packageUri": "lib/",
      "languageVersion": "3.6"
    },
    {
      "name": "url_launcher_windows",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "uuid",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1",
      "packageUri": "lib/",
      "languageVersion": "3.0"
    },
    {
      "name": "vector_graphics",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics-1.1.18",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "vector_graphics_codec",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics_codec-1.1.13",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "vector_graphics_compiler",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics_compiler-1.1.16",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "vector_math",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4",
      "packageUri": "lib/",
      "languageVersion": "2.14"
    },
    {
      "name": "vm_service",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-14.3.0",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "watcher",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/watcher-1.1.1",
      "packageUri": "lib/",
      "languageVersion": "3.1"
    },
    {
      "name": "web",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.0",
      "packageUri": "lib/",
      "languageVersion": "3.4"
    },
    {
      "name": "web_socket",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket-0.1.6",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "web_socket_channel",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket_channel-3.0.2",
      "packageUri": "lib/",
      "languageVersion": "3.3"
    },
    {
      "name": "win32",
      "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.10.1",
      "packageUri": "lib/",
      "languageVersion": "3.5"
    },
    {
   