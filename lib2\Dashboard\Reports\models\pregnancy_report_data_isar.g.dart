// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pregnancy_report_data_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetPregnancyReportDataIsarCollection on Isar {
  IsarCollection<PregnancyReportDataIsar> get pregnancyReportDataIsars =>
      this.collection();
}

const PregnancyReportDataIsarSchema = CollectionSchema(
  name: r'PregnancyReportDataIsar',
  id: 3008060464178084390,
  properties: {
    r'abortedPregnancies': PropertySchema(
      id: 0,
      name: r'abortedPregnancies',
      type: IsarType.long,
    ),
    r'averageGestationLength': PropertySchema(
      id: 1,
      name: r'averageGestationLength',
      type: IsarType.double,
    ),
    r'businessId': PropertySchema(
      id: 2,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'calvingCounts': PropertySchema(
      id: 3,
      name: r'calvingCounts',
      type: IsarType.longList,
    ),
    r'calvingDates': PropertySchema(
      id: 4,
      name: r'calvingDates',
      type: IsarType.dateTimeList,
    ),
    r'calvingSurvivalRate': PropertySchema(
      id: 5,
      name: r'calvingSurvivalRate',
      type: IsarType.double,
    ),
    r'completedPregnancies': PropertySchema(
      id: 6,
      name: r'completedPregnancies',
      type: IsarType.long,
    ),
    r'createdAt': PropertySchema(
      id: 7,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'currentPregnancies': PropertySchema(
      id: 8,
      name: r'currentPregnancies',
      type: IsarType.long,
    ),
    r'endDate': PropertySchema(
      id: 9,
      name: r'endDate',
      type: IsarType.dateTime,
    ),
    r'filterCriteria': PropertySchema(
      id: 10,
      name: r'filterCriteria',
      type: IsarType.string,
    ),
    r'generatedAt': PropertySchema(
      id: 11,
      name: r'generatedAt',
      type: IsarType.dateTime,
    ),
    r'pregnancySuccessRate': PropertySchema(
      id: 12,
      name: r'pregnancySuccessRate',
      type: IsarType.double,
    ),
    r'reportType': PropertySchema(
      id: 13,
      name: r'reportType',
      type: IsarType.string,
    ),
    r'startDate': PropertySchema(
      id: 14,
      name: r'startDate',
      type: IsarType.dateTime,
    ),
    r'statusColors': PropertySchema(
      id: 15,
      name: r'statusColors',
      type: IsarType.longList,
    ),
    r'statusCounts': PropertySchema(
      id: 16,
      name: r'statusCounts',
      type: IsarType.longList,
    ),
    r'statusLabels': PropertySchema(
      id: 17,
      name: r'statusLabels',
      type: IsarType.stringList,
    ),
    r'title': PropertySchema(
      id: 18,
      name: r'title',
      type: IsarType.string,
    ),
    r'totalPregnancies': PropertySchema(
      id: 19,
      name: r'totalPregnancies',
      type: IsarType.long,
    ),
    r'trimesterColors': PropertySchema(
      id: 20,
      name: r'trimesterColors',
      type: IsarType.longList,
    ),
    r'trimesterCounts': PropertySchema(
      id: 21,
      name: r'trimesterCounts',
      type: IsarType.longList,
    ),
    r'trimesterLabels': PropertySchema(
      id: 22,
      name: r'trimesterLabels',
      type: IsarType.stringList,
    ),
    r'updatedAt': PropertySchema(
      id: 23,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _pregnancyReportDataIsarEstimateSize,
  serialize: _pregnancyReportDataIsarSerialize,
  deserialize: _pregnancyReportDataIsarDeserialize,
  deserializeProp: _pregnancyReportDataIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'reportType': IndexSchema(
      id: 3559997651334899995,
      name: r'reportType',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'reportType',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _pregnancyReportDataIsarGetId,
  getLinks: _pregnancyReportDataIsarGetLinks,
  attach: _pregnancyReportDataIsarAttach,
  version: '3.1.0+1',
);

int _pregnancyReportDataIsarEstimateSize(
  PregnancyReportDataIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.calvingCounts;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.calvingDates;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.filterCriteria;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.reportType;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.statusColors;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.statusCounts;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final list = object.statusLabels;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.title;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.trimesterColors;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.trimesterCounts;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final list = object.trimesterLabels;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  return bytesCount;
}

void _pregnancyReportDataIsarSerialize(
  PregnancyReportDataIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.abortedPregnancies);
  writer.writeDouble(offsets[1], object.averageGestationLength);
  writer.writeString(offsets[2], object.businessId);
  writer.writeLongList(offsets[3], object.calvingCounts);
  writer.writeDateTimeList(offsets[4], object.calvingDates);
  writer.writeDouble(offsets[5], object.calvingSurvivalRate);
  writer.writeLong(offsets[6], object.completedPregnancies);
  writer.writeDateTime(offsets[7], object.createdAt);
  writer.writeLong(offsets[8], object.currentPregnancies);
  writer.writeDateTime(offsets[9], object.endDate);
  writer.writeString(offsets[10], object.filterCriteria);
  writer.writeDateTime(offsets[11], object.generatedAt);
  writer.writeDouble(offsets[12], object.pregnancySuccessRate);
  writer.writeString(offsets[13], object.reportType);
  writer.writeDateTime(offsets[14], object.startDate);
  writer.writeLongList(offsets[15], object.statusColors);
  writer.writeLongList(offsets[16], object.statusCounts);
  writer.writeStringList(offsets[17], object.statusLabels);
  writer.writeString(offsets[18], object.title);
  writer.writeLong(offsets[19], object.totalPregnancies);
  writer.writeLongList(offsets[20], object.trimesterColors);
  writer.writeLongList(offsets[21], object.trimesterCounts);
  writer.writeStringList(offsets[22], object.trimesterLabels);
  writer.writeDateTime(offsets[23], object.updatedAt);
}

PregnancyReportDataIsar _pregnancyReportDataIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = PregnancyReportDataIsar(
    endDate: reader.readDateTimeOrNull(offsets[9]),
    startDate: reader.readDateTimeOrNull(offsets[14]),
  );
  object.abortedPregnancies = reader.readLongOrNull(offsets[0]);
  object.averageGestationLength = reader.readDoubleOrNull(offsets[1]);
  object.businessId = reader.readStringOrNull(offsets[2]);
  object.calvingCounts = reader.readLongList(offsets[3]);
  object.calvingDates = reader.readDateTimeList(offsets[4]);
  object.calvingSurvivalRate = reader.readDoubleOrNull(offsets[5]);
  object.completedPregnancies = reader.readLongOrNull(offsets[6]);
  object.createdAt = reader.readDateTimeOrNull(offsets[7]);
  object.currentPregnancies = reader.readLongOrNull(offsets[8]);
  object.filterCriteria = reader.readStringOrNull(offsets[10]);
  object.generatedAt = reader.readDateTimeOrNull(offsets[11]);
  object.id = id;
  object.pregnancySuccessRate = reader.readDoubleOrNull(offsets[12]);
  object.reportType = reader.readStringOrNull(offsets[13]);
  object.statusColors = reader.readLongList(offsets[15]);
  object.statusCounts = reader.readLongList(offsets[16]);
  object.statusLabels = reader.readStringList(offsets[17]);
  object.title = reader.readStringOrNull(offsets[18]);
  object.totalPregnancies = reader.readLongOrNull(offsets[19]);
  object.trimesterColors = reader.readLongList(offsets[20]);
  object.trimesterCounts = reader.readLongList(offsets[21]);
  object.trimesterLabels = reader.readStringList(offsets[22]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[23]);
  return object;
}

P _pregnancyReportDataIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset)) as P;
    case 1:
      return (reader.readDoubleOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readLongList(offset)) as P;
    case 4:
      return (reader.readDateTimeList(offset)) as P;
    case 5:
      return (reader.readDoubleOrNull(offset)) as P;
    case 6:
      return (reader.readLongOrNull(offset)) as P;
    case 7:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 8:
      return (reader.readLongOrNull(offset)) as P;
    case 9:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 12:
      return (reader.readDoubleOrNull(offset)) as P;
    case 13:
      return (reader.readStringOrNull(offset)) as P;
    case 14:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 15:
      return (reader.readLongList(offset)) as P;
    case 16:
      return (reader.readLongList(offset)) as P;
    case 17:
      return (reader.readStringList(offset)) as P;
    case 18:
      return (reader.readStringOrNull(offset)) as P;
    case 19:
      return (reader.readLongOrNull(offset)) as P;
    case 20:
      return (reader.readLongList(offset)) as P;
    case 21:
      return (reader.readLongList(offset)) as P;
    case 22:
      return (reader.readStringList(offset)) as P;
    case 23:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _pregnancyReportDataIsarGetId(PregnancyReportDataIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _pregnancyReportDataIsarGetLinks(
    PregnancyReportDataIsar object) {
  return [];
}

void _pregnancyReportDataIsarAttach(
    IsarCollection<dynamic> col, Id id, PregnancyReportDataIsar object) {
  object.id = id;
}

extension PregnancyReportDataIsarByIndex
    on IsarCollection<PregnancyReportDataIsar> {
  Future<PregnancyReportDataIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  PregnancyReportDataIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<PregnancyReportDataIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<PregnancyReportDataIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(PregnancyReportDataIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(PregnancyReportDataIsar object,
      {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<PregnancyReportDataIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<PregnancyReportDataIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension PregnancyReportDataIsarQueryWhereSort
    on QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QWhere> {
  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterWhere>
      anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension PregnancyReportDataIsarQueryWhere on QueryBuilder<
    PregnancyReportDataIsar, PregnancyReportDataIsar, QWhereClause> {
  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterWhereClause> idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterWhereClause> idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterWhereClause> idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterWhereClause> idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterWhereClause> reportTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'reportType',
        value: [null],
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterWhereClause> reportTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'reportType',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterWhereClause> reportTypeEqualTo(String? reportType) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'reportType',
        value: [reportType],
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterWhereClause> reportTypeNotEqualTo(String? reportType) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [],
              upper: [reportType],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [reportType],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [reportType],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [],
              upper: [reportType],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterWhereClause> businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterWhereClause> businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterWhereClause> businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterWhereClause> businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension PregnancyReportDataIsarQueryFilter on QueryBuilder<
    PregnancyReportDataIsar, PregnancyReportDataIsar, QFilterCondition> {
  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> abortedPregnanciesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'abortedPregnancies',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> abortedPregnanciesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'abortedPregnancies',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> abortedPregnanciesEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'abortedPregnancies',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> abortedPregnanciesGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'abortedPregnancies',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> abortedPregnanciesLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'abortedPregnancies',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> abortedPregnanciesBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'abortedPregnancies',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> averageGestationLengthIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'averageGestationLength',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> averageGestationLengthIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'averageGestationLength',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> averageGestationLengthEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'averageGestationLength',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> averageGestationLengthGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'averageGestationLength',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> averageGestationLengthLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'averageGestationLength',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> averageGestationLengthBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'averageGestationLength',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
          QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
          QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingCountsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'calvingCounts',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingCountsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'calvingCounts',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingCountsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'calvingCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingCountsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'calvingCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingCountsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'calvingCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingCountsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'calvingCounts',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingCountsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'calvingCounts',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingCountsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'calvingCounts',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingCountsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'calvingCounts',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingCountsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'calvingCounts',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingCountsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'calvingCounts',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingCountsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'calvingCounts',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingDatesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'calvingDates',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingDatesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'calvingDates',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingDatesElementEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'calvingDates',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingDatesElementGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'calvingDates',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingDatesElementLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'calvingDates',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingDatesElementBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'calvingDates',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingDatesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'calvingDates',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingDatesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'calvingDates',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingDatesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'calvingDates',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingDatesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'calvingDates',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingDatesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'calvingDates',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingDatesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'calvingDates',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingSurvivalRateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'calvingSurvivalRate',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingSurvivalRateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'calvingSurvivalRate',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingSurvivalRateEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'calvingSurvivalRate',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingSurvivalRateGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'calvingSurvivalRate',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingSurvivalRateLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'calvingSurvivalRate',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> calvingSurvivalRateBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'calvingSurvivalRate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> completedPregnanciesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'completedPregnancies',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> completedPregnanciesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'completedPregnancies',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> completedPregnanciesEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'completedPregnancies',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> completedPregnanciesGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'completedPregnancies',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> completedPregnanciesLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'completedPregnancies',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> completedPregnanciesBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'completedPregnancies',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> currentPregnanciesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'currentPregnancies',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> currentPregnanciesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'currentPregnancies',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> currentPregnanciesEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'currentPregnancies',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> currentPregnanciesGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'currentPregnancies',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> currentPregnanciesLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'currentPregnancies',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> currentPregnanciesBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'currentPregnancies',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> endDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> endDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> endDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> endDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> endDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> endDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'endDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'filterCriteria',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'filterCriteria',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> filterCriteriaEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> filterCriteriaGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> filterCriteriaLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> filterCriteriaBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'filterCriteria',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> filterCriteriaStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> filterCriteriaEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
          QAfterFilterCondition>
      filterCriteriaContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
          QAfterFilterCondition>
      filterCriteriaMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'filterCriteria',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filterCriteria',
        value: '',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'filterCriteria',
        value: '',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> generatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'generatedAt',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> generatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'generatedAt',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> generatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> generatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> generatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> generatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'generatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> pregnancySuccessRateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'pregnancySuccessRate',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> pregnancySuccessRateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'pregnancySuccessRate',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> pregnancySuccessRateEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'pregnancySuccessRate',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> pregnancySuccessRateGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'pregnancySuccessRate',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> pregnancySuccessRateLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'pregnancySuccessRate',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> pregnancySuccessRateBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'pregnancySuccessRate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> reportTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reportType',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> reportTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reportType',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> reportTypeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> reportTypeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> reportTypeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> reportTypeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'reportType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> reportTypeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> reportTypeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
          QAfterFilterCondition>
      reportTypeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
          QAfterFilterCondition>
      reportTypeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'reportType',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> reportTypeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reportType',
        value: '',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> reportTypeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'reportType',
        value: '',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> startDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> startDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> startDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> startDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> startDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> startDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'startDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusColorsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'statusColors',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusColorsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'statusColors',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusColorsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'statusColors',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusColorsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'statusColors',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusColorsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'statusColors',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusColorsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'statusColors',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusColorsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusColors',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusColorsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusColors',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusColorsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusColors',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusColorsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusColors',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusColorsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusColors',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusColorsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusColors',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusCountsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'statusCounts',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusCountsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'statusCounts',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusCountsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'statusCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusCountsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'statusCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusCountsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'statusCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusCountsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'statusCounts',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusCountsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusCounts',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusCountsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusCounts',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusCountsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusCounts',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusCountsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusCounts',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusCountsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusCounts',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusCountsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusCounts',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'statusLabels',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'statusLabels',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'statusLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'statusLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'statusLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'statusLabels',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'statusLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'statusLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
          QAfterFilterCondition>
      statusLabelsElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'statusLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
          QAfterFilterCondition>
      statusLabelsElementMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'statusLabels',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'statusLabels',
        value: '',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'statusLabels',
        value: '',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusLabels',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusLabels',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusLabels',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusLabels',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusLabels',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> statusLabelsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'statusLabels',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> titleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> titleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> titleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> titleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> titleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> titleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'title',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> titleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> titleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
          QAfterFilterCondition>
      titleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
          QAfterFilterCondition>
      titleMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'title',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> titleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> titleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> totalPregnanciesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'totalPregnancies',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> totalPregnanciesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'totalPregnancies',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> totalPregnanciesEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalPregnancies',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> totalPregnanciesGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalPregnancies',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> totalPregnanciesLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalPregnancies',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> totalPregnanciesBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalPregnancies',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterColorsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'trimesterColors',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterColorsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'trimesterColors',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterColorsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'trimesterColors',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterColorsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'trimesterColors',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterColorsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'trimesterColors',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterColorsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'trimesterColors',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterColorsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterColors',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterColorsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterColors',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterColorsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterColors',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterColorsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterColors',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterColorsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterColors',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterColorsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterColors',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterCountsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'trimesterCounts',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterCountsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'trimesterCounts',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterCountsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'trimesterCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterCountsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'trimesterCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterCountsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'trimesterCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterCountsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'trimesterCounts',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterCountsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterCounts',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterCountsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterCounts',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterCountsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterCounts',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterCountsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterCounts',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterCountsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterCounts',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterCountsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterCounts',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'trimesterLabels',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'trimesterLabels',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'trimesterLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'trimesterLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'trimesterLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'trimesterLabels',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'trimesterLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'trimesterLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
          QAfterFilterCondition>
      trimesterLabelsElementContains(String value,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'trimesterLabels',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
          QAfterFilterCondition>
      trimesterLabelsElementMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'trimesterLabels',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'trimesterLabels',
        value: '',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'trimesterLabels',
        value: '',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterLabels',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterLabels',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterLabels',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterLabels',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterLabels',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> trimesterLabelsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'trimesterLabels',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar,
      QAfterFilterCondition> updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension PregnancyReportDataIsarQueryObject on QueryBuilder<
    PregnancyReportDataIsar, PregnancyReportDataIsar, QFilterCondition> {}

extension PregnancyReportDataIsarQueryLinks on QueryBuilder<
    PregnancyReportDataIsar, PregnancyReportDataIsar, QFilterCondition> {}

extension PregnancyReportDataIsarQuerySortBy
    on QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QSortBy> {
  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByAbortedPregnancies() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'abortedPregnancies', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByAbortedPregnanciesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'abortedPregnancies', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByAverageGestationLength() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageGestationLength', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByAverageGestationLengthDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageGestationLength', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByCalvingSurvivalRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calvingSurvivalRate', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByCalvingSurvivalRateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calvingSurvivalRate', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByCompletedPregnancies() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedPregnancies', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByCompletedPregnanciesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedPregnancies', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByCurrentPregnancies() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentPregnancies', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByCurrentPregnanciesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentPregnancies', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByFilterCriteria() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByFilterCriteriaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByGeneratedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByPregnancySuccessRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancySuccessRate', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByPregnancySuccessRateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancySuccessRate', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByReportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByReportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByTotalPregnancies() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalPregnancies', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByTotalPregnanciesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalPregnancies', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension PregnancyReportDataIsarQuerySortThenBy on QueryBuilder<
    PregnancyReportDataIsar, PregnancyReportDataIsar, QSortThenBy> {
  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByAbortedPregnancies() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'abortedPregnancies', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByAbortedPregnanciesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'abortedPregnancies', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByAverageGestationLength() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageGestationLength', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByAverageGestationLengthDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageGestationLength', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByCalvingSurvivalRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calvingSurvivalRate', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByCalvingSurvivalRateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calvingSurvivalRate', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByCompletedPregnancies() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedPregnancies', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByCompletedPregnanciesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'completedPregnancies', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByCurrentPregnancies() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentPregnancies', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByCurrentPregnanciesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'currentPregnancies', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByFilterCriteria() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByFilterCriteriaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByGeneratedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByPregnancySuccessRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancySuccessRate', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByPregnancySuccessRateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pregnancySuccessRate', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByReportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByReportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByTotalPregnancies() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalPregnancies', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByTotalPregnanciesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalPregnancies', Sort.desc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension PregnancyReportDataIsarQueryWhereDistinct on QueryBuilder<
    PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct> {
  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByAbortedPregnancies() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'abortedPregnancies');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByAverageGestationLength() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'averageGestationLength');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByCalvingCounts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'calvingCounts');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByCalvingDates() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'calvingDates');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByCalvingSurvivalRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'calvingSurvivalRate');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByCompletedPregnancies() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'completedPregnancies');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByCurrentPregnancies() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'currentPregnancies');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'endDate');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByFilterCriteria({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'filterCriteria',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'generatedAt');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByPregnancySuccessRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'pregnancySuccessRate');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByReportType({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'reportType', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'startDate');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByStatusColors() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'statusColors');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByStatusCounts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'statusCounts');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByStatusLabels() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'statusLabels');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByTitle({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'title', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByTotalPregnancies() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalPregnancies');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByTrimesterColors() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'trimesterColors');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByTrimesterCounts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'trimesterCounts');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByTrimesterLabels() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'trimesterLabels');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, PregnancyReportDataIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension PregnancyReportDataIsarQueryProperty on QueryBuilder<
    PregnancyReportDataIsar, PregnancyReportDataIsar, QQueryProperty> {
  QueryBuilder<PregnancyReportDataIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, int?, QQueryOperations>
      abortedPregnanciesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'abortedPregnancies');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, double?, QQueryOperations>
      averageGestationLengthProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'averageGestationLength');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, List<int>?, QQueryOperations>
      calvingCountsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'calvingCounts');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, List<DateTime>?, QQueryOperations>
      calvingDatesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'calvingDates');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, double?, QQueryOperations>
      calvingSurvivalRateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'calvingSurvivalRate');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, int?, QQueryOperations>
      completedPregnanciesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'completedPregnancies');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, int?, QQueryOperations>
      currentPregnanciesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'currentPregnancies');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, DateTime?, QQueryOperations>
      endDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'endDate');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, String?, QQueryOperations>
      filterCriteriaProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'filterCriteria');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, DateTime?, QQueryOperations>
      generatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'generatedAt');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, double?, QQueryOperations>
      pregnancySuccessRateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'pregnancySuccessRate');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, String?, QQueryOperations>
      reportTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reportType');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, DateTime?, QQueryOperations>
      startDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'startDate');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, List<int>?, QQueryOperations>
      statusColorsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'statusColors');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, List<int>?, QQueryOperations>
      statusCountsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'statusCounts');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, List<String>?, QQueryOperations>
      statusLabelsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'statusLabels');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, String?, QQueryOperations>
      titleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'title');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, int?, QQueryOperations>
      totalPregnanciesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalPregnancies');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, List<int>?, QQueryOperations>
      trimesterColorsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'trimesterColors');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, List<int>?, QQueryOperations>
      trimesterCountsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'trimesterCounts');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, List<String>?, QQueryOperations>
      trimesterLabelsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'trimesterLabels');
    });
  }

  QueryBuilder<PregnancyReportDataIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}
