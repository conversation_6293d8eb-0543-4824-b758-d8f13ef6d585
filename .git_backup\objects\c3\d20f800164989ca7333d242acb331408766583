import 'package:tubewell_water_billing_app/models/payment.dart';

class Bill {
  int id;
  int customerId;
  DateTime billDate;
  DateTime startTime;
  DateTime endTime;
  double durationHours;
  int durationHoursWhole;
  int durationMinutes;
  double hourlyRate;
  double amount;

  // Discount fields
  double? discountAmount;
  double? discountTime; // in minutes

  String? remarks;

  // Payment status fields
 