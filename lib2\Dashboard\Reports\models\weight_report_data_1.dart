import 'package:flutter/material.dart';
import '../../Weight/models/weight_record.dart';
import '../../Cattle/models/cattle.dart';
import 'report_data.dart';
import 'package:intl/intl.dart';
import 'chart_data.dart';

class WeightReportData extends ReportData {
  final List<WeightRecord> weightRecords;
  final List<Cattle> cattle;
  final String? category;

  WeightReportData({
    required this.weightRecords,
    required this.cattle,
    this.category,
    required DateTime startDate,
    required DateTime endDate,
  }) : super(startDate: startDate, endDate: endDate);

  @override
  String get reportTitle => 'Weight Report';

  @override
  List<DataColumn> get tableColumns => const [
        DataColumn(label: Text('Cattle ID')),
        DataColumn(label: Text('Category')),
        DataColumn(label: Text('Date')),
        DataColumn(label: Text('Weight (kg)')),
        DataColumn(label: Text('Change')),
        DataColumn(label: Text('Target')),
      ];

  @override
  List<DataRow> get tableRows => filteredRecords.map((record) {
        final cattleInfo = _getCattleInfo(record.cattleId);
        final previousWeight = _getPreviousWeight(record);
        final weightChange =
            previousWeight != null ? record.weight - previousWeight : 0.0;
        final target = _getWeightTarget(cattleInfo?.category ?? '');

        return DataRow(
          cells: [
            DataCell(Text(record.cattleId)),
            DataCell(Text(cattleInfo?.category ?? 'Unknown')),
            DataCell(Text(DateFormat('yyyy-MM-dd').format(record.date))),
            DataCell(Text(record.weight.toStringAsFixed(1))),
            DataCell(Text(weightChange != 0
                ? '${weightChange > 0 ? '+' : ''}${weightChange.toStringAsFixed(1)}'
                : '-')),
            DataCell(Text(target.toStringAsFixed(1))),
          ],
        );
      }).toList();

  List<WeightRecord> get filteredRecords {
    return weightRecords.where((record) {
      if (startDate != null && record.date.isBefore(startDate!)) return false;
      if (endDate != null && record.date.isAfter(endDate!)) return false;
      if (category != null) {
        final cattleInfo = _getCattleInfo(record.cattleId);
        if (cattleInfo?.category != category) return false;
      }
      return true;
    }).toList()
      ..sort((a, b) => b.date.compareTo(a.date));
  }

  Cattle? _getCattleInfo(String cattleId) {
    try {
      return cattle.firstWhere((c) => c.id == cattleId);
    } catch (e) {
      return null;
    }
  }

  double? _getPreviousWeight(WeightRecord current) {
    final records = weightRecords
        .where((r) => r.cattleId == current.cattleId && r.date.isBefore(current.date))
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));
    
    return records.isNotEmpty ? records.first.weight : null;
  }

  double _getWeightTarget(String category) {
    switch (category.toLowerCase()) {
      case 'dairy':
        return 600.0;
      case 'beef':
        return 700.0;
      case 'calf':
        return 150.0;
      case 'heifer':
        return 400.0;
      case 'bull':
        return 1000.0;
      default:
        return 500.0;
    }
  }

  @override
  Map<String, dynamic> get summaryData {
    if (filteredRecords.isEmpty) {
      return {
        'Average Weight': 0.0,
        'Total Records': 0,
        'Above Target': 0,
        'Below Target': 0,
        'Average Growth': 0.0,
      };
    }

    double totalWeight = 0;
    int aboveTarget = 0;
    int belowTarget = 0;
    double totalGrowth = 0;
    int growthCount = 0;

    for (var record in filteredRecords) {
      totalWeight += record.weight;

      final cattleInfo = _getCattleInfo(record.cattleId);
      final target = _getWeightTarget(cattleInfo?.category ?? '');

      if (record.weight >= target) {
        aboveTarget++;
      } else {
        belowTarget++;
      }

      final previousWeight = _getPreviousWeight(record);
      if (previousWeight != null) {
        totalGrowth += record.weight - previousWeight;
        growthCount++;
      }
    }

    return {
      'Average Weight': totalWeight / filteredRecords.length,
      'Total Records': filteredRecords.length,
      'Above Target': aboveTarget,
      'Below Target': belowTarget,
      'Average Growth': growthCount > 0 ? totalGrowth / growthCount : 0.0,
    };
  }

  Map<String, List<WeightRecord>> get weightTrends {
    final trends = <String, List<WeightRecord>>{};

    for (var record in filteredRecords) {
      if (!trends.containsKey(record.cattleId)) {
        trends[record.cattleId] = [];
      }
      trends[record.cattleId]!.add(record);
    }

    // Sort records by date for each cattle
    trends.forEach((_, records) {
      records.sort((a, b) => a.date.compareTo(b.date));
    });

    return trends;
  }

  Map<String, double> get categoryAverages {
    final averages = <String, double>{};
    final counts = <String, int>{};

    for (var record in filteredRecords) {
      final cattleInfo = _getCattleInfo(record.cattleId);
      final category = cattleInfo?.category ?? 'Unknown';

      averages[category] = (averages[category] ?? 0) + record.weight;
      counts[category] = (counts[category] ?? 0) + 1;
    }

    return averages.map((category, total) {
      return MapEntry(category, total / (counts[category] ?? 1));
    });
  }

  @override
  List<ChartData> get chartData {
    final sortedRecords = List<WeightRecord>.from(weightRecords)
      ..sort((a, b) {
        final aDate = a.date;
        final bDate = b.date;
        return aDate.compareTo(bDate);
      });

    return sortedRecords.map((record) {
      return ChartData(
        label: record.cattleId,
        value: record.weight,
        date: record.date,
        color: Colors.blue,
      );
    }).toList();
  }
}
