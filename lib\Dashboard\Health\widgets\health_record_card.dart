import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/health_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../constants/app_colors.dart';

class HealthRecordCard extends StatelessWidget {
  final HealthRecordIsar record;
  final CattleIsar? cattle;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final ValueChanged<bool?>? onSelectionChanged;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onNavigateToCattle;
  final bool compact;

  const HealthRecordCard({
    Key? key,
    required this.record,
    this.cattle,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onLongPress,
    this.onSelectionChanged,
    this.onEdit,
    this.onDelete,
    this.onNavigateToCattle,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final statusColor = _getStatusColor();
    final conditionColor = _getConditionColor();
    final dateColor = _getDateColor();
    final cattleColor = _getCattleColor();
    final veterinarianColor = _getVeterinarianColor();
    final notesColor = _getNotesColor();
    final statusIcon = _getStatusIcon();
    final cattleName = cattle?.name ?? 'Unknown Cattle';
    final tagId = cattle?.tagId ?? '';
    final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;
    final formattedDate = DateFormat('MMM dd, yyyy').format(record.date ?? DateTime.now());
    final status = record.healthStatus ?? 'Unknown';
    final condition = record.condition ?? 'General Check';
    final veterinarian = record.veterinarian ?? 'Unknown Vet';

    return Card(
      margin: compact
          ? const EdgeInsets.symmetric(horizontal: 8, vertical: 4)
          : const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shadowColor: Colors.black26,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(
          color: Color(0xFFEEEEEE),
          width: 1,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: isSelectionMode
            ? () => onSelectionChanged?.call(!isSelected)
            : onTap,
        onLongPress: onLongPress,
        child: Container(
          padding: compact
              ? const EdgeInsets.all(12)
              : const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Row 1: Date + Status + Menu/Checkbox
              IntrinsicHeight(
                child: Row(
                  children: [
                    if (isSelectionMode)
                      SizedBox(
                        width: 56,
                        child: Checkbox(
                          value: isSelected,
                          onChanged: onSelectionChanged,
                          activeColor: AppColors.primary,
                        ),
                      ),
                    // Status indicator - 4px vertical stripe
                    Container(
                      width: 4,
                      height: 40,
                      decoration: BoxDecoration(
                        color: statusColor,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Date with icon - flexible width
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            Icons.calendar_today_outlined,
                            size: 20,
                            color: dateColor,
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              formattedDate,
                              style: TextStyle(
                                color: dateColor,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Status - right aligned with minimum width
                    Container(
                      constraints: const BoxConstraints(minWidth: 80),
                      child: Text(
                        status,
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.right,
                        maxLines: 1,
                      ),
                    ),
                    if (!isSelectionMode && (onEdit != null || onDelete != null))
                      SizedBox(
                        width: 24,
                        child: PopupMenuButton<String>(
                          icon: Icon(
                            Icons.more_vert,
                            color: const Color(0xFF303F9F), // Indigo shade
                          ),
                          onSelected: (String choice) {
                            if (choice == 'Edit' && onEdit != null) {
                              onEdit!();
                            } else if (choice == 'Delete' && onDelete != null) {
                              onDelete!();
                            }
                          },
                          itemBuilder: (BuildContext context) => [
                            if (onEdit != null)
                              const PopupMenuItem(
                                value: 'Edit',
                                child: Row(
                                  children: [
                                    Icon(Icons.edit_outlined),
                                    SizedBox(width: 8),
                                    Text('Edit'),
                                  ],
                                ),
                              ),
                            if (onDelete != null)
                              const PopupMenuItem(
                                value: 'Delete',
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.delete_outline,
                                      color: Colors.red,
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'Delete',
                                      style: TextStyle(color: Colors.red),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
              // Row 2: Cattle + Condition
              Row(
                children: [
                  if (isSelectionMode)
                    const SizedBox(width: 56),
                  const SizedBox(width: 16),
                  // Cattle with icon - flexible width
                  Expanded(
                    child: Row(
                      children: [
                        Icon(
                          Icons.pets,
                          color: cattleColor,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            displayName,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: cattleColor,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Condition with icon - fixed width
                  Container(
                    constraints: const BoxConstraints(minWidth: 100),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.medical_information,
                          color: conditionColor,
                          size: 18,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          condition,
                          style: TextStyle(
                            color: conditionColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  if (!isSelectionMode && (onEdit != null || onDelete != null))
                    const SizedBox(width: 24),
                ],
              ),
              const SizedBox(height: 8),
              // Row 3: Veterinarian + Temperature
              Row(
                children: [
                  if (isSelectionMode)
                    const SizedBox(width: 56),
                  const SizedBox(width: 16),
                  // Veterinarian with icon - flexible width
                  Expanded(
                    child: Row(
                      children: [
                        Icon(
                          Icons.person,
                          color: veterinarianColor,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            veterinarian,
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                              color: veterinarianColor,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Temperature - right aligned
                  if (record.temperature != null)
                    Container(
                      constraints: const BoxConstraints(minWidth: 80),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.thermostat,
                            color: Colors.grey[600],
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${record.temperature!.toStringAsFixed(1)}°F',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (!isSelectionMode && (onEdit != null || onDelete != null))
                    const SizedBox(width: 24),
                ],
              ),

              // Notes (if available)
              if (record.notes != null && record.notes!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    if (isSelectionMode)
                      const SizedBox(width: 56),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        record.notes!,
                        style: TextStyle(
                          fontSize: 14,
                          color: notesColor,
                          fontStyle: FontStyle.italic,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (!isSelectionMode && (onEdit != null || onDelete != null))
                      const SizedBox(width: 24),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (record.healthStatus?.toLowerCase()) {
      case 'healthy':
      case 'good':
        return const Color(0xFF2E7D32); // Dark Green
      case 'sick':
      case 'poor':
        return const Color(0xFFD32F2F); // Dark Red
      case 'recovering':
      case 'fair':
        return const Color(0xFFE65100); // Dark Orange
      case 'critical':
        return const Color(0xFF7B1FA2); // Purple
      default:
        return const Color(0xFF616161); // Dark Grey
    }
  }

  Color _getConditionColor() {
    return const Color(0xFF1565C0); // Dark Blue
  }

  IconData _getStatusIcon() {
    switch (record.healthStatus?.toLowerCase()) {
      case 'healthy':
      case 'good':
        return Icons.check_circle;
      case 'sick':
      case 'poor':
        return Icons.warning;
      case 'recovering':
      case 'fair':
        return Icons.healing;
      case 'critical':
        return Icons.emergency;
      default:
        return Icons.help;
    }
  }

  Color _getDateColor() {
    return const Color(0xFF303F9F); // Indigo
  }

  Color _getCattleColor() {
    return const Color(0xFF6A1B9A); // Purple
  }

  Color _getVeterinarianColor() {
    return const Color(0xFF00796B); // Teal
  }

  Color _getNotesColor() {
    return const Color(0xFF757575); // Medium Grey
  }
}
