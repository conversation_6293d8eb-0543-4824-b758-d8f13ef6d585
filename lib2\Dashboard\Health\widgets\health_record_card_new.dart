import 'package:flutter/material.dart';
import '../models/health_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../widgets/generic_record_card.dart';

/// Health Record Card using the generic reusable card widget
class HealthRecordCardNew extends StatelessWidget {
  final HealthRecordIsar record;
  final CattleIsar? cattle;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final ValueChanged<bool?>? onSelectionChanged;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool compact;

  const HealthRecordCardNew({
    Key? key,
    required this.record,
    this.cattle,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onLongPress,
    this.onSelectionChanged,
    this.onEdit,
    this.onDelete,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final cattleName = cattle?.name ?? 'Unknown Cattle';
    final tagId = cattle?.tagId ?? '';
    final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;

    return GenericRecordCard.health(
      recordDate: record.date,
      cattleName: displayName,
      condition: record.condition,
      healthStatus: record.healthStatus,
      treatment: record.treatment,
      veterinarian: record.veterinarian,
      treatmentCost: record.treatmentCost,
      notes: record.notes,
      isSelected: isSelected,
      isSelectionMode: isSelectionMode,
      onTap: onTap,
      onLongPress: onLongPress,
      onSelectionChanged: onSelectionChanged,
      onEdit: onEdit,
      onDelete: onDelete,
      compact: compact,
    );
  }
}
