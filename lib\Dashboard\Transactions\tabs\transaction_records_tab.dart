import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'dart:async';
import '../models/category_isar.dart';
import '../models/transaction_isar.dart';
import '../services/transactions_handler.dart';
import '../dialogs/transaction_form_dialog.dart';
import '../widgets/transaction_list_item.dart';
import '../../widgets/history_record_card.dart';
// New component imports
import '../../widgets/filter_widget.dart';
import '../../widgets/sort_widget.dart';
import '../../widgets/search_widget.dart';
import '../../widgets/date_range_filter_widget.dart';
import '../../widgets/filter_status_bar.dart';
import '../../../utils/message_utils.dart';
import '../../../services/streams/stream_service.dart';
import '../../../constants/app_colors.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import '../../widgets/index.dart';


class TransactionRecordsTab extends StatefulWidget {
  final Function(bool isSelectionMode, int selectedCount)? onSelectionModeChanged;

  const TransactionRecordsTab({
    Key? key,
    this.onSelectionModeChanged,
  }) : super(key: key);

  @override
  State<TransactionRecordsTab> createState() => TransactionRecordsTabState();
}

class TransactionRecordsTabState extends State<TransactionRecordsTab> {
  final TransactionsHandler _transactionsHandler =
      GetIt.instance<TransactionsHandler>();
  final FarmSetupHandler _farmSetupHandler = GetIt.instance<FarmSetupHandler>();
  bool _isLoading = true;
  List<CategoryIsar> _categories = [];
  List<TransactionIsar> _transactions = [];
  List<TransactionIsar> _filteredTransactions = [];

  // Currency settings
  String _currencySymbol = '\$';
  bool _symbolBeforeAmount = true;

  // Filter states
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedType;
  String? _selectedCategory;
  String? _selectedPaymentMethod;
  String? _amountRange;
  String? _sortBy;
  bool _sortAscending = true; // Default to ascending (more intuitive)
  String _searchQuery = '';

  // Selection mode state
  bool _isSelectionMode = false;
  final Set<String> _selectedTransactions = <String>{};

  // Stream subscription for real-time updates
  StreamSubscription<Map<String, dynamic>>? _transactionStreamSubscription;

  // Cache for performance optimization
  DateTime? _lastDataLoadTime;
  static const Duration _cacheValidDuration = Duration(minutes: 5);

  @override
  void initState() {
    super.initState();
    _subscribeToTransactionUpdates();
    // Load data asynchronously without blocking UI
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeScreen();
    });
  }

  Future<void> _initializeScreen() async {
    // Load currency settings and data in parallel for faster loading
    await Future.wait([
      _loadCurrencySettings(),
      _loadData(),
    ]);
  }

  @override
  void dispose() {
    _transactionStreamSubscription?.cancel();
    super.dispose();
  }

  /// Subscribe to transaction updates for real-time UI updates
  void _subscribeToTransactionUpdates() {
    final streamService = GetIt.instance<StreamService>();

    // Subscribe to transaction updates
    _transactionStreamSubscription = streamService.transactionStream.listen((event) {
      debugPrint('Transaction stream event: ${event['action']}');
      if (mounted) {
        _refreshData(); // Refresh data when transactions change
      }
    }, onError: (error) {
      debugPrint('Error in transaction stream: $error');
    });
  }

  Future<void> _loadCurrencySettings() async {
    try {
      final currencySettings = await _farmSetupHandler.getCurrencySettings();
      if (mounted) {
        setState(() {
          _currencySymbol = currencySettings.currencySymbol;
          _symbolBeforeAmount = currencySettings.symbolBeforeAmount;
        });
      }
    } catch (e) {
      debugPrint('Error loading currency settings: $e');
      // Use default values if loading fails
      if (mounted) {
        setState(() {
          _currencySymbol = '\$';
          _symbolBeforeAmount = true;
        });
      }
    }
  }

  String _formatCurrency(double amount) {
    final formattedAmount = amount.toStringAsFixed(2);
    return _symbolBeforeAmount
        ? _currencySymbol + formattedAmount
        : formattedAmount + _currencySymbol;
  }

  Future<void> _loadData() async {
    try {
      // Check if we have recent cached data
      if (_lastDataLoadTime != null &&
          DateTime.now().difference(_lastDataLoadTime!) < _cacheValidDuration &&
          _transactions.isNotEmpty) {
        // Apply filters to existing data
        Future.microtask(() => _applyFilters());
        return; // Use cached data
      }

      if (mounted) setState(() => _isLoading = true);

      // Load categories and transactions in parallel for faster loading
      final futures = await Future.wait([
        _transactionsHandler.getAllCategories(),
        _transactionsHandler.getAllTransactions(),
      ]);

      final categories = futures[0] as List<CategoryIsar>;
      final transactions = futures[1] as List<TransactionIsar>;

      if (!mounted) return;

      setState(() {
        _categories = categories;
        _transactions = transactions;
        _isLoading = false;
      });

      // Apply filters in background to avoid blocking UI
      Future.microtask(() => _applyFilters());
      _lastDataLoadTime = DateTime.now();
    } catch (e) {
      debugPrint('Error loading data: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        MessageUtils.showError(context, 'Error loading data');
      }
    }
  }

  Future<void> _refreshData() async {
    if (!mounted) return;
    try {
      await _loadData();
    } catch (e) {
      debugPrint('Error refreshing transaction data: $e');
      if (mounted) {
        MessageUtils.showError(context, 'Error refreshing data: $e');
      }
    }
  }

  void _applyFilters() {
    setState(() {
      // Start with all transactions
      _filteredTransactions = List.from(_transactions);

      // Apply Date Range Filter
      if (_startDate != null && _endDate != null) {
        _filteredTransactions = _filteredTransactions.where((transaction) {
          return (transaction.date.isAtSameMomentAs(_startDate!) ||
                  transaction.date.isAfter(_startDate!)) &&
              (transaction.date.isAtSameMomentAs(_endDate!) ||
                  transaction.date.isBefore(_endDate!));
        }).toList();
      }

      // Apply Transaction Type Filter
      if (_selectedType != null && _selectedType != 'All Types') {
        _filteredTransactions = _filteredTransactions.where((transaction) {
          return transaction.categoryType == _selectedType;
        }).toList();
      }

      // Apply Category Filter
      if (_selectedCategory != null && _selectedCategory != 'All Categories') {
        _filteredTransactions = _filteredTransactions.where((transaction) {
          return transaction.category == _selectedCategory;
        }).toList();
      }

      // Apply Payment Method Filter
      if (_selectedPaymentMethod != null &&
          _selectedPaymentMethod != 'All Methods') {
        _filteredTransactions = _filteredTransactions.where((transaction) {
          return transaction.paymentMethod == _selectedPaymentMethod;
        }).toList();
      }

      // Apply Amount Range Filter
      if (_amountRange != null && _amountRange != 'All Amounts') {
        _filteredTransactions = _filteredTransactions.where((transaction) {
          switch (_amountRange) {
            case 'Under 1000':
              return transaction.amount < 1000;
            case '1000-5000':
              return transaction.amount >= 1000 && transaction.amount <= 5000;
            case '5000-10000':
              return transaction.amount > 5000 && transaction.amount <= 10000;
            case 'Over 10000':
              return transaction.amount > 10000;
            default:
              return true;
          }
        }).toList();
      }

      // Apply Search Query
      if (_searchQuery.isNotEmpty) {
        final lowercaseQuery = _searchQuery.toLowerCase();
        _filteredTransactions = _filteredTransactions.where((transaction) {
          return transaction.categoryType.toLowerCase().contains(lowercaseQuery) ||
              transaction.category.toLowerCase().contains(lowercaseQuery) ||
              transaction.amount.toString().contains(lowercaseQuery) ||
              transaction.description.toLowerCase().contains(lowercaseQuery) ||
              transaction.paymentMethod.toLowerCase().contains(lowercaseQuery);
        }).toList();
      }

      // Apply Sorting
      if (_sortBy != null) {
        switch (_sortBy) {
          case 'Date':
            _filteredTransactions.sort((a, b) {
              // Primary sort: transaction date
              final dateComparison = a.date.compareTo(b.date);

              // Secondary sort: if dates are the same, sort by creation time
              if (dateComparison == 0) {
                return a.createdAt.compareTo(b.createdAt);
              }
              return dateComparison;
            });
            break;
          case 'Amount':
            _filteredTransactions.sort((a, b) => a.amount.compareTo(b.amount));
            break;
          case 'Category':
            _filteredTransactions.sort((a, b) => a.category.compareTo(b.category));
            break;
        }

        // Reverse if not ascending
        if (!_sortAscending) {
          _filteredTransactions = _filteredTransactions.reversed.toList();
        }
      } else {
        // Default sorting by date in descending order if no specific sort is selected
        _filteredTransactions.sort((a, b) {
          // Primary sort: transaction date (newest first)
          final dateComparison = b.date.compareTo(a.date);

          // Secondary sort: if dates are the same, sort by creation time (newest first)
          if (dateComparison == 0) {
            return b.createdAt.compareTo(a.createdAt);
          }
          return dateComparison;
        });
      }
    });
  }

  Future<void> _showAddTransactionDialog([TransactionIsar? transaction]) async {
    await showDialog<TransactionIsar>(
      context: context,
      builder: (context) => TransactionFormDialog(
        categories: _categories,
        transaction: transaction,
        onSave: (newTransaction) async {
          try {
            if (transaction != null) {
              await _transactionsHandler.updateTransaction(newTransaction);
            } else {
              await _transactionsHandler.addTransaction(newTransaction);
            }
            await _loadData();
          } catch (e) {
            debugPrint('Error ${transaction != null ? 'updating' : 'adding'} transaction: $e');
            if (!mounted) return;
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (!mounted) return;
              MessageUtils.showError(context,
                'Error ${transaction != null ? 'updating' : 'adding'} transaction');
            });
          }
        },
      ),
    );
  }

  Future<void> _deleteTransaction(TransactionIsar transaction) async {
    try {
      await _transactionsHandler.deleteTransaction(transaction.transactionId);
      if (mounted) {
        MessageUtils.showSuccess(context, 'Transaction deleted successfully');
      }
      await _loadData();
    } catch (e) {
      debugPrint('Error deleting transaction: $e');
      if (mounted) {
        MessageUtils.showError(context, 'Error deleting transaction');
      }
    }
  }

  void _confirmDeleteTransaction(TransactionIsar transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: Text(
          'Are you sure you want to delete this ${transaction.categoryType.toLowerCase()} transaction for ${_formatCurrency(transaction.amount)}?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteTransaction(transaction);
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _clearFilters() {
    setState(() {
      _startDate = null;
      _endDate = null;
      _selectedType = null;
      _selectedCategory = null;
      _selectedPaymentMethod = null;
      _amountRange = null;
      _sortBy = null;
      _sortAscending = true; // Default to ascending (more intuitive)
      _searchQuery = '';
    });
    _applyFilters();
  }

  void _onFilterChanged(String key, String? value) {
    setState(() {
      switch (key) {
        case 'type':
          _selectedType = value;
          break;
        case 'category':
          _selectedCategory = value;
          break;
        case 'payment':
          _selectedPaymentMethod = value;
          break;
        case 'amount':
          _amountRange = value;
          break;
      }
    });
    Future.microtask(() => _applyFilters());
  }

  // Public methods for parent screen to control selection
  void enableSelectionMode() {
    setState(() {
      _isSelectionMode = true;
    });
    _notifySelectionModeChanged();
  }

  void deleteSelectedTransactions() {
    if (_selectedTransactions.isNotEmpty) {
      _confirmDeleteSelectedTransactions();
    }
  }

  void cancelSelection() {
    setState(() {
      _isSelectionMode = false;
      _selectedTransactions.clear();
    });
    _notifySelectionModeChanged();
  }

  void selectAll() {
    setState(() {
      if (_selectedTransactions.length == _filteredTransactions.length) {
        _selectedTransactions.clear();
      } else {
        _selectedTransactions.addAll(
          _filteredTransactions.map((t) => t.transactionId),
        );
      }
    });
    _notifySelectionModeChanged();
  }

  bool areAllSelected() {
    return _filteredTransactions.isNotEmpty &&
           _selectedTransactions.length == _filteredTransactions.length;
  }

  void _notifySelectionModeChanged() {
    widget.onSelectionModeChanged?.call(_isSelectionMode, _selectedTransactions.length);
  }

  bool _hasActiveFilters() {
    // Check if any filters are applied
    bool hasFilters = _selectedType != null ||
                     _selectedCategory != null ||
                     _selectedPaymentMethod != null ||
                     _amountRange != null;

    // Check if sort is applied
    bool hasSort = _sortBy != null;

    // Check if search is applied
    bool hasSearch = _searchQuery.isNotEmpty;

    // Check if date range is different from default (last 30 days)
    final defaultStartDate = DateTime.now().subtract(const Duration(days: 30));
    final defaultEndDate = DateTime.now();
    bool hasDateFilter = _startDate != null && _endDate != null &&
                        (!_isSameDay(_startDate!, defaultStartDate) ||
                         !_isSameDay(_endDate!, defaultEndDate));

    return hasFilters || hasSort || hasSearch || hasDateFilter;
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  List<String> _getActiveFiltersList() {
    List<String> activeFilters = [];

    if (_selectedType != null) {
      activeFilters.add('Type: $_selectedType');
    }
    if (_selectedCategory != null) {
      activeFilters.add('Category: $_selectedCategory');
    }
    if (_selectedPaymentMethod != null) {
      activeFilters.add('Payment: $_selectedPaymentMethod');
    }
    if (_amountRange != null) {
      activeFilters.add('Amount: $_amountRange');
    }
    if (_startDate != null && _endDate != null) {
      activeFilters.add('Date Range: ${_startDate!.day}/${_startDate!.month} - ${_endDate!.day}/${_endDate!.month}');
    }
    if (_sortBy != null) {
      activeFilters.add('Sort: $_sortBy (${_sortAscending ? 'Asc' : 'Desc'})');
    }
    if (_searchQuery.isNotEmpty) {
      activeFilters.add('Search: "$_searchQuery"');
    }

    return activeFilters;
  }



  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading transactions...'),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      body: Column(
        children: [
          // NEW LAYOUT: Filter + Date + Sort in Row 1, Search in Row 2
          Column(
            children: [
              // Row 1: Filter + Date + Sort
              Row(
                children: [
                  // Transaction Filters (Type, Category, Payment, Amount)
                  Expanded(
                    child: FilterWidget(
                      filterFields: [
                        FilterField(
                          key: 'type',
                          label: 'Transaction Type',
                          icon: Icons.swap_horiz,
                          iconColor: _selectedType == 'Income' ? Colors.green :
                                    _selectedType == 'Expense' ? Colors.red : Colors.purple,
                          options: ModuleSpecificFilterHelper.generateTransactionTypeOptions(),
                          currentValue: _selectedType,
                        ),
                        FilterField(
                          key: 'category',
                          label: 'Category',
                          icon: Icons.category,
                          iconColor: Colors.blue,  // Multi-color rule: Blue for category
                          options: _categories.map((category) => FilterOption(
                            value: category.name,
                            label: category.name,
                          )).toList(),
                          currentValue: _selectedCategory,
                        ),
                        FilterField(
                          key: 'payment',
                          label: 'Payment Method',
                          icon: Icons.payment,
                          iconColor: Colors.teal,  // Multi-color rule: Teal for payment method
                          options: ModuleSpecificFilterHelper.generatePaymentMethodOptions(),
                          currentValue: _selectedPaymentMethod,
                        ),
                        FilterField(
                          key: 'amount',
                          label: 'Amount Range',
                          icon: Icons.attach_money,
                          iconColor: Colors.purple,  // Multi-color rule: Purple for amount (no repetition)
                          options: const [
                            FilterOption(value: 'Under 1000', label: 'Under 1000'),
                            FilterOption(value: '1000-5000', label: '1000-5000'),
                            FilterOption(value: '5000-10000', label: '5000-10000'),
                            FilterOption(value: 'Over 10000', label: 'Over 10000'),
                          ],
                          currentValue: _amountRange,
                        ),
                      ],
                      onFilterChanged: _onFilterChanged,
                      onClearFilters: _clearFilters,
                      onApplyFilters: _applyFilters,
                      themeColor: Colors.purple,  // Use purple for consistency across all modules
                      dialogTitle: 'Filter Transactions',
                      compact: true,
                      totalCount: _transactions.length,
                      filteredCount: _filteredTransactions.length,
                      showFilterCount: true,
                      buttonHeight: 44, // Consistent button height
                    ),
                  ),
                  const SizedBox(width: 8),

                  // Date Range Filter
                  Expanded(
                    child: DateRangeFilterWidget(
                      startDate: _startDate,  // Pass null when no filter active
                      endDate: _endDate,      // Pass null when no filter active
                      onStartDateChanged: (date) {
                        setState(() => _startDate = date);
                        Future.microtask(() => _applyFilters());
                      },
                      onEndDateChanged: (date) {
                        setState(() => _endDate = date);
                        Future.microtask(() => _applyFilters());
                      },
                      onClearFilter: () {
                        setState(() {
                          _startDate = null;
                          _endDate = null;
                        });
                        _applyFilters();
                      },
                      onFiltersChanged: () => _applyFilters(),
                      themeColor: Colors.purple,  // Use purple for consistency across all modules
                      activeBackgroundOpacity: 0.7,  // 70% opacity for active state
                      compact: true,
                      buttonHeight: 44, // Consistent button height
                    ),
                  ),
                  const SizedBox(width: 8),

                  // Sort Widget
                  Expanded(
                    child: SortWidget(
                      sortFields: SortHelper.getTransactionSortFields(),
                      sortBy: _sortBy,
                      sortAscending: _sortAscending,
                      onSortByChanged: (sortBy) {
                        setState(() => _sortBy = sortBy);
                        Future.microtask(() => _applyFilters());
                      },
                      onSortAscendingChanged: (ascending) {
                        setState(() => _sortAscending = ascending);
                        Future.microtask(() => _applyFilters());
                      },
                      onApplySort: _applyFilters,
                      themeColor: Colors.purple,
                      compact: true,
                      showSortIndicator: true,
                      buttonHeight: 44, // Consistent button height
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Filter Status Bar (Clear Filters + Count)
              FilterStatusBar.transaction(
                filterStates: {
                  'type': _selectedType,
                  'category': _selectedCategory,
                  'paymentMethod': _selectedPaymentMethod,
                  'amountRange': _amountRange,
                  'startDate': _startDate,
                  'endDate': _endDate,
                  'sortBy': _sortBy,
                  'sortAscending': _sortAscending,  // Add sort direction
                  'searchQuery': _searchQuery,
                },
                totalCount: _transactions.length,
                filteredCount: _filteredTransactions.length,
                onClearFilters: _clearFilters,
                // No default dates - any date selection should be considered active
              ),

              // Row 2: Search
              SearchWidget(
                searchQuery: _searchQuery,
                onSearchChanged: (query) {
                  setState(() => _searchQuery = query);
                  Future.microtask(() => _applyFilters());
                },
                config: SearchConfig.transactions,
                themeColor: Colors.purple,  // Use purple for consistency across all modules
                resultCount: _filteredTransactions.length,
                showResultCount: true,
                height: 44, // Consistent height with buttons
              ),
            ],
          ),
          Expanded(
            child: _filteredTransactions.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.only(top: 8, bottom: 88),
                    itemCount: _filteredTransactions.length,
                    // Add caching for better performance
                    cacheExtent: 1000,
                    itemBuilder: (context, index) {
                      final transaction = _filteredTransactions[index];
                      final category = _categories.firstWhere(
                        (c) => c.name == transaction.category,
                        orElse: () => CategoryIsar()..name = transaction.category,
                      );

                      // Helper functions for icons
                      IconData getCategoryIcon() {
                        // Use the stored icon if available
                        if (transaction.iconCodePoint != null) {
                          return IconData(
                            transaction.iconCodePoint!,
                            fontFamily: transaction.iconFontFamily,
                          );
                        }

                        // Check if we have a category with stored icon
                        if (category?.iconCodePoint != null) {
                          return IconData(
                            category!.iconCodePoint!,
                            fontFamily: category!.iconFontFamily,
                          );
                        }

                        // Fallback icons based on category name and type
                        final lowerCategory = transaction.category.toLowerCase();
                        if (transaction.categoryType == 'Income') {
                          if (lowerCategory.contains('sale')) return Icons.shopping_cart;
                          if (lowerCategory.contains('rent')) return Icons.home;
                          if (lowerCategory.contains('investment')) return Icons.trending_up;
                          if (lowerCategory.contains('other')) return Icons.attach_money;
                          return Icons.arrow_circle_up;
                        } else {
                          if (lowerCategory.contains('feed')) return Icons.grass;
                          if (lowerCategory.contains('medicine')) return Icons.medical_services;
                          if (lowerCategory.contains('equipment')) return Icons.build;
                          if (lowerCategory.contains('labor')) return Icons.engineering;
                          if (lowerCategory.contains('transport')) return Icons.local_shipping;
                          if (lowerCategory.contains('utility')) return Icons.power;
                          if (lowerCategory.contains('other')) return Icons.money_off;
                          return Icons.arrow_circle_down;
                        }
                      }

                      IconData getPaymentMethodIcon() {
                        switch (transaction.paymentMethod.toLowerCase()) {
                          case 'cash':
                            return Icons.money;
                          case 'card':
                          case 'credit card':
                            return Icons.credit_card;
                          case 'bank transfer':
                          case 'transfer':
                            return Icons.account_balance;
                          case 'check':
                          case 'cheque':
                            return Icons.receipt;
                          case 'mobile payment':
                          case 'mobile':
                            return Icons.phone_android;
                          case 'other':
                            return Icons.payment;
                          default:
                            return Icons.help_outline;
                        }
                      }

                      // Use HistoryRecordCard.transaction() directly
                      return HistoryRecordCard.transaction(
                        transactionDate: transaction.date,
                        category: transaction.category,
                        paymentMethod: transaction.paymentMethod,
                        amount: transaction.amount,
                        categoryType: transaction.categoryType,
                        description: transaction.description.isNotEmpty ? transaction.description : null,
                        categoryIcon: getCategoryIcon(),
                        paymentIcon: getPaymentMethodIcon(),
                        currencySymbol: _currencySymbol,
                        symbolBeforeAmount: _symbolBeforeAmount,
                        isSelected: _selectedTransactions.contains(transaction.transactionId),
                        isSelectionMode: _isSelectionMode,
                        onTap: _isSelectionMode
                            ? () {
                                setState(() {
                                  if (_selectedTransactions.contains(transaction.transactionId)) {
                                    _selectedTransactions.remove(transaction.transactionId);
                                  } else {
                                    _selectedTransactions.add(transaction.transactionId);
                                  }
                                });
                                _notifySelectionModeChanged();
                              }
                            : () => _showAddTransactionDialog(transaction),
                        onLongPress: () {
                          if (!_isSelectionMode) {
                            setState(() {
                              _isSelectionMode = true;
                              _selectedTransactions.add(transaction.transactionId);
                            });
                            _notifySelectionModeChanged();
                          }
                        },
                        onSelectionChanged: (selected) {
                          setState(() {
                            if (selected == true) {
                              _selectedTransactions.add(transaction.transactionId);
                            } else {
                              _selectedTransactions.remove(transaction.transactionId);
                            }
                          });
                          _notifySelectionModeChanged();
                        },
                        onEdit: () => _showAddTransactionDialog(transaction),
                        onDelete: () => _confirmDeleteTransaction(transaction),
                        compact: false,
                      );
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddTransactionDialog,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 300),
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 32),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.withAlpha(76)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: EmptyState.transaction(
        hasData: _transactions.isNotEmpty,
        action: _transactions.isEmpty
            ? EmptyState.createActionButton(
                onPressed: () => _showAddTransactionDialog(),
                icon: Icons.add,
                label: 'Add First Transaction',
                backgroundColor: Colors.blue,
              )
            : EmptyState.createActionButton(
                onPressed: () => _clearFilters(),
                icon: Icons.clear_all,
                label: 'Clear Filters',
                backgroundColor: Colors.blue,
              ),
      ),
    );
  }

  void _confirmDeleteSelectedTransactions() {
    final count = _selectedTransactions.length;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transactions'),
        content: Text(
          'Are you sure you want to delete $count selected transaction${count > 1 ? 's' : ''}?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteSelectedTransactions();
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteSelectedTransactions() async {
    try {
      for (final transactionId in _selectedTransactions) {
        await _transactionsHandler.deleteTransaction(transactionId);
      }

      if (mounted) {
        MessageUtils.showSuccess(
          context,
          '${_selectedTransactions.length} transaction${_selectedTransactions.length > 1 ? 's' : ''} deleted successfully'
        );
        setState(() {
          _isSelectionMode = false;
          _selectedTransactions.clear();
        });
        _notifySelectionModeChanged();
      }

      await _loadData();
    } catch (e) {
      debugPrint('Error deleting selected transactions: $e');
      if (mounted) {
        FinancialMessageUtils.showError(context, 'Error deleting transactions');
      }
    }
  }
}
