import 'package:flutter/material.dart';


import '../models/health_record_isar.dart';
import '../models/treatment_record_isar.dart';
import '../models/vaccination_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';

class HealthInsightsTab extends StatefulWidget {
  final List<HealthRecordIsar> healthRecords;
  final List<TreatmentRecordIsar> treatmentRecords;
  final List<VaccinationRecordIsar> vaccinationRecords;
  final Map<String, CattleIsar> cattleMap;

  const HealthInsightsTab({
    Key? key,
    required this.healthRecords,
    required this.treatmentRecords,
    required this.vaccinationRecords,
    required this.cattleMap,
  }) : super(key: key);

  @override
  State<HealthInsightsTab> createState() => _HealthInsightsTabState();
}

class _HealthInsightsTabState extends State<HealthInsightsTab> {
  // Color schemes for different insight categories - following user preferences
  final List<Color> _healthColors = [
    const Color(0xFFE74C3C), // Red
    const Color(0xFF27AE60), // Green
    const Color(0xFF8E44AD), // Purple
  ];

  final List<Color> _treatmentColors = [
    const Color(0xFF1976D2), // Blue
    const Color(0xFF00796B), // Dark Teal
    const Color(0xFF7B1FA2), // Deep Purple
  ];

  final List<Color> _vaccinationColors = [
    const Color(0xFF2E7D32), // Dark Green
    const Color(0xFF388E3C), // Green
    const Color(0xFF16A085), // Teal
  ];

  final List<Color> _recommendationColors = [
    const Color(0xFF1976D2), // Blue
    const Color(0xFF27AE60), // Green
    const Color(0xFF8E44AD), // Purple
    const Color(0xFF16A085), // Teal
  ];

  double _getResponsivePadding() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 12.0;
    if (screenWidth < 1200) return 16.0;
    return 20.0;
  }

  double _getResponsiveSpacing() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 16.0;
    if (screenWidth < 1200) return 20.0;
    return 24.0;
  }

  double _getResponsiveFontSize() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 12.0;
    if (screenWidth < 1200) return 14.0;
    return 16.0;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(_getResponsivePadding()),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHealthStatusInsights(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildTreatmentInsights(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildVaccinationInsights(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildPredictiveAnalytics(),
          SizedBox(height: _getResponsiveSpacing()),
          _buildRecommendations(),
        ],
      ),
    );
  }

  Widget _buildHealthStatusInsights() {
    final insights = _calculateHealthStatusInsights();
    
    return _buildInsightSection(
      'Health Status Insights',
      Icons.health_and_safety,
      _healthColors[0],
      [
        _buildInsightCard(
          'Overall Health Score',
          insights['healthScore'] ?? 'No Data',
          'Average health status across all cattle',
          _healthColors[0],
        ),
        _buildInsightCard(
          'At-Risk Cattle',
          insights['atRiskCount'] ?? '0',
          'Number of cattle requiring immediate attention',
          _healthColors[1],
        ),
        _buildInsightCard(
          'Health Trend',
          insights['healthTrend'] ?? 'Stable',
          'Recent health trend compared to previous period',
          _healthColors[2],
        ),
      ],
    );
  }

  Widget _buildTreatmentInsights() {
    final insights = _calculateTreatmentInsights();
    
    return _buildInsightSection(
      'Treatment Analysis',
      Icons.medication,
      _treatmentColors[0],
      [
        _buildInsightCard(
          'Active Treatments',
          insights['activeCount'] ?? '0',
          'Number of ongoing treatments',
          _treatmentColors[0],
        ),
        _buildInsightCard(
          'Success Rate',
          insights['successRate'] ?? 'No Data',
          'Treatment completion and success rate',
          _treatmentColors[1],
        ),
        _buildInsightCard(
          'Most Common Issue',
          insights['commonIssue'] ?? 'No Data',
          'Most frequently treated condition',
          _treatmentColors[2],
        ),
      ],
    );
  }

  Widget _buildVaccinationInsights() {
    final insights = _calculateVaccinationInsights();
    
    return _buildInsightSection(
      'Vaccination Status',
      Icons.vaccines,
      _vaccinationColors[0],
      [
        _buildInsightCard(
          'Up to Date',
          insights['upToDateCount'] ?? '0',
          'Cattle with current vaccinations',
          _vaccinationColors[0],
        ),
        _buildInsightCard(
          'Overdue',
          insights['overdueCount'] ?? '0',
          'Cattle with overdue vaccinations',
          _vaccinationColors[1],
        ),
        _buildInsightCard(
          'Coverage Rate',
          insights['coverageRate'] ?? 'No Data',
          'Overall vaccination coverage percentage',
          _vaccinationColors[2],
        ),
      ],
    );
  }

  Widget _buildPredictiveAnalytics() {
    final predictions = _calculatePredictiveAnalytics();

    return _buildInsightSection(
      'Predictive Health Analytics',
      Icons.psychology,
      _treatmentColors[2],
      [
        _buildPredictionCard(
          'Health Risk Forecast',
          predictions['riskForecast'] ?? 'No Data',
          'Predicted health risks based on historical patterns',
          _treatmentColors[0],
          Icons.warning,
        ),
        _buildPredictionCard(
          'Vaccination Schedule',
          predictions['nextVaccinations'] ?? 'No Data',
          'Upcoming vaccination requirements',
          _vaccinationColors[0],
          Icons.schedule,
        ),
        _buildPredictionCard(
          'Treatment Needs',
          predictions['treatmentNeeds'] ?? 'No Data',
          'Predicted treatment requirements',
          _treatmentColors[1],
          Icons.medical_services,
        ),
      ],
    );
  }

  Widget _buildPredictionCard(String title, String value, String description, Color color, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize(),
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize() + 2,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize() - 1,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendations() {
    final recommendations = _generateRecommendations();
    
    return _buildInsightSection(
      'Health Recommendations',
      Icons.lightbulb,
      _recommendationColors[0],
      recommendations.asMap().entries.map((entry) {
        final index = entry.key;
        final recommendation = entry.value;
        return _buildRecommendationCard(
          recommendation['title']!,
          recommendation['description']!,
          recommendation['priority']!,
          _recommendationColors[index % _recommendationColors.length],
        );
      }).toList(),
    );
  }

  Widget _buildInsightSection(String title, IconData icon, Color color, List<Widget> children) {
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          SizedBox(height: _getResponsiveSpacing()),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInsightCard(String title, String value, String description, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: _getResponsiveFontSize(),
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: _getResponsiveFontSize() + 2,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: TextStyle(
              fontSize: _getResponsiveFontSize() - 1,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationCard(String title, String description, String priority, Color color) {
    IconData priorityIcon;
    Color priorityColor;
    
    switch (priority.toLowerCase()) {
      case 'high':
        priorityIcon = Icons.priority_high;
        priorityColor = Colors.red;
        break;
      case 'medium':
        priorityIcon = Icons.warning;
        priorityColor = Colors.orange;
        break;
      default:
        priorityIcon = Icons.info;
        priorityColor = Colors.blue;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(_getResponsivePadding()),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(priorityIcon, color: priorityColor, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize(),
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: priorityColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  priority.toUpperCase(),
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize() - 2,
                    fontWeight: FontWeight.bold,
                    color: priorityColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: TextStyle(
              fontSize: _getResponsiveFontSize() - 1,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Map<String, String> _calculateHealthStatusInsights() {
    if (widget.healthRecords.isEmpty) {
      return {
        'healthScore': 'No Data',
        'atRiskCount': '0',
        'healthTrend': 'No Data',
      };
    }

    // Calculate health score based on recent records
    final recentRecords = widget.healthRecords.where((record) {
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      return record.date != null && record.date!.isAfter(thirtyDaysAgo);
    }).toList();

    int healthyCount = 0;
    int atRiskCount = 0;

    for (final record in recentRecords) {
      final status = record.healthStatus?.toLowerCase() ?? '';
      if (status.contains('healthy') || status.contains('good')) {
        healthyCount++;
      } else if (status.contains('sick') || status.contains('poor') || status.contains('critical')) {
        atRiskCount++;
      }
    }

    final totalCattle = widget.cattleMap.length;
    final healthScore = totalCattle > 0
        ? '${((healthyCount / totalCattle) * 100).round()}%'
        : 'No Data';

    // Determine health trend
    final lastMonth = widget.healthRecords.where((record) {
      final sixtyDaysAgo = DateTime.now().subtract(const Duration(days: 60));
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      return record.date != null && record.date!.isAfter(sixtyDaysAgo) && record.date!.isBefore(thirtyDaysAgo);
    }).length;

    final thisMonth = recentRecords.length;
    String healthTrend;
    if (thisMonth > lastMonth) {
      healthTrend = 'Improving';
    } else if (thisMonth < lastMonth) {
      healthTrend = 'Declining';
    } else {
      healthTrend = 'Stable';
    }

    return {
      'healthScore': healthScore,
      'atRiskCount': atRiskCount.toString(),
      'healthTrend': healthTrend,
    };
  }

  Map<String, String> _calculateTreatmentInsights() {
    if (widget.treatmentRecords.isEmpty) {
      return {
        'activeCount': '0',
        'successRate': 'No Data',
        'commonIssue': 'No Data',
      };
    }

    // Count active treatments
    final activeCount = widget.treatmentRecords.where((record) {
      final status = record.status?.toLowerCase() ?? '';
      return status == 'active' || status == 'ongoing';
    }).length;

    // Calculate success rate
    final completedTreatments = widget.treatmentRecords.where((record) {
      final status = record.status?.toLowerCase() ?? '';
      return status == 'completed' || status == 'discontinued';
    }).toList();

    final successfulTreatments = widget.treatmentRecords.where((record) {
      return record.status?.toLowerCase() == 'completed';
    }).length;

    final successRate = completedTreatments.isNotEmpty
        ? '${((successfulTreatments / completedTreatments.length) * 100).round()}%'
        : 'No Data';

    // Find most common issue
    final reasonCounts = <String, int>{};
    for (final record in widget.treatmentRecords) {
      final reason = record.reason ?? 'Unknown';
      reasonCounts[reason] = (reasonCounts[reason] ?? 0) + 1;
    }

    final commonIssue = reasonCounts.isNotEmpty
        ? reasonCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key
        : 'No Data';

    return {
      'activeCount': activeCount.toString(),
      'successRate': successRate,
      'commonIssue': commonIssue,
    };
  }

  Map<String, String> _calculateVaccinationInsights() {
    if (widget.vaccinationRecords.isEmpty) {
      return {
        'upToDateCount': '0',
        'overdueCount': '0',
        'coverageRate': 'No Data',
      };
    }

    final now = DateTime.now();
    int upToDateCount = 0;
    int overdueCount = 0;

    for (final record in widget.vaccinationRecords) {
      if (record.nextDueDate != null) {
        if (record.nextDueDate!.isAfter(now)) {
          upToDateCount++;
        } else {
          overdueCount++;
        }
      }
    }

    final totalCattle = widget.cattleMap.length;
    final coverageRate = totalCattle > 0
        ? '${((widget.vaccinationRecords.length / totalCattle) * 100).round()}%'
        : 'No Data';

    return {
      'upToDateCount': upToDateCount.toString(),
      'overdueCount': overdueCount.toString(),
      'coverageRate': coverageRate,
    };
  }

  Map<String, String> _calculatePredictiveAnalytics() {
    // Risk forecast based on health trends
    final recentHealthIssues = widget.healthRecords.where((record) {
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      return record.date != null && record.date!.isAfter(thirtyDaysAgo) &&
             (record.healthStatus?.toLowerCase().contains('sick') ?? false);
    }).length;

    String riskForecast;
    if (recentHealthIssues > 3) {
      riskForecast = 'High Risk';
    } else if (recentHealthIssues > 1) {
      riskForecast = 'Medium Risk';
    } else {
      riskForecast = 'Low Risk';
    }

    // Next vaccinations due
    final upcomingVaccinations = widget.vaccinationRecords.where((record) {
      if (record.nextDueDate == null) return false;
      final thirtyDaysFromNow = DateTime.now().add(const Duration(days: 30));
      return record.nextDueDate!.isBefore(thirtyDaysFromNow) &&
             record.nextDueDate!.isAfter(DateTime.now());
    }).length;

    final nextVaccinations = upcomingVaccinations > 0
        ? '$upcomingVaccinations due soon'
        : 'None due soon';

    // Treatment needs prediction
    final activeTreatments = widget.treatmentRecords.where((record) {
      final status = record.status?.toLowerCase() ?? '';
      return status == 'active' || status == 'ongoing';
    }).length;

    String treatmentNeeds;
    if (activeTreatments > 5) {
      treatmentNeeds = 'High demand';
    } else if (activeTreatments > 2) {
      treatmentNeeds = 'Moderate demand';
    } else {
      treatmentNeeds = 'Low demand';
    }

    return {
      'riskForecast': riskForecast,
      'nextVaccinations': nextVaccinations,
      'treatmentNeeds': treatmentNeeds,
    };
  }

  List<Map<String, String>> _generateRecommendations() {
    final recommendations = <Map<String, String>>[];

    // Check for overdue vaccinations
    final overdueVaccinations = widget.vaccinationRecords.where((record) {
      return record.nextDueDate != null && record.nextDueDate!.isBefore(DateTime.now());
    }).length;

    if (overdueVaccinations > 0) {
      recommendations.add({
        'title': 'Update Overdue Vaccinations',
        'description': '$overdueVaccinations cattle have overdue vaccinations. Schedule vaccination appointments to maintain herd immunity.',
        'priority': 'High',
      });
    }

    // Check for active treatments
    final activeTreatments = widget.treatmentRecords.where((record) {
      final status = record.status?.toLowerCase() ?? '';
      return status == 'active' || status == 'ongoing';
    }).length;

    if (activeTreatments > 3) {
      recommendations.add({
        'title': 'Monitor Active Treatments',
        'description': '$activeTreatments cattle are currently under treatment. Ensure proper follow-up and monitoring.',
        'priority': 'Medium',
      });
    }

    // Check for health screening
    final recentHealthChecks = widget.healthRecords.where((record) {
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      return record.date != null && record.date!.isAfter(thirtyDaysAgo);
    }).length;

    final totalCattle = widget.cattleMap.length;
    if (totalCattle > 0 && recentHealthChecks < totalCattle * 0.5) {
      recommendations.add({
        'title': 'Schedule Health Screenings',
        'description': 'Less than 50% of cattle have had recent health checks. Consider scheduling routine health screenings.',
        'priority': 'Medium',
      });
    }

    // Default recommendation if no specific issues
    if (recommendations.isEmpty) {
      recommendations.add({
        'title': 'Maintain Current Health Program',
        'description': 'Your cattle health management appears to be on track. Continue with regular monitoring and preventive care.',
        'priority': 'Low',
      });
    }

    return recommendations;
  }
}
