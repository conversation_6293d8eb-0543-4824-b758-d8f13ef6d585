import 'dart:io';
import '../../../services/logging_service.dart';
import '../models/backup_settings_isar.dart';
import 'farm_setup_handler.dart';

/// Service for backup analytics and monitoring
class BackupAnalyticsService {
  static final LoggingService _logger = LoggingService.instance;
  final FarmSetupHandler _farmSetupHandler;

  // Singleton instance
  static final BackupAnalyticsService _instance = BackupAnalyticsService._internal();
  static BackupAnalyticsService get instance => _instance;

  // Private constructor
  BackupAnalyticsService._internal()
      : _farmSetupHandler = FarmSetupHandler.instance;

  /// Get comprehensive backup analytics
  Future<BackupAnalytics> getBackupAnalytics() async {
    try {
      _logger.info('Generating backup analytics');
      
      final settings = await _farmSetupHandler.getBackupSettings();
      final backupHistory = await _getBackupHistory(settings.backupLocation);
      final storageInfo = await _getStorageInfo(settings.backupLocation);
      
      return BackupAnalytics(
        totalBackups: backupHistory.length,
        successfulBackups: backupHistory.where((b) => b['isValid'] == true).length,
        failedBackups: backupHistory.where((b) => b['isValid'] == false).length,
        lastBackupDate: settings.lastBackupDate,
        nextScheduledBackup: _calculateNextBackup(settings),
        totalStorageUsed: storageInfo['totalSize'] ?? 0,
        averageBackupSize: _calculateAverageSize(backupHistory),
        backupFrequency: settings.autoBackupFrequency,
        autoBackupEnabled: settings.autoBackupEnabled,
        backupLocation: settings.backupLocation,
        backupHistory: backupHistory,
        storageInfo: storageInfo,
        healthScore: _calculateHealthScore(settings, backupHistory),
        recommendations: _generateRecommendations(settings, backupHistory),
      );
    } catch (e) {
      _logger.severe('Error generating backup analytics: $e');
      return BackupAnalytics.empty();
    }
  }

  /// Get backup history from the backup directory
  Future<List<Map<String, dynamic>>> _getBackupHistory(String backupLocation) async {
    try {
      if (backupLocation.isEmpty) return [];
      
      return await _farmSetupHandler.listBackupFiles(backupLocation);
    } catch (e) {
      _logger.warning('Error getting backup history: $e');
      return [];
    }
  }

  /// Get storage information for the backup location
  Future<Map<String, dynamic>> _getStorageInfo(String backupLocation) async {
    try {
      if (backupLocation.isEmpty) {
        return {'totalSize': 0, 'availableSpace': 0, 'usedSpace': 0};
      }

      final directory = Directory(backupLocation);
      if (!await directory.exists()) {
        return {'totalSize': 0, 'availableSpace': 0, 'usedSpace': 0};
      }

      int totalSize = 0;
      await for (final entity in directory.list(recursive: false)) {
        if (entity is File && entity.path.endsWith('.isar')) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }

      // Note: Getting available disk space requires platform-specific implementation
      // For now, we'll return basic information
      return {
        'totalSize': totalSize,
        'availableSpace': 0, // Would need platform-specific implementation
        'usedSpace': totalSize,
        'fileCount': await _countBackupFiles(backupLocation),
      };
    } catch (e) {
      _logger.warning('Error getting storage info: $e');
      return {'totalSize': 0, 'availableSpace': 0, 'usedSpace': 0};
    }
  }

  /// Count backup files in directory
  Future<int> _countBackupFiles(String backupLocation) async {
    try {
      final directory = Directory(backupLocation);
      if (!await directory.exists()) return 0;

      int count = 0;
      await for (final entity in directory.list(recursive: false)) {
        if (entity is File && entity.path.endsWith('.isar')) {
          count++;
        }
      }
      return count;
    } catch (e) {
      return 0;
    }
  }

  /// Calculate next scheduled backup date
  DateTime? _calculateNextBackup(BackupSettingsIsar settings) {
    if (!settings.autoBackupEnabled || settings.lastBackupDate == null) {
      return null;
    }
    
    return settings.lastBackupDate!.add(Duration(days: settings.autoBackupFrequency));
  }

  /// Calculate average backup size
  double _calculateAverageSize(List<Map<String, dynamic>> backupHistory) {
    if (backupHistory.isEmpty) return 0.0;
    
    final totalSize = backupHistory.fold<int>(
      0, 
      (sum, backup) => sum + (backup['fileSize'] as int? ?? 0),
    );
    
    return totalSize / backupHistory.length;
  }

  /// Calculate backup health score (0-100)
  int _calculateHealthScore(BackupSettingsIsar settings, List<Map<String, dynamic>> backupHistory) {
    int score = 100;
    
    // Deduct points for no recent backups
    if (settings.lastBackupDate == null) {
      score -= 50;
    } else {
      final daysSinceLastBackup = DateTime.now().difference(settings.lastBackupDate!).inDays;
      if (daysSinceLastBackup > settings.autoBackupFrequency * 2) {
        score -= 30;
      } else if (daysSinceLastBackup > settings.autoBackupFrequency) {
        score -= 15;
      }
    }
    
    // Deduct points for failed backups
    final failedBackups = backupHistory.where((b) => b['isValid'] == false).length;
    if (failedBackups > 0) {
      score -= (failedBackups * 10).clamp(0, 30);
    }
    
    // Deduct points if auto backup is disabled
    if (!settings.autoBackupEnabled) {
      score -= 20;
    }
    
    // Deduct points for too few backups
    if (backupHistory.length < 3) {
      score -= 15;
    }
    
    return score.clamp(0, 100);
  }

  /// Generate recommendations based on backup status
  List<String> _generateRecommendations(BackupSettingsIsar settings, List<Map<String, dynamic>> backupHistory) {
    final recommendations = <String>[];
    
    // Check if auto backup is disabled
    if (!settings.autoBackupEnabled) {
      recommendations.add('Enable automatic backups to ensure regular data protection');
    }
    
    // Check for recent backups
    if (settings.lastBackupDate == null) {
      recommendations.add('Create your first backup to protect your farm data');
    } else {
      final daysSinceLastBackup = DateTime.now().difference(settings.lastBackupDate!).inDays;
      if (daysSinceLastBackup > settings.autoBackupFrequency * 2) {
        recommendations.add('Your last backup is overdue. Create a backup soon');
      }
    }
    
    // Check backup frequency
    if (settings.autoBackupFrequency > 14) {
      recommendations.add('Consider more frequent backups (weekly) for better data protection');
    }
    
    // Check for failed backups
    final failedBackups = backupHistory.where((b) => b['isValid'] == false).length;
    if (failedBackups > 0) {
      recommendations.add('Some backup files appear corrupted. Verify your backup location');
    }
    
    // Check backup count
    if (backupHistory.length < 3) {
      recommendations.add('Maintain at least 3 backup copies for better data safety');
    } else if (backupHistory.length > 20) {
      recommendations.add('Consider cleaning up old backups to save storage space');
    }
    
    // Check storage location
    if (settings.backupLocation.isEmpty) {
      recommendations.add('Set a proper backup location for your backups');
    }
    
    return recommendations;
  }

  /// Get backup trends over time
  Future<BackupTrends> getBackupTrends() async {
    try {
      final settings = await _farmSetupHandler.getBackupSettings();
      final backupHistory = await _getBackupHistory(settings.backupLocation);
      
      // Group backups by month
      final monthlyBackups = <String, int>{};
      final monthlySizes = <String, double>{};
      
      for (final backup in backupHistory) {
        final date = backup['backupDate'] as DateTime;
        final monthKey = '${date.year}-${date.month.toString().padLeft(2, '0')}';
        
        monthlyBackups[monthKey] = (monthlyBackups[monthKey] ?? 0) + 1;
        monthlySizes[monthKey] = (monthlySizes[monthKey] ?? 0) + 
            ((backup['fileSize'] as int? ?? 0) / 1024 / 1024); // Convert to MB
      }
      
      return BackupTrends(
        monthlyBackupCounts: monthlyBackups,
        monthlyBackupSizes: monthlySizes,
        totalBackupsOverTime: backupHistory.length,
        averageMonthlyBackups: monthlyBackups.values.isNotEmpty 
            ? monthlyBackups.values.reduce((a, b) => a + b) / monthlyBackups.length 
            : 0,
      );
    } catch (e) {
      _logger.severe('Error getting backup trends: $e');
      return BackupTrends.empty();
    }
  }

  /// Log backup operation for analytics
  Future<void> logBackupOperation({
    required String operation, // 'create', 'restore', 'delete'
    required bool success,
    String? filePath,
    int? fileSize,
    String? errorMessage,
  }) async {
    try {
      final logEntry = {
        'timestamp': DateTime.now().toIso8601String(),
        'operation': operation,
        'success': success,
        'filePath': filePath,
        'fileSize': fileSize,
        'errorMessage': errorMessage,
      };
      
      // In a real implementation, you might want to store this in a separate log collection
      _logger.info('Backup operation logged: $logEntry');
    } catch (e) {
      _logger.warning('Error logging backup operation: $e');
    }
  }
}

/// Backup analytics data class
class BackupAnalytics {
  final int totalBackups;
  final int successfulBackups;
  final int failedBackups;
  final DateTime? lastBackupDate;
  final DateTime? nextScheduledBackup;
  final int totalStorageUsed;
  final double averageBackupSize;
  final int backupFrequency;
  final bool autoBackupEnabled;
  final String backupLocation;
  final List<Map<String, dynamic>> backupHistory;
  final Map<String, dynamic> storageInfo;
  final int healthScore;
  final List<String> recommendations;

  BackupAnalytics({
    required this.totalBackups,
    required this.successfulBackups,
    required this.failedBackups,
    this.lastBackupDate,
    this.nextScheduledBackup,
    required this.totalStorageUsed,
    required this.averageBackupSize,
    required this.backupFrequency,
    required this.autoBackupEnabled,
    required this.backupLocation,
    required this.backupHistory,
    required this.storageInfo,
    required this.healthScore,
    required this.recommendations,
  });

  factory BackupAnalytics.empty() {
    return BackupAnalytics(
      totalBackups: 0,
      successfulBackups: 0,
      failedBackups: 0,
      totalStorageUsed: 0,
      averageBackupSize: 0.0,
      backupFrequency: 7,
      autoBackupEnabled: false,
      backupLocation: '',
      backupHistory: [],
      storageInfo: {},
      healthScore: 0,
      recommendations: [],
    );
  }
}

/// Backup trends data class
class BackupTrends {
  final Map<String, int> monthlyBackupCounts;
  final Map<String, double> monthlyBackupSizes;
  final int totalBackupsOverTime;
  final double averageMonthlyBackups;

  BackupTrends({
    required this.monthlyBackupCounts,
    required this.monthlyBackupSizes,
    required this.totalBackupsOverTime,
    required this.averageMonthlyBackups,
  });

  factory BackupTrends.empty() {
    return BackupTrends(
      monthlyBackupCounts: {},
      monthlyBackupSizes: {},
      totalBackupsOverTime: 0,
      averageMonthlyBackups: 0.0,
    );
  }
}
